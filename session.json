{"groups": {}, "workspaces": {"default": {"id": "default", "name": "默认", "homepage": "https://www.baidu.com", "activeTabId": 2, "groupId": "default-group", "tabs": {"2": {"url": "https://www.baidu.com/", "title": "百度一下，你就知道", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753312720079}}}, "workspace_7": {"id": "workspace_7", "name": "📺视频", "homepage": "http://bilibili.com", "activeTabId": 20, "groupId": "default-group", "tabs": {"7": {"url": "https://www.bilibili.com/", "title": "哔哩哔哩 (゜-゜)つロ 干杯~-bilibili", "customTitle": null, "isSleeping": true, "lastActiveTime": 1753236141360}, "20": {"url": "https://www.bilibili.com/video/BV1UTgJz1EHK/?spm_id_from=333.1007.tianma.2-3-6.click", "title": "量子密码：84年才有的技术，没资格在中国面前谈领先！_哔哩哔哩_bilibili", "customTitle": null, "isSleeping": true, "lastActiveTime": 1753236147559}}}, "workspace_8": {"id": "workspace_8", "name": "comfyui", "homepage": "http://192.168.101.11:8168", "activeTabId": 37, "groupId": "default-group", "tabs": {"37": {"url": "http://192.168.101.11:8168/", "title": "[25%] *Unsaved Workflow - ComfyUI", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753276736039}}}, "workspace_9": {"id": "workspace_9", "name": "👩豆包", "homepage": "http://doubao.com", "activeTabId": 43, "groupId": "default-group", "tabs": {"43": {"url": "https://www.doubao.com/auth/callback?code=a22356b20ef2ef41ZdRG4AnSxjVzUE7NfhlT_hl&state=eyJlbnRyeSI6IiIsInRyYWNrTWV0YSI6eyJsb2dpbl9lbnRyYW5jZSI6InNpZ25faW4iLCJpc19mcm9tX3VnIjoiMCIsImlzX2Zyb21fc2hhcmUiOiIwIn0sInBsYXRmb3JtIjoiYXdlbWVfdjIiLCJuYXZpZ2F0ZVBhdGgiOiIvY2hhdC8%2FZnJvbV9sb2dpbj0xJm9yaWdpbl9sYW5kaW5nPWh0dHBzJTNBJTJGJTJGd3d3LmRvdWJhby5jb20lMkZjaGF0JTJGIiwidHlwZSI6ImxvZ2luIiwiY3NyZlRva2VuIjoiNTQ0NmRhMjAtZmRlZS00YWQ4LTk1NGQtZjViMGI5OGZkNmI1In0%3D&scopes=user_info,mobile", "title": "创建 JavaScript 文本记录脚本 - 豆包", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753315169718}, "61": {"url": "https://jimeng.jianying.com/ai-tool/generate/", "title": "即梦AI - 一站式AI创作平台", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753315277304}, "62": {"url": "https://jimeng.jianying.com/ai-tool/third-party-callback?is_new_user=0", "title": "即梦AI - 一站式AI创作平台", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753315223223}}}, "workspace_19": {"id": "workspace_19", "name": "🌺花瓣", "homepage": "http://huaban.com", "activeTabId": 60, "groupId": "default-group", "tabs": {"21": {"url": "https://huaban.com/materials", "title": "花瓣素材-设计素材图库-专业设计模版下载", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753314793267}, "29": {"url": "https://huaban.com/follow", "title": "花瓣网 - 陪你做生活的设计师（创意灵感天堂，搜索、发现设计灵感、设计素材）", "customTitle": null, "isSleeping": true, "lastActiveTime": 1753237152258}, "55": {"url": "https://www.gaoding.art/", "title": "稿定AI社区-在线AI创意灵感平台(AI生图做同款-创意图像-AI工具)", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753314794425}, "56": {"url": "https://www.gaoding.art/inspirations/194437477", "title": "3D玉石质感｜国风山水背景艺术创意_AI做同款工具-稿定AI社区", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753314856468}, "57": {"url": "https://www.gaoding.art/inspirations/194189533", "title": "运动男孩｜潮流服饰3d人偶！_AI做同款工具-稿定AI社区", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753314911855}, "58": {"url": "https://www.gaoding.art/inspirations/194437486", "title": "插画贴纸素材｜红色樱桃小女孩_AI做同款工具-稿定AI社区", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753315008297}, "59": {"url": "https://www.gaoding.art/inspirations/194436171", "title": "国风水墨质感 | 荷间小憩_AI做同款工具-稿定AI社区", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753315049442}, "60": {"url": "https://www.gaoding.art/editor/canvas?type=board&mode=create", "title": "未命名_设计页－稿定", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753315075702}}}, "workspace_20": {"id": "workspace_20", "name": "搜索", "homepage": "http://127.0.0.1:4157", "activeTabId": 27, "groupId": "default-group", "tabs": {"27": {"url": "https://i.360.cn/login?src=pcw_newso&destUrl=https%3A%2F%2Fwww.so.com%2F%3Fsrc%3Dso.com", "title": "登录-360帐号中心", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753312413456}, "49": {"url": "https://www.baidu.com/", "title": "百度一下，你就知道", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753312412087}}}, "workspace_21": {"id": "workspace_21", "name": "💰消费", "homepage": "https://myseller.taobao.com/home.htm", "activeTabId": 42, "groupId": "default-group", "tabs": {"12": {"url": "http://saikr.haixingdiy.cn/auth/login", "title": "赛客诺有限公司", "customTitle": null, "isSleeping": true, "lastActiveTime": 1753312440372}, "13": {"url": "https://work.1688.com/home/<USER>/index.htm", "title": "1688-买家工作台", "customTitle": null, "isSleeping": true, "lastActiveTime": 1753313452979}, "42": {"url": "https://mms.pinduoduo.com/print/order/list", "title": "打单工具", "customTitle": "多打单", "isSleeping": false, "lastActiveTime": 1753313458080}, "51": {"url": "https://myseller.taobao.com/home.htm", "title": "千牛商家工作台", "customTitle": null, "isSleeping": true, "lastActiveTime": 1753313455645}, "53": {"url": "https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?spm=tbpc.mytb_index.leftnav.2.6db2782d3rKGBj", "title": "已买到的宝贝", "customTitle": null, "isSleeping": true, "lastActiveTime": 1753313451583}}}, "workspace_22": {"id": "workspace_22", "name": "🛍️库存", "homepage": "http://127.0.0.1:7215/?sort=id", "activeTabId": 34, "groupId": "default-group", "tabs": {"34": {"url": "http://127.0.0.1:7215/?sort=id", "title": "商品进销存", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753312424013}}}, "workspace_23": {"id": "workspace_23", "name": "🩲瑟瑟", "homepage": "http://x.com", "activeTabId": 50, "groupId": "default-group", "tabs": {"17": {"url": "https://x.com/", "title": "(5) X 上的 小纶：“@r4ch4G 看样子给男主播看呆了 https://t.co/tXf91oCzgz” / X", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753312454056}, "31": {"url": "https://x.com/", "title": "(5) 主页 / X", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753312452908}, "50": {"url": "https://x.com/xieziqiu1027", "title": "(5) xieziqiu (@xieziqiu1027) / X", "customTitle": null, "isSleeping": false, "lastActiveTime": 1753312604953}}}, "workspace_25": {"id": "workspace_25", "name": "<PERSON><PERSON><PERSON>", "homepage": "http://github.com", "activeTabId": 33, "groupId": "default-group", "tabs": {"33": {"url": "https://github.com/yami-pro/yami-rpg-editor", "title": "whitesmith/kimi-code: Claude Code for Kimi K2", "customTitle": null, "isSleeping": true, "lastActiveTime": 1753238862820}}}}, "separators": {"separator_5": {"id": "separator_5", "name": "娱乐", "type": "separator", "collapsed": false}, "separator_6": {"id": "separator_6", "name": "工作", "type": "separator", "collapsed": false}, "separator_7": {"id": "separator_7", "name": "ai", "type": "separator", "collapsed": false}, "separator_24": {"id": "separator_24", "name": "开发", "type": "separator", "collapsed": false}}, "workspaceOrder": [{"type": "workspace", "id": "default"}, {"type": "separator", "id": "separator_5"}, {"type": "workspace", "id": "workspace_20"}, {"type": "workspace", "id": "workspace_19"}, {"type": "workspace", "id": "workspace_23"}, {"type": "workspace", "id": "workspace_7"}, {"type": "separator", "id": "separator_6"}, {"type": "workspace", "id": "workspace_21"}, {"type": "workspace", "id": "workspace_22"}, {"type": "separator", "id": "separator_7"}, {"type": "workspace", "id": "workspace_8"}, {"type": "workspace", "id": "workspace_9"}, {"type": "separator", "id": "separator_24"}, {"type": "workspace", "id": "workspace_25"}], "lastActiveWorkspace": "workspace_9", "lastActiveTab": 61}