<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>用户脚本管理</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    .header {
      background: #007acc;
      color: white;
      padding: 20px;
      text-align: center;
      position: relative;
    }

    .header h1 {
      margin: 0;
      font-size: 24px;
    }

    .header-close-btn {
      position: absolute;
      top: 15px;
      right: 20px;
      background: none;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;
      transition: background-color 0.3s;
    }

    .header-close-btn:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
    
    .content {
      padding: 20px;
    }
    
    .script-item {
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 15px;
      padding: 15px;
      background: #fafafa;
    }
    
    .script-header {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .script-name {
      font-weight: bold;
      font-size: 16px;
      color: #333;
    }
    
    .script-url {
      color: #666;
      font-size: 14px;
      margin-bottom: 10px;
    }
    
    .script-actions {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .btn-primary {
      background: #007acc;
      color: white;
    }
    
    .btn-secondary {
      background: #e0e0e0;
      color: #333;
    }
    
    .btn-danger {
      background: #dc3545;
      color: white;
    }
    
    .btn:hover {
      opacity: 0.8;
    }
    
    .add-script {
      border: 2px dashed #ddd;
      border-radius: 4px;
      padding: 20px;
      text-align: center;
      margin-bottom: 20px;
      cursor: pointer;
      transition: border-color 0.3s;
    }
    
    .add-script:hover {
      border-color: #007acc;
    }

    .actions-bar {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      align-items: flex-start;
    }

    .add-script {
      flex: 1;
    }

    .import-export-buttons {
      display: flex;
      flex-direction: column;
      gap: 10px;
      min-width: 150px;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    .form-group input,
    .form-group textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .form-group textarea {
      height: 200px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
    
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 1000;
    }
    
    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 8px;
      padding: 20px;
      width: 90%;
      max-width: 600px;
      max-height: 80%;
      overflow-y: auto;
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .modal-title {
      font-size: 18px;
      font-weight: bold;
    }
    
    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }
    
    .close-btn:hover {
      color: #333;
    }
    
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }
    
    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }
    
    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    
    input:checked + .slider {
      background-color: #007acc;
    }
    
    input:checked + .slider:before {
      transform: translateX(26px);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>用户脚本管理</h1>
      <button class="header-close-btn" onclick="closeWindow()" title="关闭窗口">&times;</button>
    </div>
    
    <div class="content">
      <div class="actions-bar">
        <div class="add-script" onclick="showAddScriptModal()">
          <h3>+ 添加新脚本</h3>
          <p>点击这里添加新的用户脚本</p>
        </div>

        <div class="import-export-buttons">
          <button class="btn btn-secondary" onclick="exportUserScripts()">导出所有脚本</button>
          <button class="btn btn-secondary" onclick="importUserScripts()">导入脚本</button>
          <input type="file" id="import-file" style="display: none;" accept=".json" onchange="handleImportFile(event)">
        </div>
      </div>

      <div id="scripts-list">
        <!-- 脚本列表将在这里动态生成 -->
      </div>
    </div>
  </div>
  
  <!-- 添加/编辑脚本模态框 -->
  <div id="script-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title" id="modal-title">添加用户脚本</div>
        <button class="close-btn" onclick="hideScriptModal()">&times;</button>
      </div>
      
      <form id="script-form">
        <div class="form-group">
          <label for="script-name">脚本名称:</label>
          <input type="text" id="script-name" required>
        </div>
        
        <div class="form-group">
          <label for="script-match">匹配规则 (支持通配符 *):</label>
          <input type="text" id="script-match" placeholder="例如: https://www.example.com/* 或 *://github.com/*" required>
        </div>
        
        <div class="form-group">
          <label for="script-code">JavaScript 代码:</label>
          <textarea id="script-code" placeholder="在这里输入您的 JavaScript 代码..." required></textarea>
        </div>
        
        <div class="form-group">
          <label>
            <input type="checkbox" id="script-enabled" checked>
            启用此脚本
          </label>
        </div>
        
        <div class="script-actions">
          <button type="submit" class="btn btn-primary">保存</button>
          <button type="button" class="btn btn-secondary" onclick="hideScriptModal()">取消</button>
        </div>
      </form>
    </div>
  </div>
  
  <script>
    let userScripts = []
    let editingScriptId = null
    
    // 初始化
    document.addEventListener('DOMContentLoaded', async () => {
      await loadUserScripts()
      renderScriptsList()
    })

    // 关闭窗口
    function closeWindow() {
      window.close()
    }
    
    // 加载用户脚本
    async function loadUserScripts() {
      try {
        userScripts = await window.electronAPI.getUserScripts()
      } catch (error) {
        console.error('Failed to load user scripts:', error)
        userScripts = []
      }
    }
    
    // 渲染脚本列表
    function renderScriptsList() {
      const container = document.getElementById('scripts-list')
      container.innerHTML = ''
      
      if (userScripts.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #666;">暂无用户脚本</p>'
        return
      }
      
      userScripts.forEach(script => {
        const scriptElement = createScriptElement(script)
        container.appendChild(scriptElement)
      })
    }
    
    // 创建脚本元素
    function createScriptElement(script) {
      const div = document.createElement('div')
      div.className = 'script-item'
      div.innerHTML = `
        <div class="script-header">
          <div class="script-name">${script.name}</div>
          <label class="toggle-switch">
            <input type="checkbox" ${script.enabled ? 'checked' : ''} onchange="toggleScript('${script.id}')">
            <span class="slider"></span>
          </label>
        </div>
        <div class="script-url">${script.match}</div>
        <div class="script-actions">
          <button class="btn btn-primary" onclick="editScript('${script.id}')">编辑</button>
          <button class="btn btn-danger" onclick="deleteScript('${script.id}')">删除</button>
        </div>
      `
      return div
    }
    
    // 显示添加脚本模态框
    function showAddScriptModal() {
      editingScriptId = null
      document.getElementById('modal-title').textContent = '添加用户脚本'
      document.getElementById('script-form').reset()
      document.getElementById('script-modal').style.display = 'block'
    }
    
    // 隐藏脚本模态框
    function hideScriptModal() {
      document.getElementById('script-modal').style.display = 'none'
    }
    
    // 编辑脚本
    function editScript(scriptId) {
      const script = userScripts.find(s => s.id === scriptId)
      if (!script) return
      
      editingScriptId = scriptId
      document.getElementById('modal-title').textContent = '编辑用户脚本'
      document.getElementById('script-name').value = script.name
      document.getElementById('script-match').value = script.match
      document.getElementById('script-code').value = script.code
      document.getElementById('script-enabled').checked = script.enabled
      document.getElementById('script-modal').style.display = 'block'
    }
    
    // 切换脚本启用状态
    async function toggleScript(scriptId) {
      const script = userScripts.find(s => s.id === scriptId)
      if (!script) return
      
      script.enabled = !script.enabled
      await saveUserScripts()
    }
    
    // 删除脚本
    async function deleteScript(scriptId) {
      if (!confirm('确定要删除这个脚本吗？')) return
      
      userScripts = userScripts.filter(s => s.id !== scriptId)
      await saveUserScripts()
      renderScriptsList()
    }
    
    // 保存用户脚本
    async function saveUserScripts() {
      try {
        await window.electronAPI.saveUserScripts(userScripts)
      } catch (error) {
        console.error('Failed to save user scripts:', error)
        alert('保存失败: ' + error.message)
      }
    }
    
    // 表单提交处理
    document.getElementById('script-form').addEventListener('submit', async (e) => {
      e.preventDefault()
      
      const name = document.getElementById('script-name').value.trim()
      const match = document.getElementById('script-match').value.trim()
      const code = document.getElementById('script-code').value.trim()
      const enabled = document.getElementById('script-enabled').checked
      
      if (!name || !match || !code) {
        alert('请填写所有必填字段')
        return
      }
      
      const script = {
        id: editingScriptId || Date.now().toString(),
        name,
        match,
        code,
        enabled
      }
      
      if (editingScriptId) {
        const index = userScripts.findIndex(s => s.id === editingScriptId)
        if (index !== -1) {
          userScripts[index] = script
        }
      } else {
        userScripts.push(script)
      }
      
      await saveUserScripts()
      renderScriptsList()
      hideScriptModal()
    })

    // 导出用户脚本
    function exportUserScripts() {
      const dataStr = JSON.stringify(userScripts, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)

      const link = document.createElement('a')
      link.href = url
      link.download = `userscripts_${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }

    // 导入用户脚本
    function importUserScripts() {
      document.getElementById('import-file').click()
    }

    // 处理导入文件
    function handleImportFile(event) {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = async function(e) {
        try {
          const importedScripts = JSON.parse(e.target.result)

          if (!Array.isArray(importedScripts)) {
            alert('导入文件格式错误')
            return
          }

          // 验证脚本格式
          const validScripts = importedScripts.filter(script =>
            script.id && script.name && script.match && script.code
          )

          if (validScripts.length === 0) {
            alert('导入文件中没有有效的脚本')
            return
          }

          // 询问是否覆盖现有脚本
          const shouldReplace = confirm(`将导入 ${validScripts.length} 个脚本。是否覆盖现有脚本？\n点击"确定"覆盖，点击"取消"追加到现有脚本。`)

          if (shouldReplace) {
            userScripts = validScripts
          } else {
            // 追加脚本，避免ID冲突
            validScripts.forEach(script => {
              script.id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
              userScripts.push(script)
            })
          }

          await saveUserScripts()
          renderScriptsList()
          alert(`成功导入 ${validScripts.length} 个脚本`)
        } catch (error) {
          console.error('Import error:', error)
          alert('导入失败：文件格式错误')
        }
      }
      reader.readAsText(file)

      // 清空文件输入
      event.target.value = ''
    }
  </script>
</body>
</html>
