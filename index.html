<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Electron Browser</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <!-- 左侧面板 -->
  <div class="left-panel" id="left-panel">
    <div class="workspace-header">
      <button id="new-workspace-btn" class="workspace-btn">+</button>
    </div>
    <div class="workspace-list" id="workspace-list">
      <!-- 工作区将动态添加到这里 -->
    </div>
  </div>

  <div class="browser-header">
    <!-- 导航按钮 -->
    <div class="navigation-controls">
      <button id="back-btn" class="nav-btn" disabled>←</button>
      <button id="forward-btn" class="nav-btn" disabled>→</button>
      <button id="reload-btn" class="nav-btn">⟳</button>
      <button id="home-btn" class="nav-btn">🏠</button>
    </div>
    
    <!-- 地址栏 -->
    <div class="address-bar-container">
      <input type="text" id="address-bar" placeholder="输入网址或搜索...">
    </div>
    
    <!-- 标签页控制 -->
    <div class="tab-controls">
      <button id="toggle-right-panel-btn" class="nav-btn">⚏</button>
      <button id="userscripts-btn" class="nav-btn" title="用户脚本管理">🐒</button>
      <button id="notes-btn" class="nav-btn" title="笔记管理">📝</button>
      <button id="password-manager-btn" class="nav-btn" title="密码管理器">🔐</button>
      <button id="settings-btn" class="nav-btn">⚙</button>
    </div>
  </div>
  
  <!-- 标签栏 -->
  <div class="tab-bar" id="tab-bar">
    <button id="new-tab-btn" class="nav-btn new-tab-btn">+</button>
  </div>
  
  <!-- 右侧面板 -->
  <div class="right-panel" id="right-panel" style="display: none;">
    <div class="right-panel-header">
      <div class="right-navigation-controls">
        <button id="right-back-btn" class="nav-btn" disabled>←</button>
        <button id="right-forward-btn" class="nav-btn" disabled>→</button>
        <button id="right-reload-btn" class="nav-btn">⟳</button>
        <button id="right-home-btn" class="nav-btn">🏠</button>
      </div>
      
      <div class="right-address-bar-container">
        <input type="text" id="right-address-bar" placeholder="输入网址或搜索...">
      </div>
      
      <div class="right-tab-controls">
        <button id="right-new-tab-btn" class="nav-btn">+</button>
      </div>
    </div>
    
    <div class="right-tab-bar" id="right-tab-bar">
      <!-- 右侧标签页将动态添加到这里 -->
    </div>
  </div>
  
  <!-- 状态栏 -->
  <div class="status-bar">
    <span id="status-text">就绪</span>
    <span id="url-preview"></span>
  </div>
  
  <script src="renderer.js"></script>
</body>
</html>
