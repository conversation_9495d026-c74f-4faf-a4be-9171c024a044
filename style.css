:root {
  --left-panel-width: 70px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f5f5f5;
  overflow: hidden;
}

/* 左侧面板样式 */
.left-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--left-panel-width);
  height: 100vh;
  background: #2c2c2c;
  border-right: 1px solid #444;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.workspace-header {
  padding: 10px;
  border-bottom: 1px solid #444;
}

.workspace-btn {
  width: 100%;
  height: 40px;
  border: none;
  border-radius: 4px;
  background: #444;
  color: white;
  cursor: pointer;
  font-size: 16px;
}

.workspace-btn:hover {
  background: #555;
}

.workspace-list {
  flex: 1;
  padding: 10px 5px;
  overflow-y: auto;
}

.workspace-item {
  padding: 10px;
  margin-bottom: 5px;
  background: #444;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
  word-break: break-all;
}

.workspace-item:hover {
  background: #555;
}

.workspace-item.active {
  background: #007acc;
}

.browser-header {
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid #ddd;
  padding: 8px 12px;
  height: 50px;
  margin-left: var(--left-panel-width);
  -webkit-app-region: drag;
}

.navigation-controls,.right-navigation-controls {
  display: flex;
  gap: 4px;
  margin-right: 12px;
  -webkit-app-region: no-drag;
}

.right-address-bar-container {
  display: flex;
  gap: 4px;
  flex:1;
  -webkit-app-region: no-drag;
}

#right-address-bar{
  flex:1;
}

.nav-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: #f0f0f0;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.nav-btn:hover:not(:disabled) {
  background: #e0e0e0;
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.address-bar-container {
  flex: 1;
  margin-right: 12px;
  -webkit-app-region: no-drag;
}

#address-bar {
  width: 100%;
  height: 32px;
  border: 1px solid #ddd;
  border-radius: 16px;
  padding: 0 16px;
  font-size: 14px;
  outline: none;
}

#address-bar:focus {
  border-color: #007acc;
}

.tab-controls {
  -webkit-app-region: no-drag;
  display: flex;
  gap: 4px;
}

.tab-bar {
  display: flex;
  background: #f0f0f0;
  border-bottom: 1px solid #ddd;
  height: 40px;
  overflow-x: auto;
  overflow-y: hidden;
  margin-left: var(--left-panel-width);
  align-items: center;
  padding: 0 8px;
}

.new-tab-btn {
  margin-left: auto;
  flex-shrink: 0;
}

.tab {
  display: flex;
  align-items: center;
  min-width: 200px;
  max-width: 250px;
  height: 32px;
  background: #e0e0e0;
  border-right: 1px solid #ccc;
  cursor: pointer;
  padding: 0 12px;
  position: relative;
  border-radius: 4px 4px 0 0;
  margin-right: 2px;
  -webkit-app-region: no-drag;
}

.tab.active {
  background: #fff;
  border-bottom: 2px solid #007acc;
}

.tab-title {
  flex: 1;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
}

.tab-close {
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 2px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-close:hover {
  background: #ff4444;
  color: white;
}

/* 右侧面板样式 */
.right-panel {
  position: fixed;
  top: 90px;
  right: 0;
  width: 400px;
  height: calc(100vh - 114px);
  background: #fff;
  border-left: 1px solid #ddd;
  z-index: 1000;
}

.right-panel-header {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-bottom: 1px solid #ddd;
  padding: 4px 8px;
  height: 40px;
}

.right-tab-bar {
  display: flex;
  flex-direction: row;
  background: #f0f0f0;
  border-bottom: 1px solid #ddd;
  height: 40px;
  overflow-x: auto;
  overflow-y: hidden;
}

.right-tab-bar .tab {
  min-width: 120px;
  max-width: 150px;
  width: auto;
  height: 40px;
  font-size: 11px;
  border-right: 1px solid #ccc;
  border-bottom: none;
  flex-shrink: 0;
}

.right-tab-bar .tab.active {
  border-bottom: 2px solid #007acc;
  border-left: none;
}

.resize-handle {
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: transparent;
  cursor: ew-resize;
  z-index: 1001;
}

.resize-handle:hover {
  background: #007acc;
}

.status-bar {
  position: fixed;
  bottom: 0;
  left: var(--left-panel-width);
  right: 0;
  height: 24px;
  background: #f0f0f0;
  border-top: 1px solid #ddd;
  display: flex;
  align-items: center;
  padding: 0 12px;
  font-size: 12px;
  color: #666;
}

#url-preview {
  margin-left: auto;
}

/* 滚动条样式 */
.tab-bar::-webkit-scrollbar,
.right-tab-bar::-webkit-scrollbar,
.workspace-list::-webkit-scrollbar {
  height: 4px;
  width: 4px;
}

.tab-bar::-webkit-scrollbar-track,
.right-tab-bar::-webkit-scrollbar-track,
.workspace-list::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.tab-bar::-webkit-scrollbar-thumb,
.right-tab-bar::-webkit-scrollbar-thumb,
.workspace-list::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

/* 设置模态框样式 */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.settings-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.settings-content h3 {
  margin-bottom: 20px;
  text-align: center;
}

.setting-item {
  margin-bottom: 15px;
}

.setting-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.setting-item input,
.setting-item select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.setting-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.setting-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

#save-settings {
  background: #007acc;
  color: white;
}

#cancel-settings {
  background: #f0f0f0;
  color: #333;
}

.setting-buttons button:hover {
  opacity: 0.8;
}
