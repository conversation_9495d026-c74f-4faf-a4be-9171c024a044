#!/bin/bash
# macOS启动脚本 - 双击启动Electron浏览器

# 设置脚本在出错时退出
set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "=== Electron浏览器启动器 ==="
echo "当前目录: $SCRIPT_DIR"

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未检测到Node.js，请先安装Node.js"
    echo "📥 下载地址: https://nodejs.org/"
    echo "💡 建议使用Homebrew安装: brew install node"
    read -p "按回车键退出..."
    exit 1
fi

# 检查npm是否可用
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: npm不可用，请检查Node.js安装"
    read -p "按回车键退出..."
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"
echo "✅ npm版本: $(npm --version)"

# 检查package.json是否存在
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到package.json文件，请确保在正确的目录中运行"
    read -p "按回车键退出..."
    exit 1
fi

# 检查node_modules是否存在，如果不存在则安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 正在安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 错误: 依赖包安装失败"
        read -p "按回车键退出..."
        exit 1
    fi
    echo "✅ 依赖包安装完成"
fi

# 启动应用
echo "🚀 正在启动Electron浏览器..."
npm start

# 如果应用异常退出，显示错误信息
if [ $? -ne 0 ]; then
    echo "❌ 应用启动失败，错误代码: $?"
    read -p "按回车键退出..."
fi
