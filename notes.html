<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>笔记管理器</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header h1 {
      font-size: 20px;
      font-weight: 600;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s;
    }
    
    .btn-primary {
      background: rgba(255,255,255,0.2);
      color: white;
    }
    
    .btn-primary:hover {
      background: rgba(255,255,255,0.3);
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #5a6268;
    }
    
    .main-content {
      flex: 1;
      display: flex;
      overflow: hidden;
    }
    
    .notes-sidebar {
      width: 300px;
      background: white;
      border-right: 1px solid #e0e0e0;
      display: flex;
      flex-direction: column;
    }
    
    .sidebar-header {
      padding: 15px;
      border-bottom: 1px solid #e0e0e0;
      background: #f8f9fa;
    }
    
    .search-box {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      outline: none;
    }
    
    .search-box:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102,126,234,0.2);
    }
    
    .notes-list {
      flex: 1;
      overflow-y: auto;
      padding: 10px;
    }
    
    .note-item {
      padding: 12px;
      margin-bottom: 8px;
      background: #f8f9fa;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .note-item:hover {
      background: #e9ecef;
      border-color: #667eea;
    }
    
    .note-item.active {
      background: #667eea;
      color: white;
      border-color: #667eea;
    }
    
    .note-title {
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .note-preview {
      font-size: 12px;
      color: #666;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .note-item.active .note-preview {
      color: rgba(255,255,255,0.8);
    }
    
    .note-date {
      font-size: 11px;
      color: #999;
      margin-top: 4px;
    }
    
    .note-item.active .note-date {
      color: rgba(255,255,255,0.6);
    }
    
    .notes-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: white;
    }
    
    .content-header {
      padding: 15px 20px;
      border-bottom: 1px solid #e0e0e0;
      background: #f8f9fa;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .content-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
    
    .content-actions {
      display: flex;
      gap: 8px;
    }
    
    .content-body {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }
    
    .note-editor {
      width: 100%;
      height: 100%;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 15px;
      font-size: 14px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      resize: none;
      outline: none;
    }
    
    .note-editor:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102,126,234,0.2);
    }
    
    .note-viewer {
      width: 100%;
      height: 100%;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background: white;
      overflow-y: auto;
    }
    
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #666;
      text-align: center;
    }
    
    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
    
    .empty-state h3 {
      font-size: 18px;
      margin-bottom: 8px;
      color: #333;
    }
    
    .empty-state p {
      font-size: 14px;
      color: #666;
    }

    /* 紧凑模式样式 */
    body.compact-mode {
      position: fixed;
      top: 0;
      right: 0;
      width: 600px;
      height: 100vh;
      z-index: 9999;
    }

    body.compact-mode .notes-sidebar {
      display: none;
    }

    body.compact-mode .notes-content {
      flex: 1;
    }

    #compact-btn {
      transition: transform 0.3s;
    }

    body.compact-mode #compact-btn {
      transform: rotate(180deg);
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>📝 笔记管理器</h1>
    <div class="header-actions">
      <button class="btn btn-primary" onclick="createNewNote()">新建笔记</button>
      <button class="btn btn-primary" onclick="toggleCompactMode()" id="compact-btn" title="切换紧凑模式">▶️</button>
      <button class="btn btn-secondary" onclick="closeWindow()">关闭</button>
    </div>
  </div>
  
  <div class="main-content">
    <div class="notes-sidebar">
      <div class="sidebar-header">
        <input type="text" class="search-box" placeholder="搜索笔记..." onkeyup="searchNotes(this.value)">
      </div>
      <div class="notes-list" id="notes-list">
        <!-- 笔记列表将在这里动态生成 -->
      </div>
    </div>
    
    <div class="notes-content">
      <div class="content-header" id="content-header" style="display: none;">
        <div class="content-title" id="content-title">未选择笔记</div>
        <div class="content-actions">
          <button class="btn btn-primary" id="edit-btn" onclick="toggleEdit()" style="background:green">编辑</button>
          <button class="btn btn-primary" id="save-btn" onclick="saveNote()" style="display: none;background:red;">保存</button>
          <button class="btn btn-secondary" onclick="copyToClipboard()">复制</button>
          <button class="btn btn-secondary" onclick="deleteCurrentNote()">删除</button>
        </div>
      </div>
      
      <div class="content-body" id="content-body">
        <div class="empty-state" id="empty-state">
          <div class="empty-state-icon">📝</div>
          <h3>选择一个笔记开始阅读</h3>
          <p>或者创建一个新笔记</p>
        </div>
        
        <div id="note-viewer" class="note-viewer" style="display: none;"></div>
        <textarea id="note-editor" class="note-editor" style="display: none;" placeholder="在这里输入笔记内容...支持HTML标签"></textarea>
      </div>
    </div>
  </div>
<div id="提醒" style="position:fixed;top:50%;left:50%;translate: -50% -50%;background:#ddd;padding:15px;font-size:25px;display:none"></div>
  <script>
    let notes = []
    let currentNoteId = null
    let isEditing = false
    
    // 初始化
    document.addEventListener('DOMContentLoaded', async () => {
      await loadNotes()
      renderNotesList()
    })
    
    // 关闭窗口
    function closeWindow() {
      window.close()
    }
    
    // 加载笔记
    async function loadNotes() {
      try {
        notes = await window.electronAPI.getNotes()
      } catch (error) {
        console.error('Failed to load notes:', error)
        notes = []
      }
    }
    
    // 保存笔记
    async function saveNotes() {
      try {
        await window.electronAPI.saveNotes(notes)
      } catch (error) {
        console.error('Failed to save notes:', error)
      }
    }
//提醒
function showRemind(msg){
  const remind = document.getElementById('提醒')
  remind.style.display='block'
  remind.textContent=msg
  setTimeout(()=>{
    remind.style.display='none'
  },2000)
}
    // 渲染笔记列表
    function renderNotesList() {
      const notesList = document.getElementById('notes-list')
      notesList.innerHTML = ''

      if (notes.length === 0) {
        notesList.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">暂无笔记</div>'
        return
      }

      notes.forEach(note => {
        const noteItem = document.createElement('div')
        noteItem.className = 'note-item'
        if (note.id === currentNoteId) {
          noteItem.classList.add('active')
        }

        const preview = note.content.replace(/<[^>]*>/g, '').substring(0, 50)
        const date = new Date(note.updatedAt).toLocaleDateString()

        noteItem.innerHTML = `
          <div class="note-title">${escapeHtml(note.title)}</div>
          <div class="note-preview">${escapeHtml(preview)}${preview.length >= 50 ? '...' : ''}</div>
          <div class="note-date">${date}</div>
        `

        noteItem.addEventListener('click', () => selectNote(note.id))
        notesList.appendChild(noteItem)
      })
    }

    // 选择笔记
    function selectNote(noteId) {
      currentNoteId = noteId
      const note = notes.find(n => n.id === noteId)
      if (!note) return

      // 更新UI状态
      renderNotesList()
      showNoteContent(note)
    }

    // 显示笔记内容
    function showNoteContent(note) {
      const emptyState = document.getElementById('empty-state')
      const contentHeader = document.getElementById('content-header')
      const contentTitle = document.getElementById('content-title')
      const noteViewer = document.getElementById('note-viewer')
      const noteEditor = document.getElementById('note-editor')
      const editBtn = document.getElementById('edit-btn')

      emptyState.style.display = 'none'
      contentHeader.style.display = 'flex'
      contentTitle.textContent = note.title

      // 默认显示预览模式
      isEditing = false
      editBtn.textContent = '编辑'
      noteEditor.style.display = 'none'
      noteViewer.style.display = 'block'
      noteViewer.innerHTML = note.content
    }

    // 切换编辑模式
    function toggleEdit() {
      const editBtn = document.getElementById('edit-btn')
      const saveBtn = document.getElementById('save-btn')
      const noteViewer = document.getElementById('note-viewer')
      const noteEditor = document.getElementById('note-editor')

      if (!currentNoteId) return

      if (isEditing) {
        // 退出编辑模式
        isEditing = false
        editBtn.textContent = '编辑'
        editBtn.style.display = 'inline-block'
        saveBtn.style.display = 'none'
        noteEditor.style.display = 'none'
        noteViewer.style.display = 'block'

        // 显示当前内容（不自动保存）
        const note = notes.find(n => n.id === currentNoteId)
        if (note) {
          noteViewer.innerHTML = note.content
        }
      } else {
        // 进入编辑模式
        const note = notes.find(n => n.id === currentNoteId)
        if (note) {
          isEditing = true
          editBtn.textContent = '取消'
          saveBtn.style.display = 'inline-block'
          noteViewer.style.display = 'none'
          noteEditor.style.display = 'block'
          noteEditor.value = note.content
          noteEditor.focus()
        }
      }
    }

    // 保存笔记
    async function saveNote() {
      if (!currentNoteId || !isEditing) return

      const note = notes.find(n => n.id === currentNoteId)
      if (note) {
        note.content = document.getElementById('note-editor').value
        note.updatedAt = new Date().toISOString()
        await saveNotes()
        renderNotesList()

        // 退出编辑模式
        toggleEdit()
      }
    }

    // 创建新笔记
    function createNewNote() {
      showTitleInputModal()
    }

    // 显示标题输入模态框
    function showTitleInputModal() {
      const modal = document.createElement('div')
      modal.className = 'modal'
      modal.style.display = 'flex'
      modal.innerHTML = `
        <div class="modal-content" style="max-width: 600px;">
          <div class="modal-header">
            <h3 class="modal-title">新建笔记</h3>
            <button class="btn-close" onclick="this.closest('.modal').remove()">&times;</button>
          </div>

          <div class="form-group">
            <label class="form-label">笔记标题</label>
            <input type="text" class="form-input" id="note-title-input" placeholder="请输入笔记标题..." autofocus>
          </div>

          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
            <button type="button" class="btn btn-primary" onclick="createNoteWithTitle()">创建</button>
          </div>
        </div>
      `

      document.body.appendChild(modal)

      // 聚焦输入框
      setTimeout(() => {
        const input = document.getElementById('note-title-input')
        input.focus()

        // 回车键创建笔记
        input.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            createNoteWithTitle()
          }
        })
      }, 100)

      // 点击模态框外部关闭
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.remove()
        }
      })
    }

    // 使用输入的标题创建笔记
    function createNoteWithTitle() {
      const input = document.getElementById('note-title-input')
      const title = input.value.trim()

      if (!title) {
        input.focus()
        return
      }

      const newNote = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        title: title,
        content: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      notes.unshift(newNote)
      saveNotes()
      renderNotesList()
      selectNote(newNote.id)

      // 关闭模态框
      document.querySelector('.modal').remove()

      // 自动进入编辑模式
      setTimeout(() => {
        if (!isEditing) {
          toggleEdit()
        }
      }, 100)
    }

    // 删除当前笔记
    function deleteCurrentNote() {
      if (!currentNoteId) return

      const note = notes.find(n => n.id === currentNoteId)
      if (!note) return

      if (confirm(`确定要删除笔记"${note.title}"吗？`)) {
        notes = notes.filter(n => n.id !== currentNoteId)
        saveNotes()
        renderNotesList()

        // 清空内容区域
        currentNoteId = null
        isEditing = false
        document.getElementById('empty-state').style.display = 'flex'
        document.getElementById('content-header').style.display = 'none'
        document.getElementById('note-viewer').style.display = 'none'
        document.getElementById('note-editor').style.display = 'none'
      }
    }

    // 复制到剪贴板
    async function copyToClipboard() {
      if (!currentNoteId) return

      const note = notes.find(n => n.id === currentNoteId)
      if (!note) return

      try {
        // 复制纯文本内容
        const textContent = note.content.replace(/<[^>]*>/g, '')
        await navigator.clipboard.writeText(textContent)
        showRemind('已复制到剪贴板')
      } catch (error) {
        console.error('Failed to copy to clipboard:', error)
        showRemind('复制失败')
      }
    }

    // 搜索笔记
    function searchNotes(query) {
      const noteItems = document.querySelectorAll('.note-item')
      const searchQuery = query.toLowerCase()

      noteItems.forEach(item => {
        const title = item.querySelector('.note-title').textContent.toLowerCase()
        const preview = item.querySelector('.note-preview').textContent.toLowerCase()

        if (title.includes(searchQuery) || preview.includes(searchQuery)) {
          item.style.display = 'block'
        } else {
          item.style.display = 'none'
        }
      })
    }

    // HTML转义
    function escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    }

    // 切换紧凑模式
    async function toggleCompactMode() {
      const body = document.body
      const compactBtn = document.getElementById('compact-btn')

      try {
        const screenSize = await window.electronAPI.getScreenSize()

        if (body.classList.contains('compact-mode')) {
          // 退出紧凑模式 - 恢复到原始大小
          body.classList.remove('compact-mode')
          compactBtn.title = '切换紧凑模式'
          compactBtn.textContent = '▶️'

          // 恢复窗口大小和位置
          const width = Math.max(Math.floor(screenSize.width * 0.8), 1000)
          const height = Math.max(Math.floor(screenSize.height * 0.8), 700)
          await window.electronAPI.resizeWindow(width, height)

          // 居中显示
          const x = Math.floor((screenSize.width - width) / 2)
          const y = Math.floor((screenSize.height - height) / 2)
          await window.electronAPI.moveWindow(x, y)

        } else {
          // 进入紧凑模式
          body.classList.add('compact-mode')
          compactBtn.title = '退出紧凑模式'
          compactBtn.textContent = '◀️'

          // 调整窗口大小和位置到右侧
          const width = 600
          const height = screenSize.height
          await window.electronAPI.resizeWindow(width, height)

          // 移动到屏幕右侧
          const x = screenSize.width - width
          const y = 0
          await window.electronAPI.moveWindow(x, y)
        }
      } catch (error) {
        console.error('Failed to toggle compact mode:', error)
      }
    }
  </script>
</body>
</html>
