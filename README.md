# 🌐 Electron浏览器

一个基于Electron构建的现代化浏览器，具有多标签页、工作区管理、笔记功能、密码管理等特性。

## ✨ 主要特性

- 🔖 **多标签页浏览** - 支持多个标签页同时浏览
- 🏢 **工作区管理** - 按项目或主题组织标签页
- 📝 **内置笔记** - 快速保存和管理笔记，支持HTML渲染
- 🔐 **密码管理** - 安全存储和自动填充密码
- 🐒 **用户脚本** - 支持自定义JavaScript脚本
- ⚙️ **个性化设置** - 丰富的自定义选项
- 🎨 **现代化界面** - 简洁美观的用户界面

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

#### Windows用户
1. 双击 `start-windows.bat` 文件
2. 等待自动安装依赖（首次运行）
3. 应用将自动启动

#### macOS用户
1. 双击 `start-macos.command` 文件
2. 如果系统提示安全警告，请在"系统偏好设置 > 安全性与隐私"中允许运行
3. 等待自动安装依赖（首次运行）
4. 应用将自动启动

#### Linux用户
1. 双击 `start.sh` 文件或在终端运行 `./start.sh`
2. 等待自动安装依赖（首次运行）
3. 应用将自动启动

### 方法二：手动启动

```bash
# 1. 安装依赖（首次运行）
npm install

# 2. 启动应用
npm start
```

## 📋 系统要求

- **Node.js** 14.0 或更高版本
- **npm** 6.0 或更高版本
- **操作系统**: Windows 10+, macOS 10.13+, 或 Linux

### 安装Node.js

- **Windows/macOS**: 访问 [Node.js官网](https://nodejs.org/) 下载安装
- **macOS (Homebrew)**: `brew install node`
- **Ubuntu/Debian**: `sudo apt install nodejs npm`
- **CentOS/RHEL**: `sudo yum install nodejs npm`

## 🎯 功能说明

### 🔖 标签页管理
- 新建、关闭、切换标签页
- 标签页拖拽排序
- 标签页休眠机制（节省内存）
- 自定义标签页标题

### 🏢 工作区功能
- 创建多个工作区
- 按项目或主题组织标签页
- 工作区间快速切换
- 工作区重命名和删除

### 📝 笔记管理
- 创建、编辑、删除笔记
- 支持HTML标签渲染
- 网页文字快速保存为笔记
- 笔记搜索和分类
- 紧凑模式（400px宽度，屏幕右侧显示）

### 🔐 密码管理
- 安全存储账号密码
- 网页自动填充
- 密码强度检查
- 加密存储（Base64）
- 置顶悬浮窗口（屏幕右1/3宽度）

### 🐒 用户脚本
- 支持自定义JavaScript脚本
- 按网站URL匹配执行
- 脚本管理和编辑
- 实时生效

### ⚙️ 个性化设置
- 搜索引擎选择
- 标签页休眠时间
- 界面主题设置
- 快捷键配置

## 🔧 开发说明

### 项目结构
```
├── main.js              # 主进程文件
├── renderer.js          # 渲染进程文件
├── preload.js           # 预加载脚本
├── index.html           # 主界面
├── style.css            # 样式文件
├── notes.html           # 笔记管理界面
├── password-manager.html # 密码管理界面
├── settings.html        # 设置界面
├── userscripts.html     # 用户脚本界面
├── package.json         # 项目配置
├── lo.ico              # 应用图标
└── 启动脚本/
    ├── start-windows.bat    # Windows启动脚本
    ├── start-macos.command  # macOS启动脚本
    └── start.sh            # Linux启动脚本
```

### 开发模式
```bash
npm run dev
```

### 构建发布
```bash
# 安装electron-builder
npm install electron-builder --save-dev

# 构建应用
npm run build
```

## 🛠️ 故障排除

### 常见问题

1. **应用无法启动**
   - 检查Node.js版本
   - 重新安装依赖：`rm -rf node_modules && npm install`

2. **依赖安装失败**
   - 检查网络连接
   - 使用国内镜像：`npm config set registry https://registry.npmmirror.com`

3. **macOS安全警告**
   - 右键点击启动脚本，选择"打开"
   - 或在系统偏好设置中允许运行

4. **Linux权限问题**
   - 给脚本添加执行权限：`chmod +x start.sh`

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如果您遇到问题或有建议，请：
1. 查看故障排除部分
2. 检查已有的Issues
3. 创建新的Issue描述问题

---

**享受您的浏览体验！** 🎉
