const { app, <PERSON><PERSON>erWindow, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ipc<PERSON>ain, <PERSON>u, shell } = require('electron')
const path = require('path')
const fs = require('fs')

class Browser {
  constructor() {
    this.mainWindow = null
    this.tabs = new Map()
    this.activeTabId = null
    this.tabCounter = 0
    this.ipcSetup = false
    this.rightPanelVisible = false
    this.rightPanelTabs = new Map()
    this.activeRightTabId = null
    this.rightTabCounter = 1000 // 使用不同的起始ID避免冲突
    this.rightPanelWidth = 600
    this.leftPanelWidth = 100
    this.leftPanelVisible = true
    this.workspaces = new Map()
    this.separators = new Map() // 存储分割线信息
    this.activeWorkspaceId = 'default'
    this.workspaceCounter = 0
    this.downloads = [] // 下载列表
    this.downloadManagerWindow = null
    this.settings = this.loadSettings()
    this.sessionData = this.loadSessionData()
    this.initializeDefaultWorkspace()
  }

  loadSettings() {
    try {
      const settingsPath = path.join(__dirname, 'settings.json')
      if (fs.existsSync(settingsPath)) {
        return JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
    return {
      homepage: 'https://www.baidu.com',
      searchEngine: 'google',
      customSearchEngines: [],
      leftPanelWidth: 70,
      tabSleepTime: 20, // 默认20分钟
      tabWidth: 150 // 默认标签宽度150px
    }
  }

  loadSessionData() {
    try {
      const sessionPath = path.join(__dirname, 'session.json')
      if (fs.existsSync(sessionPath)) {
        return JSON.parse(fs.readFileSync(sessionPath, 'utf8'))
      }
    } catch (error) {
      console.error('Failed to load session data:', error)
    }
    return {
      groups: {},
      workspaces: {},
      separators: {},
      workspaceOrder: [],
      lastActiveWorkspace: 'default',
      lastActiveTab: null
    }
  }

  async saveSessionData(workspaceOrder = null) {
    try {
      // 如果没有提供顺序，尝试从渲染进程获取
      if (!workspaceOrder && this.mainWindow && this.mainWindow.webContents) {
        try {
          workspaceOrder = await this.mainWindow.webContents.executeJavaScript(`
            (() => {
              const workspaceList = document.querySelector('.workspace-list');
              if (!workspaceList) return [];
              console.log('保存顺序');
              const items = Array.from(workspaceList.children);
              return items.map(item => {
                if (item.classList.contains('workspace-separator')) {
                  return { type: 'separator', id: item.dataset.separatorId };
                } else if (item.classList.contains('workspace-item')) {
                  return { type: 'workspace', id: item.dataset.workspaceId };
                }
                return null;
              }).filter(item => item !== null);
            })()
          `);
        } catch (error) {
          console.error('Failed to get workspace order:', error);
          workspaceOrder = [];
        }
      }

      const sessionData = {
        groups: {},
        workspaces: {},
        separators: {},
        workspaceOrder: workspaceOrder || [], // 保存工作区和分割线的顺序
        lastActiveWorkspace: this.activeWorkspaceId,
        lastActiveTab: this.activeTabId
      }

      /*// 保存所有组信息
      for (const [groupId, group] of this.groups) {
        sessionData.groups[groupId] = {
          id: group.id,
          name: group.name,
          workspaces: group.workspaces,
          collapsed: group.collapsed
        }
      }*/

      // 保存所有工作区和标签页信息
      for (const [workspaceId, workspace] of this.workspaces) {
        console.log('保存工作区');
        sessionData.workspaces[workspaceId] = {
          id: workspace.id,
          name: workspace.name,
          homepage: workspace.homepage,
          activeTabId: workspace.activeTabId,
          groupId: workspace.groupId || 'default-group',
          tabs: {}
        }

        // 保存标签页信息
        for (const [tabId, tab] of workspace.tabs) {
          sessionData.workspaces[workspaceId].tabs[tabId] = {
            url: tab.url,
            title: tab.title,
            customTitle: tab.customTitle || null, // 保存自定义标题
            isSleeping: tab.isSleeping || false,
            lastActiveTime: tab.lastActiveTime || Date.now()
          }
        }
      }

      // 保存分割线信息
      for (const [separatorId, separator] of this.separators) {
        sessionData.separators[separatorId] = {
          id: separator.id,
          name: separator.name,
          type: separator.type,
          collapsed: separator.collapsed
        }
      }

      const sessionPath = path.join(__dirname, 'session.json')
      fs.writeFileSync(sessionPath, JSON.stringify(sessionData, null, 2))
    } catch (error) {
      console.error('Failed to save session data:', error)
    }
  }

  saveSettingsToFile() {
    try {
      const settingsPath = path.join(__dirname, 'settings.json')
      fs.writeFileSync(settingsPath, JSON.stringify(this.settings, null, 2))
    } catch (error) {
      console.error('Failed to save settings:', error)
    }
  }

  initializeDefaultWorkspace() {
    // 初始化组系统
    if (!this.groups) {
      this.groups = new Map()
    }

    // 创建默认组
    if (!this.groups.has('default-group')) {
      const defaultGroup = {
        id: 'default-group',
        name: '默认组',
        workspaces: ['default'],
        collapsed: false
      }
      this.groups.set('default-group', defaultGroup)
    }

    // 创建默认工作区
    this.workspaces.set('default', {
      id: 'default',
      name: '默认',
      tabs: new Map(),
      activeTabId: null,
      homepage: 'https://www.baidu.com',
      groupId: 'default-group'
    })
  }

  createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      icon: path.join(__dirname, 'lo.icns'), // 设置应用图标
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true
      },
      titleBarStyle: 'hidden'
    })

    this.mainWindow.loadFile('index.html')

    // 监听窗口大小变化
    this.mainWindow.on('resize', () => {
      this.updateAllTabBounds()
    })

    // 监听窗口最大化/恢复
    this.mainWindow.on('maximize', () => {
      this.updateAllTabBounds()
    })

    this.mainWindow.on('unmaximize', () => {
      this.updateAllTabBounds()
    })

    // 窗口准备就绪后更新标签页边界
    this.mainWindow.webContents.once('dom-ready', () => {
      setTimeout(() => {
        this.updateAllTabBounds()
      }, 500) // 延迟500ms确保界面完全加载
    })

    // 处理文件拖拽
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      // 如果是文件协议，允许导航
      if (navigationUrl.startsWith('file://')) {
        return
      }

      // 阻止在主窗口中导航，而是在新标签页中打开
      if (navigationUrl !== this.mainWindow.webContents.getURL()) {
        event.preventDefault()
        this.createTab(navigationUrl)
      }
    })

    // 设置下载处理
    this.setupDownloadHandling()

    if (!this.ipcSetup) {
      this.setupIPC()
      this.ipcSetup = true
    }

    // 加载浏览器插件
    this.loadExtensions()

    // 恢复会话数据或创建初始标签页
    this.restoreSession()

    // 设置定期保存会话数据（每30秒）
    setInterval(() => {
      this.saveSessionData()
    }, 30000)
  }

  getMainViewBounds() {
    const bounds = this.mainWindow.getBounds()
    const rightPanelWidth = this.rightPanelVisible ? this.rightPanelWidth : 0
    const leftPanelWidth = this.leftPanelVisible ? this.leftPanelWidth : 0
    return {
      x: leftPanelWidth,
      y: 90,
      width: bounds.width - rightPanelWidth - leftPanelWidth,
      height: bounds.height - 114
    }
  }

  getRightViewBounds() {
    const bounds = this.mainWindow.getBounds()
    return {
      x: bounds.width - this.rightPanelWidth,
      y: 170, // 增加Y坐标，为标签栏留出空间
      width: this.rightPanelWidth,
      height: bounds.height - 194 // 相应减少高度
    }
  }

  updateAllTabBounds() {
    // 更新主面板标签页
    if (this.activeTabId && this.tabs.has(this.activeTabId)) {
      const tab = this.tabs.get(this.activeTabId)
      tab.view.setBounds(this.getMainViewBounds())
    }

    // 更新右侧面板标签页
    if (this.activeRightTabId && this.rightPanelTabs.has(this.activeRightTabId)) {
      const tab = this.rightPanelTabs.get(this.activeRightTabId)
      tab.view.setBounds(this.getRightViewBounds())
    }
  }

  createTab(url = '', isRightPanel = false) {
    // 如果没有提供URL，使用当前工作区的主页
    if (!url) {
      const workspace = this.workspaces.get(this.activeWorkspaceId)
      url = workspace?.homepage || this.settings?.homepage || 'https://www.google.com'
    }
    const tabId = isRightPanel ? ++this.rightTabCounter : ++this.tabCounter
    const view = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    })

    const tabData = {
      view,
      url,
      title: 'New Tab',
      customTitle: null, // 用户自定义的标题
      canGoBack: false,
      canGoForward: false,
      isSleeping: false,
      lastActiveTime: Date.now(),
      sleepTimer: null
    }

    if (isRightPanel) {
      this.rightPanelTabs.set(tabId, tabData)
      this.switchToRightTab(tabId)
    } else {
      this.tabs.set(tabId, tabData)
      this.switchToTab(tabId)
    }

    view.webContents.loadURL(url)
    this.setupTabEvents(tabId, view, isRightPanel)
    this.setupTabDownloadHandling(view)
    
    // 通知渲染进程新标签页已创建
    this.mainWindow.webContents.send('tab:created', tabId, {
      id: tabId,
      title: 'New Tab',
      url: url,
      active: true,
      isRightPanel
    })
    
    return tabId
  }

  setupTabEvents(tabId, view, isRightPanel = false) {
    view.webContents.on('did-navigate', (event, url) => {
      this.updateTabInfo(tabId, { url }, isRightPanel)
    })

    view.webContents.on('page-title-updated', (event, title) => {
      this.updateTabInfo(tabId, { title }, isRightPanel)
    })

    view.webContents.setWindowOpenHandler(({ url }) => {
      this.createTab(url, isRightPanel)
      return { action: 'deny' }
    })

    view.webContents.on('will-navigate', (event, url) => {
      if (url.startsWith('http') || url.startsWith('https')) {
        this.updateTabInfo(tabId, { url }, isRightPanel)
      }
    })

    view.webContents.on('new-window', (event, url) => {
      event.preventDefault()
      this.createTab(url, isRightPanel)
    })

    // 页面加载完成后注入用户脚本
    view.webContents.on('dom-ready', () => {
      const tabs = isRightPanel ? this.rightPanelTabs : this.tabs
      const tab = tabs.get(tabId)
      if (tab) {
        this.injectUserScriptsToTab(tab)
      }
    })

    // 处理页面内容的右键菜单
    view.webContents.on('context-menu', (event, params) => {
      const { Menu, MenuItem } = require('electron')
      const menu = new Menu()

      // 基础菜单项
      menu.append(new MenuItem({
        label: '返回',
        enabled: view.webContents.canGoBack(),
        click: () => view.webContents.goBack()
      }))

      menu.append(new MenuItem({
        label: '前进',
        enabled: view.webContents.canGoForward(),
        click: () => view.webContents.goForward()
      }))

      menu.append(new MenuItem({
        label: '刷新',
        click: () => view.webContents.reload()
      }))

      menu.append(new MenuItem({ type: 'separator' }))

      menu.append(new MenuItem({
        label: '复制页面地址',
        click: () => {
          const tab = isRightPanel ? this.rightPanelTabs.get(tabId) : this.tabs.get(tabId)
          if (tab) {
            require('electron').clipboard.writeText(tab.url)
          }
        }
      }))

      // 如果有选中文字，添加搜索和保存笔记选项
      if (params.selectionText) {
        menu.append(new MenuItem({ type: 'separator' }))
        menu.append(new MenuItem({
          label: `搜索 "${params.selectionText}"`,
          click: () => {
            const searchEngine = this.settings?.searchEngine || 'google'
            const searchUrl = this.getSearchUrl(params.selectionText, searchEngine)
            this.createTab(searchUrl, isRightPanel)
          }
        }))

        menu.append(new MenuItem({
          label: '保存为笔记',
          click: () => this.saveTextAsNote(params.selectionText, view.webContents.getURL())
        }))
      }

      // 如果右键点击的是输入框，添加密码相关选项
      if (params.inputFieldType === 'password' || params.inputFieldType === 'text' || params.inputFieldType === 'email') {
        const passwordMenuItem = new MenuItem({
          label: '密码管理',
          submenu: [
            {
              label: '保存当前账号密码',
              click: () => this.saveCurrentPassword(view)
            },
            { type: 'separator' },
            {
              label: '填入已保存密码',
              click: () => this.showPasswordOptions(view)
            }
          ]
        })

        menu.append(new MenuItem({ type: 'separator' }))
        menu.append(passwordMenuItem)
      }

      // 如果右键点击的是图片，添加图片相关选项
      if (params.srcURL && params.mediaType === 'image') {
        menu.append(new MenuItem({ type: 'separator' }))
        menu.append(new MenuItem({
          label: '复制图片',
          click: async () => {
            try {
              await this.copyImage(params.srcURL)
            } catch (error) {
              console.error('复制图片失败:', error)
            }
          }
        }))
        menu.append(new MenuItem({
          label: '保存图片',
          click: async () => {
            try {
              await this.saveImage(params.srcURL)
            } catch (error) {
              console.error('保存图片失败:', error)
            }
          }
        }))
      }

      // 添加开发工具选项
      menu.append(new MenuItem({ type: 'separator' }))
      menu.append(new MenuItem({
        label: '打开开发工具',
        click: () => {
          if (view.webContents.isDevToolsOpened()) {
            view.webContents.closeDevTools()
          } else {
            view.webContents.openDevTools()
          }
        }
      }))

      menu.popup({ window: this.mainWindow })
    })
  }

  switchToTab(tabId) {
    if (!this.tabs.has(tabId)) return

    // 隐藏当前活跃的主面板标签页
    if (this.activeTabId && this.tabs.has(this.activeTabId)) {
      const prevTab = this.tabs.get(this.activeTabId)
      this.mainWindow.removeBrowserView(prevTab.view)
      // 为之前的标签页设置休眠定时器
      this.setSleepTimer(this.activeTabId, false)
    }

    this.activeTabId = tabId
    const tab = this.tabs.get(tabId)

    // 如果标签页正在休眠，唤醒它
    if (tab.isSleeping) {
      this.wakeUpTab(tabId, false)
    }

    // 清除休眠定时器
    this.clearSleepTimer(tabId, false)

    // 更新最后活跃时间
    tab.lastActiveTime = Date.now()

    this.mainWindow.addBrowserView(tab.view)
    tab.view.setBounds(this.getMainViewBounds())
    tab.view.setAutoResize({ width: true, height: true })

    this.updateNavigationState()
    // 只发送可序列化的标签数据
    const serializableTab = {
      id: tabId,
      title: tab.title,
      url: tab.url,
      canGoBack: tab.canGoBack,
      canGoForward: tab.canGoForward,
      isSleeping: tab.isSleeping
    }
    this.mainWindow.webContents.send('tab:switched', tabId, serializableTab)
  }

  switchToRightTab(tabId) {
    if (!this.rightPanelTabs.has(tabId)) return

    // 隐藏当前活跃的右侧面板标签页
    if (this.activeRightTabId && this.rightPanelTabs.has(this.activeRightTabId)) {
      this.mainWindow.removeBrowserView(this.rightPanelTabs.get(this.activeRightTabId).view)
    }

    this.activeRightTabId = tabId
    const tab = this.rightPanelTabs.get(tabId)
    this.mainWindow.addBrowserView(tab.view)
    tab.view.setBounds(this.getRightViewBounds())
    tab.view.setAutoResize({ width: true, height: true })

    this.updateRightNavigationState()
    // 只发送可序列化的标签数据
    const serializableTab = {
      id: tabId,
      title: tab.title,
      url: tab.url,
      canGoBack: tab.canGoBack,
      canGoForward: tab.canGoForward,
      isSleeping: tab.isSleeping
    }
    this.mainWindow.webContents.send('right-tab:switched', tabId, serializableTab)
  }

  updateTabInfo(tabId, info, isRightPanel = false) {
    const tabs = isRightPanel ? this.rightPanelTabs : this.tabs
    const activeId = isRightPanel ? this.activeRightTabId : this.activeTabId

    if (!tabs.has(tabId)) return

    const tab = tabs.get(tabId)

    // 如果有自定义标题，不要更新页面标题
    if (info.title && tab.customTitle) {
      const { title, ...otherInfo } = info
      Object.assign(tab, otherInfo)
    } else {
      Object.assign(tab, info)
    }

    if (tabId === activeId) {
      if (isRightPanel) {
        this.updateRightNavigationState()
      } else {
        this.updateNavigationState()
      }
    }

    // 发送可序列化的标签数据
    const serializableTab = {
      id: tabId,
      title: tab.customTitle || tab.title,
      url: tab.url,
      canGoBack: tab.canGoBack,
      canGoForward: tab.canGoForward,
      isSleeping: tab.isSleeping,
      customTitle: tab.customTitle
    }
    const eventName = isRightPanel ? 'right-tab:updated' : 'tab:updated'
    this.mainWindow.webContents.send(eventName, tabId, serializableTab)
  }

  updateNavigationState() {
    if (!this.activeTabId) return
    
    const tab = this.tabs.get(this.activeTabId)
    const webContents = tab.view.webContents
    
    tab.canGoBack = webContents.canGoBack()
    tab.canGoForward = webContents.canGoForward()
    
    this.mainWindow.webContents.send('navigation:updated', {
      canGoBack: tab.canGoBack,
      canGoForward: tab.canGoForward,
      url: tab.url,
      title: tab.title
    })
  }

  updateRightNavigationState() {
    if (!this.activeRightTabId) return
    
    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    const webContents = tab.view.webContents
    
    tab.canGoBack = webContents.canGoBack()
    tab.canGoForward = webContents.canGoForward()
    
    this.mainWindow.webContents.send('right-navigation:updated', {
      canGoBack: tab.canGoBack,
      canGoForward: tab.canGoForward,
      url: tab.url,
      title: tab.title
    })
  }

  toggleRightPanel() {
    this.rightPanelVisible = !this.rightPanelVisible
    
    if (this.rightPanelVisible && this.rightPanelTabs.size === 0) {
      this.createTab('https://www.baidu.com', true)
    }
    
    // 如果隐藏右侧面板，移除右侧面板的视图
    if (!this.rightPanelVisible && this.activeRightTabId) {
      const tab = this.rightPanelTabs.get(this.activeRightTabId)
      if (tab) {
        this.mainWindow.removeBrowserView(tab.view)
      }
    }
    
    this.updateAllTabBounds()
    this.mainWindow.webContents.send('right-panel:toggled', this.rightPanelVisible)
    
    // 如果显示右侧面板，重新添加右侧面板的视图
    if (this.rightPanelVisible && this.activeRightTabId) {
      const tab = this.rightPanelTabs.get(this.activeRightTabId)
      if (tab) {
        this.mainWindow.addBrowserView(tab.view)
        tab.view.setBounds(this.getRightViewBounds())
      }
    }
  }

  setupIPC() {
    // 主面板IPC
    ipcMain.handle('tab:create', (event, url) => this.createTab(url))
    ipcMain.handle('tab:close', (event, tabId) => this.closeTab(tabId))
    ipcMain.handle('tab:switch', (event, tabId) => this.switchToTab(tabId))
    ipcMain.handle('tab:navigate', (event, url) => this.navigateActiveTab(url))
    ipcMain.handle('navigation:back', () => this.goBack())
    ipcMain.handle('navigation:forward', () => this.goForward())
    ipcMain.handle('navigation:reload', () => this.reload())
    ipcMain.handle('navigation:home', () => this.goHome())
    ipcMain.handle('tabs:getAll', () => this.getAllTabs())
    
    // 右侧面板IPC
    ipcMain.handle('right-panel:toggle', () => this.toggleRightPanel())
    ipcMain.handle('right-panel:resize', (event, width) => this.resizeRightPanel(width))
    ipcMain.handle('right-tab:create', (event, url) => this.createTab(url, true))
    ipcMain.handle('right-tab:close', (event, tabId) => this.closeRightTab(tabId))
    ipcMain.handle('right-tab:switch', (event, tabId) => this.switchToRightTab(tabId))
    ipcMain.handle('right-tab:navigate', (event, url) => this.navigateActiveRightTab(url))
    ipcMain.handle('right-navigation:back', () => this.goRightBack())
    ipcMain.handle('right-navigation:forward', () => this.goRightForward())
    ipcMain.handle('right-navigation:reload', () => this.reloadRight())
    ipcMain.handle('right-navigation:home', () => this.goRightHome())
    
    // 工作区IPC
    ipcMain.handle('workspace:create', (event, name) => this.createWorkspace(name))
    ipcMain.handle('workspace:create-separator', (event, name) => this.createWorkspaceSeparator(name))
    ipcMain.handle('workspace:switch', (event, workspaceId) => this.switchWorkspace(workspaceId))
    ipcMain.handle('workspace:delete', (event, workspaceId) => this.deleteWorkspace(workspaceId))
    ipcMain.handle('workspace:update', (event, workspaceId, updates) => this.updateWorkspace(workspaceId, updates))
    ipcMain.handle('workspace:getAll', () => {
      const workspaces = []
      const separators = []

      // 如果有保存的顺序，按顺序返回
      if (this.sessionData && this.sessionData.workspaceOrder) {
        console.log('有保存的顺序')
        // 先添加按顺序的工作区
        this.sessionData.workspaceOrder.forEach(item => {
          if (item.type === 'workspace' && this.workspaces.has(item.id)) {
            const workspace = this.workspaces.get(item.id)
            workspaces.push({
              id: workspace.id,
              name: workspace.name,
              homepage: workspace.homepage,
              activeTabId: workspace.activeTabId
            })
          }else if (item.type === 'separator' && this.separators.has(item.id)) {
          const separator = this.separators.get(item.id)
          separators.push({
            type: 'separator',
            id: separator.id,
            name: separator.name,
            collapsed: separator.collapsed
          })
        }
          
        })

        // 添加不在顺序中的工作区（新创建的）
        for (const [id, workspace] of this.workspaces) {
          const alreadyAdded = workspaces.some(w => w.id === id)
          if (!alreadyAdded) {
            workspaces.push({
              id: workspace.id,
              name: workspace.name,
              homepage: workspace.homepage,
              activeTabId: workspace.activeTabId
            })
          }
        }
      } else {
        console.log('没有保存的顺序')
        // 没有保存的顺序，按原来的方式返回
       for (const [id, workspace] of this.workspaces) {
          workspaces.push({
            id: workspace.id,
            name: workspace.name,
            homepage: workspace.homepage,
            activeTabId: workspace.activeTabId
          })
        }
      }

      return workspaces
    })
    ipcMain.handle('workspace:reorder', () => this.saveSessionData())
    ipcMain.handle('workspace:get-order', () => this.getWorkspaceOrder())
    ipcMain.handle('workspace:get-ordered-items', () => this.getOrderedWorkspaceItems())
    ipcMain.handle('workspace:update-separator', (event, separatorId, updates) => this.updateSeparator(separatorId, updates))
    ipcMain.handle('workspace:delete-separator', (event, separatorId) => this.deleteSeparator(separatorId))
    ipcMain.handle('tab:moveToWorkspace', (event, tabId, targetWorkspaceId) => this.moveTabToWorkspace(tabId, targetWorkspaceId))
    ipcMain.handle('tab:updateTitle', (event, tabId, customTitle) => this.updateTabTitle(tabId, customTitle))
    ipcMain.handle('tab:toggleMute', (event, tabId) => this.toggleTabMute(tabId))
    ipcMain.handle('tab:reorder', () => this.saveSessionData())
    ipcMain.handle('workspace:prompt-input', async (event, options) => this.showInputDialog(options))
    ipcMain.handle('workspace:prompt-type', async () => {
      // 创建一个选择对话框窗口
      return new Promise((resolve) => {
        // 获取主窗口大小并计算合适的输入框大小
        const mainBounds = this.mainWindow.getBounds()
        const width = Math.min(Math.floor(mainBounds.width * 0.4), 500) // 最大500px
        const height = Math.min(Math.floor(mainBounds.height * 0.4), 300) // 最大300px

        const inputWindow = new BrowserWindow({
          width: Math.max(width, 450), // 最小宽度450px
          height: Math.max(height, 250), // 最小高度250px
          parent: this.mainWindow,
          modal: true,
          resizable: false,
          minimizable: false,
          maximizable: false,
          webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
          },
          title: '新建工作区'
        })

        const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="UTF-8">
            <style>
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                padding: 20px;
                margin: 0;
                background: #f5f5f5;
              }
              .container {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              h3 { margin-top: 0; color: #333; }
              .form-group { margin-bottom: 15px; }
              label { display: block; margin-bottom: 5px; font-weight: bold; }
              input[type="text"] {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                box-sizing: border-box;
              }
              .radio-group {
                display: flex;
                gap: 20px;
                margin-bottom: 15px;
              }
              .radio-option {
                display: flex;
                align-items: center;
                gap: 8px;
              }
              .buttons {
                display: flex;
                gap: 10px;
                justify-content: flex-end;
                margin-top: 20px;
              }
              button {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
              }
              .btn-primary { background: #007acc; color: white; }
              .btn-secondary { background: #e0e0e0; color: #333; }
              button:hover { opacity: 0.8; }
            </style>
          </head>
          <body>
            <div class="container">
              <h3>新建工作区</h3>
              <div class="form-group">
                <label>类型:</label>
                <div class="radio-group">
                  <div class="radio-option">
                    <input type="radio" id="workspace" name="type" value="workspace" checked>
                    <label for="workspace">普通工作区</label>
                  </div>
                  <div class="radio-option">
                    <input type="radio" id="separator" name="type" value="separator">
                    <label for="separator">分组</label>
                  </div>
                </div>
              </div>
              <div class="form-group">
                <label for="name">名称:</label>
                <input type="text" id="name" placeholder="请输入名称" autofocus>
              </div>
              <div class="buttons">
                <button type="button" class="btn-secondary" onclick="cancel()">取消</button>
                <button type="button" class="btn-primary" onclick="confirm()">确定</button>
              </div>
            </div>
            <script>
              const { ipcRenderer } = require('electron')

              function cancel() {
                ipcRenderer.send('workspace-type-result', null)
              }

              function confirm() {
                const name = document.getElementById('name').value.trim()
                const type = document.querySelector('input[name="type"]:checked').value

                if (!name) {
                  alert('请输入名称')
                  return
                }

                ipcRenderer.send('workspace-type-result', { type, name })
              }

              document.getElementById('name').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                  confirm()
                }
              })
            </script>
          </body>
          </html>
        `

        inputWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent))

        ipcMain.once('workspace-type-result', (event, result) => {
          inputWindow.close()
          resolve(result)
        })

        inputWindow.on('closed', () => {
          resolve(null)
        })
      })
    })

    ipcMain.handle('workspace:prompt-name', async () => {
      // 创建一个输入对话框窗口
      return new Promise((resolve) => {
        // 获取主窗口大小并计算合适的输入框大小
        const mainBounds = this.mainWindow.getBounds()
        const width = Math.min(Math.floor(mainBounds.width * 0.4), 500) // 最大500px
        const height = Math.min(Math.floor(mainBounds.height * 0.3), 250) // 最大250px

        const inputWindow = new BrowserWindow({
          width: Math.max(width, 400), // 最小宽度400px
          height: Math.max(height, 200), // 最小高度200px
          parent: this.mainWindow,
          modal: true,
          resizable: false,
          minimizable: false,
          maximizable: false,
          webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
          },
          title: '新建工作区'
        })

        const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="UTF-8">
            <style>
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background: #f5f5f5;
              }
              .container {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              h3 {
                margin: 0 0 15px 0;
                color: #333;
              }
              input {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                margin-bottom: 15px;
                box-sizing: border-box;
              }
              .buttons {
                text-align: right;
              }
              button {
                padding: 8px 16px;
                margin-left: 8px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
              }
              .btn-primary {
                background: #007acc;
                color: white;
              }
              .btn-secondary {
                background: #e0e0e0;
                color: #333;
              }
              button:hover {
                opacity: 0.8;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <h3>新建工作区</h3>
              <input type="text" id="workspace-name" placeholder="请输入工作区名称" value="工作区 ${this.workspaceCounter + 1}">
              <div class="buttons">
                <button class="btn-secondary" onclick="cancel()">取消</button>
                <button class="btn-primary" onclick="confirm()">确定</button>
              </div>
            </div>
            <script>
              const { ipcRenderer } = require('electron')

              function confirm() {
                const name = document.getElementById('workspace-name').value.trim()
                if (name) {
                  ipcRenderer.send('workspace-input-result', name)
                } else {
                  alert('请输入工作区名称')
                }
              }

              function cancel() {
                ipcRenderer.send('workspace-input-result', null)
              }

              document.getElementById('workspace-name').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                  confirm()
                }
              })

              // 自动聚焦并选中文本
              document.getElementById('workspace-name').focus()
              document.getElementById('workspace-name').select()
            </script>
          </body>
          </html>
        `

        inputWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent))

        ipcMain.once('workspace-input-result', (event, result) => {
          inputWindow.close()
          resolve(result)
        })

        inputWindow.on('closed', () => {
          resolve(null)
        })
      })
    })
    
    // 设置相关
    ipcMain.handle('settings:open', () => this.openSettingsWindow())
    ipcMain.handle('settings:get', () => this.getSettings())
    ipcMain.handle('settings:set', (event, settings) => this.setSettings(settings))

    // 用户脚本相关
    ipcMain.handle('userscripts:open', () => this.openUserScriptsWindow())
    ipcMain.handle('userscripts:get', () => this.getUserScripts())
    ipcMain.handle('userscripts:save', (event, scripts) => this.saveUserScripts(scripts))

    // 笔记相关
    ipcMain.handle('notes:open', () => this.openNotesWindow())
    ipcMain.handle('notes:get', () => this.getNotes())
    ipcMain.handle('notes:save', (event, notes) => this.saveNotes(notes))

    // 密码管理器相关
    ipcMain.handle('passwords:open', () => this.openPasswordManagerWindow())
    ipcMain.handle('passwords:get', () => this.getPasswords())
    ipcMain.handle('passwords:save', (event, passwords) => this.savePasswords(passwords))

    // 下载管理器相关
    ipcMain.handle('downloads:open', () => this.openDownloadManagerWindow())
    ipcMain.handle('downloads:get', () => this.getDownloads())
    ipcMain.handle('downloads:get-settings', () => this.getDownloadSettings())
    ipcMain.handle('downloads:save-settings', (event, settings) => this.saveDownloadSettings(settings))
    ipcMain.handle('downloads:select-path', () => this.selectDownloadPath())
    ipcMain.handle('downloads:open-file', (event, filePath) => this.openFile(filePath))
    ipcMain.handle('downloads:show-in-folder', (event, filePath) => this.showInFolder(filePath))

    // 窗口控制相关
    ipcMain.handle('window:resize', (event, width, height) => this.resizeCurrentWindow(event, width, height))
    ipcMain.handle('window:move', (event, x, y) => this.moveCurrentWindow(event, x, y))
    ipcMain.handle('window:get-screen-size', () => this.getScreenSize())


    ipcMain.handle('settings:save', (event, settings) => {
      this.setSettings(settings)
      return true
    })
    
    // 右键菜单功能
    ipcMain.handle('context:copy-url', () => this.copyCurrentUrl())
    ipcMain.handle('context:search-text', (event, text) => this.searchSelectedText(text))
    ipcMain.handle('context:copy-image', (event, imageUrl) => this.copyImage(imageUrl))
    ipcMain.handle('context:save-image', (event, imageUrl) => this.saveImage(imageUrl))
  }

  resizeRightPanel(width) {
    const bounds = this.mainWindow.getBounds()
    const maxWidth = Math.floor(bounds.width * 0.5)
    const minWidth = 300
    
    this.rightPanelWidth = Math.max(minWidth, Math.min(width, maxWidth))
    this.updateAllTabBounds()
    this.mainWindow.webContents.send('right-panel:resized', this.rightPanelWidth)
  }

  getSettings() {
    return this.settings
  }

  setSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings }
    this.saveSettingsToFile()
    
    // 更新左侧面板宽度
    if (newSettings.leftPanelWidth) {
      this.leftPanelWidth = newSettings.leftPanelWidth
      this.mainWindow.webContents.send('left-panel:resized', this.leftPanelWidth)
      // 更新所有标签页的边界
      this.updateAllTabBounds()
    }
  }

  getSearchUrl(query, searchEngine = 'baidu') {
    const engines = {
      google: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
      bing: `https://www.bing.com/search?q=${encodeURIComponent(query)}`,
      baidu: `https://www.baidu.com/s?wd=${encodeURIComponent(query)}`
    }
    
    // 检查自定义搜索引擎
    const customEngine = this.settings.customSearchEngines?.find(e => e.id === searchEngine)
    if (customEngine) {
      return customEngine.url.replace('%s', encodeURIComponent(query))
    }
    
    return engines[searchEngine] || engines.google
  }

  navigateActiveTab(url) {
    if (!this.activeTabId) return

    if (!url.includes('.') && !url.startsWith('http') && !url.startsWith('file://')) {
      const searchEngine = this.settings?.searchEngine || 'google'
      url = this.getSearchUrl(url, searchEngine)
    } else if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('file://')) {
      url = 'https://' + url
    }

    const tab = this.tabs.get(this.activeTabId)
    tab.view.webContents.loadURL(url)
  }

  navigateActiveRightTab(url) {
    if (!this.activeRightTabId) return

    if (!url.includes('.') && !url.startsWith('http') && !url.startsWith('file://')) {
      const searchEngine = this.settings?.searchEngine || 'google'
      url = this.getSearchUrl(url, searchEngine)
    } else if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('file://')) {
      url = 'https://' + url
    }

    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    tab.view.webContents.loadURL(url)
  }

  goBack() {
    if (!this.activeTabId) return
    const tab = this.tabs.get(this.activeTabId)
    if (tab.canGoBack) {
      tab.view.webContents.goBack()
    }
  }

  goRightBack() {
    if (!this.activeRightTabId) return
    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    if (tab.canGoBack) {
      tab.view.webContents.goBack()
    }
  }

  goForward() {
    if (!this.activeTabId) return
    const tab = this.tabs.get(this.activeTabId)
    if (tab.canGoForward) {
      tab.view.webContents.goForward()
    }
  }

  goRightForward() {
    if (!this.activeRightTabId) return
    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    if (tab.canGoForward) {
      tab.view.webContents.goForward()
    }
  }

  reload() {
    if (!this.activeTabId) return
    const tab = this.tabs.get(this.activeTabId)
    tab.view.webContents.reload()
  }

  reloadRight() {
    if (!this.activeRightTabId) return
    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    tab.view.webContents.reload()
  }

  goHome() {
    const homepage = this.settings?.homepage || 'https://www.google.com'
    this.navigateActiveTab(homepage)
  }

  goRightHome() {
    const homepage = this.settings?.homepage || 'https://www.google.com'
    this.navigateActiveRightTab(homepage)
  }

  closeTab(tabId) {
    if (!this.tabs.has(tabId)) return

    const tab = this.tabs.get(tabId)

    // 清理休眠定时器
    this.clearSleepTimer(tabId, false)

    this.mainWindow.removeBrowserView(tab.view)
    tab.view.webContents.destroy()
    this.tabs.delete(tabId)

    if (tabId === this.activeTabId) {
      const remainingTabs = Array.from(this.tabs.keys())
      if (remainingTabs.length > 0) {
        this.switchToTab(remainingTabs[0])
      } else {
        this.activeTabId = null
        this.createTab()
      }
    }

    this.mainWindow.webContents.send('tab:closed', tabId)
  }

  closeRightTab(tabId) {
    if (!this.rightPanelTabs.has(tabId)) return
    
    const tab = this.rightPanelTabs.get(tabId)
    this.mainWindow.removeBrowserView(tab.view)
    tab.view.webContents.destroy()
    this.rightPanelTabs.delete(tabId)
    
    if (tabId === this.activeRightTabId) {
      const remainingTabs = Array.from(this.rightPanelTabs.keys())
      if (remainingTabs.length > 0) {
        this.switchToRightTab(remainingTabs[0])
      } else {
        this.activeRightTabId = null
      }
    }
    
    this.mainWindow.webContents.send('right-tab:closed', tabId)
  }

  createInitialTab() {
    // 直接调用createTab()，它会自动使用工作区主页
    this.createTab()
  }

  async loadExtensions() {
    try {
      const { session } = require('electron')
      const extensionPath = path.join(__dirname, 'caijichajian')

      // 检查扩展文件夹是否存在
      if (fs.existsSync(extensionPath)) {
        // 在开发模式下加载扩展
        const extension = await session.defaultSession.loadExtension(extensionPath, {
          allowFileAccess: true
        })
        console.log('Extension loaded:', extension.name)

        // 为扩展提供必要的API适配
        this.setupExtensionAPIs()
      } else {
        console.log('Extension folder not found:', extensionPath)
      }
    } catch (error) {
      console.error('Failed to load extension:', error)
    }
  }

  setupExtensionAPIs() {
    // 为caijichajian插件提供API适配
    // 这里可以添加一些必要的API桥接，但由于插件主要依赖Chrome API，
    // 我们将在页面右键菜单中直接集成其功能
    console.log('Extension APIs setup completed')
  }

  async showInputDialog(options = {}) {
    const {
      title = '输入',
      message = '请输入内容:',
      defaultValue = '',
      placeholder = '请输入...'
    } = options

    return new Promise((resolve) => {
      // 获取主窗口大小并计算合适的输入框大小
      const mainBounds = this.mainWindow.getBounds()
      const width = Math.min(Math.floor(mainBounds.width * 0.4), 500) // 最大500px
      const height = Math.min(Math.floor(mainBounds.height * 0.3), 250) // 最大250px

      const inputWindow = new BrowserWindow({
        width: Math.max(width, 400), // 最小宽度400px
        height: Math.max(height, 200), // 最小高度200px
        parent: this.mainWindow,
        modal: true,
        resizable: false,
        minimizable: false,
        maximizable: false,
        webPreferences: {
          nodeIntegration: true,
          contextIsolation: false
        },
        title: title
      })

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 20px;
              background: #f5f5f5;
            }
            .container {
              background: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h3 {
              margin: 0 0 15px 0;
              color: #333;
            }
            input {
              width: 100%;
              padding: 8px 12px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 14px;
              margin-bottom: 15px;
              box-sizing: border-box;
            }
            .buttons {
              text-align: right;
            }
            button {
              padding: 8px 16px;
              margin-left: 8px;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
            }
            .btn-primary {
              background: #007acc;
              color: white;
            }
            .btn-secondary {
              background: #e0e0e0;
              color: #333;
            }
            button:hover {
              opacity: 0.8;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h3>${message}</h3>
            <input type="text" id="input-value" placeholder="${placeholder}" value="${defaultValue}">
            <div class="buttons">
              <button class="btn-secondary" onclick="cancel()">取消</button>
              <button class="btn-primary" onclick="confirm()">确定</button>
            </div>
          </div>
          <script>
            const { ipcRenderer } = require('electron')

            function confirm() {
              const value = document.getElementById('input-value').value.trim()
              ipcRenderer.send('input-dialog-result', value)
            }

            function cancel() {
              ipcRenderer.send('input-dialog-result', null)
            }

            document.getElementById('input-value').addEventListener('keypress', (e) => {
              if (e.key === 'Enter') {
                confirm()
              }
            })

            // 自动聚焦并选中文本
            document.getElementById('input-value').focus()
            document.getElementById('input-value').select()
          </script>
        </body>
        </html>
      `

      inputWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent))

      ipcMain.once('input-dialog-result', (event, result) => {
        inputWindow.close()
        resolve(result)
      })

      inputWindow.on('closed', () => {
        resolve(null)
      })
    })
  }

  // 标签休眠相关方法
  setSleepTimer(tabId, isRightPanel = false) {
    const tabs = isRightPanel ? this.rightPanelTabs : this.tabs
    const tab = tabs.get(tabId)
    if (!tab || tab.isSleeping) return

    // 清除现有定时器
    this.clearSleepTimer(tabId, isRightPanel)

    // 设置新的休眠定时器
    const sleepTime = (this.settings.tabSleepTime || 20) * 60 * 1000 // 转换为毫秒
    tab.sleepTimer = setTimeout(() => {
      this.sleepTab(tabId, isRightPanel)
    }, sleepTime)
  }

  clearSleepTimer(tabId, isRightPanel = false) {
    const tabs = isRightPanel ? this.rightPanelTabs : this.tabs
    const tab = tabs.get(tabId)
    if (tab && tab.sleepTimer) {
      clearTimeout(tab.sleepTimer)
      tab.sleepTimer = null
    }
  }

  sleepTab(tabId, isRightPanel = false) {
    const tabs = isRightPanel ? this.rightPanelTabs : this.tabs
    const tab = tabs.get(tabId)
    if (!tab || tab.isSleeping) return

    // 标记为休眠状态
    tab.isSleeping = true
    tab.sleepTimer = null

    // 暂停页面渲染以节省资源
    try {
      tab.view.webContents.setBackgroundThrottling(true)
    } catch (error) {
      console.error('Failed to set background throttling:', error)
    }

    // 通知渲染进程标签页已休眠
    const eventName = isRightPanel ? 'right-tab:slept' : 'tab:slept'
    this.mainWindow.webContents.send(eventName, tabId)

    console.log(`Tab ${tabId} went to sleep`)
  }

  wakeUpTab(tabId, isRightPanel = false) {
    const tabs = isRightPanel ? this.rightPanelTabs : this.tabs
    const tab = tabs.get(tabId)
    if (!tab || !tab.isSleeping) return

    // 标记为活跃状态
    tab.isSleeping = false
    tab.lastActiveTime = Date.now()

    // 恢复页面渲染
    try {
      tab.view.webContents.setBackgroundThrottling(false)
    } catch (error) {
      console.error('Failed to disable background throttling:', error)
    }

    // 如果页面还没有加载URL，现在加载它
    if (tab.url && !tab.view.webContents.getURL()) {
      try {
        tab.view.webContents.loadURL(tab.url)
      } catch (error) {
        console.error('Failed to load URL on wake up:', error)
      }
    }

    // 通知渲染进程标签页已唤醒
    const eventName = isRightPanel ? 'right-tab:woke-up' : 'tab:woke-up'
    this.mainWindow.webContents.send(eventName, tabId)
    this.injectUserScriptsToTab(tab)
    console.log(`Tab ${tabId} woke up`)
  }

  // 移动标签页到其他工作区
  moveTabToWorkspace(tabId, targetWorkspaceId) {
    if (!this.tabs.has(tabId) || !this.workspaces.has(targetWorkspaceId)) {
      return false
    }

    const tab = this.tabs.get(tabId)
    const currentWorkspace = this.workspaces.get(this.activeWorkspaceId)
    const targetWorkspace = this.workspaces.get(targetWorkspaceId)

    // 从当前工作区移除标签页
    currentWorkspace.tabs.delete(tabId)
    this.tabs.delete(tabId)

    // 添加到目标工作区
    targetWorkspace.tabs.set(tabId, tab)

    // 如果移动的是当前活跃标签页，需要切换到其他标签页
    if (tabId === this.activeTabId) {
      this.mainWindow.removeBrowserView(tab.view)
      this.clearSleepTimer(tabId, false)

      const remainingTabs = Array.from(this.tabs.keys())
      if (remainingTabs.length > 0) {
        this.switchToTab(remainingTabs[0])
      } else {
        this.activeTabId = null
        this.createTab()
      }
    }

    // 通知渲染进程标签页已移动
    this.mainWindow.webContents.send('tab:moved', tabId, targetWorkspaceId)
    return true
  }

  // 更新标签页自定义标题
  updateTabTitle(tabId, customTitle) {
    const tab = this.tabs.get(tabId)
    if (!tab) return false

    tab.customTitle = customTitle || null

    // 发送更新事件
    const serializableTab = {
      id: tabId,
      title: tab.customTitle || tab.title,
      url: tab.url,
      canGoBack: tab.canGoBack,
      canGoForward: tab.canGoForward,
      isSleeping: tab.isSleeping,
      customTitle: tab.customTitle
    }
    this.mainWindow.webContents.send('tab:updated', tabId, serializableTab)

    // 保存会话数据
    this.saveSessionData()

    return true
  }

  // 切换标签页静音状态
  toggleTabMute(tabId) {
    const tab = this.tabs.get(tabId)
    if (!tab) return false

    try {
      const webContents = tab.view.webContents
      if (webContents.isAudioMuted()) {
        webContents.setAudioMuted(false)
        console.log(`Tab ${tabId} unmuted`)
      } else {
        webContents.setAudioMuted(true)
        console.log(`Tab ${tabId} muted`)
      }
      return true
    } catch (error) {
      console.error('Failed to toggle tab mute:', error)
      return false
    }
  }

  restoreSession() {
    const sessionData = this.sessionData

    // 恢复组数据
    if (sessionData.groups) {
      for (const [groupId, groupData] of Object.entries(sessionData.groups)) {
        this.groups.set(groupId, {
          id: groupData.id,
          name: groupData.name,
          workspaces: groupData.workspaces || [],
          collapsed: groupData.collapsed || false
        })
      }
    }

    // 如果没有保存的会话数据，创建初始标签页
    if (!sessionData.workspaces || Object.keys(sessionData.workspaces).length === 0) {
      this.createInitialTab()
      return
    }

    // 恢复工作区
    for (const [workspaceId, workspaceData] of Object.entries(sessionData.workspaces)) {
      if (workspaceId !== 'default') {
        // 创建非默认工作区
        this.workspaces.set(workspaceId, {
          id: workspaceData.id,
          name: workspaceData.name,
          homepage: workspaceData.homepage,
          tabs: new Map(),
          activeTabId: workspaceData.activeTabId,
          groupId: workspaceData.groupId || 'default-group'
        })
        this.workspaceCounter = Math.max(this.workspaceCounter, parseInt(workspaceId.replace('workspace_', '')) || 0)
      } else {
        // 更新默认工作区
        const defaultWorkspace = this.workspaces.get('default')
        if (workspaceData.homepage) {
          defaultWorkspace.homepage = workspaceData.homepage
        }
        defaultWorkspace.activeTabId = workspaceData.activeTabId
        defaultWorkspace.groupId = workspaceData.groupId || 'default-group'
      }

      // 恢复标签页
      const workspace = this.workspaces.get(workspaceId)
      for (const [tabIdStr, tabData] of Object.entries(workspaceData.tabs)) {
        const tabId = parseInt(tabIdStr)
        this.tabCounter = Math.max(this.tabCounter, tabId)

        // 创建标签页但设置为休眠状态（除了最后活跃的标签页）
        const isLastActiveTab = tabId === sessionData.lastActiveTab && workspaceId === sessionData.lastActiveWorkspace
        this.createTabFromSession(tabId, tabData, workspaceId, !isLastActiveTab)
      }
    }

    // 恢复分割线数据到内存
    if (sessionData.separators) {
      for (const [separatorId, separatorData] of Object.entries(sessionData.separators)) {
        this.separators.set(separatorId, {
          id: separatorData.id,
          name: separatorData.name,
          type: separatorData.type,
          collapsed: separatorData.collapsed
        })
        this.workspaceCounter = Math.max(this.workspaceCounter, parseInt(separatorId.replace('separator_', '')) || 0)
      }
    }

    // 按照保存的顺序恢复分割线 - 延迟执行确保渲染进程准备好
    // 注意：工作区已经通过getAllWorkspaces在渲染进程初始化时加载，这里只需要恢复分割线
    if (sessionData.workspaceOrder && sessionData.workspaceOrder.length > 0) {
      
    } else {
      // 如果没有保存的顺序，使用旧的恢复方式
      setTimeout(() => {
        if (sessionData.separators) {
          for (const [separatorId, separatorData] of Object.entries(sessionData.separators)) {
            this.mainWindow.webContents.send('workspace:separator-created', separatorId, separatorData)
          }
        }
        console.log('恢复工作区顺序：', sessionData.workspaceOrder)
      }, 1000)
    }

    // 切换到最后活跃的工作区
    if (sessionData.lastActiveWorkspace && this.workspaces.has(sessionData.lastActiveWorkspace)) {
      this.switchWorkspace(sessionData.lastActiveWorkspace)
    }

    // 如果没有标签页，创建一个初始标签页
    if (this.tabs.size === 0) {
      this.createInitialTab()
    }
  }

  createTabFromSession(tabId, tabData, workspaceId, shouldSleep = false) {
    const { BrowserView } = require('electron')
    const view = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    })

    const tabInfo = {
      view,
      url: tabData.url,
      title: tabData.title || 'New Tab',
      customTitle: tabData.customTitle || null, // 恢复自定义标题
      canGoBack: false,
      canGoForward: false,
      isSleeping: shouldSleep,
      lastActiveTime: tabData.lastActiveTime || Date.now(),
      sleepTimer: null
    }

    // 添加到对应工作区
    const workspace = this.workspaces.get(workspaceId)
    workspace.tabs.set(tabId, tabInfo)

    // 如果是当前工作区，也添加到主tabs
    if (workspaceId === this.activeWorkspaceId) {
      this.tabs.set(tabId, tabInfo)
    }

    // 设置事件监听
    this.setupTabEvents(tabId, view, false)

    // 如果应该休眠，直接设置为休眠状态
    if (shouldSleep) {
      tabInfo.isSleeping = true
      try {
        view.webContents.setBackgroundThrottling(true)
      } catch (error) {
        console.error('Failed to set background throttling:', error)
      }
    } else {
      // 加载URL
      view.webContents.loadURL(tabData.url)
    }

    // 通知渲染进程
    this.mainWindow.webContents.send('tab:created', tabId, tabInfo)
  }

  getAllTabs() {
    const tabs = {}
    for (const [id, tab] of this.tabs) {
      tabs[id] = {
        id,
        title: tab.title,
        customTitle: tab.customTitle,
        url: tab.url,
        active: id === this.activeTabId,
        isSleeping: tab.isSleeping || false
      }
    }
    return tabs
  }

  copyCurrentUrl() {
    if (!this.activeTabId) return
    const tab = this.tabs.get(this.activeTabId)
    if (tab) {
      require('electron').clipboard.writeText(tab.url)
    }
  }

  searchSelectedText(text) {
    if (!text) return
    const searchEngine = this.settings?.searchEngine || 'google'
    const searchUrl = this.getSearchUrl(text, searchEngine)
    this.createTab(searchUrl)
  }

  async copyImage(imageUrl) {
    if (!imageUrl) return

    try {
      const { net, clipboard, nativeImage } = require('electron')
      const request = net.request(imageUrl)

      return new Promise((resolve, reject) => {
        let data = Buffer.alloc(0)

        request.on('response', (response) => {
          response.on('data', (chunk) => {
            data = Buffer.concat([data, chunk])
          })

          response.on('end', () => {
            try {
              const image = nativeImage.createFromBuffer(data)
              clipboard.writeImage(image)
              resolve(true)
            } catch (error) {
              reject(error)
            }
          })
        })

        request.on('error', reject)
        request.end()
      })
    } catch (error) {
      console.error('Failed to copy image:', error)
      return false
    }
  }

  async saveImage(imageUrl) {
    if (!imageUrl) return

    try {
      const { dialog, net } = require('electron')
      const path = require('path')
      const fs = require('fs')

      // 获取图片文件名
      const urlPath = new URL(imageUrl).pathname
      const fileName = path.basename(urlPath) || 'image.jpg'

      const result = await dialog.showSaveDialog(this.mainWindow, {
        title: '保存图片',
        defaultPath: fileName,
        filters: [
          { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      })

      if (result.canceled) return false

      const request = net.request(imageUrl)

      return new Promise((resolve, reject) => {
        let data = Buffer.alloc(0)

        request.on('response', (response) => {
          response.on('data', (chunk) => {
            data = Buffer.concat([data, chunk])
          })

          response.on('end', () => {
            try {
              fs.writeFileSync(result.filePath, data)
              resolve(true)
            } catch (error) {
              reject(error)
            }
          })
        })

        request.on('error', reject)
        request.end()
      })
    } catch (error) {
      console.error('Failed to save image:', error)
      return false
    }
  }

  createWorkspace(name) {
    const workspaceId = `workspace_${++this.workspaceCounter}`
    this.workspaces.set(workspaceId, {
      id: workspaceId,
      name: name || `工作区 ${this.workspaceCounter}`,
      tabs: new Map(),
      activeTabId: null,
      homepage: this.settings?.homepage || 'https://www.baidu.com'
    })
    const workspace = this.workspaces.get(workspaceId)
    const serializableWorkspace = {
      id: workspace.id,
      name: workspace.name,
      homepage: workspace.homepage,
      activeTabId: workspace.activeTabId
    }
    this.mainWindow.webContents.send('workspace:created', workspaceId, serializableWorkspace)
    return workspaceId
  }

  createWorkspaceSeparator(name) {
    const separatorId = `separator_${++this.workspaceCounter}`
    const separator = {
      id: separatorId,
      name: name || `分割线 ${this.workspaceCounter}`,
      type: 'separator',
      collapsed: false
    }

    // 保存分割线到separators Map中
    this.separators.set(separatorId, separator)

    // 保存会话数据
    this.saveSessionData()

    this.mainWindow.webContents.send('workspace:separator-created', separatorId, separator)
    return separatorId
  }

  // 更新分割线状态
  updateSeparator(separatorId, updates) {
    const separator = this.separators.get(separatorId)
    if (!separator) return false

    Object.assign(separator, updates)
    this.saveSessionData()
    return true
  }

  // 删除分割线
  deleteSeparator(separatorId) {
    const separator = this.separators.get(separatorId)
    if (!separator) return false

    this.separators.delete(separatorId)
    this.saveSessionData()
    return true
  }

  // 获取按顺序排列的工作区和分割线
  getOrderedWorkspaceItems() {
    const items = []

    if (this.sessionData && this.sessionData.workspaceOrder) {
      // 按保存的顺序返回
      this.sessionData.workspaceOrder.forEach(item => {
        if (item.type === 'workspace' && this.workspaces.has(item.id)) {
          const workspace = this.workspaces.get(item.id)
          items.push({
            type: 'workspace',
            id: workspace.id,
            name: workspace.name,
            homepage: workspace.homepage,
            activeTabId: workspace.activeTabId
          })
        } else if (item.type === 'separator' && this.separators.has(item.id)) {
          const separator = this.separators.get(item.id)
          items.push({
            type: 'separator',
            id: separator.id,
            name: separator.name,
            collapsed: separator.collapsed
          })
        }
      })

      // 添加不在顺序中的工作区（新创建的）
      for (const [id, workspace] of this.workspaces) {
        const alreadyAdded = items.some(item => item.type === 'workspace' && item.id === id)
        if (!alreadyAdded) {
          items.push({
            type: 'workspace',
            id: workspace.id,
            name: workspace.name,
            homepage: workspace.homepage,
            activeTabId: workspace.activeTabId
          })
        }
        
      }
    } else {
      // 没有保存的顺序，先返回所有工作区，再返回所有分割线
      for (const [id, workspace] of this.workspaces) {
        items.push({
          type: 'workspace',
          id: workspace.id,
          name: workspace.name,
          homepage: workspace.homepage,
          activeTabId: workspace.activeTabId
        })
      }

      for (const [id, separator] of this.separators) {
        items.push({
          type: 'separator',
          id: separator.id,
          name: separator.name,
          collapsed: separator.collapsed
        })
      }
    }

    return items
  }

  deleteWorkspace(workspaceId) {
    if (workspaceId === 'default') {
      return false // 不能删除默认工作区
    }

    if (!this.workspaces.has(workspaceId)) {
      return false
    }

    // 如果删除的是当前活跃工作区，切换到默认工作区
    if (this.activeWorkspaceId === workspaceId) {
      this.switchWorkspace('default')
    }

    // 清理工作区的所有标签页
    const workspace = this.workspaces.get(workspaceId)
    for (const [tabId, tab] of workspace.tabs) {
      this.mainWindow.removeBrowserView(tab.view)
      tab.view.webContents.destroy()
    }

    this.workspaces.delete(workspaceId)
    this.mainWindow.webContents.send('workspace:deleted', workspaceId)
    return true
  }

  updateWorkspace(workspaceId, updates) {
    if (!this.workspaces.has(workspaceId)) {
      return false
    }

    const workspace = this.workspaces.get(workspaceId)
    Object.assign(workspace, updates)

    const serializableWorkspace = {
      id: workspace.id,
      name: workspace.name,
      homepage: workspace.homepage,
      activeTabId: workspace.activeTabId
    }
    this.mainWindow.webContents.send('workspace:updated', workspaceId, serializableWorkspace)
    return true
  }

  switchWorkspace(workspaceId) {
    if (!this.workspaces.has(workspaceId)) return

    // 保存当前工作区的活跃标签页ID
    if (this.activeWorkspaceId && this.workspaces.has(this.activeWorkspaceId)) {
      const currentWorkspace = this.workspaces.get(this.activeWorkspaceId)
      currentWorkspace.activeTabId = this.activeTabId
    }

    // 隐藏当前工作区的所有标签页
    for (const [tabId, tab] of this.tabs) {
      this.mainWindow.removeBrowserView(tab.view)
    }

    this.activeWorkspaceId = workspaceId
    const workspace = this.workspaces.get(workspaceId)
    this.tabs = workspace.tabs

    // 如果工作区没有标签页，创建一个
    if (this.tabs.size === 0) {
      this.createInitialTab()
    } else {
      // 恢复工作区的最后活跃标签页，如果不存在则显示第一个标签页
      let targetTabId = workspace.activeTabId
      if (!targetTabId || !this.tabs.has(targetTabId)) {
        targetTabId = Array.from(this.tabs.keys())[0]
      }
      this.switchToTab(targetTabId)
    }

    // 只发送可序列化的工作区数据
    const serializableWorkspace = {
      id: workspace.id,
      name: workspace.name,
      homepage: workspace.homepage,
      activeTabId: workspace.activeTabId
    }
    this.mainWindow.webContents.send('workspace:switched', workspaceId, serializableWorkspace)
  }

  // 添加设置窗口功能
  openSettingsWindow() {
    if (this.settingsWindow) {
      this.settingsWindow.focus()
      return
    }

    // 获取主窗口大小并计算70%
    const mainBounds = this.mainWindow.getBounds()
    const width = Math.floor(mainBounds.width * 0.7)
    const height = Math.floor(mainBounds.height * 0.7)

    this.settingsWindow = new BrowserWindow({
      width: Math.max(width, 800), // 最小宽度800px
      height: Math.max(height, 600), // 最小高度600px
      parent: this.mainWindow,
      modal: true,
      webPreferences: {
        preload: path.join(__dirname, 'settings-preload.js'),
        nodeIntegration: false,
        contextIsolation: true
      },
      resizable: true,
      minimizable: false,
      maximizable: false,
      title: '设置'
    })

    this.settingsWindow.loadFile('settings.html')
    
    this.settingsWindow.on('closed', () => {
      this.settingsWindow = null
    })
  }

  openUserScriptsWindow() {
    if (this.userScriptsWindow) {
      this.userScriptsWindow.focus()
      return
    }

    // 获取主窗口大小并计算70%
    const mainBounds = this.mainWindow.getBounds()
    const width = Math.floor(mainBounds.width * 0.7)
    const height = Math.floor(mainBounds.height * 0.7)

    this.userScriptsWindow = new BrowserWindow({
      width: Math.max(width, 900), // 最小宽度900px
      height: Math.max(height, 700), // 最小高度700px
      parent: this.mainWindow,
      modal: true,
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true
      },
      resizable: true,
      minimizable: false,
      maximizable: false,
      title: '用户脚本管理'
    })

    this.userScriptsWindow.loadFile('userscripts.html')

    this.userScriptsWindow.on('closed', () => {
      this.userScriptsWindow = null
    })
  }

  openNotesWindow() {
    if (this.notesWindow) {
      this.notesWindow.focus()
      return
    }

    // 获取主窗口大小并计算80%
    const mainBounds = this.mainWindow.getBounds()
    const width = Math.floor(mainBounds.width * 0.8)
    const height = Math.floor(mainBounds.height * 0.8)

    this.notesWindow = new BrowserWindow({
      width: Math.max(width, 1000), // 最小宽度1000px
      height: Math.max(height, 700), // 最小高度700px
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true
      },
      resizable: true,
      minimizable: true,
      maximizable: true,
      title: '笔记管理器',
      alwaysOnTop: true // 置顶显示
    })

    this.notesWindow.loadFile('notes.html')

    this.notesWindow.on('closed', () => {
      this.notesWindow = null
    })
  }



  getUserScripts() {
    try {
      const userScriptsPath = path.join(__dirname, 'userscripts.json')
      if (fs.existsSync(userScriptsPath)) {
        return JSON.parse(fs.readFileSync(userScriptsPath, 'utf8'))
      }
    } catch (error) {
      console.error('Failed to load user scripts:', error)
    }
    return []
  }

  saveUserScripts(scripts) {
    try {
      const userScriptsPath = path.join(__dirname, 'userscripts.json')
      fs.writeFileSync(userScriptsPath, JSON.stringify(scripts, null, 2))

      // 重新注入所有脚本到现有标签页
      this.injectUserScriptsToAllTabs()

      return true
    } catch (error) {
      console.error('Failed to save user scripts:', error)
      return false
    }
  }

  // 注入用户脚本到所有标签页
  injectUserScriptsToAllTabs() {
    const userScripts = this.getUserScripts()

    // 注入到主面板标签页
    for (const [tabId, tab] of this.tabs) {
      this.injectUserScriptsToTab(tab, userScripts)
    }

    // 注入到右面板标签页
    for (const [tabId, tab] of this.rightPanelTabs) {
      this.injectUserScriptsToTab(tab, userScripts)
    }
  }

  // 注入用户脚本到指定标签页
  injectUserScriptsToTab(tab, userScripts = null) {
    if (!userScripts) {
      userScripts = this.getUserScripts()
    }

    const enabledScripts = userScripts.filter(script => script.enabled)

    // 等待页面加载完成后注入脚本
    tab.view.webContents.once('dom-ready', () => {
      const currentUrl = tab.view.webContents.getURL()

      for (const script of enabledScripts) {
        if (this.matchesPattern(currentUrl, script.match)) {
          try {
            tab.view.webContents.executeJavaScript(script.code)
            console.log(`Injected user script "${script.name}" into ${currentUrl}`)
          } catch (error) {
            console.error(`Failed to inject user script "${script.name}":`, error)
          }
        }
      }
    })
  }

  // 检查URL是否匹配模式
  matchesPattern(url, patterns) {
    // 支持多个模式，用逗号分隔
    const patternList = patterns.split(',').map(p => p.trim()).filter(p => p.length > 0)

    for (const pattern of patternList) {
      // 将通配符模式转换为正则表达式
      const regexPattern = pattern
        .replace(/[.+^${}()|[\]\\]/g, '\\$&') // 转义特殊字符
        .replace(/\*/g, '.*') // 将 * 替换为 .*

      const regex = new RegExp(`^${regexPattern}$`, 'i')
      if (regex.test(url)) {
        return true
      }
    }

    return false
  }

  // 笔记管理方法
  getNotes() {
    try {
      const notesPath = path.join(__dirname, 'notes.json')
      if (fs.existsSync(notesPath)) {
        const data = fs.readFileSync(notesPath, 'utf8')
        return JSON.parse(data)
      }
    } catch (error) {
      console.error('Failed to load notes:', error)
    }
    return []
  }

  saveNotes(notes) {
    try {
      const notesPath = path.join(__dirname, 'notes.json')
      fs.writeFileSync(notesPath, JSON.stringify(notes, null, 2))
      return true
    } catch (error) {
      console.error('Failed to save notes:', error)
      return false
    }
  }

  // 密码管理方法
  openPasswordManagerWindow() {
    if (this.passwordManagerWindow) {
      this.passwordManagerWindow.focus()
      return
    }

    // 获取屏幕大小
    const { screen } = require('electron')
    const primaryDisplay = screen.getPrimaryDisplay()
    const screenWidth = primaryDisplay.workAreaSize.width
    const screenHeight = primaryDisplay.workAreaSize.height

    // 设置为屏幕右1/3的宽度
    const width = Math.floor(screenWidth / 3)
    const height = screenHeight
    const x = screenWidth - width
    const y = 0

    this.passwordManagerWindow = new BrowserWindow({
      width: width,
      height: height,
      x: x,
      y: y,
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true
      },
      resizable: true,
      minimizable: true,
      maximizable: true,
      title: '密码管理器',
      alwaysOnTop: true // 置顶显示
    })

    this.passwordManagerWindow.loadFile('password-manager.html')

    this.passwordManagerWindow.on('closed', () => {
      this.passwordManagerWindow = null
    })
  }

  getPasswords() {
    try {
      const passwordsPath = path.join(__dirname, 'passwords.json')
      if (fs.existsSync(passwordsPath)) {
        const encryptedData = fs.readFileSync(passwordsPath, 'utf8')
        // 简单的加密/解密（实际应用中应使用更强的加密）
        const decryptedData = Buffer.from(encryptedData, 'base64').toString('utf8')
        return JSON.parse(decryptedData)
      }
    } catch (error) {
      console.error('Failed to load passwords:', error)
    }
    return []
  }

  savePasswords(passwords) {
    try {
      const passwordsPath = path.join(__dirname, 'passwords.json')
      // 简单的加密/解密（实际应用中应使用更强的加密）
      const encryptedData = Buffer.from(JSON.stringify(passwords, null, 2)).toString('base64')
      fs.writeFileSync(passwordsPath, encryptedData)
      return true
    } catch (error) {
      console.error('Failed to save passwords:', error)
      return false
    }
  }

  // 保存选中文本为笔记
  async saveTextAsNote(selectedText, url) {
    try {
      const notes = this.getNotes()
      const domain = new URL(url).hostname

      const newNote = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        title: `来自 ${domain} 的笔记`,
        content: `<p><strong>来源：</strong><a href="${url}">${url}</a></p><hr><p>${selectedText.replace(/\n/g, '<br>')}</p>`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      notes.unshift(newNote)
      this.saveNotes(notes)

      // 显示通知
      const { Notification } = require('electron')
      new Notification({
        title: '笔记已保存',
        body: `已将选中文本保存为笔记`
      }).show()

    } catch (error) {
      console.error('Failed to save note:', error)
    }
  }

  // 保存当前页面的账号密码
  async saveCurrentPassword(view) {
    try {
      const result = await view.webContents.executeJavaScript(`
        (function() {
          const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"]');
          const data = {};

          inputs.forEach(input => {
            if (input.type === 'password' && input.value) {
              data.password = input.value;
            } else if ((input.type === 'text' || input.type === 'email') && input.value) {
              if (!data.username) {
                data.username = input.value;
              }
            }
          });

          return data;
        })();
      `)

      // 如果有密码或用户名，就可以保存
      if (result.password || result.username) {
        const url = view.webContents.getURL()
        const domain = new URL(url).hostname

        const passwords = this.getPasswords()
        const newPassword = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          siteName: domain,
          url: url,
          username: result.username || '',
          password: result.password || '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        passwords.push(newPassword)
        this.savePasswords(passwords)

        // 显示通知
        const { Notification } = require('electron')
        new Notification({
          title: '密码已保存',
          body: `已保存 ${domain} 的${result.username ? '账号' : ''}${result.password ? '密码' : ''}`
        }).show()
      } else {
        const { dialog } = require('electron')
        dialog.showMessageBox(this.mainWindow, {
          type: 'warning',
          title: '保存失败',
          message: '未找到有效的用户名或密码',
          detail: '请确保页面中有填写的用户名或密码字段'
        })
      }
    } catch (error) {
      console.error('Failed to save password:', error)
    }
  }

  // 显示密码选项并填入
  async showPasswordOptions(view) {
    try {
      const url = view.webContents.getURL()
      const domain = new URL(url).hostname
      const passwords = this.getPasswordsForDomain(domain)

      if (passwords.length === 0) {
        const { dialog } = require('electron')
        dialog.showMessageBox(this.mainWindow, {
          type: 'info',
          title: '无保存的密码',
          message: `未找到 ${domain} 的保存密码`,
          detail: '您可以先保存当前页面的账号密码'
        })
        return
      }

      // 如果只有一个密码，直接填入
      if (passwords.length === 1) {
        this.fillPassword(view, passwords[0])
        return
      }

      // 多个密码时显示选择对话框
      const { dialog } = require('electron')
      const choices = passwords.map(p => p.username)
      const result = await dialog.showMessageBox(this.mainWindow, {
        type: 'question',
        title: '选择账号',
        message: '请选择要填入的账号:',
        detail: choices.join('\n'),
        buttons: [...choices, '取消'],
        defaultId: 0,
        cancelId: choices.length
      })

      if (result.response < choices.length) {
        this.fillPassword(view, passwords[result.response])
      }
    } catch (error) {
      console.error('Failed to show password options:', error)
    }
  }

  // 获取指定域名的密码
  getPasswordsForDomain(domain) {
    const passwords = this.getPasswords()
    return passwords.filter(p => {
      if (!p.url) return false
      try {
        const passwordDomain = new URL(p.url).hostname
        return passwordDomain === domain
      } catch {
        return false
      }
    })
  }

  // 填入密码
  async fillPassword(view, password) {
    try {
      await view.webContents.executeJavaScript(`
        (function() {
          const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"]');
          let usernameInput = null;
          let passwordInput = null;

          inputs.forEach(input => {
            if (input.type === 'password') {
              passwordInput = input;
            } else if ((input.type === 'text' || input.type === 'email') && !usernameInput) {
              usernameInput = input;
            }
          });

          if (usernameInput) {
            usernameInput.value = '${password.username.replace(/'/g, "\\'")}';
            usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
            usernameInput.dispatchEvent(new Event('change', { bubbles: true }));
          }

          if (passwordInput) {
            passwordInput.value = '${password.password.replace(/'/g, "\\'")}';
            passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
            passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
          }

          return { username: !!usernameInput, password: !!passwordInput };
        })();
      `)

      // 显示通知
      const { Notification } = require('electron')
      new Notification({
        title: '密码已填入',
        body: `已填入 ${password.username} 的账号密码`
      }).show()

    } catch (error) {
      console.error('Failed to fill password:', error)
    }
  }

  // 窗口控制方法
  resizeCurrentWindow(event, width, height) {
    const window = BrowserWindow.fromWebContents(event.sender)
    if (window) {
      window.setSize(width, height)
    }
  }

  moveCurrentWindow(event, x, y) {
    const window = BrowserWindow.fromWebContents(event.sender)
    if (window) {
      window.setPosition(x, y)
    }
  }

  getScreenSize() {
    const { screen } = require('electron')
    const primaryDisplay = screen.getPrimaryDisplay()
    return {
      width: primaryDisplay.workAreaSize.width,
      height: primaryDisplay.workAreaSize.height
    }
  }

  // 下载管理器方法
  openDownloadManagerWindow() {
    if (this.downloadManagerWindow) {
      this.downloadManagerWindow.focus()
      return
    }

    // 获取主窗口大小并计算70%
    const mainBounds = this.mainWindow.getBounds()
    const width = Math.floor(mainBounds.width * 0.7)
    const height = Math.floor(mainBounds.height * 0.7)

    this.downloadManagerWindow = new BrowserWindow({
      width: Math.max(width, 800), // 最小宽度800px
      height: Math.max(height, 600), // 最小高度600px
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true
      },
      resizable: true,
      minimizable: true,
      maximizable: true,
      title: '下载管理器',
      alwaysOnTop: true // 置顶显示
    })

    this.downloadManagerWindow.loadFile('download-manager.html')

    this.downloadManagerWindow.on('closed', () => {
      this.downloadManagerWindow = null
    })
  }

  getDownloads() {
    return this.downloads
  }

  getDownloadSettings() {
    try {
      const settingsPath = path.join(__dirname, 'download-settings.json')
      if (fs.existsSync(settingsPath)) {
        const data = fs.readFileSync(settingsPath, 'utf8')
        const settings = JSON.parse(data)
        return settings
      }
    } catch (error) {
      console.error('Failed to load download settings:', error)
    }
    return { downloadPath: require('os').homedir() + '/Downloads' }
  }

  saveDownloadSettings(settings) {
    try {
      const settingsPath = path.join(__dirname, 'download-settings.json')
      fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2))
      return true
    } catch (error) {
      console.error('Failed to save download settings:', error)
      return false
    }
  }

  async selectDownloadPath() {
    const { dialog } = require('electron')
    const result = await dialog.showOpenDialog(this.downloadManagerWindow || this.mainWindow, {
      properties: ['openDirectory'],
      title: '选择下载保存路径'
    })

    if (!result.canceled && result.filePaths.length > 0) {
      return result.filePaths[0]
    }
    return null
  }

  async openFile(filePath) {
    const { shell } = require('electron')
    try {
      await shell.openPath(filePath)
    } catch (error) {
      console.error('Failed to open file:', error)
      throw error
    }
  }

  async showInFolder(filePath) {
    const { shell } = require('electron')
    try {
      shell.showItemInFolder(filePath)
    } catch (error) {
      console.error('Failed to show in folder:', error)
      throw error
    }
  }

  // 设置下载处理
  setupDownloadHandling() {
    // 监听所有webContents的下载事件
    this.mainWindow.webContents.session.on('will-download', (event, item, webContents) => {
      this.handleDownload(item, webContents)
    })
  }

  // 为标签页设置下载处理
  setupTabDownloadHandling(view) {
    view.webContents.session.on('will-download', (event, item, webContents) => {
      this.handleDownload(item, webContents)
    })
  }

  // 处理下载
  handleDownload(item, webContents) {
    const downloadId = Date.now().toString() + Math.random().toString(36).substr(2, 9)

    // 获取下载设置
    const settings = this.getDownloadSettings()
    const downloadPath = settings.downloadPath || require('os').homedir() + '/Downloads'

    // 设置下载路径
    const filename = item.getFilename()
    const savePath = require('path').join(downloadPath, filename)
    item.setSavePath(savePath)

    // 创建下载记录
    const download = {
      id: downloadId,
      filename: filename,
      url: item.getURL(),
      savePath: savePath,
      totalBytes: item.getTotalBytes(),
      receivedBytes: 0,
      state: 'progressing',
      startTime: new Date().toISOString()
    }

    // 添加到下载列表
    this.downloads.unshift(download)

    // 监听下载进度
    item.on('updated', (event, state) => {
      download.receivedBytes = item.getReceivedBytes()
      download.state = state

      // 通知下载管理器窗口更新
      if (this.downloadManagerWindow) {
        this.downloadManagerWindow.webContents.send('download:updated', download)
      }
    })

    // 监听下载完成
    item.once('done', (event, state) => {
      download.state = state
      download.endTime = new Date().toISOString()

      // 通知下载管理器窗口更新
      if (this.downloadManagerWindow) {
        this.downloadManagerWindow.webContents.send('download:updated', download)
      }

      // 显示系统通知
      if (state === 'completed') {
        const { Notification } = require('electron')
        new Notification({
          title: '下载完成',
          body: `${filename} 已下载完成`,
          icon: this.mainWindow.webContents.getURL().includes('https') ? undefined : undefined
        }).show()
      }
    })
  }

}

const browser = new Browser()

app.whenReady().then(() => {
  // 设置macOS Dock图标
  if (process.platform === 'darwin') {
    try {
      const iconPath = path.join(__dirname, 'lo.png')
      if (fs.existsSync(iconPath)) {
        app.dock.setIcon(iconPath)
      }
    } catch (error) {
      console.warn('Failed to set dock icon:', error.message)
    }
  }

  browser.createWindow()
})

app.on('before-quit', () => {
  browser.saveSessionData()
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    browser.createWindow()
  }
})
