const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ipc<PERSON><PERSON>, Menu, shell } = require('electron')
const path = require('path')
const fs = require('fs')

class Browser {
  constructor() {
    this.mainWindow = null
    this.tabs = new Map()
    this.activeTabId = null
    this.tabCounter = 0
    this.ipcSetup = false
    this.rightPanelVisible = false
    this.rightPanelTabs = new Map()
    this.activeRightTabId = null
    this.rightTabCounter = 1000 // 使用不同的起始ID避免冲突
    this.rightPanelWidth = 400
    this.leftPanelWidth = 70
    this.leftPanelVisible = true
    this.workspaces = new Map()
    this.activeWorkspaceId = 'default'
    this.workspaceCounter = 0
    this.settings = this.loadSettings()
    this.initializeDefaultWorkspace()
  }

  loadSettings() {
    try {
      const settingsPath = path.join(__dirname, 'settings.json')
      if (fs.existsSync(settingsPath)) {
        return JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
    return {
      homepage: 'https://www.google.com',
      searchEngine: 'google',
      customSearchEngines: [],
      leftPanelWidth: 70
    }
  }

  saveSettingsToFile() {
    try {
      const settingsPath = path.join(__dirname, 'settings.json')
      fs.writeFileSync(settingsPath, JSON.stringify(this.settings, null, 2))
    } catch (error) {
      console.error('Failed to save settings:', error)
    }
  }

  initializeDefaultWorkspace() {
    this.workspaces.set('default', {
      id: 'default',
      name: '默认工作区',
      tabs: new Map(),
      activeTabId: null,
      homepage: 'https://www.google.com'
    })
  }

  createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true
      },
      titleBarStyle: 'hidden'
    })

    this.mainWindow.loadFile('index.html')

    // 监听窗口大小变化
    this.mainWindow.on('resize', () => {
      this.updateAllTabBounds()
    })

    if (!this.ipcSetup) {
      this.setupIPC()
      this.ipcSetup = true
    }

    // 加载浏览器插件
    this.loadExtensions()

    this.createInitialTab()
  }

  getMainViewBounds() {
    const bounds = this.mainWindow.getBounds()
    const rightPanelWidth = this.rightPanelVisible ? this.rightPanelWidth : 0
    const leftPanelWidth = this.leftPanelVisible ? this.leftPanelWidth : 0
    return {
      x: leftPanelWidth,
      y: 90,
      width: bounds.width - rightPanelWidth - leftPanelWidth,
      height: bounds.height - 114
    }
  }

  getRightViewBounds() {
    const bounds = this.mainWindow.getBounds()
    return {
      x: bounds.width - this.rightPanelWidth,
      y: 170, // 增加Y坐标，为标签栏留出空间
      width: this.rightPanelWidth,
      height: bounds.height - 194 // 相应减少高度
    }
  }

  updateAllTabBounds() {
    // 更新主面板标签页
    if (this.activeTabId && this.tabs.has(this.activeTabId)) {
      const tab = this.tabs.get(this.activeTabId)
      tab.view.setBounds(this.getMainViewBounds())
    }

    // 更新右侧面板标签页
    if (this.activeRightTabId && this.rightPanelTabs.has(this.activeRightTabId)) {
      const tab = this.rightPanelTabs.get(this.activeRightTabId)
      tab.view.setBounds(this.getRightViewBounds())
    }
  }

  createTab(url = 'https://www.google.com', isRightPanel = false) {
    const tabId = isRightPanel ? ++this.rightTabCounter : ++this.tabCounter
    const view = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    })

    const tabData = {
      view,
      url,
      title: 'New Tab',
      canGoBack: false,
      canGoForward: false
    }

    if (isRightPanel) {
      this.rightPanelTabs.set(tabId, tabData)
      this.switchToRightTab(tabId)
    } else {
      this.tabs.set(tabId, tabData)
      this.switchToTab(tabId)
    }

    view.webContents.loadURL(url)
    this.setupTabEvents(tabId, view, isRightPanel)
    
    // 通知渲染进程新标签页已创建
    this.mainWindow.webContents.send('tab:created', tabId, {
      id: tabId,
      title: 'New Tab',
      url: url,
      active: true,
      isRightPanel
    })
    
    return tabId
  }

  setupTabEvents(tabId, view, isRightPanel = false) {
    view.webContents.on('did-navigate', (event, url) => {
      this.updateTabInfo(tabId, { url }, isRightPanel)
    })

    view.webContents.on('page-title-updated', (event, title) => {
      this.updateTabInfo(tabId, { title }, isRightPanel)
    })

    view.webContents.setWindowOpenHandler(({ url }) => {
      this.createTab(url, isRightPanel)
      return { action: 'deny' }
    })

    view.webContents.on('will-navigate', (event, url) => {
      if (url.startsWith('http') || url.startsWith('https')) {
        this.updateTabInfo(tabId, { url }, isRightPanel)
      }
    })

    view.webContents.on('new-window', (event, url) => {
      event.preventDefault()
      this.createTab(url, isRightPanel)
    })

    // 处理页面内容的右键菜单
    view.webContents.on('context-menu', (event, params) => {
      const { Menu, MenuItem } = require('electron')
      const menu = new Menu()

      // 基础菜单项
      menu.append(new MenuItem({
        label: '返回',
        enabled: view.webContents.canGoBack(),
        click: () => view.webContents.goBack()
      }))

      menu.append(new MenuItem({
        label: '前进',
        enabled: view.webContents.canGoForward(),
        click: () => view.webContents.goForward()
      }))

      menu.append(new MenuItem({
        label: '刷新',
        click: () => view.webContents.reload()
      }))

      menu.append(new MenuItem({ type: 'separator' }))

      menu.append(new MenuItem({
        label: '复制页面地址',
        click: () => {
          const tab = isRightPanel ? this.rightPanelTabs.get(tabId) : this.tabs.get(tabId)
          if (tab) {
            require('electron').clipboard.writeText(tab.url)
          }
        }
      }))

      // 如果有选中文字，添加搜索选项
      if (params.selectionText) {
        menu.append(new MenuItem({ type: 'separator' }))
        menu.append(new MenuItem({
          label: `搜索 "${params.selectionText}"`,
          click: () => {
            const searchEngine = this.settings?.searchEngine || 'google'
            const searchUrl = this.getSearchUrl(params.selectionText, searchEngine)
            this.createTab(searchUrl, isRightPanel)
          }
        }))
      }

      // 如果右键点击的是图片，添加图片相关选项
      if (params.srcURL && params.mediaType === 'image') {
        menu.append(new MenuItem({ type: 'separator' }))
        menu.append(new MenuItem({
          label: '复制图片',
          click: async () => {
            try {
              await this.copyImage(params.srcURL)
            } catch (error) {
              console.error('复制图片失败:', error)
            }
          }
        }))
        menu.append(new MenuItem({
          label: '保存图片',
          click: async () => {
            try {
              await this.saveImage(params.srcURL)
            } catch (error) {
              console.error('保存图片失败:', error)
            }
          }
        }))
      }

      menu.popup({ window: this.mainWindow })
    })
  }

  switchToTab(tabId) {
    if (!this.tabs.has(tabId)) return

    // 隐藏当前活跃的主面板标签页
    if (this.activeTabId && this.tabs.has(this.activeTabId)) {
      this.mainWindow.removeBrowserView(this.tabs.get(this.activeTabId).view)
    }

    this.activeTabId = tabId
    const tab = this.tabs.get(tabId)
    this.mainWindow.addBrowserView(tab.view)
    tab.view.setBounds(this.getMainViewBounds())
    tab.view.setAutoResize({ width: true, height: true })

    this.updateNavigationState()
    this.mainWindow.webContents.send('tab:switched', tabId, tab)
  }

  switchToRightTab(tabId) {
    if (!this.rightPanelTabs.has(tabId)) return

    // 隐藏当前活跃的右侧面板标签页
    if (this.activeRightTabId && this.rightPanelTabs.has(this.activeRightTabId)) {
      this.mainWindow.removeBrowserView(this.rightPanelTabs.get(this.activeRightTabId).view)
    }

    this.activeRightTabId = tabId
    const tab = this.rightPanelTabs.get(tabId)
    this.mainWindow.addBrowserView(tab.view)
    tab.view.setBounds(this.getRightViewBounds())
    tab.view.setAutoResize({ width: true, height: true })

    this.updateRightNavigationState()
    this.mainWindow.webContents.send('right-tab:switched', tabId, tab)
  }

  updateTabInfo(tabId, info, isRightPanel = false) {
    const tabs = isRightPanel ? this.rightPanelTabs : this.tabs
    const activeId = isRightPanel ? this.activeRightTabId : this.activeTabId
    
    if (!tabs.has(tabId)) return
    
    const tab = tabs.get(tabId)
    Object.assign(tab, info)
    
    if (tabId === activeId) {
      if (isRightPanel) {
        this.updateRightNavigationState()
      } else {
        this.updateNavigationState()
      }
    }
    
    const eventName = isRightPanel ? 'right-tab:updated' : 'tab:updated'
    this.mainWindow.webContents.send(eventName, tabId, tab)
  }

  updateNavigationState() {
    if (!this.activeTabId) return
    
    const tab = this.tabs.get(this.activeTabId)
    const webContents = tab.view.webContents
    
    tab.canGoBack = webContents.canGoBack()
    tab.canGoForward = webContents.canGoForward()
    
    this.mainWindow.webContents.send('navigation:updated', {
      canGoBack: tab.canGoBack,
      canGoForward: tab.canGoForward,
      url: tab.url,
      title: tab.title
    })
  }

  updateRightNavigationState() {
    if (!this.activeRightTabId) return
    
    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    const webContents = tab.view.webContents
    
    tab.canGoBack = webContents.canGoBack()
    tab.canGoForward = webContents.canGoForward()
    
    this.mainWindow.webContents.send('right-navigation:updated', {
      canGoBack: tab.canGoBack,
      canGoForward: tab.canGoForward,
      url: tab.url,
      title: tab.title
    })
  }

  toggleRightPanel() {
    this.rightPanelVisible = !this.rightPanelVisible
    
    if (this.rightPanelVisible && this.rightPanelTabs.size === 0) {
      this.createTab('https://www.google.com', true)
    }
    
    // 如果隐藏右侧面板，移除右侧面板的视图
    if (!this.rightPanelVisible && this.activeRightTabId) {
      const tab = this.rightPanelTabs.get(this.activeRightTabId)
      if (tab) {
        this.mainWindow.removeBrowserView(tab.view)
      }
    }
    
    this.updateAllTabBounds()
    this.mainWindow.webContents.send('right-panel:toggled', this.rightPanelVisible)
    
    // 如果显示右侧面板，重新添加右侧面板的视图
    if (this.rightPanelVisible && this.activeRightTabId) {
      const tab = this.rightPanelTabs.get(this.activeRightTabId)
      if (tab) {
        this.mainWindow.addBrowserView(tab.view)
        tab.view.setBounds(this.getRightViewBounds())
      }
    }
  }

  setupIPC() {
    // 主面板IPC
    ipcMain.handle('tab:create', (event, url) => this.createTab(url))
    ipcMain.handle('tab:close', (event, tabId) => this.closeTab(tabId))
    ipcMain.handle('tab:switch', (event, tabId) => this.switchToTab(tabId))
    ipcMain.handle('tab:navigate', (event, url) => this.navigateActiveTab(url))
    ipcMain.handle('navigation:back', () => this.goBack())
    ipcMain.handle('navigation:forward', () => this.goForward())
    ipcMain.handle('navigation:reload', () => this.reload())
    ipcMain.handle('navigation:home', () => this.goHome())
    ipcMain.handle('tabs:getAll', () => this.getAllTabs())
    
    // 右侧面板IPC
    ipcMain.handle('right-panel:toggle', () => this.toggleRightPanel())
    ipcMain.handle('right-panel:resize', (event, width) => this.resizeRightPanel(width))
    ipcMain.handle('right-tab:create', (event, url) => this.createTab(url, true))
    ipcMain.handle('right-tab:close', (event, tabId) => this.closeRightTab(tabId))
    ipcMain.handle('right-tab:switch', (event, tabId) => this.switchToRightTab(tabId))
    ipcMain.handle('right-tab:navigate', (event, url) => this.navigateActiveRightTab(url))
    ipcMain.handle('right-navigation:back', () => this.goRightBack())
    ipcMain.handle('right-navigation:forward', () => this.goRightForward())
    ipcMain.handle('right-navigation:reload', () => this.reloadRight())
    ipcMain.handle('right-navigation:home', () => this.goRightHome())
    
    // 工作区IPC
    ipcMain.handle('workspace:create', (event, name) => this.createWorkspace(name))
    ipcMain.handle('workspace:switch', (event, workspaceId) => this.switchWorkspace(workspaceId))
    ipcMain.handle('workspace:delete', (event, workspaceId) => this.deleteWorkspace(workspaceId))
    ipcMain.handle('workspace:update', (event, workspaceId, updates) => this.updateWorkspace(workspaceId, updates))
    ipcMain.handle('workspace:getAll', () => Array.from(this.workspaces.values()))
    ipcMain.handle('workspace:prompt-name', async () => {
      // 创建一个输入对话框窗口
      return new Promise((resolve) => {
        const inputWindow = new BrowserWindow({
          width: 400,
          height: 200,
          parent: this.mainWindow,
          modal: true,
          resizable: false,
          minimizable: false,
          maximizable: false,
          webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
          },
          title: '新建工作区'
        })

        const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="UTF-8">
            <style>
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background: #f5f5f5;
              }
              .container {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              h3 {
                margin: 0 0 15px 0;
                color: #333;
              }
              input {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                margin-bottom: 15px;
                box-sizing: border-box;
              }
              .buttons {
                text-align: right;
              }
              button {
                padding: 8px 16px;
                margin-left: 8px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
              }
              .btn-primary {
                background: #007acc;
                color: white;
              }
              .btn-secondary {
                background: #e0e0e0;
                color: #333;
              }
              button:hover {
                opacity: 0.8;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <h3>新建工作区</h3>
              <input type="text" id="workspace-name" placeholder="请输入工作区名称" value="工作区 ${this.workspaceCounter + 1}">
              <div class="buttons">
                <button class="btn-secondary" onclick="cancel()">取消</button>
                <button class="btn-primary" onclick="confirm()">确定</button>
              </div>
            </div>
            <script>
              const { ipcRenderer } = require('electron')

              function confirm() {
                const name = document.getElementById('workspace-name').value.trim()
                if (name) {
                  ipcRenderer.send('workspace-input-result', name)
                } else {
                  alert('请输入工作区名称')
                }
              }

              function cancel() {
                ipcRenderer.send('workspace-input-result', null)
              }

              document.getElementById('workspace-name').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                  confirm()
                }
              })

              // 自动聚焦并选中文本
              document.getElementById('workspace-name').focus()
              document.getElementById('workspace-name').select()
            </script>
          </body>
          </html>
        `

        inputWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent))

        ipcMain.once('workspace-input-result', (event, result) => {
          inputWindow.close()
          resolve(result)
        })

        inputWindow.on('closed', () => {
          resolve(null)
        })
      })
    })
    
    // 设置相关
    ipcMain.handle('settings:open', () => this.openSettingsWindow())
    ipcMain.handle('settings:get', () => this.getSettings())
    ipcMain.handle('settings:set', (event, settings) => this.setSettings(settings))
    ipcMain.handle('settings:save', (event, settings) => {
      this.setSettings(settings)
      return true
    })
    
    // 右键菜单功能
    ipcMain.handle('context:copy-url', () => this.copyCurrentUrl())
    ipcMain.handle('context:search-text', (event, text) => this.searchSelectedText(text))
    ipcMain.handle('context:copy-image', (event, imageUrl) => this.copyImage(imageUrl))
    ipcMain.handle('context:save-image', (event, imageUrl) => this.saveImage(imageUrl))
  }

  resizeRightPanel(width) {
    const bounds = this.mainWindow.getBounds()
    const maxWidth = Math.floor(bounds.width * 0.5)
    const minWidth = 300
    
    this.rightPanelWidth = Math.max(minWidth, Math.min(width, maxWidth))
    this.updateAllTabBounds()
    this.mainWindow.webContents.send('right-panel:resized', this.rightPanelWidth)
  }

  getSettings() {
    return this.settings
  }

  setSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings }
    this.saveSettingsToFile()
    
    // 更新左侧面板宽度
    if (newSettings.leftPanelWidth) {
      this.leftPanelWidth = newSettings.leftPanelWidth
      this.mainWindow.webContents.send('left-panel:resized', this.leftPanelWidth)
    }
  }

  getSearchUrl(query, searchEngine = 'baidu') {
    const engines = {
      google: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
      bing: `https://www.bing.com/search?q=${encodeURIComponent(query)}`,
      baidu: `https://www.baidu.com/s?wd=${encodeURIComponent(query)}`
    }
    
    // 检查自定义搜索引擎
    const customEngine = this.settings.customSearchEngines?.find(e => e.id === searchEngine)
    if (customEngine) {
      return customEngine.url.replace('%s', encodeURIComponent(query))
    }
    
    return engines[searchEngine] || engines.google
  }

  navigateActiveTab(url) {
    if (!this.activeTabId) return
    
    if (!url.includes('.') && !url.startsWith('http')) {
      const searchEngine = this.settings?.searchEngine || 'google'
      url = this.getSearchUrl(url, searchEngine)
    } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    
    const tab = this.tabs.get(this.activeTabId)
    tab.view.webContents.loadURL(url)
  }

  navigateActiveRightTab(url) {
    if (!this.activeRightTabId) return
    
    if (!url.includes('.') && !url.startsWith('http')) {
      const searchEngine = this.settings?.searchEngine || 'google'
      url = this.getSearchUrl(url, searchEngine)
    } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    
    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    tab.view.webContents.loadURL(url)
  }

  goBack() {
    if (!this.activeTabId) return
    const tab = this.tabs.get(this.activeTabId)
    if (tab.canGoBack) {
      tab.view.webContents.goBack()
    }
  }

  goRightBack() {
    if (!this.activeRightTabId) return
    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    if (tab.canGoBack) {
      tab.view.webContents.goBack()
    }
  }

  goForward() {
    if (!this.activeTabId) return
    const tab = this.tabs.get(this.activeTabId)
    if (tab.canGoForward) {
      tab.view.webContents.goForward()
    }
  }

  goRightForward() {
    if (!this.activeRightTabId) return
    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    if (tab.canGoForward) {
      tab.view.webContents.goForward()
    }
  }

  reload() {
    if (!this.activeTabId) return
    const tab = this.tabs.get(this.activeTabId)
    tab.view.webContents.reload()
  }

  reloadRight() {
    if (!this.activeRightTabId) return
    const tab = this.rightPanelTabs.get(this.activeRightTabId)
    tab.view.webContents.reload()
  }

  goHome() {
    const homepage = this.settings?.homepage || 'https://www.google.com'
    this.navigateActiveTab(homepage)
  }

  goRightHome() {
    const homepage = this.settings?.homepage || 'https://www.google.com'
    this.navigateActiveRightTab(homepage)
  }

  closeTab(tabId) {
    if (!this.tabs.has(tabId)) return
    
    const tab = this.tabs.get(tabId)
    this.mainWindow.removeBrowserView(tab.view)
    tab.view.webContents.destroy()
    this.tabs.delete(tabId)
    
    if (tabId === this.activeTabId) {
      const remainingTabs = Array.from(this.tabs.keys())
      if (remainingTabs.length > 0) {
        this.switchToTab(remainingTabs[0])
      } else {
        this.activeTabId = null
        this.createTab()
      }
    }
    
    this.mainWindow.webContents.send('tab:closed', tabId)
  }

  closeRightTab(tabId) {
    if (!this.rightPanelTabs.has(tabId)) return
    
    const tab = this.rightPanelTabs.get(tabId)
    this.mainWindow.removeBrowserView(tab.view)
    tab.view.webContents.destroy()
    this.rightPanelTabs.delete(tabId)
    
    if (tabId === this.activeRightTabId) {
      const remainingTabs = Array.from(this.rightPanelTabs.keys())
      if (remainingTabs.length > 0) {
        this.switchToRightTab(remainingTabs[0])
      } else {
        this.activeRightTabId = null
      }
    }
    
    this.mainWindow.webContents.send('right-tab:closed', tabId)
  }

  createInitialTab() {
    const workspace = this.workspaces.get(this.activeWorkspaceId)
    const homepage = workspace?.homepage || this.settings?.homepage || 'https://www.google.com'
    this.createTab(homepage)
  }

  async loadExtensions() {
    try {
      const { session } = require('electron')
      const extensionPath = path.join(__dirname, 'caijichajian')

      // 检查扩展文件夹是否存在
      if (fs.existsSync(extensionPath)) {
        // 在开发模式下加载扩展
        const extension = await session.defaultSession.loadExtension(extensionPath, {
          allowFileAccess: true
        })
        console.log('Extension loaded:', extension.name)
      } else {
        console.log('Extension folder not found:', extensionPath)
      }
    } catch (error) {
      console.error('Failed to load extension:', error)
    }
  }

  getAllTabs() {
    const tabs = {}
    for (const [id, tab] of this.tabs) {
      tabs[id] = {
        id,
        title: tab.title,
        url: tab.url,
        active: id === this.activeTabId
      }
    }
    return tabs
  }

  copyCurrentUrl() {
    if (!this.activeTabId) return
    const tab = this.tabs.get(this.activeTabId)
    if (tab) {
      require('electron').clipboard.writeText(tab.url)
    }
  }

  searchSelectedText(text) {
    if (!text) return
    const searchEngine = this.settings?.searchEngine || 'google'
    const searchUrl = this.getSearchUrl(text, searchEngine)
    this.createTab(searchUrl)
  }

  async copyImage(imageUrl) {
    if (!imageUrl) return

    try {
      const { net, clipboard, nativeImage } = require('electron')
      const request = net.request(imageUrl)

      return new Promise((resolve, reject) => {
        let data = Buffer.alloc(0)

        request.on('response', (response) => {
          response.on('data', (chunk) => {
            data = Buffer.concat([data, chunk])
          })

          response.on('end', () => {
            try {
              const image = nativeImage.createFromBuffer(data)
              clipboard.writeImage(image)
              resolve(true)
            } catch (error) {
              reject(error)
            }
          })
        })

        request.on('error', reject)
        request.end()
      })
    } catch (error) {
      console.error('Failed to copy image:', error)
      return false
    }
  }

  async saveImage(imageUrl) {
    if (!imageUrl) return

    try {
      const { dialog, net } = require('electron')
      const path = require('path')
      const fs = require('fs')

      // 获取图片文件名
      const urlPath = new URL(imageUrl).pathname
      const fileName = path.basename(urlPath) || 'image.jpg'

      const result = await dialog.showSaveDialog(this.mainWindow, {
        title: '保存图片',
        defaultPath: fileName,
        filters: [
          { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      })

      if (result.canceled) return false

      const request = net.request(imageUrl)

      return new Promise((resolve, reject) => {
        let data = Buffer.alloc(0)

        request.on('response', (response) => {
          response.on('data', (chunk) => {
            data = Buffer.concat([data, chunk])
          })

          response.on('end', () => {
            try {
              fs.writeFileSync(result.filePath, data)
              resolve(true)
            } catch (error) {
              reject(error)
            }
          })
        })

        request.on('error', reject)
        request.end()
      })
    } catch (error) {
      console.error('Failed to save image:', error)
      return false
    }
  }

  createWorkspace(name) {
    const workspaceId = `workspace_${++this.workspaceCounter}`
    this.workspaces.set(workspaceId, {
      id: workspaceId,
      name: name || `工作区 ${this.workspaceCounter}`,
      tabs: new Map(),
      activeTabId: null,
      homepage: this.settings?.homepage || 'https://www.google.com'
    })
    this.mainWindow.webContents.send('workspace:created', workspaceId, this.workspaces.get(workspaceId))
    return workspaceId
  }

  deleteWorkspace(workspaceId) {
    if (workspaceId === 'default') {
      return false // 不能删除默认工作区
    }

    if (!this.workspaces.has(workspaceId)) {
      return false
    }

    // 如果删除的是当前活跃工作区，切换到默认工作区
    if (this.activeWorkspaceId === workspaceId) {
      this.switchWorkspace('default')
    }

    // 清理工作区的所有标签页
    const workspace = this.workspaces.get(workspaceId)
    for (const [tabId, tab] of workspace.tabs) {
      this.mainWindow.removeBrowserView(tab.view)
      tab.view.webContents.destroy()
    }

    this.workspaces.delete(workspaceId)
    this.mainWindow.webContents.send('workspace:deleted', workspaceId)
    return true
  }

  updateWorkspace(workspaceId, updates) {
    if (!this.workspaces.has(workspaceId)) {
      return false
    }

    const workspace = this.workspaces.get(workspaceId)
    Object.assign(workspace, updates)

    this.mainWindow.webContents.send('workspace:updated', workspaceId, workspace)
    return true
  }

  switchWorkspace(workspaceId) {
    if (!this.workspaces.has(workspaceId)) return

    // 保存当前工作区的活跃标签页ID
    if (this.activeWorkspaceId && this.workspaces.has(this.activeWorkspaceId)) {
      const currentWorkspace = this.workspaces.get(this.activeWorkspaceId)
      currentWorkspace.activeTabId = this.activeTabId
    }

    // 隐藏当前工作区的所有标签页
    for (const [tabId, tab] of this.tabs) {
      this.mainWindow.removeBrowserView(tab.view)
    }

    this.activeWorkspaceId = workspaceId
    const workspace = this.workspaces.get(workspaceId)
    this.tabs = workspace.tabs

    // 如果工作区没有标签页，创建一个
    if (this.tabs.size === 0) {
      this.createInitialTab()
    } else {
      // 恢复工作区的最后活跃标签页，如果不存在则显示第一个标签页
      let targetTabId = workspace.activeTabId
      if (!targetTabId || !this.tabs.has(targetTabId)) {
        targetTabId = Array.from(this.tabs.keys())[0]
      }
      this.switchToTab(targetTabId)
    }

    this.mainWindow.webContents.send('workspace:switched', workspaceId, workspace)
  }

  // 添加设置窗口功能
  openSettingsWindow() {
    if (this.settingsWindow) {
      this.settingsWindow.focus()
      return
    }

    this.settingsWindow = new BrowserWindow({
      width: 600,
      height: 500,
      parent: this.mainWindow,
      modal: true,
      webPreferences: {
        preload: path.join(__dirname, 'settings-preload.js'),
        nodeIntegration: false,
        contextIsolation: true
      },
      resizable: false,
      minimizable: false,
      maximizable: false,
      title: '设置'
    })

    this.settingsWindow.loadFile('settings.html')
    
    this.settingsWindow.on('closed', () => {
      this.settingsWindow = null
    })
  }
}

const browser = new Browser()

app.whenReady().then(() => browser.createWindow())

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    browser.createWindow()
  }
})
