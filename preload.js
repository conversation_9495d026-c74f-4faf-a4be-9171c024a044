const { contextBridge, ipcRenderer } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
  // 主面板标签页管理
  createTab: (url) => ipcRenderer.invoke('tab:create', url),
  closeTab: (tabId) => ipcRenderer.invoke('tab:close', tabId),
  switchTab: (tabId) => ipcRenderer.invoke('tab:switch', tabId),
  getAllTabs: () => ipcRenderer.invoke('tabs:getAll'),
  moveTabToWorkspace: (tabId, workspaceId) => ipcRenderer.invoke('tab:moveToWorkspace', tabId, workspaceId),
  updateTabTitle: (tabId, customTitle) => ipcRenderer.invoke('tab:updateTitle', tabId, customTitle),
  toggleTabMute: (tabId) => ipcRenderer.invoke('tab:toggleMute', tabId),
  reorderTabs: () => ipcRenderer.invoke('tab:reorder'),
  
  // 主面板导航功能
  navigate: (url) => ipcRenderer.invoke('tab:navigate', url),
  goBack: () => ipcRenderer.invoke('navigation:back'),
  goForward: () => ipcRenderer.invoke('navigation:forward'),
  reload: () => ipcRenderer.invoke('navigation:reload'),
  goHome: () => ipcRenderer.invoke('navigation:home'),
  
  // 右侧面板功能
  toggleRightPanel: () => ipcRenderer.invoke('right-panel:toggle'),
  resizeRightPanel: (width) => ipcRenderer.invoke('right-panel:resize', width),
  createRightTab: (url) => ipcRenderer.invoke('right-tab:create', url),
  closeRightTab: (tabId) => ipcRenderer.invoke('right-tab:close', tabId),
  switchRightTab: (tabId) => ipcRenderer.invoke('right-tab:switch', tabId),
  navigateRight: (url) => ipcRenderer.invoke('right-tab:navigate', url),
  goRightBack: () => ipcRenderer.invoke('right-navigation:back'),
  goRightForward: () => ipcRenderer.invoke('right-navigation:forward'),
  reloadRight: () => ipcRenderer.invoke('right-navigation:reload'),
  goRightHome: () => ipcRenderer.invoke('right-navigation:home'),
  
  // 工作区功能
  createWorkspace: (name) => ipcRenderer.invoke('workspace:create', name),
  createWorkspaceSeparator: (name) => ipcRenderer.invoke('workspace:create-separator', name),
  switchWorkspace: (workspaceId) => ipcRenderer.invoke('workspace:switch', workspaceId),
  deleteWorkspace: (workspaceId) => ipcRenderer.invoke('workspace:delete', workspaceId),
  updateWorkspace: (workspaceId, updates) => ipcRenderer.invoke('workspace:update', workspaceId, updates),
  getAllWorkspaces: () => ipcRenderer.invoke('workspace:getAll'),
  getOrderedWorkspaceItems: () => ipcRenderer.invoke('workspace:get-ordered-items'),
  reorderWorkspaces: () => ipcRenderer.invoke('workspace:reorder'),
  updateSeparator: (separatorId, updates) => ipcRenderer.invoke('workspace:update-separator', separatorId, updates),
  deleteSeparator: (separatorId) => ipcRenderer.invoke('workspace:delete-separator', separatorId),
  promptWorkspaceName: () => ipcRenderer.invoke('workspace:prompt-name'),
  promptWorkspaceType: () => ipcRenderer.invoke('workspace:prompt-type'),
  promptInput: (options) => ipcRenderer.invoke('workspace:prompt-input', options),
  
  // 设置功能
  openSettings: () => ipcRenderer.invoke('settings:open'),
  getSettings: () => ipcRenderer.invoke('settings:get'),
  setSettings: (settings) => ipcRenderer.invoke('settings:set', settings),

  // 用户脚本功能
  openUserScripts: () => ipcRenderer.invoke('userscripts:open'),
  getUserScripts: () => ipcRenderer.invoke('userscripts:get'),
  saveUserScripts: (scripts) => ipcRenderer.invoke('userscripts:save', scripts),

  // 笔记功能
  openNotes: () => ipcRenderer.invoke('notes:open'),
  getNotes: () => ipcRenderer.invoke('notes:get'),
  saveNotes: (notes) => ipcRenderer.invoke('notes:save', notes),

  // 密码管理器功能
  openPasswordManager: () => ipcRenderer.invoke('passwords:open'),
  getPasswords: () => ipcRenderer.invoke('passwords:get'),
  savePasswords: (passwords) => ipcRenderer.invoke('passwords:save', passwords),

  // 窗口控制功能
  resizeWindow: (width, height) => ipcRenderer.invoke('window:resize', width, height),
  moveWindow: (x, y) => ipcRenderer.invoke('window:move', x, y),
  getScreenSize: () => ipcRenderer.invoke('window:get-screen-size'),


  
  // 右键菜单功能
  copyCurrentUrl: () => ipcRenderer.invoke('context:copy-url'),
  searchSelectedText: (text) => ipcRenderer.invoke('context:search-text', text),
  copyImage: (imageUrl) => ipcRenderer.invoke('context:copy-image', imageUrl),
  saveImage: (imageUrl) => ipcRenderer.invoke('context:save-image', imageUrl),
  
  // 事件监听
  onTabUpdated: (callback) => ipcRenderer.on('tab:updated', callback),
  onTabSwitched: (callback) => ipcRenderer.on('tab:switched', callback),
  onTabClosed: (callback) => ipcRenderer.on('tab:closed', callback),
  onTabCreated: (callback) => ipcRenderer.on('tab:created', callback),
  onTabMoved: (callback) => ipcRenderer.on('tab:moved', callback),
  onNavigationUpdated: (callback) => ipcRenderer.on('navigation:updated', callback),
  
  onRightTabUpdated: (callback) => ipcRenderer.on('right-tab:updated', callback),
  onRightTabSwitched: (callback) => ipcRenderer.on('right-tab:switched', callback),
  onRightTabClosed: (callback) => ipcRenderer.on('right-tab:closed', callback),
  onRightNavigationUpdated: (callback) => ipcRenderer.on('right-navigation:updated', callback),
  onRightPanelToggled: (callback) => ipcRenderer.on('right-panel:toggled', callback),
  onRightPanelResized: (callback) => ipcRenderer.on('right-panel:resized', callback),
  
  onWorkspaceCreated: (callback) => ipcRenderer.on('workspace:created', callback),
  onWorkspaceSeparatorCreated: (callback) => ipcRenderer.on('workspace:separator-created', callback),
  onWorkspaceSwitched: (callback) => ipcRenderer.on('workspace:switched', callback),
  onWorkspaceDeleted: (callback) => ipcRenderer.on('workspace:deleted', callback),
  onWorkspaceUpdated: (callback) => ipcRenderer.on('workspace:updated', callback),
  onLeftPanelResized: (callback) => ipcRenderer.on('left-panel:resized', callback),
  onTabSlept: (callback) => ipcRenderer.on('tab:slept', callback),
  onTabWokeUp: (callback) => ipcRenderer.on('tab:woke-up', callback),
  onRightTabSlept: (callback) => ipcRenderer.on('right-tab:slept', callback),
  onRightTabWokeUp: (callback) => ipcRenderer.on('right-tab:woke-up', callback)
})


