<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: Arial, sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h2 {
      margin: 0;
      color: #333;
      font-size: 18px;
    }
    
    .section {
      margin-bottom: 15px;
    }
    
    .section h3 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #555;
    }
    
    .stats {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      font-size: 12px;
    }
    
    .stats-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    
    .stats-item:last-child {
      margin-bottom: 0;
    }
    
    .recent-folders {
      max-height: 120px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .folder-item {
      padding: 8px 10px;
      border-bottom: 1px solid #eee;
      font-size: 12px;
      cursor: pointer;
    }
    
    .folder-item:hover {
      background: #f5f5f5;
    }
    
    .folder-item:last-child {
      border-bottom: none;
    }
    
    .actions {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      flex: 1;
      padding: 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .btn-primary {
      background: #007cba;
      color: white;
    }
    
    .btn-primary:hover {
      background: #005a87;
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #5a6268;
    }
    
    .instructions {
      font-size: 11px;
      color: #666;
      line-height: 1.4;
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #eee;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2>图片采集插件</h2>
  </div>
  
  <div class="section">
    <h3>使用统计</h3>
    <div class="stats">
      <div class="stats-item">
        <span>今日采集:</span>
        <span id="today-count">0</span>
      </div>
      <div class="stats-item">
        <span>总计采集:</span>
        <span id="total-count">0</span>
      </div>
      <div class="stats-item">
        <span>文件夹数:</span>
        <span id="folder-count">0</span>
      </div>
    </div>
  </div>
  
  <div class="section">
    <h3>最近使用的文件夹</h3>
    <div class="recent-folders" id="recent-folders">
      <!-- 最近文件夹列表 -->
    </div>
  </div>
  
  <div class="section">
    <div class="actions">
      <button class="btn btn-primary" id="settings-btn">设置</button>
      <button class="btn btn-secondary" id="clear-btn">清除历史</button>
    </div>
  </div>
  
  <div class="instructions">
    <strong>使用说明:</strong><br>
    1. 在网页上右键点击图片<br>
    2. 选择"保存图片到文件夹"<br>
    3. 在弹出界面中选择文件夹<br>
    4. 点击保存即可
  </div>

  <script src="popup.js"></script>
</body>
</html>
