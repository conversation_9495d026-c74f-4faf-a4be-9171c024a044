// 背景脚本 - 处理右键菜单和消息传递

// 创建右键菜单
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: "saveImage",
    title: "保存图片到文件夹",
    contexts: ["image"]
  });
});

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "saveImage") {
    // 向content script发送消息，传递图片URL
    chrome.tabs.sendMessage(tab.id, {
      action: "openCollection",
      imageUrl: info.srcUrl,
      pageUrl: info.pageUrl
    });
  }
});

// 处理来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "downloadImage") {
    downloadImage(request.imageUrl, request.folderPath, request.filename);
  } else if (request.action === "getFolderHistory") {
    getFolderHistory().then(history => {
      sendResponse({ history });
    });
    return true; // 保持消息通道开放
  } else if (request.action === "saveFolderHistory") {
    saveFolderHistory(request.history);
  } else if (request.action === "openSettings") {
    chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
  }
});

// 下载图片到指定文件夹
async function downloadImage(imageUrl, folderPath, filename) {
  try {
    const downloadPath = folderPath ? `${folderPath}/${filename}` : filename;
    
    chrome.downloads.download({
      url: imageUrl,
      filename: downloadPath,
      saveAs: false
    }, (downloadId) => {
      if (chrome.runtime.lastError) {
        console.error('下载失败:', chrome.runtime.lastError);
      } else {
        console.log('下载开始:', downloadId);
      }
    });
  } catch (error) {
    console.error('下载图片时出错:', error);
  }
}

// 获取文件夹历史记录
async function getFolderHistory() {
  return new Promise((resolve) => {
    chrome.storage.local.get(['folderHistory'], (result) => {
      resolve(result.folderHistory || []);
    });
  });
}

// 保存文件夹历史记录
function saveFolderHistory(history) {
  chrome.storage.local.set({ folderHistory: history });
}
