# 图片采集插件安装指南

## 快速安装

### 1. 安装插件
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择 `caiji<PERSON>jian` 文件夹
6. 插件安装完成！

### 2. 设置常用文件夹
1. 点击插件图标，在弹出窗口中点击"设置"
2. 在"添加常用文件夹"输入框中添加以下测试文件夹：

```
Images/Screenshots
Images/Screenshots/Web
Images/Screenshots/App
Images/Wallpapers
Images/Wallpapers/Nature
Images/Wallpapers/Abstract
Projects/WebDesign
Projects/WebDesign/Assets
Projects/WebDesign/Icons
Projects/MobileApp
Projects/MobileApp/UI
Collections/Nature
Collections/Nature/Landscapes
Collections/Architecture
Collections/Architecture/Modern
Collections/People
Downloads/Pictures
Downloads/Pictures/Temp
Downloads/References
Downloads/References/Design
```

### 3. 测试插件功能
1. 打开 `test.html` 文件（双击或拖拽到浏览器）
2. 在任意图片上右键点击
3. 选择"保存图片到文件夹"
4. 体验以下功能：
   - 📁 **历史文件夹列表**：快速选择常用文件夹
   - 🌳 **文件夹树导航**：
     - 点击文件夹查看子文件夹
     - 使用"返回上级"按钮导航
     - 直接点击选择任意层级文件夹
   - ✏️ **自定义文件名**：修改保存的文件名
   - 💾 **一键保存**：点击保存按钮下载图片

## 功能演示

### 文件夹树导航示例：
```
📁 根目录
├── 📁 Images
│   ├── 📁 Screenshots
│   │   ├── 📁 Web
│   │   └── 📁 App
│   └── 📁 Wallpapers
│       ├── 📁 Nature
│       └── 📁 Abstract
├── 📁 Projects
│   ├── 📁 WebDesign
│   │   ├── 📁 Assets
│   │   └── 📁 Icons
│   └── 📁 MobileApp
│       └── 📁 UI
└── 📁 Collections
    ├── 📁 Nature
    │   └── 📁 Landscapes
    ├── 📁 Architecture
    │   └── 📁 Modern
    └── 📁 People
```

### 使用流程：
1. **选择根文件夹**：在文件夹树中点击 "Images"
2. **浏览子文件夹**：查看 Screenshots 和 Wallpapers
3. **深入导航**：点击 "Screenshots" 查看 Web 和 App 子文件夹
4. **选择目标**：点击 "Web" 选择该文件夹
5. **保存图片**：点击保存按钮完成下载

## 故障排除

### 常见问题：

**Q: 插件安装后没有出现右键菜单？**
A: 刷新网页或重启浏览器，确保插件已启用。

**Q: 文件夹树显示为空？**
A: 请先在设置中添加一些文件夹路径到历史记录。

**Q: 下载的图片找不到？**
A: 图片会保存到浏览器默认下载文件夹的指定子文件夹中。

**Q: 无法创建子文件夹？**
A: 确保有足够的磁盘空间和文件夹写入权限。

### 重置插件：
如果遇到问题，可以在设置页面点击"重置设置"清除所有数据。

## 技术支持

如有问题或建议，请查看 `README.md` 文件获取更多详细信息。
