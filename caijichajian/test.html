<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片采集插件测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin: 0;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .image-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .image-card:hover {
            transform: translateY(-2px);
        }
        
        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            cursor: pointer;
        }
        
        .image-info {
            padding: 15px;
        }
        
        .image-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .image-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .demo-section h2 {
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>图片采集插件测试页面</h1>
        <p>在下面的图片上右键点击，选择"保存图片到文件夹"来测试插件功能</p>
    </div>
    
    <div class="instructions">
        <strong>使用说明：</strong>
        <ol>
            <li>确保已安装图片采集插件</li>
            <li>在任意图片上右键点击</li>
            <li>选择"保存图片到文件夹"</li>
            <li>在弹出的采集界面中选择文件夹</li>
            <li>点击保存即可</li>
        </ol>
    </div>
    
    <div class="demo-section">
        <h2>功能特性</h2>
        <ul class="feature-list">
            <li>右键菜单快速保存图片</li>
            <li>文件夹历史记录，快速选择常用文件夹</li>
            <li>文件夹树结构，显示当前文件夹的子文件夹</li>
            <li>支持手动添加文件夹或读取本地文件夹结构</li>
            <li>采集界面内置设置按钮，快速访问设置</li>
            <li>智能文件命名，支持自定义</li>
            <li>使用统计和数据管理</li>
        </ul>
    </div>
    
    <div class="image-gallery">
        <div class="image-card">
            <img src="https://picsum.photos/400/300?random=1" alt="测试图片1">
            <div class="image-info">
                <h3>风景图片</h3>
                <p>分辨率: 400x300</p>
                <p>来源: Picsum Photos</p>
            </div>
        </div>
        
        <div class="image-card">
            <img src="https://picsum.photos/400/300?random=2" alt="测试图片2">
            <div class="image-info">
                <h3>自然风光</h3>
                <p>分辨率: 400x300</p>
                <p>来源: Picsum Photos</p>
            </div>
        </div>
        
        <div class="image-card">
            <img src="https://picsum.photos/400/300?random=3" alt="测试图片3">
            <div class="image-info">
                <h3>城市建筑</h3>
                <p>分辨率: 400x300</p>
                <p>来源: Picsum Photos</p>
            </div>
        </div>
        
        <div class="image-card">
            <img src="https://picsum.photos/400/300?random=4" alt="测试图片4">
            <div class="image-info">
                <h3>抽象艺术</h3>
                <p>分辨率: 400x300</p>
                <p>来源: Picsum Photos</p>
            </div>
        </div>
        
        <div class="image-card">
            <img src="https://picsum.photos/400/300?random=5" alt="测试图片5">
            <div class="image-info">
                <h3>人物摄影</h3>
                <p>分辨率: 400x300</p>
                <p>来源: Picsum Photos</p>
            </div>
        </div>
        
        <div class="image-card">
            <img src="https://picsum.photos/400/300?random=6" alt="测试图片6">
            <div class="image-info">
                <h3>动物世界</h3>
                <p>分辨率: 400x300</p>
                <p>来源: Picsum Photos</p>
            </div>
        </div>
    </div>
    
    <div class="demo-section">
        <h2>测试建议的文件夹结构</h2>
        <p>您可以在设置中添加以下文件夹到历史记录中进行测试文件夹树功能：</p>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <div>
                <h4>📁 Images</h4>
                <ul style="margin-left: 20px;">
                    <li>Images/Screenshots</li>
                    <li>Images/Screenshots/Web</li>
                    <li>Images/Screenshots/App</li>
                    <li>Images/Wallpapers</li>
                    <li>Images/Wallpapers/Nature</li>
                    <li>Images/Wallpapers/Abstract</li>
                </ul>
            </div>
            <div>
                <h4>📁 Projects</h4>
                <ul style="margin-left: 20px;">
                    <li>Projects/WebDesign</li>
                    <li>Projects/WebDesign/Assets</li>
                    <li>Projects/WebDesign/Icons</li>
                    <li>Projects/MobileApp</li>
                    <li>Projects/MobileApp/UI</li>
                </ul>
            </div>
            <div>
                <h4>📁 Collections</h4>
                <ul style="margin-left: 20px;">
                    <li>Collections/Nature</li>
                    <li>Collections/Nature/Landscapes</li>
                    <li>Collections/Architecture</li>
                    <li>Collections/Architecture/Modern</li>
                    <li>Collections/People</li>
                </ul>
            </div>
            <div>
                <h4>📁 Downloads</h4>
                <ul style="margin-left: 20px;">
                    <li>Downloads/Pictures</li>
                    <li>Downloads/Pictures/Temp</li>
                    <li>Downloads/References</li>
                    <li>Downloads/References/Design</li>
                </ul>
            </div>
        </div>
        <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 4px; border-left: 4px solid #ffc107;">
            <strong>💡 新功能提示：</strong>
            <ul style="margin: 10px 0 0 20px;">
                <li><strong>文件夹上传：</strong>在设置中点击"读取文件夹结构"，选择本地文件夹自动读取完整结构</li>
                <li><strong>子文件夹导航：</strong>文件夹树现在显示当前文件夹的子文件夹，点击进入下级</li>
                <li><strong>快速设置：</strong>在采集界面点击⚙️按钮直接打开设置页面</li>
                <li><strong>返回导航：</strong>使用"返回上级"按钮在文件夹层级间导航</li>
            </ul>
        </div>
    </div>
</body>
</html>
