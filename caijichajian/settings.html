<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>图片采集插件 - 设置</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .container {
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #007cba;
    }
    
    .header h1 {
      margin: 0;
      color: #333;
    }
    
    .section {
      margin-bottom: 30px;
    }
    
    .section h2 {
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    
    .setting-item {
      margin-bottom: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 4px;
    }
    
    .setting-item label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #555;
    }
    
    .setting-item input,
    .setting-item select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .setting-item .description {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    
    .folder-list {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: white;
    }
    
    .folder-list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid #eee;
    }
    
    .folder-list-item:last-child {
      border-bottom: none;
    }
    
    .folder-path {
      flex: 1;
      font-family: monospace;
      font-size: 12px;
    }
    
    .folder-actions {
      display: flex;
      gap: 5px;
    }
    
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .btn-primary {
      background: #007cba;
      color: white;
    }
    
    .btn-primary:hover {
      background: #005a87;
    }
    
    .btn-danger {
      background: #dc3545;
      color: white;
    }
    
    .btn-danger:hover {
      background: #c82333;
    }
    
    .btn-small {
      padding: 4px 8px;
      font-size: 12px;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
    }
    
    .stats-card {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      text-align: center;
    }
    
    .stats-number {
      font-size: 24px;
      font-weight: bold;
      color: #007cba;
    }
    
    .stats-label {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    
    .actions {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
    
    .actions .btn {
      margin: 0 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>图片采集插件设置</h1>
    </div>
    
    <div class="section">
      <h2>基本设置</h2>
      <div class="setting-item">
        <label for="filename-format">文件名格式</label>
        <select id="filename-format">
          <option value="original">保持原文件名</option>
          <option value="timestamp">时间戳_原文件名</option>
          <option value="domain">域名_原文件名</option>
          <option value="custom">自定义格式</option>
        </select>
        <div class="description">选择保存图片时的文件名格式</div>
      </div>

      <div class="setting-item">
        <label for="enable-large-image">启用大图解析</label>
        <input type="checkbox" id="enable-large-image" style="width: auto; margin-right: 10px;">
        <span>自动解析并保存原图</span>
        <div class="description">启用后，插件会尝试根据规则解析出原图并保存</div>
      </div>
    </div>
    
    <div class="section">
      <h2>大图解析规则</h2>
      <div class="setting-item">
        <label for="large-image-rules">解析规则</label>
        <textarea id="large-image-rules" rows="10" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 12px;" placeholder="每行一个规则，格式：原图URL模式 -> 大图URL模式&#10;例如：&#10;https://example.com/thumb/{id}_thumb.jpg -> https://example.com/large/{id}_large.jpg&#10;https://cdn.site.com/images/small/{filename} -> https://cdn.site.com/images/original/{filename}&#10;&#10;支持的占位符：&#10;{id} - 图片ID&#10;{filename} - 文件名&#10;{ext} - 扩展名&#10;{domain} - 域名&#10;{path} - 路径部分"></textarea>
        <div class="description">
          <strong>规则格式：</strong>原图URL模式 -> 大图URL模式<br>
          <strong>占位符：</strong>{id}, {filename}, {ext}, {domain}, {path}<br>
          <strong>示例：</strong>https://site.com/thumb/{id}.jpg -> https://site.com/large/{id}.jpg
        </div>
      </div>

      <div class="setting-item">
        <label>测试规则</label>
        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
          <input type="text" id="test-url" placeholder="输入测试图片URL" style="flex: 1;">
          <button class="btn btn-primary" id="test-rule-btn">测试解析</button>
        </div>
        <div id="test-result" style="padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 12px; min-height: 40px;">
          测试结果将显示在这里
        </div>
      </div>
    </div>
    

    
    <div class="actions">
      <button class="btn btn-primary" id="save-settings">保存设置</button>
      <button class="btn btn-danger" id="reset-settings">重置设置</button>
      <button class="btn" id="export-data">导出数据</button>
      <button class="btn" id="import-data">导入数据</button>
    </div>
  </div>

  <script src="settings.js"></script>
</body>
</html>
