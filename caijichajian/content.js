// 内容脚本 - 处理页面交互和采集界面

// API配置
const API_PORTS = [3456, 3457, 3458, 3459]; // 尝试多个端口
let API_BASE_URL = 'http://localhost:3456/api';
let isApiConnected = false;

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "openCollection") {
    openCollectionInterface(request.imageUrl, request.pageUrl);
  }
});

// 检查API连接状态
async function checkApiConnection() {
  // 尝试多个端口
  for (const port of API_PORTS) {
    try {
      const testUrl = `http://localhost:${port}/api/health`;
      const response = await fetch(testUrl);
      if (response.ok) {
        API_BASE_URL = `http://localhost:${port}/api`;
        isApiConnected = true;
        console.log(`图片管理软件API连接成功，端口: ${port}`);
        return true;
      }
    } catch (error) {
      // 继续尝试下一个端口
      console.log(`端口 ${port} 连接失败:`, error.message);
    }
  }

  isApiConnected = false;
  console.log('所有端口都连接失败，软件可能未运行');
  return false;
}

// 获取软件中的文件夹结构
async function getFoldersFromSoftware() {
  try {
    const response = await fetch(`${API_BASE_URL}/folders`);
    if (response.ok) {
      const data = await response.json();
      return data;
    } else if (response.status === 400) {
      // 400错误通常表示未设置根文件夹
      const errorData = await response.json();
      console.log('软件提示:', errorData.error);
      return { success: false, error: errorData.error, needSetup: true };
    } else {
      throw new Error(`API请求失败: ${response.status}`);
    }
  } catch (error) {
    console.error('获取文件夹结构失败:', error);
    return null;
  }
}

// 保存图片到软件
async function saveImageToSoftware(imageUrl, folderPath, fileName, pageUrl) {
  try {
    const response = await fetch(`${API_BASE_URL}/save-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageUrl,
        folderPath,
        fileName,
        pageUrl
      })
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('保存图片失败:', error);
    return { success: false, error: error.message };
  }
}

// 打开采集界面
function openCollectionInterface(imageUrl, pageUrl) {
  // 检查是否已经存在采集界面
  const existingInterface = document.getElementById('image-collection-interface');
  if (existingInterface) {
    existingInterface.remove();
  }

  // 创建采集界面容器
  const interfaceContainer = document.createElement('div');
  interfaceContainer.id = 'image-collection-interface';
  interfaceContainer.innerHTML = `
    <div class="collection-overlay">
      <div class="collection-modal">
        <div class="collection-header">
          <h3>图片采集</h3>
          <div class="header-buttons">
            <button class="settings-btn" id="settings-btn" title="打开设置">⚙️</button>
            <button class="close-btn">&times;</button>
          </div>
        </div>
        <div class="collection-content">
          <div class="image-preview">
            <img src="${imageUrl}" alt="预览图片" />
            <div class="image-info">
              <p>来源: ${pageUrl}</p>
              <p>图片URL: ${imageUrl}</p>
            </div>
          </div>
          <div class="folder-selection">
            <div class="folder-history" style="display:none">
              <h4>历史文件夹</h4>
              <div class="history-list" id="history-list">
                <!-- 历史文件夹列表将在这里动态加载 -->
              </div>
            </div>
            <div class="folder-tree">
              <h4 style="display:none">文件夹结构</h4>
              <div class="tree-container" id="tree-container">
                <input type="text" id="folder-input" placeholder="输入文件夹路径或选择历史文件夹" />
                <button id="create-folder-btn" style="display:none">创建新文件夹</button>
                <div class="folder-tree-view" id="folder-tree-view" style="color:#5e5c5c;">
                  <!-- 文件夹树结构将在这里显示 -->
                </div>
              </div>
            </div>
            <div class="file-naming">
              <h4>文件命名</h4>
              <input type="text" id="filename-input" placeholder="文件名（可选，默认使用原文件名）" />
            </div>
          </div>
        </div>
        <div class="collection-footer">
          <button id="save-btn" class="save-btn">保存图片</button>
          <button id="cancel-btn" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>
  `;

  // 添加样式
  const style = document.createElement('style');
  style.textContent = `
    #image-collection-interface {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10000;
      font-family: Arial, sans-serif;
    }
    
    .collection-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .collection-modal {
      background: white;
      border-radius: 8px;
      width: 80%;
      max-width: 800px;
      max-height: 90%;
      overflow-y: auto;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    
    .collection-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #eee;
    }
    
    .collection-header h3 {
      margin: 0;
      color: #333;
    }

    .header-buttons {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .settings-btn {
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      color: #666;
      padding: 5px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .settings-btn:hover {
      background: #f0f0f0;
      color: #333;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }
    
    .collection-content {
      padding: 20px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    
    .image-preview img {
      max-width: 100%;
      max-height: 200px;
      object-fit: contain;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .image-info {
      margin-top: 10px;
      font-size: 12px;
      color: #666;
    }
    
    .image-info p {
      margin: 5px 0;
      word-break: break-all;
    }
    
    .folder-selection h4 {
      margin: 0 0 10px 0;
      color: #333;
    }
    
    .history-list {
      max-height: 150px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 15px;
    }
    
    .history-item {
      padding: 8px 12px;
      cursor: pointer;
      border-bottom: 1px solid #eee;
    }
    
    .history-item:hover {
      background: #f5f5f5;
    }
    
    .history-item:last-child {
      border-bottom: none;
    }
    
    .tree-container input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 10px;
    }

    .tree-container button {
      padding: 8px 16px;
      background: #007cba;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-bottom: 10px;
    }

    .tree-container button:hover {
      background: #005a87;
    }

    .folder-tree-view {
      max-height: 650px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #f9f9f9;
    }

    .tree-node {
      padding: 4px 8px;
      cursor: pointer;
      border-bottom: 1px solid #eee;
      display: flex;
      align-items: center;
    }

    .tree-node:hover {
      background: #e9ecef;
    }

    .tree-node.selected {
      background: #007cba;
      color: white;
    }

    .tree-node-icon {
      margin-right: 6px;
      font-size: 12px;
      width: 12px;
      text-align: center;
    }

    .tree-node-text {
      flex: 1;
      font-size: 13px;
    }

    .tree-node.level-0 { padding-left: 8px; }
    .tree-node.level-1 { padding-left: 24px; }
    .tree-node.level-2 { padding-left: 40px; }
    .tree-node.level-3 { padding-left: 56px; }

    .tree-node.back-button {
      background: #e3f2fd;
      border-bottom: 2px solid #2196f3;
      font-weight: bold;
    }

    .tree-node.back-button:hover {
      background: #bbdefb;
    }
    
    .file-naming input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .collection-footer {
      padding: 20px;
      border-top: 1px solid #eee;
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
    
    .save-btn {
      padding: 10px 20px;
      background: #28a745;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .save-btn:hover {
      background: #218838;
    }
    
    .cancel-btn {
      padding: 10px 20px;
      background: #6c757d;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .cancel-btn:hover {
      background: #5a6268;
    }
  `;

  document.head.appendChild(style);
  document.body.appendChild(interfaceContainer);

  // 初始化界面
  initializeInterface(imageUrl);
}

// 初始化采集界面
async function initializeInterface(imageUrl) {
  // 检查API连接
  const connected = await checkApiConnection();

  if (connected) {
    // 使用软件API加载文件夹结构
    await loadFolderTreeFromSoftware();

    // 显示连接状态
    showConnectionStatus(true);
  } else {
    // 回退到历史记录模式
    loadFolderHistory();
    loadFolderTree();

    // 显示连接状态
    showConnectionStatus(false);
  }

  // 从localStorage恢复上次使用的文件夹路径
  const lastFolderPath = localStorage.getItem('lastFolderPath');
  if (lastFolderPath) {
    document.getElementById('folder-input').value = lastFolderPath;
  }

  // 设置默认文件名
  const filename = getFilenameFromUrl(imageUrl);
  document.getElementById('filename-input').value = filename;

  // 绑定事件
  bindEvents(imageUrl);
}

// 显示连接状态
function showConnectionStatus(connected) {
  const header = document.querySelector('.collection-header h3');
  if (connected) {
    header.innerHTML = '图片采集 <span style="color: #28a745; font-size: 12px;">● 已连接到软件</span>';
  } else {
    header.innerHTML = '图片采集 <span style="color: #dc3545; font-size: 12px;">● 软件未运行</span>';
  }
}

// 从软件API加载文件夹结构
async function loadFolderTreeFromSoftware() {
  const foldersData = await getFoldersFromSoftware();

  const treeView = document.getElementById('folder-tree-view');
  const historyList = document.getElementById('history-list');
  const folderInput = document.getElementById('folder-input');

  if (!foldersData) {
    // 网络连接失败
    treeView.innerHTML = '<div class="tree-node" style="color: #dc3545; padding: 15px; text-align: center;">无法连接到软件API</div>';
    historyList.innerHTML = '<div class="history-item" style="color: #dc3545;">API连接失败</div>';
    folderInput.placeholder = '请手动输入文件夹路径';
    return;
  }

  if (foldersData.needSetup) {
    // 软件未设置根文件夹
    treeView.innerHTML = `
      <div class="tree-node" style="color: #ffc107; padding: 15px; text-align: center;">
        <div style="margin-bottom: 10px;">⚠️ 软件提示</div>
        <div style="font-size: 12px;">${foldersData.error}</div>
        <div style="font-size: 12px; margin-top: 8px;">请在软件中先选择根文件夹</div>
      </div>
    `;
    historyList.innerHTML = '<div class="history-item" style="color: #ffc107;">请在软件中设置根文件夹</div>';
    folderInput.placeholder = '请先在软件中选择根文件夹，或手动输入路径';
    return;
  }

  if (!foldersData.success) {
    // 其他错误
    treeView.innerHTML = `<div class="tree-node" style="color: #dc3545; padding: 15px; text-align: center;">获取文件夹失败: ${foldersData.error || '未知错误'}</div>`;
    historyList.innerHTML = '<div class="history-item" style="color: #dc3545;">获取文件夹失败</div>';
    folderInput.placeholder = '请手动输入文件夹路径';
    return;
  }

  // 成功获取文件夹结构
  folderInput.placeholder = `软件根目录: ${foldersData.rootPath}`;

  // 渲染文件夹树
  if (foldersData.folders && foldersData.folders.length > 0) {
    treeView.innerHTML = renderSoftwareFolderTree(foldersData.folders, foldersData.rootPath);

    // 绑定点击事件
    treeView.addEventListener('click', (e) => {
      const treeNode = e.target.closest('.tree-node');
      if (treeNode && treeNode.dataset.path) {
        const folderPath = treeNode.dataset.path;
        folderInput.value = folderPath;

        // 更新选中状态
        treeView.querySelectorAll('.tree-node').forEach(node => {
          node.classList.remove('selected');
        });
        treeNode.classList.add('selected');
      }
    });

    // 更新历史记录显示
    historyList.innerHTML = '<div class="history-item" style="color: #28a745;">使用软件文件夹结构</div>';
  } else {
    treeView.innerHTML = '<div class="tree-node" style="color: #666; padding: 15px; text-align: center;">软件中暂无文件夹</div>';
    historyList.innerHTML = '<div class="history-item" style="color: #dc3545;">软件中暂无文件夹</div>';
  }
}

// 加载文件夹历史记录
function loadFolderHistory() {
  chrome.runtime.sendMessage({ action: "getFolderHistory" }, (response) => {
    const historyList = document.getElementById('history-list');
    const history = response.history || [];

    if (history.length === 0) {
      historyList.innerHTML = '<div class="history-item">暂无历史记录</div>';
      return;
    }

    historyList.innerHTML = history.map(folder =>
      `<div class="history-item" data-folder="${folder}">${folder}</div>`
    ).join('');

    // 绑定历史文件夹点击事件
    historyList.addEventListener('click', (e) => {
      if (e.target.classList.contains('history-item')) {
        const folderPath = e.target.dataset.folder;
        document.getElementById('folder-input').value = folderPath;
        updateFolderTreeSelection(folderPath);
        // 加载该文件夹下的子文件夹
        loadFolderTree(folderPath);
      }
    });
  });
}

// 加载文件夹树结构
function loadFolderTree(currentPath = '') {
  chrome.runtime.sendMessage({ action: "getFolderHistory" }, (response) => {
    const history = response.history || [];
    const treeView = document.getElementById('folder-tree-view');

    // 获取当前路径下的子文件夹
    const subfolders = getSubfolders(history, currentPath);

    if (subfolders.length === 0) {
      treeView.innerHTML = `
        <div class="tree-node" style="color: #666; cursor: default; padding: 15px; text-align: center;">
          ${currentPath ? `"${currentPath}" 下暂无子文件夹` : '暂无文件夹结构'}
        </div>
      `;
      return;
    }

    treeView.innerHTML = renderSubfolders(subfolders, currentPath);

    // 移除之前的事件监听器，避免重复绑定
    const newTreeView = treeView.cloneNode(true);
    treeView.parentNode.replaceChild(newTreeView, treeView);

    // 绑定文件夹树点击事件
    newTreeView.addEventListener('click', (e) => {
      const treeNode = e.target.closest('.tree-node');
      if (treeNode && treeNode.dataset.path) {
        const folderPath = treeNode.dataset.path;
        document.getElementById('folder-input').value = folderPath;

        // 更新选中状态
        newTreeView.querySelectorAll('.tree-node').forEach(node => {
          node.classList.remove('selected');
        });
        treeNode.classList.add('selected');

        // 重新加载该文件夹下的子文件夹
        loadFolderTree(folderPath);
      }
    });
  });
}

// 绑定事件
function bindEvents(imageUrl) {
  // 关闭按钮
  document.querySelector('.close-btn').addEventListener('click', closeInterface);
  document.getElementById('cancel-btn').addEventListener('click', closeInterface);

  // 设置按钮
  document.getElementById('settings-btn').addEventListener('click', openSettings);

  // 点击遮罩层关闭
  document.querySelector('.collection-overlay').addEventListener('click', (e) => {
    if (e.target === e.currentTarget) {
      closeInterface();
    }
  });

  // 保存按钮
  document.getElementById('save-btn').addEventListener('click', () => {
    saveImage(imageUrl);
  });

  // 创建文件夹按钮
  document.getElementById('create-folder-btn').addEventListener('click', createNewFolder);

  // 文件夹输入框变化时更新树选中状态和加载子文件夹
  document.getElementById('folder-input').addEventListener('input', (e) => {
    const currentPath = e.target.value.trim();
    updateFolderTreeSelection(currentPath);
    // 延迟加载子文件夹，避免频繁更新
    clearTimeout(window.folderTreeTimeout);
    window.folderTreeTimeout = setTimeout(() => {
      loadFolderTree(currentPath);
    }, 500);
  });
}

// 关闭界面
function closeInterface() {
  const interfaceElement = document.getElementById('image-collection-interface');
  if (interfaceElement) {
    interfaceElement.remove();
  }
}

// 打开设置页面
function openSettings() {
  chrome.runtime.sendMessage({ action: "openSettings" });
}

// 保存图片
async function saveImage(imageUrl) {
  const folderPath = document.getElementById('folder-input').value.trim();
  const customFilename = document.getElementById('filename-input').value.trim();
  const filename = customFilename || getFilenameFromUrl(imageUrl);
  const pageUrl = window.location.href;

  if (!folderPath) {
    提示('请选择保存文件夹');
    return;
  }

  // 显示保存状态
  const saveBtn = document.getElementById('save-btn');
  const originalText = saveBtn.textContent;
  saveBtn.textContent = '保存中...';
  saveBtn.disabled = true;

  try {
    // 保存文件夹路径到localStorage
    if (folderPath) {
      localStorage.setItem('lastFolderPath', folderPath);
    }

    // 尝试解析大图
    let finalImageUrl = imageUrl;
    const largeImageUrl = await tryParseLargeImage(imageUrl);
    if (largeImageUrl && largeImageUrl !== imageUrl) {
      finalImageUrl = largeImageUrl;
      console.log('解析到大图URL:', finalImageUrl);
    }

    if (isApiConnected) {
      // 使用软件API保存
      const result = await saveImageToSoftware(finalImageUrl, folderPath, filename, pageUrl);

      if (result.success) {
        提示(`图片保存成功！\n文件路径: ${result.filePath}`);
      } else {
        提示(`保存失败: ${result.error}`);
        return;
      }
    } else {
      // 回退到浏览器下载
      chrome.runtime.sendMessage({
        action: "downloadImage",
        imageUrl: finalImageUrl,
        folderPath: folderPath,
        filename: filename
      });

      // 保存到历史记录
      if (folderPath) {
        saveFolderToHistory(folderPath);
      }
    }

    // 关闭界面
    closeInterface();

  } catch (error) {
    console.error('保存图片时出错:', error);
    提示('保存图片时出错: ' + error.message);
  } finally {
    // 恢复按钮状态
    saveBtn.textContent = originalText;
    saveBtn.disabled = false;
  }
}

// 创建新文件夹
function createNewFolder() {
  const folderName = prompt('请输入新文件夹名称:');
  if (folderName) {
    const currentPath = document.getElementById('folder-input').value.trim();
    const newPath = currentPath ? `${currentPath}/${folderName}` : folderName;
    document.getElementById('folder-input').value = newPath;
  }
}

// 保存文件夹到历史记录
function saveFolderToHistory(folderPath) {
  chrome.runtime.sendMessage({ action: "getFolderHistory" }, (response) => {
    let history = response.history || [];
    
    // 移除重复项
    history = history.filter(item => item !== folderPath);
    
    // 添加到开头
    history.unshift(folderPath);
    
    // 限制历史记录数量
    if (history.length > 10) {
      history = history.slice(0, 10);
    }
    
    // 保存更新后的历史记录
    chrome.runtime.sendMessage({
      action: "saveFolderHistory",
      history: history
    });
  });
}

// 获取指定路径下的子文件夹
function getSubfolders(folderPaths, currentPath) {
  const subfolders = new Set();

  folderPaths.forEach(path => {
    if (!path) return;

    if (currentPath === '') {
      // 如果当前路径为空，显示所有根级文件夹
      const firstPart = path.split('/')[0];
      if (firstPart) {
        subfolders.add(firstPart);
      }
    } else {
      // 如果当前路径不为空，查找子文件夹
      if (path.startsWith(currentPath + '/')) {
        const remainingPath = path.substring(currentPath.length + 1);
        const nextPart = remainingPath.split('/')[0];
        if (nextPart) {
          subfolders.add(currentPath + '/' + nextPart);
        }
      }
    }
  });

  return Array.from(subfolders).sort();
}

// 渲染子文件夹列表
function renderSubfolders(subfolders, currentPath) {
  let html = '';

  // 如果不是根目录，添加返回上级按钮
  if (currentPath) {
    const parentPath = currentPath.split('/').slice(0, -1).join('/');
    html += `
      <div class="tree-node back-button" data-path="${parentPath}">
        <span class="tree-node-icon">⬆️</span>
        <span class="tree-node-text">返回上级 (${parentPath || '根目录'})</span>
      </div>
    `;
  }

  // 添加子文件夹
  subfolders.forEach(folderPath => {
    const folderName = folderPath.split('/').pop();
    html += `
      <div class="tree-node" data-path="${folderPath}">
        <span class="tree-node-icon">📁</span>
        <span class="tree-node-text">${folderName}</span>
      </div>
    `;
  });

  return html;
}

// 构建文件夹树结构（保留原函数以备后用）
function buildFolderTree(folderPaths) {
  const tree = {};

  folderPaths.forEach(path => {
    if (!path) return;

    const parts = path.split('/').filter(part => part.trim());
    let current = tree;

    parts.forEach((part, index) => {
      if (!current[part]) {
        current[part] = {
          name: part,
          path: parts.slice(0, index + 1).join('/'),
          children: {},
          level: index
        };
      }
      current = current[part].children;
    });
  });

  return flattenTree(tree, 0);
}

// 将树结构扁平化为数组
function flattenTree(tree, level) {
  const result = [];

  Object.keys(tree).sort().forEach(key => {
    const node = tree[key];
    result.push({
      name: node.name,
      path: node.path,
      level: level,
      hasChildren: Object.keys(node.children).length > 0
    });

    // 递归添加子节点
    result.push(...flattenTree(node.children, level + 1));
  });

  return result;
}

// 渲染文件夹树
function renderFolderTree(treeNodes) {
  return treeNodes.map(node => {
    const icon = node.hasChildren ? '📁' : '📂';
    return `
      <div class="tree-node level-${node.level}" data-path="${node.path}">
        <span class="tree-node-icon">${icon}</span>
        <span class="tree-node-text">${node.name}</span>
      </div>
    `;
  }).join('');
}

// 渲染软件文件夹树
function renderSoftwareFolderTree(folders, rootPath, level = 0) {
  let html = '';

  folders.forEach(folder => {
    const hasChildren = folder.children && folder.children.length > 0;
    const icon = hasChildren ? '📁' : '📂';

    html += `
      <div class="tree-node level-${level}" data-path="${folder.path}">
        <span class="tree-node-icon">${icon}</span>
        <span class="tree-node-text">${folder.name}</span>
      </div>
    `;

    // 递归渲染子文件夹
    if (hasChildren && level < 2) { // 限制显示层级
      html += renderSoftwareFolderTree(folder.children, rootPath, level + 1);
    }
  });

  return html;
}

// 更新文件夹树选中状态
function updateFolderTreeSelection(folderPath) {
  const treeView = document.getElementById('folder-tree-view');
  if (!treeView) return;

  treeView.querySelectorAll('.tree-node').forEach(node => {
    node.classList.remove('selected');
    if (node.dataset.path === folderPath) {
      node.classList.add('selected');
    }
  });
}

// 从URL获取文件名
function getFilenameFromUrl(url) {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop();//.pop是取出最后一个元素

    // 如果没有扩展名，添加.jpg
     if (filename.includes('.png')) {
      return filename + '.png';
    }else if (filename.includes('.jpg')) {
      return filename + '.jpg';
    }else if (filename.includes('.jpeg')) {
      return filename + '.jpeg';
    }else if (filename.includes('.gif')) {
      return filename + '.gif';
    }else if (filename.includes('webp')) {
      return filename + '.webp';
    }else if (filename.includes('.svg')) {
      return filename + '.svg';
    }else if (!filename.includes('.')) {
      return filename + '.png';
    }

    return filename || 'image.png';
  } catch (error) {
    return 'image.png';
  }
}
function 提示(message) {
  var 提示信息 = document.createElement('div')
  提示信息.id = '提示信息';
    document.body.appendChild(提示信息);
    提示信息.style.position = 'fixed';
    提示信息.style.zIndex = '9999';
    提示信息.style.top = '50%';
    提示信息.style.left = '50%';
    提示信息.style.transform = 'translate(-50%, -50%)';
    提示信息.style.padding = '20px';
    提示信息.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    提示信息.style.color = 'white';
    提示信息.style.borderRadius = '10px';
    提示信息.style.fontSize = '18px';
    提示信息.innerHTML = message
    提示信息.style.display = 'block  ';
    setTimeout(function() {
        提示信息.style.display = 'none';
    }, 2000);
}

// 尝试解析大图
async function tryParseLargeImage(originalUrl) {
  try {
    // 从存储中获取设置
    const result = await new Promise((resolve) => {
      chrome.storage.local.get(['settings'], resolve);
    });

    const settings = result.settings || {};

    // 检查是否启用大图解析
    if (!settings.enableLargeImage || !settings.largeImageRules) {
      return originalUrl;
    }

    return parseLargeImageUrl(originalUrl, settings.largeImageRules);
  } catch (error) {
    console.error('大图解析失败:', error);
    return originalUrl;
  }
}

// 解析大图URL
function parseLargeImageUrl(originalUrl, rules) {
  const ruleLines = rules.split('\n').filter(line => line.trim() && !line.trim().startsWith('#'));

  for (const rule of ruleLines) {
    const parts = rule.split('->').map(part => part.trim());
    if (parts.length !== 2) continue;

    const [pattern, replacement] = parts;

    try {
      // 将模式转换为正则表达式
      const regexPattern = pattern
        .replace(/\{id\}/g, '([^/]+)')
        .replace(/\{filename\}/g, '([^/]+)')
        .replace(/\{ext\}/g, '([^.]+)')
        .replace(/\{domain\}/g, '([^/]+)')
        .replace(/\{path\}/g, '(.+)')
        .replace(/\./g, '\\.')
        .replace(/\//g, '\\/');

      const regex = new RegExp('^' + regexPattern + '$');
      const match = originalUrl.match(regex);

      if (match) {
        // 替换占位符
        let result = replacement;
        const placeholders = pattern.match(/\{[^}]+\}/g) || [];

        placeholders.forEach((placeholder, index) => {
          const value = match[index + 1];
          result = result.replace(new RegExp('\\' + placeholder, 'g'), value);
        });

        return result;
      }
    } catch (error) {
      console.error('规则解析错误:', rule, error);
    }
  }

  return originalUrl; // 如果没有匹配的规则，返回原URL
}
