// 设置页面脚本

document.addEventListener('DOMContentLoaded', function() {
  loadSettings();
  bindEvents();
});

// 加载设置
function loadSettings() {
  chrome.storage.local.get(['settings'], (result) => {
    const settings = result.settings || {
      filenameFormat: 'original',
      enableLargeImage: false,
      largeImageRules: ''
    };

    document.getElementById('filename-format').value = settings.filenameFormat;
    document.getElementById('enable-large-image').checked = settings.enableLargeImage;
    document.getElementById('large-image-rules').value = settings.largeImageRules || '';
  });
}



// 绑定事件
function bindEvents() {
  // 保存设置
  document.getElementById('save-settings').addEventListener('click', saveSettings);

  // 重置设置
  document.getElementById('reset-settings').addEventListener('click', resetSettings);

  // 导出数据
  document.getElementById('export-data').addEventListener('click', exportData);

  // 导入数据
  document.getElementById('import-data').addEventListener('click', importData);

  // 测试规则
  document.getElementById('test-rule-btn').addEventListener('click', testLargeImageRule);
}

// 保存设置
function saveSettings() {
  const settings = {
    filenameFormat: document.getElementById('filename-format').value,
    enableLargeImage: document.getElementById('enable-large-image').checked,
    largeImageRules: document.getElementById('large-image-rules').value
  };

  chrome.storage.local.set({ settings }, () => {
    alert('设置已保存');
  });
}

// 重置设置
function resetSettings() {
  if (confirm('确定要重置所有设置吗？这将清除所有数据！')) {
    chrome.storage.local.clear(() => {
      alert('设置已重置');
      location.reload();
    });
  }
}

// 导出数据
function exportData() {
  chrome.storage.local.get(null, (data) => {
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      data: data
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `image-collector-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
  });
}

// 导入数据
function importData() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  
  input.onchange = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importData = JSON.parse(e.target.result);
        
        if (importData.version && importData.data) {
          if (confirm('确定要导入数据吗？这将覆盖当前所有设置！')) {
            chrome.storage.local.set(importData.data, () => {
              alert('数据导入成功');
              location.reload();
            });
          }
        } else {
          alert('无效的备份文件格式');
        }
      } catch (error) {
        alert('文件解析失败：' + error.message);
      }
    };
    
    reader.readAsText(file);
  };
  
  input.click();
}

// 测试大图解析规则
function testLargeImageRule() {
  const testUrl = document.getElementById('test-url').value.trim();
  const rules = document.getElementById('large-image-rules').value.trim();
  const resultDiv = document.getElementById('test-result');

  if (!testUrl) {
    resultDiv.innerHTML = '<span style="color: #dc3545;">请输入测试URL</span>';
    return;
  }

  if (!rules) {
    resultDiv.innerHTML = '<span style="color: #dc3545;">请先设置解析规则</span>';
    return;
  }

  try {
    const largeImageUrl = parseLargeImageUrl(testUrl, rules);

    if (largeImageUrl && largeImageUrl !== testUrl) {
      resultDiv.innerHTML = `
        <div style="color: #28a745; margin-bottom: 10px;"><strong>✓ 解析成功</strong></div>
        <div><strong>原图URL:</strong></div>
        <div style="word-break: break-all; margin-bottom: 10px; color: #666;">${testUrl}</div>
        <div><strong>大图URL:</strong></div>
        <div style="word-break: break-all; color: #007cba;">${largeImageUrl}</div>
      `;
    } else {
      resultDiv.innerHTML = '<span style="color: #ffc107;">⚠ 未找到匹配的规则或解析结果与原URL相同</span>';
    }
  } catch (error) {
    resultDiv.innerHTML = `<span style="color: #dc3545;">✗ 解析失败: ${error.message}</span>`;
  }
}

// 解析大图URL
function parseLargeImageUrl(originalUrl, rules) {
  const ruleLines = rules.split('\n').filter(line => line.trim() && !line.trim().startsWith('#'));

  for (const rule of ruleLines) {
    const parts = rule.split('->').map(part => part.trim());
    if (parts.length !== 2) continue;

    const [pattern, replacement] = parts;

    try {
      // 将模式转换为正则表达式
      const regexPattern = pattern
        .replace(/\{id\}/g, '([^/]+)')
        .replace(/\{filename\}/g, '([^/]+)')
        .replace(/\{ext\}/g, '([^.]+)')
        .replace(/\{domain\}/g, '([^/]+)')
        .replace(/\{path\}/g, '(.+)')
        .replace(/\./g, '\\.')
        .replace(/\//g, '\\/');

      const regex = new RegExp('^' + regexPattern + '$');
      const match = originalUrl.match(regex);

      if (match) {
        // 替换占位符
        let result = replacement;
        const placeholders = pattern.match(/\{[^}]+\}/g) || [];

        placeholders.forEach((placeholder, index) => {
          const value = match[index + 1];
          result = result.replace(new RegExp('\\' + placeholder, 'g'), value);
        });

        return result;
      }
    } catch (error) {
      console.error('规则解析错误:', rule, error);
    }
  }

  return originalUrl; // 如果没有匹配的规则，返回原URL
}
