# 图片采集Chrome插件

一个功能强大的Chrome浏览器插件，可以让您轻松地右键保存网页图片到指定文件夹，并提供文件夹历史记录和管理功能。

## 功能特性

- 🖱️ **右键菜单保存**: 在任意网页图片上右键，选择"保存图片到文件夹"
- 📁 **文件夹历史**: 自动记录最近使用的文件夹，支持手动添加或读取本地文件夹结构
- 🌳 **文件夹树**: 显示当前文件夹的子文件夹，支持点击导航和层级浏览
- 📂 **文件夹上传**: 选择本地文件夹自动读取完整的子文件夹结构
- 🎯 **智能命名**: 支持多种文件命名格式
- 📊 **使用统计**: 跟踪下载数量和使用情况
- ⚙️ **快速设置**: 采集界面内置设置按钮，一键访问设置页面

## 安装方法

### 方法一：开发者模式安装（推荐）

1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `caijichajian` 文件夹
6. 插件安装完成

### 方法二：打包安装

1. 在扩展程序页面点击"打包扩展程序"
2. 选择 `caijichajian` 文件夹
3. 生成 `.crx` 文件
4. 拖拽 `.crx` 文件到扩展程序页面安装

## 使用方法

### 基本使用

1. **保存图片**:
   - 在任意网页上右键点击图片
   - 选择"保存图片到文件夹"
   - 在弹出的采集界面中选择文件夹
   - 点击"保存图片"

2. **选择文件夹**:
   - 从历史文件夹列表中快速选择
   - 在文件夹树中点击选择子文件夹
   - 在文件夹输入框中手动输入路径
   - 点击"创建新文件夹"按钮创建子文件夹

3. **自定义文件名**:
   - 在文件命名输入框中输入自定义文件名
   - 留空则使用原文件名

### 高级功能

1. **查看统计**:
   - 点击插件图标查看使用统计
   - 查看今日采集数量和总计数量
   - 查看最近使用的文件夹

2. **设置管理**:
   - 在弹出窗口或采集界面中点击"设置"按钮
   - 配置文件命名格式和历史记录数量
   - **手动添加**：输入文件夹路径手动添加到历史记录
   - **读取结构**：选择本地文件夹自动读取完整的子文件夹结构
   - 管理和删除文件夹历史记录
   - 导出/导入设置数据

## 文件结构

```
caijichajian/
├── manifest.json          # 插件配置文件
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── popup.html            # 弹出窗口HTML
├── popup.js              # 弹出窗口脚本
├── settings.html         # 设置页面HTML
├── settings.js           # 设置页面脚本
├── test.html             # 测试页面
└── README.md             # 说明文档
```

## 技术特性

- **Manifest V3**: 使用最新的Chrome扩展API
- **现代化UI**: 响应式设计，美观易用
- **数据持久化**: 使用Chrome存储API保存设置和历史
- **安全下载**: 使用Chrome下载API安全下载文件
- **跨域支持**: 支持所有网站的图片采集

## 权限说明

- `contextMenus`: 创建右键菜单
- `activeTab`: 访问当前标签页
- `storage`: 保存设置和历史记录
- `downloads`: 下载图片文件
- `<all_urls>`: 在所有网站上工作

## 测试插件

安装插件后，您可以：

1. **使用测试页面**: 打开 `test.html` 文件，在测试图片上右键测试插件功能
2. **添加测试文件夹**: 在设置中添加以下文件夹进行测试：
   - `Images/Screenshots`
   - `Images/Wallpapers`
   - `Downloads/Pictures`
   - `Projects/WebDesign/Assets`
   - `Collections/Nature`
   - `Collections/Architecture`

## 浏览器兼容性

- Chrome 88+
- Edge 88+
- 其他基于Chromium的浏览器

## 更新日志

### v1.1.0
- 🆕 添加文件夹结构读取功能
- 🆕 采集界面内置设置按钮
- 🔄 文件夹树现在显示子文件夹而非父级结构
- 🗑️ 移除未使用的默认保存文件夹设置
- 🎨 优化用户界面和交互体验

### v1.0.0
- 初始版本发布
- 基本的图片保存功能
- 文件夹历史记录
- 设置管理界面
- 使用统计功能

## 常见问题

**Q: 为什么有些图片无法保存？**
A: 可能是由于图片的跨域限制或网站的安全策略。插件会尽力处理这些情况。

**Q: 文件夹路径应该如何填写？**
A: 使用相对于下载文件夹的路径，例如 `Images/Screenshots` 会保存到下载文件夹下的 Images/Screenshots 目录。

**Q: 如何备份我的设置？**
A: 在设置页面点击"导出数据"按钮，可以导出包含所有设置和历史记录的JSON文件。

## 开发说明

如果您想要修改或扩展这个插件：

1. 修改相应的文件
2. 在Chrome扩展程序页面点击"重新加载"
3. 测试修改后的功能

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个插件！
