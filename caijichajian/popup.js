// 弹出窗口脚本

document.addEventListener('DOMContentLoaded', function() {
  loadPopupData();
  bindEvents();
});

// 加载弹出窗口数据
function loadPopupData() {
  // 加载统计数据
  loadStats();
  
  // 加载最近使用的文件夹
  loadRecentFolders();
}

// 加载统计数据
function loadStats() {
  chrome.storage.local.get(['stats'], (result) => {
    const stats = result.stats || {
      todayCount: 0,
      totalCount: 0,
      lastDate: new Date().toDateString()
    };
    
    // 检查是否是新的一天
    const today = new Date().toDateString();
    if (stats.lastDate !== today) {
      stats.todayCount = 0;
      stats.lastDate = today;
      chrome.storage.local.set({ stats });
    }
    
    document.getElementById('today-count').textContent = stats.todayCount;
    document.getElementById('total-count').textContent = stats.totalCount;
  });
  
  // 加载文件夹数量
  chrome.storage.local.get(['folderHistory'], (result) => {
    const folderCount = (result.folderHistory || []).length;
    document.getElementById('folder-count').textContent = folderCount;
  });
}

// 加载最近使用的文件夹
function loadRecentFolders() {
  chrome.storage.local.get(['folderHistory'], (result) => {
    const folders = result.folderHistory || [];
    const container = document.getElementById('recent-folders');
    
    if (folders.length === 0) {
      container.innerHTML = '<div class="folder-item">暂无历史记录</div>';
      return;
    }
    
    container.innerHTML = folders.slice(0, 5).map(folder => 
      `<div class="folder-item" title="${folder}">${truncateText(folder, 30)}</div>`
    ).join('');
  });
}

// 绑定事件
function bindEvents() {
  // 设置按钮
  document.getElementById('settings-btn').addEventListener('click', () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
  });
  
  // 清除历史按钮
  document.getElementById('clear-btn').addEventListener('click', () => {
    if (confirm('确定要清除所有历史记录吗？')) {
      clearHistory();
    }
  });
}

// 清除历史记录
function clearHistory() {
  chrome.storage.local.remove(['folderHistory', 'stats'], () => {
    // 重新加载数据
    loadPopupData();
    alert('历史记录已清除');
  });
}

// 截断文本
function truncateText(text, maxLength) {
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength - 3) + '...';
}

// 监听存储变化，实时更新统计
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local') {
    if (changes.stats || changes.folderHistory) {
      loadPopupData();
    }
  }
});
