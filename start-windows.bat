@echo off
REM Windows启动脚本 - 双击启动Electron浏览器
REM 设置窗口标题
title Electron Browser Launcher

REM 获取当前脚本所在目录
cd /d "%~dp0"

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未检测到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查npm是否可用
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: npm不可用，请检查Node.js安装
    pause
    exit /b 1
)

REM 检查package.json是否存在
if not exist "package.json" (
    echo 错误: 未找到package.json文件，请确保在正确的目录中运行
    pause
    exit /b 1
)

REM 检查node_modules是否存在，如果不存在则安装依赖
if not exist "node_modules" (
    echo 正在安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
    echo 依赖包安装完成
)

REM 启动应用
echo 正在启动Electron浏览器...
npm start

REM 如果应用异常退出，显示错误信息
if %errorlevel% neq 0 (
    echo 应用启动失败，错误代码: %errorlevel%
    pause
)
