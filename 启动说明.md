# Electron浏览器启动说明

## 📋 概述
本项目提供了多个平台的启动脚本，让您可以通过双击文件的方式快速启动Electron浏览器，无需手动输入命令。

## 🚀 快速启动

### Windows系统
**文件**: `start-windows.bat`
- 双击 `start-windows.bat` 文件即可启动
- 脚本会自动检查Node.js环境
- 如果缺少依赖包，会自动安装
- 启动失败时会显示错误信息

### macOS系统
**文件**: `start-macos.command`
- 双击 `start-macos.command` 文件即可启动
- 首次使用可能需要在"系统偏好设置 > 安全性与隐私"中允许运行
- 脚本会自动检查Node.js环境
- 如果缺少依赖包，会自动安装

### Linux/Unix系统
**文件**: `start.sh`
- 双击 `start.sh` 文件或在终端中运行 `./start.sh`
- 脚本会自动检查Node.js环境
- 如果缺少依赖包，会自动安装
- 支持彩色输出，更友好的用户界面

## 📋 系统要求

### 必需软件
- **Node.js** (版本 14.0 或更高)
- **npm** (通常随Node.js一起安装)

### 安装Node.js

#### Windows
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载并安装LTS版本
3. 安装完成后重启计算机

#### macOS
**方法1: 官网下载**
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载并安装LTS版本

**方法2: 使用Homebrew**
```bash
brew install node
```

#### Linux
**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install nodejs npm
```

**CentOS/RHEL:**
```bash
sudo yum install nodejs npm
```

**Arch Linux:**
```bash
sudo pacman -S nodejs npm
```

## 🔧 手动启动方法
如果启动脚本无法使用，您也可以手动启动：

1. 打开终端/命令提示符
2. 导航到项目目录
3. 安装依赖（首次运行）：
   ```bash
   npm install
   ```
4. 启动应用：
   ```bash
   npm start
   ```

## 🛠️ 故障排除

### 常见问题

#### 1. "未检测到Node.js"
**解决方案**: 安装Node.js或确保Node.js已添加到系统PATH环境变量中

#### 2. "依赖包安装失败"
**解决方案**: 
- 检查网络连接
- 尝试使用国内镜像：`npm config set registry https://registry.npmmirror.com`
- 清除npm缓存：`npm cache clean --force`

#### 3. macOS "无法打开，因为它来自身份不明的开发者"
**解决方案**:
1. 右键点击文件，选择"打开"
2. 或在"系统偏好设置 > 安全性与隐私"中允许运行

#### 4. Linux权限问题
**解决方案**:
```bash
chmod +x start.sh
```

### 获取帮助
如果遇到其他问题，请检查：
1. Node.js版本是否符合要求
2. 网络连接是否正常
3. 磁盘空间是否充足
4. 防火墙/杀毒软件是否阻止了应用运行

## 📝 注意事项
- 首次启动可能需要较长时间来下载依赖包
- 确保有稳定的网络连接
- 建议定期更新Node.js到最新LTS版本
- 如果修改了代码，重启应用即可看到更改

## 🎯 应用特性
- 现代化的浏览器界面
- 多标签页支持
- 工作区管理
- 笔记功能
- 密码管理
- 用户脚本支持
- 自定义设置

祝您使用愉快！ 🎉
