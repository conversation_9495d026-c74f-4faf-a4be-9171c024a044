<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>设置</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .settings-container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-width: 600px;
      margin: 0 auto;
    }
    
    h2 {
      margin-top: 0;
      margin-bottom: 20px;
      text-align: center;
      color: #333;
    }
    
    .setting-item {
      margin-bottom: 20px;
    }
    
    .setting-item label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
      color: #555;
    }
    
    .setting-item input,
    .setting-item select {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .setting-item input:focus,
    .setting-item select:focus {
      outline: none;
      border-color: #007acc;
    }
    
    .custom-engines {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      background: #f9f9f9;
    }
    
    .engine-item {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
      align-items: center;
    }
    
    .engine-item input {
      flex: 1;
      margin-bottom: 0;
    }
    
    .engine-item button {
      padding: 5px 10px;
      border: none;
      border-radius: 4px;
      background: #ff4444;
      color: white;
      cursor: pointer;
    }
    
    .add-engine-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      background: #007acc;
      color: white;
      cursor: pointer;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 30px;
    }
    
    button {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: opacity 0.2s;
    }
    
    .btn-primary {
      background: #007acc;
      color: white;
    }
    
    .btn-secondary {
      background: #f0f0f0;
      color: #333;
    }
    
    button:hover {
      opacity: 0.8;
    }
  </style>
</head>
<body>
  <div class="settings-container">
    <h2>浏览器设置</h2>
    
    <div class="setting-item">
      <label for="homepage">主页地址:</label>
      <input type="text" id="homepage" placeholder="https://www.google.com">
    </div>
    
    <div class="setting-item">
      <label for="searchEngine">搜索引擎:</label>
      <select id="searchEngine">
        <option value="google">Google</option>
        <option value="bing">Bing</option>
        <option value="baidu">百度</option>
      </select>
    </div>
    
    <div class="setting-item">
      <label>自定义搜索引擎:</label>
      <div class="custom-engines">
        <div id="custom-engines-list"></div>
        <button type="button" class="add-engine-btn" onclick="addCustomEngine()">添加搜索引擎</button>
      </div>
    </div>
    
    <div class="setting-item" style="display: none;">
      <label for="leftPanelWidth">左侧面板宽度 (px):</label>
      <input type="number" id="leftPanelWidth" min="50" max="200" value="70">
    </div>

    <div class="setting-item">
      <label for="tabSleepTime">标签休眠时间 (分钟):</label>
      <input type="number" id="tabSleepTime" min="1" max="120" value="20" title="标签页在非活跃状态下多长时间后进入休眠模式">
    </div>

    <div class="setting-item">
      <label for="tabWidth">标签宽度 (px):</label>
      <input type="number" id="tabWidth" min="80" max="300" value="150" title="每个标签页的宽度">
    </div>
    
    <div class="button-group">
      <button type="button" class="btn-secondary" onclick="closeWindow()">取消</button>
      <button type="button" class="btn-primary" onclick="saveSettings()">保存</button>
    </div>
  </div>

  <script>
    let customEngines = []
    let currentSettings = {}
    
    // 加载设置
    async function loadSettings() {
      try {
        currentSettings = await window.electronAPI.getSettings()
        
        document.getElementById('homepage').value = currentSettings.homepage || 'https://www.google.com'
        document.getElementById('leftPanelWidth').value = currentSettings.leftPanelWidth || 70
        document.getElementById('tabSleepTime').value = currentSettings.tabSleepTime || 20
        document.getElementById('tabWidth').value = currentSettings.tabWidth || 150
        
        customEngines = currentSettings.customSearchEngines || []
        renderCustomEngines()
        
        // 设置搜索引擎选择，确保在自定义引擎渲染后执行
        setTimeout(() => {
          const searchEngineSelect = document.getElementById('searchEngine')
          const savedEngine = currentSettings.searchEngine || 'google'
          
          // 检查选项是否存在
          const option = searchEngineSelect.querySelector(`option[value="${savedEngine}"]`)
          if (option) {
            searchEngineSelect.value = savedEngine
          } else {
            searchEngineSelect.value = 'google'
          }
        }, 100)
        
      } catch (error) {
        console.error('Failed to load settings:', error)
      }
    }
    
    function renderCustomEngines() {
      const container = document.getElementById('custom-engines-list')
      container.innerHTML = ''
      
      customEngines.forEach((engine, index) => {
        const engineDiv = document.createElement('div')
        engineDiv.className = 'engine-item'
        engineDiv.innerHTML = `
          <input type="text" placeholder="引擎名称" value="${engine.name || ''}" onchange="updateEngine(${index}, 'name', this.value)">
          <input type="text" placeholder="搜索URL (使用%s作为查询占位符)" value="${engine.url || ''}" onchange="updateEngine(${index}, 'url', this.value)">
          <button onclick="removeEngine(${index})">删除</button>
        `
        container.appendChild(engineDiv)
      })
      
      updateSearchEngineOptions()
    }
    
    function updateSearchEngineOptions() {
      const select = document.getElementById('searchEngine')
      const currentValue = select.value
      
      // 移除自定义选项
      const options = select.querySelectorAll('option')
      options.forEach(option => {
        if (option.dataset.custom) {
          option.remove()
        }
      })
      
      // 添加自定义选项
      customEngines.forEach(engine => {
        if (engine.name && engine.url) {
          const option = document.createElement('option')
          option.value = engine.id
          option.textContent = engine.name
          option.dataset.custom = 'true'
          select.appendChild(option)
        }
      })
      
      // 恢复选中状态
      if (currentValue) {
        select.value = currentValue
      }
    }
    
    function addCustomEngine() {
      const id = 'custom_' + Date.now()
      customEngines.push({
        id: id,
        name: '',
        url: ''
      })
      renderCustomEngines()
    }
    
    function updateEngine(index, field, value) {
      if (customEngines[index]) {
        customEngines[index][field] = value
        if (field === 'name') {
          updateSearchEngineOptions()
        }
      }
    }
    
    function removeEngine(index) {
      customEngines.splice(index, 1)
      renderCustomEngines()
    }
    
    // 保存设置
    async function saveSettings() {
      try {
        const settings = {
          homepage: document.getElementById('homepage').value,
          searchEngine: document.getElementById('searchEngine').value,
          leftPanelWidth: parseInt(document.getElementById('leftPanelWidth').value),
          tabSleepTime: parseInt(document.getElementById('tabSleepTime').value),
          tabWidth: parseInt(document.getElementById('tabWidth').value),
          customSearchEngines: customEngines.filter(engine => engine.name && engine.url)
        }
        
        await window.electronAPI.saveSettings(settings)
        alert('设置已保存')
        closeWindow()
      } catch (error) {
        console.error('Failed to save settings:', error)
        alert('保存设置失败')
      }
    }
    
    // 关闭窗口
    function closeWindow() {
      window.close()
    }
    
    // 页面加载时加载设置
    document.addEventListener('DOMContentLoaded', loadSettings)
  </script>
</body>
</html>
