<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地文件测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .success-message {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.2em;
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            background: rgba(255, 255, 255, 0.1);
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        
        .feature-list li::before {
            content: "✅ ";
            margin-right: 10px;
        }
        
        .test-info {
            background: rgba(33, 150, 243, 0.3);
            border: 2px solid #2196F3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .path-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 10px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 本地文件加载成功！</h1>
        
        <div class="success-message">
            <span class="emoji">🚀</span>
            恭喜！您的Electron浏览器现在可以正确加载本地文件了！
        </div>
        
        <div class="info-box">
            <h2>📋 测试信息</h2>
            <p><strong>文件路径：</strong></p>
            <div class="path-display" id="file-path">正在获取文件路径...</div>
            <p><strong>加载时间：</strong> <span id="load-time"></span></p>
            <p><strong>浏览器：</strong> Electron浏览器</p>
        </div>
        
        <div class="test-info">
            <h2>🔧 已修复的功能</h2>
            <ul class="feature-list">
                <li>支持file://协议URL直接输入</li>
                <li>正确处理URL编码的中文路径</li>
                <li>自动识别本地文件路径格式</li>
                <li>兼容Windows和macOS/Linux路径</li>
                <li>保持拖拽文件功能正常</li>
            </ul>
        </div>
        
        <div class="info-box">
            <h2>📝 使用说明</h2>
            <p><strong>现在您可以通过以下方式加载本地文件：</strong></p>
            <ul class="feature-list">
                <li>直接在地址栏输入完整的file://路径</li>
                <li>输入绝对路径（如：/Users/<USER>/file.html）</li>
                <li>拖拽文件到浏览器窗口</li>
                <li>支持包含中文字符的路径</li>
            </ul>
        </div>
        
        <div class="test-info">
            <h2>🌟 示例路径格式</h2>
            <div class="path-display">
                <strong>macOS/Linux:</strong><br>
                file:///Users/<USER>/Documents/test.html<br>
                /Users/<USER>/Documents/test.html<br><br>
                
                <strong>Windows:</strong><br>
                file:///C:/Users/<USER>/Documents/test.html<br>
                C:\Users\<USER>\Documents\test.html<br><br>
                
                <strong>URL编码路径:</strong><br>
                file:///Volumes/HIKVISION/%E4%BC%A0%E6%96%87%E4%BB%B6/test.html
            </div>
        </div>
    </div>

    <script>
        // 显示当前文件路径
        document.getElementById('file-path').textContent = window.location.href;
        
        // 显示加载时间
        document.getElementById('load-time').textContent = new Date().toLocaleString();
        
        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease-out';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
