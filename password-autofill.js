// 密码自动填充脚本
(function() {
  'use strict';

  let passwordButtons = [];
  let currentDomain = window.location.hostname;
  let hiddenInputs = new Set(); // 记录被隐藏的输入框

  // 创建样式
  const style = document.createElement('style');
  style.textContent = `
    .password-fill-btn {
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      background: #007acc;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }

    .password-fill-btn:hover {
      background: #005a9e;
    }

    .password-hide-btn {
      position: absolute;
      right: 32px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      background: #dc3545;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }

    .password-hide-btn:hover {
      background: #c82333;
    }

    .password-selector-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 100000;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .password-selector-content {
      background: white;
      border-radius: 12px;
      padding: 20px;
      width: 90%;
      max-width: 500px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    .password-selector-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    .password-selector-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }

    .password-selector-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
    }

    .password-selector-close:hover {
      background: #f0f0f0;
    }

    .password-list {
      margin-bottom: 20px;
    }

    .password-item {
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      margin-bottom: 10px;
      cursor: pointer;
      transition: all 0.3s;
    }

    .password-item:hover {
      background: #f8f9fa;
      border-color: #007acc;
    }

    .password-item-title {
      font-weight: bold;
      font-size: 16px;
      color: #333;
      margin-bottom: 5px;
    }

    .password-item-username {
      color: #666;
      font-size: 14px;
    }

    .password-item-url {
      color: #999;
      font-size: 12px;
      margin-top: 5px;
    }

    .password-actions {
      display: flex;
      gap: 10px;
      padding-top: 15px;
      border-top: 1px solid #eee;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s;
    }

    .btn-primary {
      background: #007acc;
      color: white;
    }

    .btn-primary:hover {
      background: #005a9e;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #5a6268;
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #666;
    }

    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 15px;
    }

    .input-wrapper {
      position: relative;
      display: inline-block;
    }
  `;
  document.head.appendChild(style);
  
  // 检测登录表单
  function detectLoginForms() {
    const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], input[name*="user"], input[name*="email"], input[name*="login"], input[id*="user"], input[id*="email"], input[id*="login"]');
    
    inputs.forEach(input => {
      if (input.dataset.passwordButtonAdded) return;
      
      const isPasswordField = input.type === 'password';
      const isUsernameField = input.type === 'text' || input.type === 'email' || 
                             input.name.toLowerCase().includes('user') ||
                             input.name.toLowerCase().includes('email') ||
                             input.name.toLowerCase().includes('login') ||
                             input.id.toLowerCase().includes('user') ||
                             input.id.toLowerCase().includes('email') ||
                             input.id.toLowerCase().includes('login');
      
      if (isPasswordField || isUsernameField) {
        addPasswordButton(input);
        input.dataset.passwordButtonAdded = 'true';
      }
    });
  }
  
  // 添加密码填充按钮
  function addPasswordButton(input) {
    // 检查是否已经添加过按钮
    if (input.dataset.passwordButtonAdded) return;

    // 创建包装器
    const wrapper = document.createElement('div');
    wrapper.className = 'input-wrapper';

    // 将输入框包装起来
    input.parentNode.insertBefore(wrapper, input);
    wrapper.appendChild(input);

    // 创建密码填充按钮
    const fillButton = document.createElement('button');
    fillButton.className = 'password-fill-btn';
    fillButton.innerHTML = '🔐';
    fillButton.type = 'button';
    fillButton.title = '选择密码';

    // 创建隐藏按钮
    const hideButton = document.createElement('button');
    hideButton.className = 'password-hide-btn';
    hideButton.innerHTML = '✕';
    hideButton.type = 'button';
    hideButton.title = '隐藏按钮（这不是登录框）';

    // 添加点击事件
    fillButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      showPasswordSelector(input);
    });

    hideButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      hidePasswordButtons(input);
    });

    wrapper.appendChild(fillButton);
    wrapper.appendChild(hideButton);

    input.dataset.passwordButtonAdded = 'true';
    passwordButtons.push({ fillButton, hideButton, input, wrapper });
  }

  // 隐藏密码按钮
  function hidePasswordButtons(input) {
    const wrapper = input.closest('.input-wrapper');
    if (wrapper) {
      const fillButton = wrapper.querySelector('.password-fill-btn');
      const hideButton = wrapper.querySelector('.password-hide-btn');

      if (fillButton) fillButton.style.display = 'none';
      if (hideButton) hideButton.style.display = 'none';

      hiddenInputs.add(input);
    }
  }
  
  // 显示密码选择器
  async function showPasswordSelector(input) {
    // 移除现有的选择器
    const existingModal = document.querySelector('.password-selector-modal');
    if (existingModal) {
      existingModal.remove();
      return;
    }

    // 获取当前域名的密码
    const passwords = await getPasswordsForDomain(currentDomain);

    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'password-selector-modal';

    const content = document.createElement('div');
    content.className = 'password-selector-content';

    // 创建头部
    const header = document.createElement('div');
    header.className = 'password-selector-header';
    header.innerHTML = `
      <div class="password-selector-title">选择密码 - ${currentDomain}</div>
      <button class="password-selector-close">×</button>
    `;

    // 创建密码列表
    const passwordList = document.createElement('div');
    passwordList.className = 'password-list';

    if (passwords.length > 0) {
      passwords.forEach(password => {
        const item = document.createElement('div');
        item.className = 'password-item';
        item.innerHTML = `
          <div class="password-item-title">${escapeHtml(password.title)}</div>
          <div class="password-item-username">用户名: ${escapeHtml(password.username)}</div>
          ${password.url ? `<div class="password-item-url">${escapeHtml(password.url)}</div>` : ''}
        `;

        item.addEventListener('click', () => {
          fillPassword(password, input);
          modal.remove();
        });

        passwordList.appendChild(item);
      });
    } else {
      const emptyState = document.createElement('div');
      emptyState.className = 'empty-state';
      emptyState.innerHTML = `
        <div class="empty-state-icon">🔐</div>
        <div>没有找到 ${currentDomain} 的密码</div>
      `;
      passwordList.appendChild(emptyState);
    }

    // 创建操作按钮
    const actions = document.createElement('div');
    actions.className = 'password-actions';
    actions.innerHTML = `
      <button class="btn btn-primary" onclick="openPasswordManager()">打开密码管理器</button>
      <button class="btn btn-secondary" onclick="closePasswordSelector()">取消</button>
    `;

    // 组装模态框
    content.appendChild(header);
    content.appendChild(passwordList);
    content.appendChild(actions);
    modal.appendChild(content);

    // 添加事件监听器
    const closeBtn = header.querySelector('.password-selector-close');
    closeBtn.addEventListener('click', () => modal.remove());

    const cancelBtn = actions.querySelector('.btn-secondary');
    cancelBtn.addEventListener('click', () => modal.remove());

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });

    document.body.appendChild(modal);

    // 全局函数供按钮调用
    window.closePasswordSelector = () => modal.remove();
  }

  // HTML转义函数
  function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
  
  // 获取当前域名的密码
  async function getPasswordsForDomain(domain) {
    try {
      // 通过postMessage请求密码数据
      return new Promise((resolve) => {
        const messageId = Date.now().toString();

        // 监听响应
        const handleMessage = (event) => {
          if (event.data && event.data.type === 'PASSWORD_RESPONSE' && event.data.messageId === messageId) {
            window.removeEventListener('message', handleMessage);
            const allPasswords = event.data.passwords || [];
            const filteredPasswords = allPasswords.filter(password => {
              if (!password.url) return false;
              try {
                const passwordDomain = new URL(password.url).hostname;
                return passwordDomain === domain || passwordDomain.endsWith('.' + domain) || domain.endsWith('.' + passwordDomain);
              } catch {
                return false;
              }
            });
            resolve(filteredPasswords);
          }
        };

        window.addEventListener('message', handleMessage);

        // 发送请求
        window.postMessage({
          type: 'GET_PASSWORDS',
          messageId: messageId,
          domain: domain
        }, '*');

        // 超时处理
        setTimeout(() => {
          window.removeEventListener('message', handleMessage);
          resolve([]);
        }, 5000);
      });
    } catch (error) {
      console.error('Failed to get passwords:', error);
      return [];
    }
  }
  
  // 填充密码
  function fillPassword(password, targetInput = null) {
    // 查找用户名和密码字段
    const usernameFields = document.querySelectorAll('input[type="text"], input[type="email"], input[name*="user"], input[name*="email"], input[name*="login"], input[id*="user"], input[id*="email"], input[id*="login"]');
    const passwordFields = document.querySelectorAll('input[type="password"]');

    // 如果指定了目标输入框，优先填充它
    if (targetInput) {
      if (targetInput.type === 'password') {
        targetInput.value = password.password;
        targetInput.dispatchEvent(new Event('input', { bubbles: true }));
        targetInput.dispatchEvent(new Event('change', { bubbles: true }));

        // 查找最近的用户名字段
        const form = targetInput.closest('form') || document;
        const nearbyUsernameFields = form.querySelectorAll('input[type="text"], input[type="email"], input[name*="user"], input[name*="email"], input[name*="login"], input[id*="user"], input[id*="email"], input[id*="login"]');
        if (nearbyUsernameFields.length > 0) {
          const usernameField = nearbyUsernameFields[0];
          usernameField.value = password.username;
          usernameField.dispatchEvent(new Event('input', { bubbles: true }));
          usernameField.dispatchEvent(new Event('change', { bubbles: true }));
        }
      } else {
        targetInput.value = password.username;
        targetInput.dispatchEvent(new Event('input', { bubbles: true }));
        targetInput.dispatchEvent(new Event('change', { bubbles: true }));

        // 查找最近的密码字段
        const form = targetInput.closest('form') || document;
        const nearbyPasswordFields = form.querySelectorAll('input[type="password"]');
        if (nearbyPasswordFields.length > 0) {
          const passwordField = nearbyPasswordFields[0];
          passwordField.value = password.password;
          passwordField.dispatchEvent(new Event('input', { bubbles: true }));
          passwordField.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }
    } else {
      // 原有的全局填充逻辑
      if (usernameFields.length > 0) {
        const usernameField = usernameFields[0];
        usernameField.value = password.username;
        usernameField.dispatchEvent(new Event('input', { bubbles: true }));
        usernameField.dispatchEvent(new Event('change', { bubbles: true }));
      }

      if (passwordFields.length > 0) {
        const passwordField = passwordFields[0];
        passwordField.value = password.password;
        passwordField.dispatchEvent(new Event('input', { bubbles: true }));
        passwordField.dispatchEvent(new Event('change', { bubbles: true }));
      }
    }
  }
  window.openPasswordManager = () => openPasswordManager();
  // 打开密码管理器
  function openPasswordManager() {
    try {
      // 通过postMessage请求打开密码管理器
      // 通过postMessage请求打开密码管理器
      window.postMessage({
        type: 'OPEN_PASSWORD_MANAGER'
      }, '*');
    } catch (error) {
      // 捕获异常并输出错误信息
      console.error('Failed to open password manager:', error);
    }
  }
  
  // 初始化
  function init() {
    detectLoginForms();
    
    // 监听DOM变化
    const observer = new MutationObserver(() => {
      detectLoginForms();
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
  
  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
  
})();
