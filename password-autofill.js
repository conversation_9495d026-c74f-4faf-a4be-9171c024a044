// 密码自动填充脚本
(function() {
  'use strict';
  
  let passwordButtons = [];
  let currentDomain = window.location.hostname;
  
  // 创建样式
  const style = document.createElement('style');
  style.textContent = `
    .password-fill-btn {
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      background: #007acc;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
    
    .password-fill-btn:hover {
      background: #005a9e;
    }
    
    .password-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 10001;
      min-width: 250px;
      max-height: 300px;
      overflow-y: auto;
    }
    
    .password-item {
      padding: 10px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .password-item:hover {
      background: #f0f0f0;
    }
    
    .password-item:last-child {
      border-bottom: none;
    }
    
    .password-info {
      flex: 1;
    }
    
    .password-title {
      font-weight: bold;
      font-size: 14px;
    }
    
    .password-username {
      color: #666;
      font-size: 12px;
    }
    
    .add-password-item {
      padding: 10px;
      text-align: center;
      color: #007acc;
      font-weight: bold;
      border-top: 1px solid #eee;
    }
    
    .add-password-item:hover {
      background: #f0f0f0;
    }
    
    .input-wrapper {
      position: relative;
      display: inline-block;
    }
  `;
  document.head.appendChild(style);
  
  // 检测登录表单
  function detectLoginForms() {
    const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], input[name*="user"], input[name*="email"], input[name*="login"], input[id*="user"], input[id*="email"], input[id*="login"]');
    
    inputs.forEach(input => {
      if (input.dataset.passwordButtonAdded) return;
      
      const isPasswordField = input.type === 'password';
      const isUsernameField = input.type === 'text' || input.type === 'email' || 
                             input.name.toLowerCase().includes('user') ||
                             input.name.toLowerCase().includes('email') ||
                             input.name.toLowerCase().includes('login') ||
                             input.id.toLowerCase().includes('user') ||
                             input.id.toLowerCase().includes('email') ||
                             input.id.toLowerCase().includes('login');
      
      if (isPasswordField || isUsernameField) {
        addPasswordButton(input);
        input.dataset.passwordButtonAdded = 'true';
      }
    });
  }
  
  // 添加密码填充按钮
  function addPasswordButton(input) {
    // 创建包装器
    const wrapper = document.createElement('div');
    wrapper.className = 'input-wrapper';
    
    // 将输入框包装起来
    input.parentNode.insertBefore(wrapper, input);
    wrapper.appendChild(input);
    
    // 创建按钮
    const button = document.createElement('button');
    button.className = 'password-fill-btn';
    button.innerHTML = '🔐';
    button.type = 'button';
    
    // 添加点击事件
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      showPasswordDropdown(button, input);
    });
    
    wrapper.appendChild(button);
    passwordButtons.push({ button, input, wrapper });
  }
  
  // 显示密码下拉菜单
  async function showPasswordDropdown(button, input) {
    // 移除现有的下拉菜单
    const existingDropdown = document.querySelector('.password-dropdown');
    if (existingDropdown) {
      existingDropdown.remove();
      return;
    }
    
    // 获取当前域名的密码
    const passwords = await getPasswordsForDomain(currentDomain);
    
    // 创建下拉菜单
    const dropdown = document.createElement('div');
    dropdown.className = 'password-dropdown';
    
    if (passwords.length > 0) {
      passwords.forEach(password => {
        const item = document.createElement('div');
        item.className = 'password-item';
        item.innerHTML = `
          <div class="password-info">
            <div class="password-title">${password.title}</div>
            <div class="password-username">${password.username}</div>
          </div>
        `;
        
        item.addEventListener('click', () => {
          fillPassword(password);
          dropdown.remove();
        });
        
        dropdown.appendChild(item);
      });
    }
    
    // 添加新建密码选项
    const addItem = document.createElement('div');
    addItem.className = 'add-password-item';
    addItem.textContent = '+ 为此网站添加新密码';
    addItem.addEventListener('click', () => {
      openPasswordManager();
      dropdown.remove();
    });
    dropdown.appendChild(addItem);
    
    // 定位下拉菜单
    const rect = button.getBoundingClientRect();
    dropdown.style.position = 'fixed';
    dropdown.style.top = (rect.bottom + 5) + 'px';
    dropdown.style.right = (window.innerWidth - rect.right) + 'px';
    
    document.body.appendChild(dropdown);
    
    // 点击其他地方关闭下拉菜单
    const closeDropdown = (e) => {
      if (!dropdown.contains(e.target) && e.target !== button) {
        dropdown.remove();
        document.removeEventListener('click', closeDropdown);
      }
    };
    setTimeout(() => document.addEventListener('click', closeDropdown), 0);
  }
  
  // 获取当前域名的密码
  async function getPasswordsForDomain(domain) {
    try {
      // 检查是否在Electron环境中
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.getPasswords) {
        const allPasswords = await window.electronAPI.getPasswords();
        return allPasswords.filter(password => {
          if (!password.url) return false;
          try {
            const passwordDomain = new URL(password.url).hostname;
            return passwordDomain === domain || passwordDomain.endsWith('.' + domain) || domain.endsWith('.' + passwordDomain);
          } catch {
            return false;
          }
        });
      } else {
        console.log('ElectronAPI not available');
        return [];
      }
    } catch (error) {
      console.error('Failed to get passwords:', error);
      return [];
    }
  }
  
  // 填充密码
  function fillPassword(password) {
    // 查找用户名和密码字段
    const usernameFields = document.querySelectorAll('input[type="text"], input[type="email"], input[name*="user"], input[name*="email"], input[name*="login"], input[id*="user"], input[id*="email"], input[id*="login"]');
    const passwordFields = document.querySelectorAll('input[type="password"]');
    
    // 填充用户名
    if (usernameFields.length > 0) {
      const usernameField = usernameFields[0];
      usernameField.value = password.username;
      usernameField.dispatchEvent(new Event('input', { bubbles: true }));
      usernameField.dispatchEvent(new Event('change', { bubbles: true }));
    }
    
    // 填充密码
    if (passwordFields.length > 0) {
      const passwordField = passwordFields[0];
      passwordField.value = password.password;
      passwordField.dispatchEvent(new Event('input', { bubbles: true }));
      passwordField.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }
  
  // 打开密码管理器
  function openPasswordManager() {
    try {
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.openPasswordManager) {
        window.electronAPI.openPasswordManager();
      } else {
        console.log('Password manager not available');
        alert('密码管理器不可用');
      }
    } catch (error) {
      console.error('Failed to open password manager:', error);
    }
  }
  
  // 初始化
  function init() {
    detectLoginForms();
    
    // 监听DOM变化
    const observer = new MutationObserver(() => {
      detectLoginForms();
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
  
  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
  
})();
