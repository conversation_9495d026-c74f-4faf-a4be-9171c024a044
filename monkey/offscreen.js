(()=>{"use strict";const{AbortController:n,FileReader:e,TextDecoder:t,atob:o,btoa:r,clearInterval:s,clearTimeout:a,crypto:i,decodeURIComponent:c,encodeURIComponent:l,escape:d,location:u,setInterval:h,setTimeout:p,unescape:m}=self;let{fetch:f}=self;const g=self,{addEventListener:b,removeEventListener:y}=(u.origin,u.host,self),w=b,{DOMParser:v,Notification:k,Image:j,Worker:x,alert:T,confirm:_,document:E,screen:A,webkitNotifications:S}=self;let O;try{O=self.localStorage}catch(n){}const R=O
;let{XMLHttpRequest:U}=self,L=0;const C=[],z=()=>{const n=["debug"],e=["log"],t=["warn","info"],o=["error"],r=[...n,...e,...t,...o],s=o;L>=80&&s.push(...n),L>=60&&s.push(...e),L>=30&&s.push(...t),r.forEach((n=>I[n]=s.includes(n)?console[n].bind(console):()=>{}))},I={set:n=>{L=n,C.forEach((n=>{n(I,L)})),z()},get:()=>L,get verbose(){return(I.debug||(()=>{})).bind(console)},debug:()=>{},log:()=>{},warn:()=>{},info:()=>{},error:()=>{},addChangeListener:n=>{C.push(n)}};z()
;const M=globalThis,{chrome:D,browser:N}=M;let B,q,P,H;P=()=>{if(void 0!==B)return B;try{const n=navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./);n&&(B=parseInt(n[2]))}catch(n){}return B},H=()=>{if(void 0!==q)return q;try{q=-1!=navigator.userAgent.search(/Android|Mobile/)}catch(n){}return q},P();const $=["chrome-extension:"];[].concat(["chrome"]);const F=(()=>{const n={getInternalPathRegexp:function(n,e){
const t=new RegExp("(\\"+["/",".","+","?","|","(",")","[","]","{","}","\\"].join("|\\")+")","g"),o=$[0]+"//"+F.id+"/";return new RegExp(o.replace(t,"\\$1")+"([a-zA-Z"+(n?"\\/":"")+"]*)"+(e||"").replace(t,"\\$1"))},getInternalPageRegexp:function(){return F.getInternalPathRegexp(!1,".html")},getPlatformInfo:n=>D.runtime.getPlatformInfo(n),getBrowserInfo:n=>{n({name:"Chrome",version:`${P()}`,vendor:"unknown"})}};return Object.defineProperty(n,"lastError",{get:()=>D.runtime.lastError,enumerable:!0
}),Object.defineProperty(n,"id",{get:()=>D.runtime.id,enumerable:!0}),Object.defineProperty(n,"short_id",{get:()=>n.id.replace(/[^0-9a-zA-Z]/g,"").substr(0,4),enumerable:!0}),n})(),V=Object.defineProperties({},{inIncognitoContext:{get:()=>{throw new Error("Not supported in offscreen context")},enumerable:!0},...Object.getOwnPropertyDescriptors({getURL:function(n){return D.runtime.getURL(n)},sendMessage:(n,e)=>D.runtime.sendMessage(n,e),onMessage:{addListener:n=>D.runtime.onMessage.addListener(n)
},connect:n=>D.runtime.connect({name:n}),urls:{prepareForReport:function(n){return n}}})});let G=[];const X={},J={},Y={},W=n=>{const e=(()=>{let n,e;const t=[],o=[],r=n=>{o.push(n),s()},s=()=>{if(void 0!==n){let t;for(;o.length;)t=o.shift(),void 0!==t.state&&t.state!==n||"function"==typeof t.f&&t.f.call(a,e)}},a={promise:()=>a,done:n=>(r({state:!0,f:n}),a),fail:n=>(r({state:!1,f:n}),a),always:n=>(r({f:n}),a),progress:n=>(n&&t.push(n),a),then:(n,t,o)=>W((r=>{const s=(n,t)=>(...o)=>{
const s=n?n(e):void 0,a=s&&"function"==typeof s.promise?s.promise():null,i=s&&"function"==typeof s.then?s:null;if(a)a.done((n=>r.resolve(n))).fail(((...n)=>r.reject(n[0]))).progress(((...n)=>r.notify(...n)));else if(i)i.then(((...n)=>r.resolve(...n)),(n=>r.reject(n)));else{const e=n?[s]:o;r[t](e[0])}};a.done(s(n,"resolve")),a.fail(s(t,"reject")),a.progress(s(o,"notify"))})).promise(),each:n=>{const e=W();return a.then((t=>{const o=Array.isArray(t)?t:[t];W.when(o.map((e=>n(e)))).then(e.resolve)
})),e.promise()},iterate:n=>{const e=W();return a.then((t=>{const o=(Array.isArray(t)?t:[t]).map((e=>()=>n(e)));W.onebyone(o,!0).done((n=>{e.resolve(n)})).fail(e.reject)})),e.promise()}};return{get:()=>a,try_resolve:t=>(void 0===n&&(n=!0,e=t),s(),a),try_reject:t=>(void 0===n&&(n=!1,e=t),s(),a),do_notify:n=>((n=>{t.forEach((e=>e(n)))})(n),a)}})(),t={promise:()=>e.get(),resolve:n=>e.try_resolve(n),reject:n=>e.try_reject(n),notify:n=>e.do_notify(n),
consume:n=>(n&&n.promise?n.promise().done((n=>t.resolve(n))).fail(((...n)=>t.reject(n[0]))).progress((n=>t.notify(n))):n&&n.then?n.then((n=>t.resolve(n)),((...n)=>t.reject(n[0]))):I.warn("promise: incompatible object given to consume()",n),t.promise())};return n&&n(t),t};W.Pledge=(...n)=>{const e=W();return e.resolve(...n),e.promise()},W.Breach=(...n)=>{const e=W();return e.reject(n[0]),e.promise()},W.onebyone=(n,e=!0)=>{const t=[],o=W();let r=0;const s=()=>{if(r<n.length){const a=(0,n[r++])()
;a&&a.promise?a.promise().done((n=>{t.push(n),s()})).fail((()=>{if(t.push(null),e)return o.reject();s()})):a&&a.then?a.then((n=>{t.push(n),s()}),(()=>{if(t.push(null),e)return o.reject();s()})):(t.push(a),s())}else o.resolve(t)};return s(),o.promise()},W.or=n=>{let e;const t=W(),o=()=>{n.length?(e=n.shift(),e&&W.Pledge().then(e).done(t.resolve).fail(o)):t.reject()};return o(),t.promise()},W.sidebyside=n=>{n=Array.isArray(n)?n:[n];const e=W();let t=n.length;return t?n.forEach((n=>{
n&&n.promise&&n.promise().always((()=>{0==--t&&e.resolve()}))})):e.resolve(),e.promise()},W.when=n=>{n=Array.isArray(n)?n:[n];const e=W();let t=n.length;const o=[];return t?n.forEach((n=>{let r;if(n&&n.promise)r=n.promise();else{if(!n.then)return void I.warn("promise: incompatible object given to when()",n);r=n}r.fail((()=>{e.reject(),t=-1})).done((n=>{o.push(n),0==--t&&e.resolve(o)}))})):e.resolve(o),e.promise()},Object.defineProperties(Promise.prototype,{done:{value:function(n){
return this.then(((...e)=>n.apply(this,e)))},configurable:!0,enumerable:!0,writable:!1},fail:{value:function(n){return this.then((()=>{}),((...e)=>n.apply(this,e)))},configurable:!0,enumerable:!0,writable:!1},always:{value:function(n){return this.then(((...e)=>n.apply(this,e)),((...e)=>n.apply(this,e)))},configurable:!0,enumerable:!0,writable:!1}});const Z=Promise;let K=0;const Q=async function(n,...e){await(()=>{const n=Date.now();if(K+1e3<n)return new Z((n=>p((()=>{K=Date.now(),n()}),0)))})(),
n.apply(this,e)},nn=function(n,e){return e?p.apply(this,[n,e]):(Q.apply(this,[n]),0)},en=n=>{const e=[],t=[],o=()=>{let r;if(t.length<n.threads&&e.length&&(r=e.shift())){const n=r.fn();let e;if(void 0!==n.catch){const t=W();n.then(t.resolve).catch(t.reject),e=t.promise()}else e=n;t.push(e),e.always((()=>{let n;(n=t.indexOf(e))>-1&&t.splice(n,1),o()})),r.p.consume(e)}};return{add:function(n){const t=W();return e.push({fn:n,p:t}),o(),t.promise()}}};var tn;!function(n){n.TESTING="TESTING",
n.AUDIO_PLAYBACK="AUDIO_PLAYBACK",n.IFRAME_SCRIPTING="IFRAME_SCRIPTING",n.DOM_SCRAPING="DOM_SCRAPING",n.BLOBS="BLOBS",n.DOM_PARSER="DOM_PARSER",n.USER_MEDIA="USER_MEDIA",n.DISPLAY_MEDIA="DISPLAY_MEDIA",n.WEB_RTC="WEB_RTC",n.CLIPBOARD="CLIPBOARD",n.LOCAL_STORAGE="LOCAL_STORAGE",n.WORKERS="WORKERS",n.BATTERY_STATUS="BATTERY_STATUS",n.MATCH_MEDIA="MATCH_MEDIA",n.GEOLOCATION="GEOLOCATION"}(tn||(tn={}));const on=n=>{if(!v)throw new Error("DOMParser not available")
;return(new v).parseFromString(n,"text/xml")},rn=n=>{var e;let t;return n&&(t=(null===(e=n.firstChild)||void 0===e?void 0:e.nextSibling)?n.firstChild.nextSibling:n.firstChild),t},sn=(n,e)=>{let t,o;if((t=n.getElementsByTagNameNS("*",e)[0])&&(o=t.firstChild))return o.nodeValue||void 0},an=n=>m(l(n)),cn=n=>c(d(n)),ln=n=>{let e="";for(let t=0;t<n.length;t++)e+=String.fromCharCode(255&n.charCodeAt(t));return r(e)},dn=(n,e)=>{try{let t;t="object"==typeof e?e.encoding:e,
t&&"utf-8"==t.toLowerCase()&&(n=an(n));const o=new Uint8Array(n.length);for(let e=0;e<n.length;e++)o[e]=255&n.charCodeAt(e);return o.buffer}catch(n){I.warn(n)}return new Uint8Array(0).buffer},un=n=>{let e;e=n.split(",")[0].includes("base64")?o(n.split(",")[1]):m(n.split(",")[1]);const t=n.split(",")[0].split(":")[1].split(";")[0],r=new Uint8Array(e.length);for(let n=0;n<e.length;n++)r[n]=e.charCodeAt(n);return new Blob([r],{type:t})},hn=n=>new Promise((t=>{const o=new e;o.onload=n=>{var e
;t((null===(e=n.target)||void 0===e?void 0:e.result)||void 0)},o.readAsDataURL(n)})),pn=async(n,t)=>new Promise((o=>{const r=new e;r.onload=()=>{o(r.result||"")},r.onerror=n=>{I.warn(`unable to decode data ${n}`),o("")},t?r.readAsText(n,t):r.readAsBinaryString(n)}));var mn;!function(n){n[n.OBJ_URL=1]="OBJ_URL",n[n.BLOB=2]="BLOB",n[n.DATA_URI=4]="DATA_URI",n[n.BINARY=8]="BINARY"}(mn||(mn={}))
;const fn=mn.OBJ_URL+0+0+mn.BINARY,gn=n=>void 0!==n.objUrl,bn=n=>void 0!==n.blob,yn=n=>void 0!==n.dataUri,wn=n=>void 0!==n.binary;class vn{constructor(n,e){if(gn(n))this.objUrl=n.objUrl.url,this.type=n.objUrl.type;else if(bn(n))this.blob=n.blob;else if(yn(n))this.dataUri=n.dataUri;else{if(!wn(n))throw new Error("incompatible TransferableData");this.binary=n.binary}this.kinds=e||fn}dispose(){this.objUrl&&URL.revokeObjectURL(this.objUrl)}async toTransferableData(){if(this.kinds&mn.OBJ_URL){
if(!this.objUrl&&(!this.blob&&this.dataUri&&(this.blob=un(this.dataUri)),this.blob&&(this.objUrl=URL.createObjectURL(this.blob)),!this.objUrl))throw new Error("incomplete Transferable");return{objUrl:{url:this.objUrl,type:this.type}}}if(this.kinds&mn.BLOB){if(!this.blob&&(this.objUrl?this.blob=await f(this.objUrl).then((n=>n.blob())):this.dataUri&&(this.blob=un(this.dataUri)),!this.blob))throw new Error("incomplete Transferable");return{blob:this.blob}}if(this.kinds&mn.DATA_URI){
if(!this.dataUri){let n=this.blob;if(!n&&this.objUrl&&(n=await f(this.objUrl).then((n=>n.blob()))),n&&(this.dataUri=await hn(n)),!this.dataUri)throw new Error("incomplete Transferable")}return{dataUri:this.dataUri}}if(this.kinds&mn.BINARY){if(!this.binary){let n=this.blob;if(!n&&this.objUrl&&(n=await f(this.objUrl).then((n=>n.blob()))),n&&(this.binary=await pn(n)),!this.binary)throw new Error("incomplete Transferable")}return{binary:this.binary,type:"application/octet-stream"}}
throw new Error("incompatible Transferable")}get tryObjectUrl(){return this.objUrl}get tryBlob(){return this.blob}get tryDataUri(){return this.dataUri}get tryBinary(){return this.binary}async toBlob(){if(this.blob)return this.blob;if(!this.objUrl){if(this.dataUri)return un(this.dataUri);if(this.binary)return new Blob([dn(this.binary)],{type:"application/octet-stream"});throw new Error("incompatible Transferable")}try{return await(await f(this.objUrl)).blob()}catch(n){return}}async toDataUri(){
if(this.dataUri)return this.dataUri;{const n=await this.toBlob();if(!n)throw new Error("incompatible Transferable");return await hn(n)}}static fromTransferableData(n){return n&&(bn(n)||gn(n)||yn(n)||wn(n))?new vn(n):void 0}}const kn=(n,e)=>{let t=!0;if("object"!=typeof n||"object"!=typeof e||null===n||null===e)return n===e;for(const[o,r]of Object.entries(e))if(void 0!==r&&(t=void 0!==n[o]&&(null===r?null===n[o]:"object"==typeof r?kn(n[o],r):n[o]===r),!t))break;return t},jn=(n,e)=>{const t=[]
;for(let o=0,r=n.length;o<r;o+=e)t.push(n.slice(o,e+o));return t
},xn="aaa\naarp\nabb\nabbott\nabogado\nac\nacademy\naccenture\naccountant\naccountants\naco\nactive\nactor\nad\nadac\nads\nadult\nae\naeg\naero\naf\nafl\nag\nagency\nai\naig\nairforce\nairtel\nal\nalibaba\nalipay\nallfinanz\nalsace\nam\namica\namsterdam\nan\nanalytics\nandroid\nao\napartments\napp\napple\naq\naquarelle\nar\naramco\narchi\narmy\narpa\narte\nas\nasia\nassociates\nat\nattorney\nau\nauction\naudi\naudio\nauthor\nauto\nautos\naw\nax\naxa\naz\nazure\nba\nbaidu\nband\nbank\nbar\nbarcelona\nbarclaycard\nbarclays\nbargains\nbauhaus\nbayern\nbb\nbbc\nbbva\nbcn\nbd\nbe\nbeats\nbeer\nbentley\nberlin\nbest\nbet\nbf\nbg\nbh\nbharti\nbi\nbible\nbid\nbike\nbing\nbingo\nbio\nbiz\nbj\nbl\nblack\nblackfriday\nbloomberg\nblue\nbm\nbms\nbmw\nbn\nbnl\nbnpparibas\nbo\nboats\nboehringer\nbom\nbond\nboo\nbook\nboots\nbosch\nbostik\nbot\nboutique\nbq\nbr\nbradesco\nbridgestone\nbroadway\nbroker\nbrother\nbrussels\nbs\nbt\nbudapest\nbugatti\nbuild\nbuilders\nbusiness\nbuy\nbuzz\nbv\nbw\nby\nbz\nbzh\nca\ncab\ncafe\ncal\ncall\ncamera\ncamp\ncancerresearch\ncanon\ncapetown\ncapital\ncar\ncaravan\ncards\ncare\ncareer\ncareers\ncars\ncartier\ncasa\ncash\ncasino\ncat\ncatering\ncba\ncbn\ncc\ncd\nceb\ncenter\nceo\ncern\ncf\ncfa\ncfd\ncg\nch\nchanel\nchannel\nchat\ncheap\nchloe\nchristmas\nchrome\nchurch\nci\ncipriani\ncircle\ncisco\ncitic\ncity\ncityeats\nck\ncl\nclaims\ncleaning\nclick\nclinic\nclinique\nclothing\ncloud\nclub\nclubmed\ncm\ncn\nco\ncoach\ncodes\ncoffee\ncollege\ncologne\ncom\ncommbank\ncommunity\ncompany\ncompare\ncomputer\ncomsec\ncondos\nconstruction\nconsulting\ncontact\ncontractors\ncooking\ncool\ncoop\ncorsica\ncountry\ncoupons\ncourses\ncr\ncredit\ncreditcard\ncreditunion\ncricket\ncrown\ncrs\ncruises\ncsc\ncu\ncuisinella\ncv\ncw\ncx\ncy\ncymru\ncyou\ncz\ndabur\ndad\ndance\ndate\ndating\ndatsun\nday\ndclk\nde\ndealer\ndeals\ndegree\ndelivery\ndell\ndeloitte\ndelta\ndemocrat\ndental\ndentist\ndesi\ndesign\ndev\ndiamonds\ndiet\ndigital\ndirect\ndirectory\ndiscount\ndj\ndk\ndm\ndnp\ndo\ndocs\ndog\ndoha\ndomains\ndoosan\ndownload\ndrive\ndubai\ndurban\ndvag\ndz\nearth\neat\nec\nedeka\nedu\neducation\nee\neg\neh\nemail\nemerck\nenergy\nengineer\nengineering\nenterprises\nepson\nequipment\ner\nerni\nes\nesq\nestate\net\neu\neurovision\neus\nevents\neverbank\nexchange\nexpert\nexposed\nexpress\nfage\nfail\nfairwinds\nfaith\nfamily\nfan\nfans\nfarm\nfashion\nfast\nfeedback\nferrero\nfi\nfilm\nfinal\nfinance\nfinancial\nfirestone\nfirmdale\nfish\nfishing\nfit\nfitness\nfj\nfk\nflickr\nflights\nflorist\nflowers\nflsmidth\nfly\nfm\nfo\nfoo\nfootball\nford\nforex\nforsale\nforum\nfoundation\nfox\nfr\nfresenius\nfrl\nfrogans\nfrontier\nfund\nfurniture\nfutbol\nfyi\nga\ngal\ngallery\ngallup\ngame\ngarden\ngb\ngbiz\ngd\ngdn\nge\ngea\ngent\ngenting\ngf\ngg\nggee\ngh\ngi\ngift\ngifts\ngives\ngiving\ngl\nglass\ngle\nglobal\nglobo\ngm\ngmail\ngmo\ngmx\ngn\ngold\ngoldpoint\ngolf\ngoo\ngoog\ngoogle\ngop\ngot\ngov\ngp\ngq\ngr\ngrainger\ngraphics\ngratis\ngreen\ngripe\ngroup\ngs\ngt\ngu\ngucci\nguge\nguide\nguitars\nguru\ngw\ngy\nhamburg\nhangout\nhaus\nhdfcbank\nhealth\nhealthcare\nhelp\nhelsinki\nhere\nhermes\nhiphop\nhitachi\nhiv\nhk\nhm\nhn\nhockey\nholdings\nholiday\nhomedepot\nhomes\nhonda\nhorse\nhost\nhosting\nhoteles\nhotmail\nhouse\nhow\nhr\nhsbc\nht\nhu\nhyundai\nibm\nicbc\nice\nicu\nid\nie\nifm\niinet\nil\nim\nimmo\nimmobilien\nin\nindustries\ninfiniti\ninfo\ning\nink\ninstitute\ninsurance\ninsure\nint\ninternational\ninvestments\nio\nipiranga\niq\nir\nirish\nis\niselect\nist\nistanbul\nit\nitau\niwc\njaguar\njava\njcb\nje\njetzt\njewelry\njlc\njll\njm\njmp\njo\njobs\njoburg\njot\njoy\njp\njprs\njuegos\nkaufen\nkddi\nke\nkfh\nkg\nkh\nki\nkia\nkim\nkinder\nkitchen\nkiwi\nkm\nkn\nkoeln\nkomatsu\nkp\nkpn\nkr\nkrd\nkred\nkw\nky\nkyoto\nkz\nla\nlacaixa\nlamborghini\nlamer\nlancaster\nland\nlandrover\nlanxess\nlasalle\nlat\nlatrobe\nlaw\nlawyer\nlb\nlc\nlds\nlease\nleclerc\nlegal\nlexus\nlgbt\nli\nliaison\nlidl\nlife\nlifeinsurance\nlifestyle\nlighting\nlike\nlimited\nlimo\nlincoln\nlinde\nlink\nlive\nliving\nlixil\nlk\nloan\nloans\nlol\nlondon\nlotte\nlotto\nlove\nlr\nls\nlt\nltd\nltda\nlu\nlupin\nluxe\nluxury\nlv\nly\nma\nmadrid\nmaif\nmaison\nmakeup\nman\nmanagement\nmango\nmarket\nmarketing\nmarkets\nmarriott\nmba\nmc\nmd\nme\nmed\nmedia\nmeet\nmelbourne\nmeme\nmemorial\nmen\nmenu\nmeo\nmf\nmg\nmh\nmiami\nmicrosoft\nmil\nmini\nmk\nml\nmm\nmma\nmn\nmo\nmobi\nmobily\nmoda\nmoe\nmoi\nmom\nmonash\nmoney\nmontblanc\nmormon\nmortgage\nmoscow\nmotorcycles\nmov\nmovie\nmovistar\nmp\nmq\nmr\nms\nmt\nmtn\nmtpc\nmtr\nmu\nmuseum\nmutuelle\nmv\nmw\nmx\nmy\nmz\nna\nnadex\nnagoya\nname\nnatura\nnavy\nnc\nne\nnec\nnet\nnetbank\nnetwork\nneustar\nnew\nnews\nnexus\nnf\nng\nngo\nnhk\nni\nnico\nnikon\nninja\nnissan\nnl\nno\nnokia\nnorton\nnowruz\nnp\nnr\nnra\nnrw\nntt\nnu\nnyc\nnz\nobi\noffice\nokinawa\nom\nomega\none\nong\nonl\nonline\nooo\noracle\norange\norg\norganic\norigins\nosaka\notsuka\novh\npa\npage\npamperedchef\npanerai\nparis\npars\npartners\nparts\nparty\npe\npet\npf\npg\nph\npharmacy\nphilips\nphoto\nphotography\nphotos\nphysio\npiaget\npics\npictet\npictures\npid\npin\nping\npink\npizza\npk\npl\nplace\nplay\nplaystation\nplumbing\nplus\npm\npn\npohl\npoker\nporn\npost\npr\npraxi\npress\npro\nprod\nproductions\nprof\npromo\nproperties\nproperty\nprotection\nps\npt\npub\npw\npwc\npy\nqa\nqpon\nquebec\nquest\nracing\nre\nread\nrealtor\nrealty\nrecipes\nred\nredstone\nredumbrella\nrehab\nreise\nreisen\nreit\nren\nrent\nrentals\nrepair\nreport\nrepublican\nrest\nrestaurant\nreview\nreviews\nrexroth\nrich\nricoh\nrio\nrip\nro\nrocher\nrocks\nrodeo\nroom\nrs\nrsvp\nru\nruhr\nrun\nrw\nrwe\nryukyu\nsa\nsaarland\nsafe\nsafety\nsakura\nsale\nsalon\nsamsung\nsandvik\nsandvikcoromant\nsanofi\nsap\nsapo\nsarl\nsas\nsaxo\nsb\nsbs\nsc\nsca\nscb\nschaeffler\nschmidt\nscholarships\nschool\nschule\nschwarz\nscience\nscor\nscot\nsd\nse\nseat\nsecurity\nseek\nselect\nsener\nservices\nseven\nsew\nsex\nsexy\nsfr\nsg\nsh\nsharp\nshell\nshia\nshiksha\nshoes\nshow\nshriram\nsi\nsingles\nsite\nsj\nsk\nski\nskin\nsky\nskype\nsl\nsm\nsmile\nsn\nsncf\nso\nsoccer\nsocial\nsoftbank\nsoftware\nsohu\nsolar\nsolutions\nsony\nsoy\nspace\nspiegel\nspreadbetting\nsr\nsrl\nss\nst\nstada\nstar\nstarhub\nstatefarm\nstatoil\nstc\nstcgroup\nstockholm\nstorage\nstudio\nstudy\nstyle\nsu\nsucks\nsupplies\nsupply\nsupport\nsurf\nsurgery\nsuzuki\nsv\nswatch\nswiss\nsx\nsy\nsydney\nsymantec\nsystems\nsz\ntab\ntaipei\ntaobao\ntatamotors\ntatar\ntattoo\ntax\ntaxi\ntc\ntci\ntd\nteam\ntech\ntechnology\ntel\ntelefonica\ntemasek\ntennis\ntf\ntg\nth\nthd\ntheater\ntheatre\ntickets\ntienda\ntiffany\ntips\ntires\ntirol\ntj\ntk\ntl\ntm\ntmall\ntn\nto\ntoday\ntokyo\ntools\ntop\ntoray\ntoshiba\ntours\ntown\ntoyota\ntoys\ntp\ntr\ntrade\ntrading\ntraining\ntravel\ntravelers\ntravelersinsurance\ntrust\ntrv\ntt\ntube\ntui\ntushu\ntv\ntvs\ntw\ntz\nua\nubs\nug\nuk\num\nunicom\nuniversity\nuno\nuol\nus\nuy\nuz\nva\nvacations\nvana\nvc\nve\nvegas\nventures\nverisign\nversicherung\nvet\nvg\nvi\nviajes\nvideo\nvillas\nvin\nvip\nvirgin\nvision\nvista\nvistaprint\nviva\nvlaanderen\nvn\nvodka\nvolkswagen\nvote\nvoting\nvoto\nvoyage\nvu\nwales\nwalter\nwang\nwanggou\nwatch\nwatches\nweather\nweatherchannel\nwebcam\nweber\nwebsite\nwed\nwedding\nweir\nwf\nwhoswho\nwien\nwiki\nwilliamhill\nwin\nwindows\nwine\nwme\nwolterskluwer\nwork\nworks\nworld\nws\nwtc\nwtf\nxbox\nxerox\nxin\n测试\nकॉम\nपरीक्षा\n佛山\n慈善\n集团\n在线\n한국\n点看\nคอม\nভারত\n八卦\n‏موقع‎\nবাংলা\n公益\n公司\n移动\n我爱你\nмосква\nиспытание\nқаз\nонлайн\nсайт\n联通\nсрб\nбел\n‏קום‎\n时尚\n테스트\n淡马锡\nорг\nनेट\n삼성\nசிங்கப்பூர்\n商标\n商店\n商城\nдети\nмкд\n‏טעסט‎\nею\nポイント\n新闻\n工行\n‏كوم‎\n中文网\n中信\n中国\n中國\n娱乐\n谷歌\nభారత్\nලංකා\n购物\n測試\nભારત\nभारत\n‏آزمایشی‎\nபரிட்சை\n网店\nसंगठन\n餐厅\n网络\nком\nукр\n香港\n诺基亚\nδοκιμή\n飞利浦\n‏إختبار‎\n台湾\n台灣\n手表\n手机\nмон\n‏الجزائر‎\n‏عمان‎\n‏ارامكو‎\n‏ایران‎\n‏امارات‎\n‏بازار‎\n‏پاکستان‎\n‏الاردن‎\n‏موبايلي‎\n‏بھارت‎\n‏المغرب‎\n‏السعودية‎\n‏سودان‎\n‏همراه‎\n‏عراق‎\n‏مليسيا‎\n澳門\n닷컴\n政府\n‏شبكة‎\n‏بيتك‎\nგე\n机构\n组织机构\n健康\nไทย\n‏سورية‎\nрус\nрф\n珠宝\n‏تونس‎\n大拿\nみんな\nグーグル\nελ\n世界\nਭਾਰਤ\n网址\n닷넷\nコム\n游戏\nvermögensberater\nvermögensberatung\n企业\n信息\n‏مصر‎\n‏قطر‎\n广东\nஇலங்கை\nஇந்தியா\nհայ\n新加坡\n‏فلسطين‎\nテスト\n政务\nxperia\nxxx\nxyz\nyachts\nyahoo\nyamaxun\nyandex\nye\nyodobashi\nyoga\nyokohama\nyoutube\nyt\nza\nzara\nzero\nzip\nzm\nzone\nzuerich\nzw".split("\n").join("|"),Tn="ac.cn\nac.jp\nac.uk\nad.jp\nah.cn\naichi.jp\nakita.jp\naomori.jp\nasn.au\nbj.cn\nchiba.jp\nco.cc\nco.ck\nco.fk\nco.gg\nco.im\nco.in\nco.ir\nco.je\nco.jp\nco.kr\nco.ma\ncom.ac\ncom.af\ncom.ag\ncom.ai\ncom.al\ncom.ar\ncom.au\ncom.aw\ncom.az\ncom.ba\ncom.bb\ncom.bh\ncom.bi\ncom.bm\ncom.bo\ncom.br\ncom.bs\ncom.bt\ncom.by\ncom.bz\ncom.ci\ncom.cm\ncom.cn\ncom.co\ncom.cu\ncom.cw\ncom.cy\ncom.de\ncom.dm\ncom.do\ncom.dz\ncom.ec\ncom.ee\ncom.eg\ncom.es\ncom.et\ncom.fr\ncom.ge\ncom.gh\ncom.gi\ncom.gl\ncom.gn\ncom.gp\ncom.gr\ncom.gt\ncom.gu\ncom.gy\ncom.hk\ncom.hn\ncom.hr\ncom.ht\ncom.im\ncom.io\ncom.iq\ncom.is\ncom.jo\ncom.kg\ncom.ki\ncom.km\ncom.kp\ncom.ky\ncom.kz\ncom.la\ncom.lb\ncom.lc\ncom.lk\ncom.lr\ncom.lv\ncom.ly\ncom.mg\ncom.mk\ncom.ml\ncom.mo\ncom.ms\ncom.mt\ncom.mu\ncom.mv\ncom.mw\ncom.mx\ncom.my\ncom.na\ncom.nf\ncom.ng\ncom.ni\ncom.nr\ncom.om\ncom.pa\ncom.pe\ncom.pf\ncom.ph\ncom.pk\ncom.pl\ncom.pr\ncom.ps\ncom.pt\ncom.py\ncom.qa\ncom.re\ncom.ro\ncom.ru\ncom.rw\ncom.sa\ncom.sb\ncom.sc\ncom.sd\ncom.se\ncom.sg\ncom.sh\ncom.sl\ncom.sn\ncom.so\ncom.st\ncom.sv\ncom.sy\ncom.tj\ncom.tm\ncom.tn\ncom.to\ncom.tr\ncom.tt\ncom.tw\nco.mu\ncom.ua\ncom.ug\ncom.uy\ncom.uz\ncom.vc\ncom.ve\ncom.vi\ncom.vn\ncom.vu\ncom.ws\ncom.zm\nconf.au\nco.nz\nco.rw\nco.th\nco.tj\nco.tt\nco.tv\nco.tz\nco.ug\nco.uk\nco.us\nco.ve\nco.yu\nco.za\nco.zm\nco.zw\ncq.cn\ncsiro.au\nde.net\ndk.org\ned.jp\nedu.au\nedu.cn\nedu.uk\nehime.jp\neu.org\nfukui.jp\nfukuoka.jp\nfukushima.jp\ngb.net\ngd.cn\ngifu.jp\ngo.jp\ngov.au\ngov.cn\ngov.jp\ngov.uk\ngr.jp\ngs.cn\ngunma.jp\ngx.cn\ngz.cn\nhb.cn\nhe.cn\nhi.cn\nhiroshima.jp\nhk.cn\nhl.cn\nhn.cn\nhokkaido.jp\nhyogo.jp\nibaraki.jp\nid.au\ninfo.au\nishikawa.jp\niwate.jp\njl.cn\njs.cn\nkagawa.jp\nkagoshima.jp\nkanagawa.jp\nkanazawa.jp\nkawasaki.jp\nkitakyushu.jp\nkobe.jp\nkochi.jp\nkumamoto.jp\nkyoto.jp\nlg.jp\nln.cn\nltd.uk\nmatsuyama.jp\nme.uk\nmie.jp\nmiyagi.jp\nmiyazaki.jp\nmo.cn\nmod.uk\nnagano.jp\nnagasaki.jp\nnagoya.jp\nnara.jp\nne.jp\nnet.au\nnet.cn\nnet.jp\nnet.uk\nnhs.uk\nnic.uk\nniigata.jp\nnm.cn\nnx.cn\noita.jp\nokayama.jp\nokinawa.jp\norg.au\norg.cn\norg.jp\norg.uk\nor.jp\nosaka.jp\notc.au\noz.au\nplc.uk\npolice.uk\nqh.cn\nsaga.jp\nsaitama.jp\nsapporo.jp\nsc.cn\nsch.uk\nsendai.jp\nsh.cn\nshiga.jp\nshimane.jp\nshizuoka.jp\nsn.cn\nsx.cn\ntakamatsu.jp\ntelememo.au\ntj.cn\ntochigi.jp\ntokushima.jp\ntokyo.jp\ntottori.jp\ntoyama.jp\ntw.cn\nuk.net\nutsunomiya.jp\nwakayama.jp\nxj.cn\nxz.cn\nyamagata.jp\nyamaguchi.jp\nyamanashi.jp\nyn.cn\nyokohama.jp\nzj.cn".split("\n").join("|"),_n=((".("+[xn,Tn].join("|")+")").replace(/\./gi,"\\."),
(()=>{const n={},{retimeout_on_get:e,timeout:t,check_interval:o,auto_remove_mutex:r}=Object.assign({retimeout_on_get:!1,timeout:300,check_interval:120},{timeout:180,check_interval:120,retimeout_on_get:!0}||{});let s;const i=()=>{c(),s=p(d,1e3*o)},c=()=>{s&&a(s),s=null},l=e=>{const t=n[e];return delete n[e],t?t.value:void 0},d=()=>{s=null;const e=Date.now()-1e3*t,o=Object.entries(n).filter((([n,t])=>t.ts<e));o.length?(r?r.acquire():W.Pledge()).then((()=>{o.forEach((([n,e])=>{const t=e&&e.d
;t&&t.resolve([n,e.value]),l(n)})),r&&r.release(),i()})):i()};return t&&i(),{set:(e,t)=>{const o=W();return n[e]={value:t,ts:Date.now(),d:o},o.promise()},get:(t,o)=>{const r=n[t];return r?(e&&(r.ts=Date.now()),r.value):o},remove:l,removeAll:()=>{Object.keys(n).map((n=>l(n)))}}})()),En=["protocol","hostname","origin"],An=["port"],Sn=["pathname"],On=["search","hash"];let Rn;const Un={protocol:"",scheme:"",origin:"",pathname:"",hostname:"",port:void 0,search:"",hash:""},Ln=(n,e)=>{
let t=Object.assign({},Un);if(null==n);else if(["data:","view-source:"].some((e=>n.startsWith(e)))){t.origin="null";const e=n.indexOf(":");t.protocol=n.substr(0,e+1),t.pathname=n.substr(e+1)}else{let o,r;try{o=new URL(n)}catch(e){try{const e="http:";r="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(n=>{const e=16*Math.random()|0;return("x"==n?e:3&e|8).toString(16)})),o=new URL(n||"/",`${e}//${r}/${r}`)}catch(n){r=void 0}}if(o){
let{protocol:s,origin:a,pathname:i,hostname:c,port:l,search:d,hash:u,username:h,password:p}=o;if(r&&(s=a="",c===r&&(c=""),i=i.replace(`/${r}`,"")),t={protocol:s,origin:a,pathname:i,hostname:c,port:parseInt(l)||void 0,search:d,hash:u,username:h,password:p},!e&&s){if(0!==n.toLowerCase().indexOf(s))if(n.startsWith("//"))t.origin="",t.protocol="";else{if(!["/","?","#"].includes(n[0]))return Ln("/"+n);Rn=Rn||Ln("",!0),[...On,...Sn].forEach((n=>{Rn[n]===t[n]&&(t[n]="")})),En.forEach((n=>{t[n]=""})),
An.forEach((n=>{t[n]=void 0}))}["tampermonkey:"].includes(t.protocol)&&(t.pathname=((t.hostname?"/"+t.hostname:"")+(t.pathname||"")).replace(/^\/+/,"/"),t.hostname="")}}}return 0===t.port&&(t.port=void 0),Object.defineProperties(t,{domain:{get:function(){const n=t.hostname.split("."),e=n.pop();let o=`${n.pop()}.${e}`;return zn(o)&&(o=`${n.pop()}.${o}`),o}},scheme:{get:function(){return t.protocol.replace(/:$/,"")}}}),t};let Cn;const zn=n=>(Cn=Cn||new RegExp("^("+Tn.replace(/\./g,"\\.")+")$"),
!!n.match(Cn)),In=new class{constructor(n=1){if(this.waiting=[],n<0)throw new Error("Count must not be negative");this._count=n}acquire(){if(this._count>0)return this._count--,W.Pledge();{const n=W();return this.waiting.push(n.resolve),n.promise()}}release(){if(this.waiting.length>0){const n=this.waiting.shift();n&&n(void 0)}else this._count++}get count(){return this._count}}(1),Mn=new class{constructor(){this.events={}}on(n,e,t){const o=void 0===t||Array.isArray(t)?t:[t],{events:r}=this
;let s=r[n];return s||(s=[],r[n]=s),s.push({listener:e,filter:o}),()=>this.off(n,e)}once(n,e,t){const o=void 0===t||Array.isArray(t)?t:[t],r=this.on(n,((...n)=>(r(),e.bind(this)(...n))),o);return r}off(n,e){const t=this.events[n];t&&(this.events[n]=t.filter((n=>n.listener!==e)))}has(n,e){const t=this.events[n];return!!t&&t.some((n=>n.listener===e))}emit(n,...e){const t=this.events[n];if(t)for(const n of t){const{listener:t,filter:o}=n;if((!o||kn(e,o))&&!0===t(...e))return!0}return!1}
listening(n,e){const t=this.events[n];if(t){if(e){const n=Array.isArray(e)?e:[e];return t.filter((e=>!e.filter||kn(e.filter,n))).length}return t.length}return 0}},Dn={};Mn.on("xhr",(function(n){let e,t;void 0!==(e=n.id)&&void 0!==(t=Dn[e])&&t.handle(n)&&delete Dn[e]}));class Nn{constructor(n){this.readyState=Nn.UNSENT,this.status=0,this.statusText="",this.responseType="",this.async=!0,this.timeout=-1,this.canceled=!1,this.loaded=0,this.headers={},this.onreadystatechange=null,this.onopen=null,
this.onload=null,this.onloadstart=null,this.onprogress=null,this.onabort=null,this.onerror=null,this.ontimeout=null,this.upload={onprogress:null,loaded:0,total:void 0},this.open=function(n,e,t=!0,o,r){if(this.method=n,this.url=e,this.async=t,this.user=o,this.password=r,this.responseURL=e,!this.async)throw new Error("Synchronous requests are not supported");this.onopen&&this.onopen(this)},this.send=async function(n){this.mime_type&&(this.request_headers||(this.request_headers={}),
this.request_headers["content-type"]||(this.request_headers["content-type"]=this.mime_type)),(this.user||this.password)&&(this.request_headers||(this.request_headers={}),this.request_headers.Authorization="Basic "+r((this.user||"")+":"+(this.password||"")));const e=this.method||"GET",t=this.url;if(!t)throw new Error("No URL specified");const o={method:e,url:t,headers:this.request_headers,timeout:this.timeout>0?this.timeout:void 0,
anonymous:"withCredentials"in this?!this.withCredentials:this.options&&!!this.options.anonymous},s=n=>{this.method&&"POST"==this.method.toUpperCase()&&(this.request_headers||(this.request_headers={}),this.request_headers["content-type"]||(this.request_headers["content-type"]=n))};if(n){let e,t="";if(n instanceof Blob){e=n.size;const r=await pn(n);""===r?(this.readyState=0,this.statusText="data read error",this.onerror&&this.onerror(this)):(o.data=ln(r),s(t))}else e=n.length,t="text/plain",
o.data=ln(an(n));s(t),this.upload&&(this.upload.loaded=0,this.upload.total=e)}(async n=>{await In.acquire();try{let e;const t=await Promise.race([new Promise(((t,o)=>{e=p((()=>{e=void 0,o(`timeout: ${n.method}`)}),15e3)})),N.runtime.sendNativeMessage("application",n)]);return void 0!==e&&a(e),t}catch(n){throw console.error("sendNativeMessage",n),n}finally{In.release()}})({method:"xhr",id:this.id,details:o}),n&&this.upload&&this.upload.onprogress&&this.upload.onprogress.apply(this,[this.upload])
},this.abort=function(){this.canceled=!0,delete Dn[this.id],this.onabort&&this.onabort(this)},this.overrideMimeType=function(n){this.mime_type=n},this.getAllResponseHeaders=function(){const n=this.headers;return Object.keys(n).map((function(e){const t=n[e];return t&&t.includes(",")?t.split(",").map((function(n){return e+":"+n.trimStart()})).join("\r\n"):e+":"+t})).join("\r\n")},this.getResponseHeader=function(n){return this.headers[n]},this.setRequestHeader=function(n,e){
null!==e&&(this.request_headers||(this.request_headers={}),this.request_headers[n.trim().toLowerCase()]=e.trim())},this.toString=function(){return"[object XMLHttpRequest]"},this.handle=function(n){const e=n.data,t=()=>{this.onprogress&&this.onprogress(this)};if(1==e.readyState&&"loaded"in e)this.upload&&this.upload.onprogress&&(this.upload.loaded=e.loaded||0,this.upload.total=e.total,this.upload.onprogress.apply(this,[this.upload]));else if(e.readyState!=this.readyState){
if(e.timeout)return void(this.ontimeout&&this.ontimeout.apply(this,[this]));if(Object.assign(this,e),this.onreadystatechange&&this.onreadystatechange(this),4==e.readyState){const n=e.status;return n>=400?this.onload&&this.onload.apply(this,[this]):n>=300||(n>=200?this.onload&&this.onload.apply(this,[this]):n>=100||0===n&&this.onerror&&this.onerror.apply(this,[this])),!0}e.readyState>1?t():1==e.readyState&&this.onloadstart&&this.onloadstart.apply(this,[this])
}else 3==e.readyState&&(Object.assign(this,e),t())},this.id=Date.now()+(Math.random()+1).toString(36).substring(7),this.options=n,Dn[this.id]=this}get lengthComputable(){return null!=this.total}get target(){return this}get response(){const n=qn(this.responseData);if(null==n)return null;const e=this.responseType?this.responseType.toLowerCase():"",t=(this.headers?this.headers["content-type"]:null)||"binary/octet-stream";return Bn(n,e,t)}get responseText(){const n=qn(this.responseData)
;return null==n?null:Bn(n,this.mime_type?"binary":"text")}get responseXML(){const n=qn(this.responseData);if(null==n)return null;const e=(this.headers?this.headers["content-type"]:null)||"";return Bn(n,"document",e.split(";")[0])}}Nn.UNSENT=0,Nn.OPENED=1,Nn.HEADERS_RECEIVED=2,Nn.LOADING=3,Nn.DONE=4,Nn.toString=function(){return"[XMLHttpRequest]"};const Bn=function(n,e,t){let o;if("arraybuffer"==e)o=dn(n);else if("blob"==e)o=new Blob([dn(n)],{type:t});else if("json"==e)o=JSON.parse(cn(n));else{
if("document"==e)return"text/xml"==t&&v?(new v).parseFromString(n,t):null;if("text"==e||t&&(t.match(/charset=(utf[-_]?8)/i)||"text/html"==t))try{o=cn(n)}catch(e){o=n}else o=n}return o},qn=n=>{try{if(void 0===n)return;return o(n)}catch(n){return void console.warn("XmlHttpRequest:",n)}};let Pn={},Hn=()=>Z.resolve({done:()=>{}})
;const $n=["internal","user-agent","accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","date","dnt","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],Fn={"cache-control":"no-cache",pragma:"no-cache"},Vn={"cache-control":"max-age=0, must-revalidate"},Gn=n=>{const e={},t={};return Object.keys(n).forEach((o=>{let r=o,s=n[o];var a;a=o.toLowerCase(),
$n.includes(a)||0===a.indexOf("proxy-")||0===a.indexOf("sec-")?t[r]=s:e[r]=s})),{...Object.keys(t).length?{forbidden:t}:{},...Object.keys(e).length?{regular:e}:{}}},Xn=n=>({responseText:"",response:null,readyState:4,responseHeaders:"",status:0,statusText:"",error:n=n||"Forbidden"}),Jn=n=>n&&"object"==typeof n&&"type"in n&&"value"in n,Yn=n=>{if("Blob"===n.type)return new Blob([dn(n.value)]);if("File"===n.type){if(!n.name)return;return new File([dn(n.value)],n.name,{type:n.meta,
lastModified:n.lastModified||Date.now()})}if("FormData"==n.type){const e=new FormData;return Object.keys(n.value).forEach((t=>{const o="Array"===n.value[t].type,r=Yn(n.value[t]),s=o?r:[r];s.forEach(((n,o)=>{e.append(t,s[o])}))})),e}if("URLSearchParams"===n.type)return new URLSearchParams(n.value);if("Array"===n.type||"Object"===n.type){let e,t,o;"Object"===n.type?(o=Object.keys(n.value),t=n=>n<o.length?o[n]:null,e={}):(t=e=>e<n.value.length?e:null,e=[])
;for(let o,r=0;null!==(o=t(r));r++)e[o]=Yn(n.value[o]);return e}return n.value},Wn=n=>{const e={};return n&&n.split("\n").forEach((n=>{const t=n.match(/^([^:]+): ?(.*)/);if(t){const n=t[1].toLowerCase();e[n]=(void 0!==e[n]?e[n]+",":"")+(t[2]||"").replace(/,/g,"%2C")}})),e},Zn="tm-finalurl"+F.short_id.toLowerCase(),Kn="tm-setcookie"+F.short_id.toLowerCase(),Qn=(e,o,s,i,c,l)=>{const d=v,u=!e.redirect||"follow"===e.redirect;let h,m,g,b;const y=(n,t,o)=>{const r=[];let s;const a=o||n.headers;if(a){
const t=[],o=[];a.forEach(((n,e)=>{const a=e.toLowerCase();a.startsWith(Kn)?t.push(n):"set-cookie"===a?o.push(n):a===Zn?s=n:r.push(a+":"+n)})),s=u?s||n.url:e.url,(t.length?t:o).forEach((n=>r.push("set-cookie:"+n)))}const i=void 0===t?4:t,c=n.status||0,l=n.statusText||"";return{readyState:i,responseHeaders:r.join("\r\n"),finalUrl:s,status:c,statusText:l}},w=n=>{let e;b&&b(),m||(g?(e=y({status:408,statusText:"Request Timeout"}),
c("ontimeout")):"AbortError"==(null==n?void 0:n.name)?(e=Xn("aborted"),c("onabort",e)):(e=y({status:408,statusText:(null==n?void 0:n.message)||"Request Timeout"}),c("onerror",e)),m=!0,c("ondone",e))},k=e.responseType?e.responseType.toLowerCase():"";if("document"==k&&!d)return void w({name:"Error",message:"response type not supported"});const j=async(n,e,t)=>{let o;if(!t){const e=n;let t;t=s.no_blob||s.foreign_context?mn.BINARY:mn.OBJ_URL;const o=new vn({blob:e},t);return l("onpartial",{
tfd:await o.toTransferableData()}),void p((()=>o.dispose()),3e5)}o=n;const r=jn(o,e);r.forEach(((n,e)=>{l("onpartial",{partial:n,index:e,length:r.length})}))};let x=!1;const T=n=>{n&&(g=!0),_?_.abort():g?w():w({name:"AbortError",message:"Aborted by user"})};let _;return(async()=>{try{const E={};E.method=e.method||"GET";const A=(n=>{if(null==n)return;let e=_n.get(n);return e||(e=Ln(n),_n.set(n,e),e)})(e.url);let S,O;if(A){let n=!1;A.username&&(e.user=e.user||A.username,n=!0),
A.password&&(e.password=e.password||A.password,n=!0),S=n?(g=A,((v=v||{}).noprotocol?"":["about:"].includes(g.protocol)?g.protocol:g.protocol+"//")+(v.auth&&(g.username||g.password)?((n,e)=>{const t=n=>n.replace(/[^A-Za-z0-9-._~!$&'()*+,;=]/g,(n=>"%"+n.charCodeAt(0).toString(16).toUpperCase()));return t(n)+(e?":"+t(e):"")+"@"
})(g.username||"",g.password)+"@":"")+g.hostname+(!v.noport&&g.port&&g.port>0?":"+g.port:"")+(v.nopath?"":v.nofile?g.pathname.replace(/\/[^/]*$/,"/"):g.pathname+(v.nosearch?"":g.search))):e.url}else S=e.url;if(e.headers){const{forbidden:n,regular:t}=Gn(e.headers);O=t,n&&I.warn("xhr: setting forbidden headers in non-bg environment is not supported!",n)}if(u||(E.redirect=e.redirect,I.warn("xhr: setting redirect in non-bg environment is not supported!",e.redirect)),!b){
const{done:n}=await Hn(E.method);b=()=>{n(),b=void 0}}e.nocache?E.cache="reload":e.revalidate&&(E.cache="default",O=O||{},O={...O,...Vn}),e.anonymous?E.credentials="omit":E.credentials="include",e.user&&e.password&&(O=O||{},O.Authorization="Basic "+r(e.user+":"+e.password)),O&&(E.headers=new Headers(O)),void 0!==e.data&&null!==e.data&&(Jn(e.data)&&"typified"===e.data_type?E.body=Yn(e.data):"string"==typeof e.data?E.body=e.data:E.body=JSON.stringify(e.data)),_=n?new n:void 0,
_&&(E.signal=_.signal),c("onloadstart",y({status:0,statusText:""},1)),f(S,E).then((async n=>{var r;let u;if(h&&(a(h),h=null),b&&b(),m)return;let p=y(n,void 0,u);if(p.status>0&&(p.status<200||p.status>=399)&&i>0)return void Qn(e,o,s,i-1,c,l);const{partialSize:f,overrideMimeType:g,responseType:w}=e;if(n.ok)if(l("onreadystatechange",y(n,2,u)),"stream"==k){let t;if(t=null===(r=n.body)||void 0===r?void 0:r.getReader())for(;;){const{done:n,value:o}=await t.read();if(o){const n=new Blob([o])
;await j(n,parseInt(e.partialSize),!1)}if(n)break}}else if(f){let t;["arraybuffer","blob"].includes(k)||void 0!==g?(p.response=await n.blob(),t=!1):(p.response=await n.text(),t=!0),p=await(async(n,t)=>{if(e.partialSize){const o=n.response;["response","responseText","responseXML"].forEach((e=>{delete n[e]})),!x&&o&&(x=!0,await j(o,parseInt(e.partialSize),t))}return n})(p,t)}else if(void 0!==w){let e
;if("arraybuffer"==k)p.response=await n.arrayBuffer();else if("blob"==k)p.response=await n.blob();else if("document"==k){e=(Wn(p.responseHeaders)["content-type"]||"text/xml").toString().split(";")[0];const t=new d;p.response=t.parseFromString(await n.text(),e)}else if("json"==k){const e=await n.text();p.response=JSON.parse(e)}else I.warn("xhr: responseType",k," is not implemented!"),p.responseText=p.response=await n.text()}else if(void 0!==g&&t){
const e=await n.arrayBuffer(),o=(g.toLowerCase().match(/charset=([^;]+)/)||[])[1];p.responseText=p.response=new t(o).decode(e)}else{const e=await n.text();p.responseText=p.response=e}else if(p.responseXML=void 0,"follow"!==e.redirect&&"opaqueredirect"==n.type){const e=y(n,2,u);e.responseText=e.response=p.responseText=p.response=void 0,e.status=p.status=301,l("onreadystatechange",e)}else{l("onreadystatechange",y(n,2,u));try{if("stream"==k){const t=await n.text(),o=new Blob([t])
;await j(o,parseInt(e.partialSize),!1)}else p.responseText=p.response=await n.text()}catch(n){}}c("onreadystatechange",p),c("onload",p),c("ondone",p)})).catch((async n=>{w(n)})),void 0!==e.timeout&&null!==e.data&&(h=p((()=>{h=null,T(!0)}),e.timeout))}catch(n){b&&b();const e=n;I.error(e.message,e.stack);const t=Xn(e.message);c("onerror",t),c("ondone",t)}var g,v})(),{abort:()=>T()}},ne=(n,e,t,o,r,s)=>{const a=n.responseType?n.responseType.toLowerCase():"";let i,c;n.anonymous&&(i=Pn.mozAnon?{
mozAnon:!0}:{anonymous:!0});const l=U;if(!l)throw new Error("internal error: XMLHttpRequest not available!");const d=new l(i),u=e=>{let t="",o=n.url;if(d.readyState>=2){let n;t=d.getAllResponseHeaders(),t&&(t=t.replace(/tm-finalurl[0-9a-zA-Z]*: .*[\r\n]{1,2}/i,""),t=t.replace(/tm-setcookie[0-9a-zA-Z]+-[0-9]+:/i,"set-cookie:")),(n=d.getResponseHeader(Zn)||d.responseURL)&&(o=n)}const r={readyState:d.readyState,responseHeaders:t,finalUrl:o,status:d.readyState>=2?d.status:0,
statusText:d.readyState>=2?d.statusText:""};return e&&4==d.readyState?d.responseType?(r.responseXML=void 0,r.responseText=void 0,r.responseType=d.responseType,r.response=d.response):(r.responseXML=d.responseXML||void 0,r.responseText=d.responseText,r.response=d.response):(r.responseXML=void 0,r.responseText="",r.response=void 0),r};let h=!1;const m=async e=>{if(n.partialSize){const o=e.response,r=!["arraybuffer","blob"].includes(a);["response","responseText","responseXML"].forEach((n=>{
delete e[n]})),!h&&o&&(h=!0,await(async(n,e,o)=>{let r;if(!o){const e=n;let o;o=t.no_blob||t.foreign_context?mn.BINARY:mn.OBJ_URL;const r=new vn({blob:e},o);return s("onpartial",{tfd:await r.toTransferableData()}),void p((()=>r.dispose()),3e5)}r=n;const a=jn(r,e);a.forEach(((n,e)=>{s("onpartial",{partial:n,index:e,length:a.length})}))})(o,parseInt(n.partialSize),r))}return e},f=en({threads:1}),g=n=>e=>f.add((()=>n(e)));return(async()=>{n.method=n.method||"GET"
;const{done:i}=await Hn(n.url,n.method),l=async n=>{await r("ondone",n),i()},h={onload:g((async()=>{let a=u(!0);a.status>0&&(a.status<200||a.status>=300)&&o>0?ne(n,e,t,o-1,r,s):(n.partialSize&&(a=await m(a)),await r("onload",a),4==a.readyState&&await l(a))})),onerror:g((async()=>{const a=u();4==a.readyState&&200!=a.status&&0!=a.status&&o>0?ne(n,e,t,o-1,r,s):(await r("onerror",a),await l(a))})),onloadstart:g((async()=>{await s("onloadstart",(()=>u()))})),onreadystatechange:g((async()=>{
await s("onreadystatechange",(async()=>{let n=u();return n=await m(n),n}))})),onprogress:g((async n=>{await s("onprogress",(async()=>{let e=u();return e=await m(e),b(n,e)}))})),ontimeout:g((async()=>{const n=u();await r("ontimeout"),await l(n)})),onabort:g((async()=>{const n=Xn("aborted");await r("onabort"),await l(n)}))},p={onuploadprogress:g((async n=>{await s("onuploadprogress",(async()=>b(n)))}))},f=0==Object.keys(h).concat(["ondone"]).filter((n=>!!e[n])).length
;if(f)throw new Error("Synchronous XHR is not supported anymore");const b=(n,e)=>{let t,o,r,s,a,i;try{if(n.lengthComputable||n.total>0)t=n.loaded,o=n.total;else if(e){const r=!d.responseType||["","text"].includes(d.responseType)?d.responseText:null;let s=Number(Wn(e.responseHeaders)["content-length"]||"");const a=e.readyState>2&&r?r.length:0;0==s&&(s=-1),t=n.loaded||a,o=n.total||s}s=n.lengthComputable,r=t,a=t,i=o}catch(n){}return Object.assign(e||{},{lengthComputable:s,loaded:r,done:t,
position:a,total:o,totalSize:i})},y=["ontimeout","onload","onerror","onabort"];Object.keys(h).forEach((n=>{(e[n]||y.includes(n))&&(d[n]=h[n])}));const w={onuploadprogress:"onprogress"};d.upload&&Object.keys(p).forEach((n=>{const t=w[n];t&&e[n]&&(d.upload[t]=p[n])}));try{let e;if(d.open(n.method,n.url,!f,n.user,n.password),n.headers){const{forbidden:t,regular:o}=Gn(n.headers);e={...o,...t}}(n.nocache||n.revalidate)&&(e=e||{},n.nocache?e={...e,...Fn}:n.revalidate&&(e={...e,...Vn})),
e&&Object.keys(e).forEach((n=>{try{d.setRequestHeader(n,e[n])}catch(t){I.warn("xhr: rejected header",n,e[n])}})),void 0!==n.overrideMimeType&&d.overrideMimeType(n.overrideMimeType),n.partialSize?["arraybuffer","blob"].includes(a)?d.responseType=n.responseType="blob":delete n.responseType:void 0!==n.responseType&&(c=n.responseType.toLowerCase(),"xml"!=c&&"headers"!=c&&"stream"!=c&&(d.responseType=c)),void 0!==n.timeout&&(d.timeout=n.timeout),
void 0!==n.data?Jn(n.data)&&"typified"===n.data_type?d.send(Yn(n.data)):"string"==typeof n.data?d.send(n.data):d.send(JSON.stringify(n.data)):d.send()}catch(n){const e=n;I.error(e.stack);const t=Xn(e.message);r("onerror",t),await l(t)}})(),{abort:function(){d.abort()}}},ee="mtm_visitor",te="default",oe="pageview",re="script_update",se="script",ae="cloud",ie="event",ce="pageview",le="ping",de="https://a.tampermonkey.net/matomo.php",ue=R;let he,pe,me,fe=null;const ge=()=>({url:de,siteId:4,
tracker:{[te]:{enabled:Ae(4,1)},[se]:{enabled:!0},[re]:{enabled:Ae(4,10)},[ae]:{enabled:Ae(4,5e-4)}}}),be=()=>({url:de,siteId:5,tracker:{[te]:{enabled:Ae(5,10)},[se]:{enabled:!0},[re]:{enabled:Ae(5,10)},[ae]:{enabled:Ae(5,.001)}}}),ye=()=>({url:de,siteId:6,tracker:{[te]:{enabled:Ae(6,50)},[se]:{enabled:!0},[re]:{enabled:Ae(6,10)},[ae]:{enabled:Ae(6,.01)}}}),we={default:be,gcal:be,iikm:ge,fcmf:ge,saap:()=>({url:de,siteId:7,tracker:{[te]:{enabled:!0},[se]:{enabled:!0},[re]:{enabled:Ae(7,10)},
[ae]:{enabled:Ae(7,.01)}}}),fire:ye,firb:ye,dhdg:()=>({url:de,siteId:3,tracker:{[te]:{enabled:Ae(3,1)},[se]:{enabled:!0},[re]:{enabled:Ae(3,10)},[ae]:{enabled:Ae(3,5e-4)}}}),mfdh:be,heif:()=>({url:"http://a.userscript.grobilan:8081/matomo.php",siteId:2,tracker:{[te]:{enabled:!0}}})};let ve;const ke=[{msg:"a disconnected port"},{msg:"Function.prototype.apply: Arguments list has wrong type",url:"event_bindings"},{msg:"Script error."
}],je=n=>[...Array(n)].map((()=>Math.floor(16*Math.random()).toString(16))).join(""),xe=n=>{if(!ue)return;const e=[n.uuid,n.createTs,n.visitCount,n.currentVisitTs,n.lastVisitTs].join(".");ue.setItem(ee,e)},Te=n=>{const e=n||te,t=ve.tracker[e]||ve.tracker[te];return t.enabled?{url:ve.url,siteId:ve.siteId,options:t}:null},_e=je(6),Ee=async(n,e,t)=>{if(!n)return;const o=(()=>{if(!ue)return;const n=ue.getItem(ee);if(!n)return;const e=n.split(".");if(e.length>=5){e.unshift("0");const[n,t,o,r,s,a]=e
;return{createdNow:!1,newVisitor:n,uuid:t,createTs:o,visitCount:r,currentVisitTs:s,lastVisitTs:a}}})()||(()=>{const n=Math.floor(Date.now()/1e3).toString(),e={createdNow:!0,newVisitor:"1",uuid:je(16),createTs:n,visitCount:"0",currentVisitTs:n,lastVisitTs:""};return xe(e),e})();let r;const s=new Date,a={idsite:n.siteId,rec:1,action_name:E?E.title:u.href||F.short_id,url:u.href,_id:o.uuid,rand:je(4),apiv:1,h:s.getHours(),m:s.getMinutes(),s:s.getSeconds(),cookie:1,pv_id:_e},i={...a,
_idts:Number(o.createTs),_idvc:Number(o.visitCount),_viewts:Number(o.lastVisitTs),res:A?`${A.width}x${A.height}`:"0x0"};if(e==ce){const n=pe?{gt_ms:pe}:{},e={...a,...i,...n,new_visit:1};xe((n=>{const e=Math.floor(Date.now()/1e3).toString();return n.newVisitor="0",n.visitCount=(Number(n.visitCount)+1).toString(),n.lastVisitTs=n.currentVisitTs,n.currentVisitTs=e,n})(o)),r=e}else if(e==ie){if(!t)return;r={...a,ca:1,e_c:t.category,e_a:t.action,e_n:t.name,e_v:t.value}}else{if(e!=le)return;r={...a,
...i,ping:1}}r=Object.assign(a,r);const c=`${n.url}?${d=r,Object.entries(d).map((([n,e])=>void 0===e?null:l(n)+"="+l(e))).filter((n=>n)).join("&")}`;var d;if(E){const n=E.createElement("img");n.src=c,n.onload=()=>{var e;null===(e=n.parentNode)||void 0===e||e.removeChild(n)},n.onerror=()=>{var e;null===(e=n.parentNode)||void 0===e||e.removeChild(n)},(E.body||E.head||E.documentElement).appendChild(n)}else try{await f(c)}catch(n){I.warn("stats:",n)}},Ae=(n,e)=>{let t=100*Math.random()<e;if(ue)try{
let o,r;const s=["wsr",n,e].join("_"),a=Date.now(),i=864e7;if(o=ue.getItem(s)){try{r=JSON.parse(o)}catch(n){}(!r||r.ts+i<a)&&(r={ts:a,w:t})}else r={ts:a,w:t};t=r.w,ue.setItem(s,JSON.stringify(r))}catch(n){}return t};let Se,Oe=W();const Re=async(n,e,t)=>{if(!await(null===fe&&Oe?Oe.promise():W.Pledge(!!fe)))return;void 0===t&&(e+=" "+u.href,t="");let o=!1;for(const t of ke){if(!t.msg&&!t.url)return;t.msg&&-1==n.indexOf(t.msg)||t.url&&-1==e.indexOf(t.url)||(o=!0)}o||Ee(Te("error"),ie,{
category:"Error",action:n,name:e+":"+t})},Ue=en({threads:3});((n,e)=>{Se=`${(null==e?void 0:e.version)||""} `;const t=F.short_id;ve=(we[t]||we[te])(),w("error",(n=>{const{message:e,filename:t,lineno:o,colno:r,error:s}=n;((n,e,t,o,r)=>{let s="";if(r)try{s=r.stack||""}catch(n){}Re(n.toString(),Se+"off@"+V.urls.prepareForReport(e||""),[t+":"+o,s].join(";"))})(e,t,o,r,s)}));const o=n=>{let e="";try{e=n.stack}catch(n){}
Re("CSP violation of "+n.effectiveDirective,Se+"off@"+V.urls.prepareForReport(n.documentURI),[n.sourceFile," -> ",n.lineNumber+":"+n.columnNumber,e].join(";"))};E?E.addEventListener("securitypolicyviolation",(n=>o(n))):w("securitypolicyviolation",(n=>o(n))),(null==e?void 0:e.started)&&(pe=Date.now()-e.started.getTime())})(0,{version:"?"});let Le=()=>{const n=W();return(e=>{const t="string"==typeof e?[e]:e,o=()=>{t.every((n=>!!Y[n]))?(Le=W.Pledge,n.resolve()):G.push(o)};t.forEach((n=>{
if(void 0===Y[n]&&void 0===X[n]){const e=p((()=>{J[n]||(delete X[n],(n=>{J[n]=!0})(n),((n,e)=>{let t=1;const o=()=>{0==--t&&e&&e()};("string"==typeof n?[n]:n).forEach((n=>{t++;try{!function(n,e){{const t=document.createElement("script");t.setAttribute("src",n),e&&(t.onload=()=>e(!0),t.onerror=()=>e(!1)),(document.head||document.body||document.documentElement||document).appendChild(t)}}(n,(e=>{e||I.warn("registry: self.load "+n+" failed! "),o()}))}catch(e){
I.warn("registry: self.load "+n+" failed! ",e),o()}})),o()})(V.getURL(n+".js"),(()=>{(n=>{if(!Y[n]){let e;Y[n]=!0,delete J[n],(e=X[n])&&e(),(()=>{const n=G;G=[];for(const e of n)e()})()}})(n)})))}),0);X[n]=()=>{a(e),delete X[n]}}})),o()})(["vendor/saveas/filesaver"]),n.promise()};const Ce=(()=>{const n=[],e=[];let t;const o=()=>{if(!t||Me&&!e.length)return;const n=e.shift();t({result:n,poll:Me||e.length?1:0}),t=void 0};return{send:n=>{e.push(n),o()},message:(r,s)=>{const{details:a}=r
;if("message"in a){const{message:e}=a;n.forEach((n=>{n(e)}))}if(t){const n=e.length;t({result:void 0,poll:n?1:0})}t=s,o()},onMessage:{addListener:e=>{n.push(e)}}}})(),ze=(n=>{const e={};let t;const o=t=>{let o=[],s=[];const a=()=>{o=[],s=[],i=null,delete e[t]};let i={postMessage:e=>{(e=>{if(!i)throw"port is disconnected";n.send({response_id:t,value:e})})(e)},onMessage:{addListener:n=>{o.push(n)}},onDisconnect:{addListener:n=>{s.push(n)}},disconnect:()=>{a(),r(t)}};return e[t]={message:n=>{
o&&o.forEach((e=>e(n)))},disconnect:()=>{s.length&&s.forEach((n=>n())),a()}},i},r=e=>{n.send({response_id:e,disconnect:!0})};return n.onMessage.addListener((n=>{let r;if(n.connect){if(!n.destination||!n.response_id)throw"invalid message";t&&t(n.destination,o(n.response_id))}else{if(!n.response_id)throw"invalid message";if(!(r=e[n.response_id]))return void console.warn("ports: unknown id",n.response_id,n);if(n.disconnect)r.disconnect();else{const e=n.value;e&&r.message(e)}}})),{
connect:function(e){const t=Math.random().toString(36).slice(2);return((e,t)=>{n.send({response_id:t,connect:!0,destination:e})})(e,t),o(t)},onConnect:{addListener:n=>{t=n}}}})(Ce),Ie=async(n,e)=>{const{action:t}=n;if("port"===t)Ce.message(n,e);else if("download"===t){const{details:t,id:o}=n,{name:r,tfd:s}=t,a=vn.fromTransferableData(s),i=a&&await a.toBlob();if(!i)return console.warn("downs: invalid transferable"),void e({result:{id:o,error:"not_supported"}});Le().then((async()=>{
const n=g.saveAs;return n?Ue.add((async()=>{var t;n(i,r),await(t=500,new Z((n=>nn(n,t)))),e({result:{id:o}})})):(e({result:{id:o,error:"failed"}}),void console.warn("Unable to load saveAs!"))}))}else if("config"===t){const{config:t,version:o}=n;Se=o,t.statistics_enabled&&(fe=!0,Oe&&(Oe.resolve(fe),Oe=void 0),fe?(he&&(Ee(Te(oe),ce),he=!1),me=h((()=>Ee(Te(oe),le)),864e5)):me&&(s(me),me=void 0)),e({result:!0})}else{const{doc:o}=n;if("xmlToWebdavResults"===t){const n=await(async n=>{if(v){
const e=on(n);let t;if(!e||!(t=rn(e))||!t.childNodes)return;return(async n=>{const e=[],t=n.getElementsByTagNameNS("*","response");for(let n=0;n<t.length;n++){const o=t[n];let r=sn(o,"href");if(null==r)continue;r=r.replace(/\/$/,"");const s=o.getElementsByTagNameNS("*","propstat")[0].getElementsByTagNameNS("*","prop")[0],a=sn(s,"getlastmodified"),i=sn(s,"getetag"),c=parseInt(sn(s,"getcontentlength")||"")
;if(s.getElementsByTagNameNS("*","resourcetype")[0].getElementsByTagNameNS("*","collection")[0]);else{const n={etag:i,name:r,id:r,modifiedTime:new Date(a||0).getTime(),size:c>=0?c:void 0,removed:-1==c};e.push(n)}}return e})(t)}throw new Error("DOMParser not available")})(o);e({result:n})}else if("xmlToCursor"===t){const n=await(async n=>{if(v){const e=on(n);let t;if(!e||!(t=rn(e))||!t.childNodes)return;return(n=>sn(n,"td:cursor"))(t)}throw new Error("DOMParser not available")})(o);e({result:n})
}else if("xmlToResponseCodes"===t){const n=await(async n=>{if(v){const e=on(n);if(!e)return;return(n=>{var e;let t,o,r,s;if((t=n.childNodes[0])&&(o=t.getElementsByTagNameNS("*","status")[0])&&(r=null===(e=o.firstChild)||void 0===e?void 0:e.nodeValue)&&(s=r.match(/HTTP\/[0-9.]+ ([1-5][0-9][0-9])/)))return[parseInt(s[1])]})(e)}throw new Error("DOMParser not available")})(o);e({result:n})}else console.warn("offscreen: unknown action",t),e({error:"unknown_action"})}};let Me=0;const De={}
;ze.onConnect.addListener(((n,e)=>{if(Me++,"xhr"===n){const n=Math.random().toString(36).slice(2),t=()=>{const e=De[n];e?(e.abort(),delete De[n]):console.log("offscreen: xhr already deleted",n)};e.onMessage.addListener((o=>{if("abort"in o)t();else{const{details:t,options:r,callbacks:s}=o,a={};for(const n of Object.keys(s)){const t=t=>{e.postMessage({data:t,type:n})};a[n]=t}const i=a.ondone;a.ondone=t=>{i&&i(t),delete De[n],e.disconnect()},De[n]=((n,e,t)=>{const o={...e||{}},r=async(n,e)=>{
const t=o[n];t&&t("function"==typeof e?await e():e)},s=async(n,e)=>{o[n]&&(await r(n,e),o[n]=void 0)};if(!(t=t||{}).internal&&(a=n.url,!["https:","http:","data:","blob"].some((n=>a.startsWith(n))))){I.warn("xhr: invalid scheme of url:",n.url);const e=Xn("Invalid scheme");return s("onerror",e),void s("ondone",e)}var a
;const i=void 0!==n.responseType?n.responseType.toLowerCase():void 0,c=n.redirect&&"follow"!==n.redirect,l=!Pn.mozAnon&&n.anonymous||"stream"==i||c||!n.partialSize&&i||"typified"===n.data_type,d=n.fetch||!1;return l||d?Qn(n,o,t,n.retries||0,s,r):ne(n,o,t,n.retries||0,s,r)})(t,a,r)}})),e.onDisconnect.addListener((()=>{t(),r||(r=!0,Me--)}));const o=e.disconnect;let r=!1;e.disconnect=()=>{r||(r=!0,o(),Me--)}}})),V.onMessage.addListener(((n,e,t)=>{const{target:o,method:r,request:s}=n
;if("offscreen"===o&&"offscreen"===r){let n=!1;if(Ie(s,(e=>{n=!0,void 0!==e&&t(e)})),!n)return!0}}))})();