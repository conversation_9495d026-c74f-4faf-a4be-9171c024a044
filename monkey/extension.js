(()=>{function e(r){var i=n[r];if(void 0!==i)return i.exports;var s=n[r]={exports:{}};return t[r](s,s.exports,e),s.exports}var t={2672:e=>{e.exports={meta:{type:"suggestion",docs:{description:"aligns attributes in the metadata",category:"Stylistic Issues"},schema:[{type:"integer",minimum:1,default:2}],messages:{spaceMetadata:"The metadata is not spaced"},fixable:"code"},create:e=>{const t=e.options[0]||2,n=e.getSourceCode(),r=n.getAllComments();let i=!1,s=!1,o=[],a={},l={}
;for(const e of r.filter((e=>"Line"===e.type)))s||(i&&"==/UserScript=="===e.value.trim()?(l=e.loc.end,s=!0):i||"==UserScript=="!==e.value.trim()?i&&e.value.trim().startsWith("@")&&o.push({key:e.value.trim().slice(1).split(/\s/)[0],space:/^\S*(\s+)/.exec(e.value.trim().slice(1))[1].length,line:e.loc.start.line,comment:e}):(a=e.loc.start,i=!0));if(0===Object.keys(l).length&&(l=n.getLocFromIndex(n.getText().length)),0===o.length)return{};const c=Math.max(...o.map((e=>e.key.length)))+t
;return(o.map((e=>e.space)).sort()[0]<t||o.map((e=>e.key.length+e.space)).find((e=>e!==c)))&&e.report({loc:{start:a,end:l},messageId:"spaceMetadata",fix:function(e){const t=[];for(const r of o)if(r.key.length+r.space!==c){const i=/^(.*?@\S*)/.exec(n.getLines()[r.line-1])[1].length;t.push(e.replaceTextRange([n.getIndexFromLoc({line:r.line,column:i}),n.getIndexFromLoc({line:r.line,column:i+r.space})]," ".repeat(c-r.key.length)))}return t}}),{}}}},3316:(e,t,n)=>{const r=n(2598)
;e.exports=r("include",!1,(({attrVal:e,context:t})=>{e.val.startsWith("/")&&t.report({loc:{start:{line:e.loc.start.line,column:0},end:e.loc.end},messageId:"avoidRegExpInclude"})}),{avoidRegExpInclude:"Using a regular expression at '@include' can cause performance issues. Use a regular @include or @match instead."})},8039:e=>{e.exports={meta:{type:"suggestion",docs:{description:"ensure userscripts end with .user.js",category:"Best Practices"},schema:[{enum:["always","never"]}],messages:{
filenameExtension:"Rename '{{ oldFilename }}' to '{{ newFilename }}'"}},create:e=>{const t=e.getFilename();return"<input>"===t||"<text>"===t?{}:{Program(){(!t.endsWith(".user.js")&&(!e.options[0]||"always"===e.options[0])||t.endsWith(".user.js")&&"never"===e.options[0])&&e.report({loc:{column:0,line:1},messageId:"filenameExtension",data:{newFilename:t.replace("always"===e.options[0]?/.js$/:/.user.js$/,"always"===e.options[0]?".user.js":".js"),oldFilename:t}})}}}}},70:(e,t,n)=>{
const r=n(2598),i=["addElement","addStyle","addValueChangeListener","cookie","deleteValue","deleteValues","download","getResourceText","getResourceURL","getTab","getTabs","getValue","getValues","info","listValues","log","notification","openInTab","registerMenuCommand","removeValueChangeListener","saveTab","setClipboard","setValue","setValues","unregisterMenuCommand","webRequest","xmlhttpRequest"].map((e=>`GM_${e}`)),s=["addElement","addStyle","addValueChangeListener","cookie","deleteValue","deleteValues","download","getResourceText","getResourceUrl","getTab","getTabs","getValue","getValues","info","listValues","log","notification","openInTab","registerMenuCommand","removeValueChangeListener","saveTab","setClipboard","setValue","setValues","unregisterMenuCommand","webRequest","xmlHttpRequest"].map((e=>`GM.${e}`)),o=new Set([...i,...s,"none","unsafeWindow","window.close","window.focus","window.onurlchange"])
;e.exports=r("grant",!1,(({attrVal:e,context:t})=>{const n=e.val;o.has(n)||t.report({loc:{start:{line:e.loc.start.line,column:0},end:e.loc.end},messageId:"grantHasInvalidArgument",data:{argument:n}})}),{grantHasInvalidArgument:"'{{ argument }}' is not a valid @grant argument"})},8754:(e,t,n)=>{
const r=n(2598),i=new Set(["antifeature","author","connect","contributor","contributors","copyright","defaulticon","description","developer","downloadURL","exclude","grant","history","homepage","homepageURL","icon","icon64","icon64URL","iconURL","include","license","match","name","namespace","nocompat","noframes","require","resource","run-at","run-in","sandbox","source","supportURL","tag","unwrap","updateURL","version","website"].map((e=>`@${e}`))),s=["name","description","antifeature"].map((e=>new RegExp(`^@${e}(:\\S+)?$`)))
;e.exports=r("headers",!1,(({attrVal:e,context:t})=>{const n=t.options[0],r=new Set((n&&n.allowed||[]).map((e=>`@${e}`)));for(const n of e){const e=`@${n.key}`;i.has(e)||r.has(e)||s.some((t=>t.test(e)))||t.report({loc:{start:{line:n.loc.start.line,column:0},end:n.loc.end},messageId:"invalidHeader",data:{header:e}})}}),{invalidHeader:"'{{ header }}' is not a valid userscript header"},!1,/./,!0,[{type:"object",properties:{allowed:{type:"array"}},additionalProperties:!1}])},1763:e=>{e.exports={
meta:{type:"suggestion",docs:{description:"ensure userscripts have valid metadata",category:"Possible Errors"},messages:{metadataRequired:"Add metadata to the userscript",moveMetadataToTop:"Move the metadata to the top of the file",noClosingMetadata:"Closing metadata comment not found",noCodeBetween:"Code found between in metadata",attributeNotStartsWithAtTheRate:"Attributes should begin with @"},schema:[{type:"object",properties:{top:{enum:["required","optional"],default:"required"}},
additionalProperties:!1}]},create:e=>{const t=e.getSourceCode(),n=t.getAllComments(),r=t.lines;let i=!1,s=!1;for(const[t,n]of r.entries()){if(s)continue;const r={start:{line:t+1,column:0},end:{line:t+1,column:n.length}};i&&!n.trim().startsWith("//")&&n.trim()?e.report({loc:r,messageId:"noCodeBetween"
}):i&&n.trim().startsWith("//")&&"==/UserScript=="===n.trim().slice(2).trim()?s=!0:!i&&n.trim().startsWith("//")&&"==UserScript=="===n.trim().slice(2).trim()?i=!0:i&&!n.trim().slice(2).trim().startsWith("@")&&n.trim().slice(2).trim()&&e.report({loc:r,messageId:"attributeNotStartsWithAtTheRate"})}return{Program(t){0!==n.length&&n.find((e=>"==UserScript=="===e.value.trim()&&"Line"===e.type))?(n.find((e=>"==/UserScript=="===e.value.trim()&&"Line"===e.type))||e.report({
loc:n.find((e=>"==UserScript=="===e.value.trim()&&"Line"===e.type)).loc,messageId:"noClosingMetadata"}),e.options[0]&&e.options[0].top&&"required"!==e.options[0].top||"==UserScript=="===n[0].value.trim()&&1===n[0].loc.start.line||e.report({loc:n.find((e=>"==UserScript=="===e.value.trim()&&"Line"===e.type)).loc,messageId:"moveMetadataToTop"})):e.report({node:t,messageId:"metadataRequired"})}}}}},4350:e=>{e.exports={meta:{type:"suggestion",docs:{
description:"ensure atributes are prefixed by one space",category:"Possible Errors"},messages:{attributeNotPrefixedBySpace:"Attributes should be prefixed by one space"},schema:[]},create:e=>{const t=e.getSourceCode().lines;let n=!1,r=!1;for(const[i,s]of t.entries()){if(r)continue;const t=s.trim()
;n&&t.startsWith("//")&&"==/UserScript=="===t.slice(2).trim()?r=!0:!n&&t.startsWith("//")&&"==UserScript=="===t.slice(2).trim()?n=!0:n&&t.slice(2).trim().startsWith("@")&&t.startsWith("//")&&(!t.startsWith("// ")||t.startsWith("//  "))&&e.report({loc:{start:{line:i+1,column:0},end:{line:i+1,column:s.length}},messageId:"attributeNotPrefixedBySpace"})}return{}}}},230:(e,t,n)=>{const r=n(2598);e.exports=r("description",!0,(({attrVal:e,context:t})=>{let n=[]
;for(let r of e)n.includes(r.key)?t.report({loc:r.loc,messageId:"multipleDescriptions"}):n.push(r.key)}),{multipleDescriptions:"Include only one description for each language"},!1,/^description(:\S+)?$/,!0)},8295:(e,t,n)=>{const r=n(2598),i=/^name(:\S+)?$/;e.exports=r("name",!0,(({attrVal:e,context:t,metadata:n})=>{let r=[];for(let n of e)r.includes(n.key)?t.report({loc:n.loc,messageId:"multipleNames"}):r.push(n.key);const s=Object.values(n)
;if(s.find(((e,t)=>0!==t&&i.test(e[0]?e[0].key:e.key)&&!i.test(s[t-1][0]?s[t-1][0].key:s[t-1].key)))){const n=t.getSourceCode(),r=n.getAllComments(),i=r.find((e=>"==/UserScript=="===e.value.trim()&&"Line"===e.type));t.report({loc:{start:r.find((e=>"==UserScript=="===e.value.trim()&&"Line"===e.type)).loc.start,end:i?i.loc.end:{line:n.lines.length,column:0}},messageId:"nameAtBeginning",fix:function(n){let r=[];for(let i of e){Array.isArray(i)||(i=[i])
;for(let e of i)r.push(n.removeRange(e.comment.range.map(((n,r)=>0===r?n-t.getSourceCode().lines[e.loc.start.line-1].split("//")[0].length-1:n))))}return r.push(n.insertTextAfterRange(t.getSourceCode().getAllComments().find((e=>"==UserScript=="===e.value.trim())).range,e.sort(((e,t)=>"name"===e.key?-1:"name"===t.key?1:0)).map((e=>`\n${t.getSourceCode().lines[e.loc.start.line-1].split("//")[0]}//${e.comment.value}`)).join(""))),r}})}}),{multipleNames:"Include only one name for each language",
nameAtBeginning:"The names should be at the beginning of the metadata"},!0,i,!0)},3880:(e,t,n)=>{const r=n(2598);e.exports=r("version",!0,(({attrVal:e,index:t,context:n})=>{t>0&&n.report({loc:e.loc,messageId:"multipleVersions"}),/^([^\s.]+)(\.[^\s.]+)*$/.test(e.val)||n.report({loc:{start:{line:e.loc.start.line,column:/^(\s*\/\/\s*)/.exec(n.getSourceCode().lines[e.comment.loc.start.line])[1].length-1},end:e.loc.end},messageId:"invalidVersion"})}),{multipleVersions:"Include only one version",
invalidVersion:"Invalid version"})},2215:(e,t,n)=>{const r=n(2598),i=["downloadURL","updateURL"];e.exports=r(i,!1,(({attrVal:e,metadata:t,context:n,keyName:r})=>{const s=i.find((e=>e!==r));t[s]||n.report({loc:e.loc,messageId:"missingAttribute",data:{attribute:s},fix:function(t){return t.insertTextAfterRange(e.comment.range,`\n${n.getSourceCode().lines[e.comment.loc.start.line-1].replace(/^(\s*\/\/\s*@)\S*/,"$1"+s)}`)}})}),{
missingAttribute:"Didn't find attribute '{{ attribute }}' in the metadata"},!0)},1933:(e,t,n)=>{const r=n(2598),i=["homepage","homepageURL"];e.exports=r(i,!1,(({attrVal:e,metadata:t,context:n,keyName:r})=>{const s=i.find((e=>e!==r));t[s]||n.report({loc:e.loc,messageId:"missingAttribute",data:{attribute:s},fix:function(t){return t.insertTextAfterRange(e.comment.range,`\n${n.getSourceCode().lines[e.comment.loc.start.line-1].replace(/^(\s*\/\/\s*@)\S*/,"$1"+s)}`)}})}),{
missingAttribute:"Didn't find attribute '{{ attribute }}' in the metadata"},!0)},2598:e=>{e.exports=function(e,t,n=!1,r={},i=!1,s=new RegExp("^("+("string"==typeof e?e:e.join("|"))+")$"),o=!1,a){return"string"==typeof e&&(e=[e]),{meta:{type:"suggestion",docs:{description:`${t?"require "+(n?"and validate ":""):"validate "}${e.join(" and ")} in the metadata for userscripts`,category:"Best Practices"},schema:t?[{enum:["required","optional"],default:"required"}]:a||void 0,messages:{
missingAttribute:`Didn't find attribute '${e}' in the metadata`,...r},fixable:i?"code":void 0},create:e=>{const r=e.getSourceCode().getAllComments();let i=!1,a=!1,l=!1,c={};for(const e of r.filter((e=>"Line"===e.type)))if(!l)if(i&&"==/UserScript=="===e.value.trim())l=!0;else if(i||"==UserScript=="!==e.value.trim()){if(i&&e.value.trim().startsWith("@")){const t=e.value.trim().slice(1).split(/[\t ]/)[0],n={val:e.value.trim().slice(1).split(/[\t ]/).slice(1).join(" ").trim(),loc:e.loc,comment:e,
key:t};if(c[t]){Array.isArray(c[t])||(c[t]=[c[t]]),c[t].push(n);continue}c[t]=n}}else i=!0,a=!0;const d=Object.keys(c);if(!t||!a||e.options[0]&&"required"!==e.options[0]||d.find((e=>s.test(e)))){if(n&&d.find((e=>s.test(e))))if(o){const t=[];for(const e in d)s.test(d[e])&&t.push(+e);const r=t.map((e=>c[d[e]])).reduce(((e,t)=>Array.isArray(t)?[...e,...t]:[...e,t]),[]);n({attrVal:r,index:[...r.keys()],indexMatch:t.reduce(((e,t)=>Array.isArray(c[d[t]])?[...e,...c[d[t]].map((()=>d))]:[...e,t]),[]),
metadata:c,context:e,keyName:t.map((e=>d[e]))})}else for(const t in d)if(s.test(d[t]))if(Array.isArray(c[d[t]]))for(const[r,i]of c[d[t]].entries())n({attrVal:i,index:r,indexMatch:t,metadata:c,context:e,keyName:d[t]});else n({attrVal:c[d[t]],index:0,indexMatch:t,metadata:c,context:e,keyName:d[t]})}else e.report({loc:r.find((e=>"==UserScript=="===e.value.trim()&&"Line"===e.type)).loc,messageId:"missingAttribute"});return{}}}}},2462:(e,t,n)=>{e.exports=function e(t,n,r){function i(o){if(!n[o]){
if(!t[o]){if(s)return s(o,!0);var a=new Error("Cannot find module '"+o+"'");throw a.code="MODULE_NOT_FOUND",a}var l=n[o]={exports:{}};t[o][0].call(l.exports,(function(e){return i(t[o][1][e]||e)}),l,l.exports,e,t,n,r)}return n[o].exports}for(var s=void 0,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(e,t,n){"use strict";var r=e("./utils"),i=e("./support"),s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";n.encode=function(e){
for(var t,n,i,o,a,l,c,d=[],A=0,u=e.length,h=u,p="string"!==r.getTypeOf(e);A<e.length;)h=u-A,p?(t=e[A++],n=A<u?e[A++]:0,i=A<u?e[A++]:0):(t=e.charCodeAt(A++),n=A<u?e.charCodeAt(A++):0,i=A<u?e.charCodeAt(A++):0),o=t>>2,a=(3&t)<<4|n>>4,l=h>1?(15&n)<<2|i>>6:64,c=h>2?63&i:64,d.push(s.charAt(o)+s.charAt(a)+s.charAt(l)+s.charAt(c));return d.join("")},n.decode=function(e){var t,n,r,o,a,l,c=0,d=0;if("data:"===e.substr(0,5))throw new Error("Invalid base64 input, it looks like a data url.")
;var A,u=3*(e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"")).length/4;if(e.charAt(e.length-1)===s.charAt(64)&&u--,e.charAt(e.length-2)===s.charAt(64)&&u--,u%1!=0)throw new Error("Invalid base64 input, bad content length.");for(A=i.uint8array?new Uint8Array(0|u):new Array(0|u);c<e.length;)t=s.indexOf(e.charAt(c++))<<2|(o=s.indexOf(e.charAt(c++)))>>4,n=(15&o)<<4|(a=s.indexOf(e.charAt(c++)))>>2,r=(3&a)<<6|(l=s.indexOf(e.charAt(c++))),A[d++]=t,64!==a&&(A[d++]=n),64!==l&&(A[d++]=r);return A}},{"./support":30,
"./utils":32}],2:[function(e,t){"use strict";function n(e,t,n,r,i){this.compressedSize=e,this.uncompressedSize=t,this.crc32=n,this.compression=r,this.compressedContent=i}var r=e("./external"),i=e("./stream/DataWorker"),s=e("./stream/DataLengthProbe"),o=e("./stream/Crc32Probe");s=e("./stream/DataLengthProbe"),n.prototype={getContentWorker:function(){var e=new i(r.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new s("data_length")),t=this
;return e.on("end",(function(){if(this.streamInfo.data_length!==t.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")})),e},getCompressedWorker:function(){return new i(r.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},n.createWorkerFrom=function(e,t,n){
return e.pipe(new o).pipe(new s("uncompressedSize")).pipe(t.compressWorker(n)).pipe(new s("compressedSize")).withStreamInfo("compression",t)},t.exports=n},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(e,t,n){"use strict";var r=e("./stream/GenericWorker");n.STORE={magic:"\0\0",compressWorker:function(){return new r("STORE compression")},uncompressWorker:function(){return new r("STORE decompression")}},n.DEFLATE=e("./flate")},{
"./flate":7,"./stream/GenericWorker":28}],4:[function(e,t){"use strict";var n=e("./utils"),r=function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}();t.exports=function(e,t){return void 0!==e&&e.length?"string"!==n.getTypeOf(e)?function(e,t,n){var i=r,s=0+n;e^=-1;for(var o=0;o<s;o++)e=e>>>8^i[255&(e^t[o])];return-1^e}(0|t,e,e.length):function(e,t,n){var i=r,s=0+n;e^=-1;for(var o=0;o<s;o++)e=e>>>8^i[255&(e^t.charCodeAt(o))];return-1^e
}(0|t,e,e.length):0}},{"./utils":32}],5:[function(e,t,n){"use strict";n.base64=!1,n.binary=!1,n.dir=!1,n.createFolders=!0,n.date=null,n.compression=null,n.compressionOptions=null,n.comment=null,n.unixPermissions=null,n.dosPermissions=null},{}],6:[function(e,t){"use strict";var n;n="undefined"!=typeof Promise?Promise:e("lie"),t.exports={Promise:n}},{lie:37}],7:[function(e,t,n){"use strict";function r(e,t){a.call(this,"FlateWorker/"+e),this._pako=null,this._pakoAction=e,this._pakoOptions=t,
this.meta={}}var i="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,s=e("pako"),o=e("./utils"),a=e("./stream/GenericWorker"),l=i?"uint8array":"array";n.magic="\b\0",o.inherits(r,a),r.prototype.processChunk=function(e){this.meta=e.meta,null===this._pako&&this._createPako(),this._pako.push(o.transformTo(l,e.data),!1)},r.prototype.flush=function(){a.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},
r.prototype.cleanUp=function(){a.prototype.cleanUp.call(this),this._pako=null},r.prototype._createPako=function(){this._pako=new s[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var e=this;this._pako.onData=function(t){e.push({data:t,meta:e.meta})}},n.compressWorker=function(e){return new r("Deflate",e)},n.uncompressWorker=function(){return new r("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(e,t){"use strict";function n(e,t,n,r){
i.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=t,this.zipPlatform=n,this.encodeFileName=r,this.streamFiles=e,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}var r=e("../utils"),i=e("../stream/GenericWorker"),s=e("../utf8"),o=e("../crc32"),a=e("../signature"),l=function(e,t){var n,r="";for(n=0;n<t;n++)r+=String.fromCharCode(255&e),e>>>=8;return r},c=function(e,t,n,i,c,d){
var A,u,h=e.file,p=e.compression,f=d!==s.utf8encode,m=r.transformTo("string",d(h.name)),g=r.transformTo("string",s.utf8encode(h.name)),_=h.comment,b=r.transformTo("string",d(_)),v=r.transformTo("string",s.utf8encode(_)),k=g.length!==h.name.length,y=v.length!==_.length,w="",R="",C="",x=h.dir,E=h.date,G={crc32:0,compressedSize:0,uncompressedSize:0};t&&!n||(G.crc32=e.crc32,G.compressedSize=e.compressedSize,G.uncompressedSize=e.uncompressedSize);var Z=0;t&&(Z|=8),f||!k&&!y||(Z|=2048);var S,B,I=0,T=0
;x&&(I|=16),"UNIX"===c?(T=798,I|=(B=S=h.unixPermissions,S||(B=x?16893:33204),(65535&B)<<16)):(T=20,I|=63&(h.dosPermissions||0)),A=E.getUTCHours(),A<<=6,A|=E.getUTCMinutes(),A<<=5,A|=E.getUTCSeconds()/2,u=E.getUTCFullYear()-1980,u<<=4,u|=E.getUTCMonth()+1,u<<=5,u|=E.getUTCDate(),k&&(R=l(1,1)+l(o(m),4)+g,w+="up"+l(R.length,2)+R),y&&(C=l(1,1)+l(o(b),4)+v,w+="uc"+l(C.length,2)+C);var U="";return U+="\n\0",U+=l(Z,2),U+=p.magic,U+=l(A,2),U+=l(u,2),U+=l(G.crc32,4),U+=l(G.compressedSize,4),
U+=l(G.uncompressedSize,4),U+=l(m.length,2),U+=l(w.length,2),{fileRecord:a.LOCAL_FILE_HEADER+U+m+w,dirRecord:a.CENTRAL_FILE_HEADER+l(T,2)+U+l(b.length,2)+"\0\0\0\0"+l(I,4)+l(i,4)+m+w+b}},d=function(e){return a.DATA_DESCRIPTOR+l(e.crc32,4)+l(e.compressedSize,4)+l(e.uncompressedSize,4)};r.inherits(n,i),n.prototype.push=function(e){var t=e.meta.percent||0,n=this.entriesCount,r=this._sources.length;this.accumulate?this.contentBuffer.push(e):(this.bytesWritten+=e.data.length,
i.prototype.push.call(this,{data:e.data,meta:{currentFile:this.currentFile,percent:n?(t+100*(n-r-1))/n:100}}))},n.prototype.openedSource=function(e){this.currentSourceOffset=this.bytesWritten,this.currentFile=e.file.name;var t=this.streamFiles&&!e.file.dir;if(t){var n=c(e,t,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:n.fileRecord,meta:{percent:0}})}else this.accumulate=!0},n.prototype.closedSource=function(e){this.accumulate=!1
;var t=this.streamFiles&&!e.file.dir,n=c(e,t,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(n.dirRecord),t)this.push({data:d(e),meta:{percent:100}});else for(this.push({data:n.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},n.prototype.flush=function(){for(var e=this.bytesWritten,t=0;t<this.dirRecords.length;t++)this.push({data:this.dirRecords[t],meta:{percent:100}})
;var n=this.bytesWritten-e,i=function(e,t,n,i,s){var o=r.transformTo("string",s(i));return a.CENTRAL_DIRECTORY_END+"\0\0\0\0"+l(e,2)+l(e,2)+l(t,4)+l(n,4)+l(o.length,2)+o}(this.dirRecords.length,n,e,this.zipComment,this.encodeFileName);this.push({data:i,meta:{percent:100}})},n.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},n.prototype.registerPrevious=function(e){
this._sources.push(e);var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.closedSource(t.previous.streamInfo),t._sources.length?t.prepareNextSource():t.end()})),e.on("error",(function(e){t.error(e)})),this},n.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},n.prototype.error=function(e){
var t=this._sources;if(!i.prototype.error.call(this,e))return!1;for(var n=0;n<t.length;n++)try{t[n].error(e)}catch(e){}return!0},n.prototype.lock=function(){i.prototype.lock.call(this);for(var e=this._sources,t=0;t<e.length;t++)e[t].lock()},t.exports=n},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(e,t,n){"use strict";var r=e("../compressions"),i=e("./ZipFileWorker");n.generateWorker=function(e,t,n){
var s=new i(t.streamFiles,n,t.platform,t.encodeFileName),o=0;try{e.forEach((function(e,n){o++;var i=function(e,t){var n=e||t,i=r[n];if(!i)throw new Error(n+" is not a valid compression method !");return i}(n.options.compression,t.compression),a=n.options.compressionOptions||t.compressionOptions||{},l=n.dir,c=n.date;n._compressWorker(i,a).withStreamInfo("file",{name:e,dir:l,date:c,comment:n.comment||"",unixPermissions:n.unixPermissions,dosPermissions:n.dosPermissions}).pipe(s)})),s.entriesCount=o
}catch(e){s.error(e)}return s}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(e,t){"use strict";function n(){if(!(this instanceof n))return new n;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files={},this.comment=null,this.root="",this.clone=function(){var e=new n;for(var t in this)"function"!=typeof this[t]&&(e[t]=this[t]);return e}}n.prototype=e("./object"),n.prototype.loadAsync=e("./load"),
n.support=e("./support"),n.defaults=e("./defaults"),n.version="3.5.0",n.loadAsync=function(e,t){return(new n).loadAsync(e,t)},n.external=e("./external"),t.exports=n},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(e,t){"use strict";function n(e){return new i.Promise((function(t,n){var r=e.decompressed.getContentWorker().pipe(new a);r.on("error",(function(e){n(e)})).on("end",(function(){
r.streamInfo.crc32!==e.decompressed.crc32?n(new Error("Corrupted zip : CRC32 mismatch")):t()})).resume()}))}var r=e("./utils"),i=e("./external"),s=e("./utf8"),o=(r=e("./utils"),e("./zipEntries")),a=e("./stream/Crc32Probe"),l=e("./nodejsUtils");t.exports=function(e,t){var a=this;return t=r.extend(t||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:s.utf8decode}),
l.isNode&&l.isStream(e)?i.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):r.prepareContent("the loaded zip file",e,!0,t.optimizedBinaryString,t.base64).then((function(e){var n=new o(t);return n.load(e),n})).then((function(e){var r=[i.Promise.resolve(e)],s=e.files;if(t.checkCRC32)for(var o=0;o<s.length;o++)r.push(n(s[o]));return i.Promise.all(r)})).then((function(e){for(var n=e.shift(),r=n.files,i=0;i<r.length;i++){var s=r[i];a.file(s.fileNameStr,s.decompressed,{
binary:!0,optimizedBinaryString:!0,date:s.date,dir:s.dir,comment:s.fileCommentStr.length?s.fileCommentStr:null,unixPermissions:s.unixPermissions,dosPermissions:s.dosPermissions,createFolders:t.createFolders})}return n.zipComment.length&&(a.comment=n.zipComment),a}))}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(e,t){"use strict";function n(e,t){i.call(this,"Nodejs stream input adapter for "+e),this._upstreamEnded=!1,
this._bindStream(t)}var r=e("../utils"),i=e("../stream/GenericWorker");r.inherits(n,i),n.prototype._bindStream=function(e){var t=this;this._stream=e,e.pause(),e.on("data",(function(e){t.push({data:e,meta:{percent:0}})})).on("error",(function(e){t.isPaused?this.generatedError=e:t.error(e)})).on("end",(function(){t.isPaused?t._upstreamEnded=!0:t.end()}))},n.prototype.pause=function(){return!!i.prototype.pause.call(this)&&(this._stream.pause(),!0)},n.prototype.resume=function(){
return!!i.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},t.exports=n},{"../stream/GenericWorker":28,"../utils":32}],13:[function(e,t){"use strict";function n(e,t,n){r.call(this,t),this._helper=e;var i=this;e.on("data",(function(e,t){i.push(e)||i._helper.pause(),n&&n(t)})).on("error",(function(e){i.emit("error",e)})).on("end",(function(){i.push(null)}))}var r=e("readable-stream").Readable;e("../utils").inherits(n,r),n.prototype._read=function(){
this._helper.resume()},t.exports=n},{"../utils":32,"readable-stream":16}],14:[function(e,t){"use strict";t.exports={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(e,t){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(e,t);if("number"==typeof e)throw new Error('The "data" argument must not be a number');return new Buffer(e,t)},allocBuffer:function(e){if(Buffer.alloc)return Buffer.alloc(e);var t=new Buffer(e);return t.fill(0),t},isBuffer:function(e){
return Buffer.isBuffer(e)},isStream:function(e){return e&&"function"==typeof e.on&&"function"==typeof e.pause&&"function"==typeof e.resume}}},{}],15:[function(e,t){"use strict";function n(e){return"[object RegExp]"==={}.toString.call(e)}var r=e("./utf8"),i=e("./utils"),s=e("./stream/GenericWorker"),o=e("./stream/StreamHelper"),a=e("./defaults"),l=e("./compressedObject"),c=e("./zipObject"),d=e("./generate"),A=e("./nodejsUtils"),u=e("./nodejs/NodejsStreamInputAdapter"),h=function(e,t,n){
var r,o=i.getTypeOf(t),d=i.extend(n||{},a);d.date=d.date||new Date,null!==d.compression&&(d.compression=d.compression.toUpperCase()),"string"==typeof d.unixPermissions&&(d.unixPermissions=parseInt(d.unixPermissions,8)),d.unixPermissions&&16384&d.unixPermissions&&(d.dir=!0),d.dosPermissions&&16&d.dosPermissions&&(d.dir=!0),d.dir&&(e=f(e)),d.createFolders&&(r=p(e))&&m.call(this,r,!0);var h,g="string"===o&&!1===d.binary&&!1===d.base64;n&&void 0!==n.binary||(d.binary=!g),
(t instanceof l&&0===t.uncompressedSize||d.dir||!t||0===t.length)&&(d.base64=!1,d.binary=!0,t="",d.compression="STORE",o="string"),h=t instanceof l||t instanceof s?t:A.isNode&&A.isStream(t)?new u(e,t):i.prepareContent(e,t,d.binary,d.optimizedBinaryString,d.base64);var _=new c(e,h,d);this.files[e]=_},p=function(e){"/"===e.slice(-1)&&(e=e.substring(0,e.length-1));var t=e.lastIndexOf("/");return t>0?e.substring(0,t):""},f=function(e){return"/"!==e.slice(-1)&&(e+="/"),e},m=function(e,t){
return t=void 0!==t?t:a.createFolders,e=f(e),this.files[e]||h.call(this,e,null,{dir:!0,createFolders:t}),this.files[e]},g={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(e){var t,n,r;for(t in this.files)this.files.hasOwnProperty(t)&&(r=this.files[t],(n=t.slice(this.root.length,t.length))&&t.slice(0,this.root.length)===this.root&&e(n,r))},filter:function(e){var t=[];return this.forEach((function(n,r){
e(n,r)&&t.push(r)})),t},file:function(e,t,r){if(1===arguments.length){if(n(e)){var i=e;return this.filter((function(e,t){return!t.dir&&i.test(e)}))}var s=this.files[this.root+e];return s&&!s.dir?s:null}return e=this.root+e,h.call(this,e,t,r),this},folder:function(e){if(!e)return this;if(n(e))return this.filter((function(t,n){return n.dir&&e.test(t)}));var t=this.root+e,r=m.call(this,t),i=this.clone();return i.root=r.name,i},remove:function(e){e=this.root+e;var t=this.files[e]
;if(t||("/"!==e.slice(-1)&&(e+="/"),t=this.files[e]),t&&!t.dir)delete this.files[e];else for(var n=this.filter((function(t,n){return n.name.slice(0,e.length)===e})),r=0;r<n.length;r++)delete this.files[n[r].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(e){var t,n={};try{if((n=i.extend(e||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",
comment:null,mimeType:"application/zip",encodeFileName:r.utf8encode})).type=n.type.toLowerCase(),n.compression=n.compression.toUpperCase(),"binarystring"===n.type&&(n.type="string"),!n.type)throw new Error("No output type specified.");i.checkSupport(n.type),"darwin"!==n.platform&&"freebsd"!==n.platform&&"linux"!==n.platform&&"sunos"!==n.platform||(n.platform="UNIX"),"win32"===n.platform&&(n.platform="DOS");var a=n.comment||this.comment||"";t=d.generateWorker(this,n,a)}catch(e){
(t=new s("error")).error(e)}return new o(t,n.type||"string",n.mimeType)},generateAsync:function(e,t){return this.generateInternalStream(e).accumulate(t)},generateNodeStream:function(e,t){return(e=e||{}).type||(e.type="nodebuffer"),this.generateInternalStream(e).toNodejsStream(t)}};t.exports=g},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,
"./zipObject":35}],16:[function(e,t){t.exports=e("stream")},{stream:void 0}],17:[function(e,t){"use strict";function n(e){r.call(this,e);for(var t=0;t<this.data.length;t++)e[t]=255&e[t]}var r=e("./DataReader");e("../utils").inherits(n,r),n.prototype.byteAt=function(e){return this.data[this.zero+e]},n.prototype.lastIndexOfSignature=function(e){
for(var t=e.charCodeAt(0),n=e.charCodeAt(1),r=e.charCodeAt(2),i=e.charCodeAt(3),s=this.length-4;s>=0;--s)if(this.data[s]===t&&this.data[s+1]===n&&this.data[s+2]===r&&this.data[s+3]===i)return s-this.zero;return-1},n.prototype.readAndCheckSignature=function(e){var t=e.charCodeAt(0),n=e.charCodeAt(1),r=e.charCodeAt(2),i=e.charCodeAt(3),s=this.readData(4);return t===s[0]&&n===s[1]&&r===s[2]&&i===s[3]},n.prototype.readData=function(e){if(this.checkOffset(e),0===e)return[]
;var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=n},{"../utils":32,"./DataReader":18}],18:[function(e,t){"use strict";function n(e){this.data=e,this.length=e.length,this.index=0,this.zero=0}var r=e("../utils");n.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<this.zero+e||e<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+e+"). Corrupted zip ?")},
setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(){},readInt:function(e){var t,n=0;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)n=(n<<8)+this.byteAt(t);return this.index+=e,n},readString:function(e){return r.transformTo("string",this.readData(e))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var e=this.readInt(4)
;return new Date(Date.UTC(1980+(e>>25&127),(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(31&e)<<1))}},t.exports=n},{"../utils":32}],19:[function(e,t){"use strict";function n(e){r.call(this,e)}var r=e("./Uint8ArrayReader");e("../utils").inherits(n,r),n.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=n},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(e,t){"use strict";function n(e){
r.call(this,e)}var r=e("./DataReader");e("../utils").inherits(n,r),n.prototype.byteAt=function(e){return this.data.charCodeAt(this.zero+e)},n.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)-this.zero},n.prototype.readAndCheckSignature=function(e){return e===this.readData(4)},n.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=n},{"../utils":32,"./DataReader":18}],
21:[function(e,t){"use strict";function n(e){r.call(this,e)}var r=e("./ArrayReader");e("../utils").inherits(n,r),n.prototype.readData=function(e){if(this.checkOffset(e),0===e)return new Uint8Array(0);var t=this.data.subarray(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=n},{"../utils":32,"./ArrayReader":17}],22:[function(e,t){"use strict"
;var n=e("../utils"),r=e("../support"),i=e("./ArrayReader"),s=e("./StringReader"),o=e("./NodeBufferReader"),a=e("./Uint8ArrayReader");t.exports=function(e){var t=n.getTypeOf(e);return n.checkSupport(t),"string"!==t||r.uint8array?"nodebuffer"===t?new o(e):r.uint8array?new a(n.transformTo("uint8array",e)):new i(n.transformTo("array",e)):new s(e)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(e,t,n){"use strict"
;n.LOCAL_FILE_HEADER="PK",n.CENTRAL_FILE_HEADER="PK",n.CENTRAL_DIRECTORY_END="PK",n.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",n.ZIP64_CENTRAL_DIRECTORY_END="PK",n.DATA_DESCRIPTOR="PK\b"},{}],24:[function(e,t){"use strict";function n(e){r.call(this,"ConvertWorker to "+e),this.destType=e}var r=e("./GenericWorker"),i=e("../utils");i.inherits(n,r),n.prototype.processChunk=function(e){this.push({data:i.transformTo(this.destType,e.data),meta:e.meta})},t.exports=n},{"../utils":32,
"./GenericWorker":28}],25:[function(e,t){"use strict";function n(){r.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}var r=e("./GenericWorker"),i=e("../crc32");e("../utils").inherits(n,r),n.prototype.processChunk=function(e){this.streamInfo.crc32=i(e.data,this.streamInfo.crc32||0),this.push(e)},t.exports=n},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(e,t){"use strict";function n(e){i.call(this,"DataLengthProbe for "+e),this.propName=e,this.withStreamInfo(e,0)}
var r=e("../utils"),i=e("./GenericWorker");r.inherits(n,i),n.prototype.processChunk=function(e){if(e){var t=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=t+e.data.length}i.prototype.processChunk.call(this,e)},t.exports=n},{"../utils":32,"./GenericWorker":28}],27:[function(e,t){"use strict";function n(e){i.call(this,"DataWorker");var t=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,e.then((function(e){t.dataIsReady=!0,
t.data=e,t.max=e&&e.length||0,t.type=r.getTypeOf(e),t.isPaused||t._tickAndRepeat()}),(function(e){t.error(e)}))}var r=e("../utils"),i=e("./GenericWorker");r.inherits(n,i),n.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this.data=null},n.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,r.delay(this._tickAndRepeat,[],this)),!0)},n.prototype._tickAndRepeat=function(){this._tickScheduled=!1,
this.isPaused||this.isFinished||(this._tick(),this.isFinished||(r.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},n.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var e=null,t=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":e=this.data.substring(this.index,t);break;case"uint8array":e=this.data.subarray(this.index,t);break;case"array":case"nodebuffer":e=this.data.slice(this.index,t)}
return this.index=t,this.push({data:e,meta:{percent:this.max?this.index/this.max*100:0}})},t.exports=n},{"../utils":32,"./GenericWorker":28}],28:[function(e,t){"use strict";function n(e){this.name=e||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}n.prototype={push:function(e){this.emit("data",e)},end:function(){if(this.isFinished)return!1;this.flush()
;try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(e){this.emit("error",e)}return!0},error:function(e){return!this.isFinished&&(this.isPaused?this.generatedError=e:(this.isFinished=!0,this.emit("error",e),this.previous&&this.previous.error(e),this.cleanUp()),!0)},on:function(e,t){return this._listeners[e].push(t),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(e,t){
if(this._listeners[e])for(var n=0;n<this._listeners[e].length;n++)this._listeners[e][n].call(this,t)},pipe:function(e){return e.registerPrevious(this)},registerPrevious:function(e){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=e.streamInfo,this.mergeStreamInfo(),this.previous=e;var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.end()})),e.on("error",(function(e){t.error(e)})),this},pause:function(){
return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;this.isPaused=!1;var e=!1;return this.generatedError&&(this.error(this.generatedError),e=!0),this.previous&&this.previous.resume(),!e},flush:function(){},processChunk:function(e){this.push(e)},withStreamInfo:function(e,t){return this.extraStreamInfo[e]=t,this.mergeStreamInfo(),this},mergeStreamInfo:function(){
for(var e in this.extraStreamInfo)this.extraStreamInfo.hasOwnProperty(e)&&(this.streamInfo[e]=this.extraStreamInfo[e])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var e="Worker "+this.name;return this.previous?this.previous+" -> "+e:e}},t.exports=n},{}],29:[function(e,t){"use strict";function n(e,t,n){var o=t;switch(t){case"blob":case"arraybuffer":o="uint8array";break
;case"base64":o="string"}try{this._internalType=o,this._outputType=t,this._mimeType=n,r.checkSupport(o),this._worker=e.pipe(new i(o)),e.lock()}catch(e){this._worker=new s("error"),this._worker.error(e)}}var r=e("../utils"),i=e("./ConvertWorker"),s=e("./GenericWorker"),o=e("../base64"),a=e("../support"),l=e("../external"),c=null;if(a.nodestream)try{c=e("../nodejs/NodejsStreamOutputAdapter")}catch(e){}n.prototype={accumulate:function(e){return t=this,n=e,new l.Promise((function(e,i){
var s=[],a=t._internalType,l=t._outputType,c=t._mimeType;t.on("data",(function(e,t){s.push(e),n&&n(t)})).on("error",(function(e){s=[],i(e)})).on("end",(function(){try{var t=function(e,t,n){switch(e){case"blob":return r.newBlob(r.transformTo("arraybuffer",t),n);case"base64":return o.encode(t);default:return r.transformTo(e,t)}}(l,function(e,t){var n,r=0,i=null,s=0;for(n=0;n<t.length;n++)s+=t[n].length;switch(e){case"string":return t.join("");case"array":return[].concat.apply([],t)
;case"uint8array":for(i=new Uint8Array(s),n=0;n<t.length;n++)i.set(t[n],r),r+=t[n].length;return i;case"nodebuffer":return Buffer.concat(t);default:throw new Error("concat : unsupported type '"+e+"'")}}(a,s),c);e(t)}catch(e){i(e)}s=[]})).resume()}));var t,n},on:function(e,t){var n=this;return"data"===e?this._worker.on(e,(function(e){t.call(n,e.data,e.meta)})):this._worker.on(e,(function(){r.delay(t,arguments,n)})),this},resume:function(){return r.delay(this._worker.resume,[],this._worker),this},
pause:function(){return this._worker.pause(),this},toNodejsStream:function(e){if(r.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new c(this,{objectMode:"nodebuffer"!==this._outputType},e)}},t.exports=n},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(e,t,n){"use strict";if(n.base64=!0,n.array=!0,
n.string=!0,n.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,n.nodebuffer="undefined"!=typeof Buffer,n.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)n.blob=!1;else{var r=new ArrayBuffer(0);try{n.blob=0===new Blob([r],{type:"application/zip"}).size}catch(e){try{var i=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);i.append(r),n.blob=0===i.getBlob("application/zip").size}catch(e){n.blob=!1}}}try{
n.nodestream=!!e("readable-stream").Readable}catch(e){n.nodestream=!1}},{"readable-stream":16}],31:[function(e,t,n){"use strict";function r(){l.call(this,"utf-8 decode"),this.leftOver=null}function i(){l.call(this,"utf-8 encode")}for(var s=e("./utils"),o=e("./support"),a=e("./nodejsUtils"),l=e("./stream/GenericWorker"),c=new Array(256),d=0;d<256;d++)c[d]=d>=252?6:d>=248?5:d>=240?4:d>=224?3:d>=192?2:1;c[254]=c[254]=1,n.utf8encode=function(e){
return o.nodebuffer?a.newBufferFrom(e,"utf-8"):function(e){var t,n,r,i,s,a=e.length,l=0;for(i=0;i<a;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<a&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),l+=n<128?1:n<2048?2:n<65536?3:4;for(t=o.uint8array?new Uint8Array(l):new Array(l),s=0,i=0;s<l;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<a&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),n<128?t[s++]=n:n<2048?(t[s++]=192|n>>>6,
t[s++]=128|63&n):n<65536?(t[s++]=224|n>>>12,t[s++]=128|n>>>6&63,t[s++]=128|63&n):(t[s++]=240|n>>>18,t[s++]=128|n>>>12&63,t[s++]=128|n>>>6&63,t[s++]=128|63&n);return t}(e)},n.utf8decode=function(e){return o.nodebuffer?s.transformTo("nodebuffer",e).toString("utf-8"):function(e){var t,n,r,i,o=e.length,a=new Array(2*o);for(n=0,t=0;t<o;)if((r=e[t++])<128)a[n++]=r;else if((i=c[r])>4)a[n++]=65533,t+=i-1;else{for(r&=2===i?31:3===i?15:7;i>1&&t<o;)r=r<<6|63&e[t++],i--
;i>1?a[n++]=65533:r<65536?a[n++]=r:(r-=65536,a[n++]=55296|r>>10&1023,a[n++]=56320|1023&r)}return a.length!==n&&(a.subarray?a=a.subarray(0,n):a.length=n),s.applyFromCharCode(a)}(e=s.transformTo(o.uint8array?"uint8array":"array",e))},s.inherits(r,l),r.prototype.processChunk=function(e){var t=s.transformTo(o.uint8array?"uint8array":"array",e.data);if(this.leftOver&&this.leftOver.length){if(o.uint8array){var r=t;(t=new Uint8Array(r.length+this.leftOver.length)).set(this.leftOver,0),
t.set(r,this.leftOver.length)}else t=this.leftOver.concat(t);this.leftOver=null}var i=function(e,t){var n;for((t=t||e.length)>e.length&&(t=e.length),n=t-1;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+c[e[n]]>t?n:t}(t),a=t;i!==t.length&&(o.uint8array?(a=t.subarray(0,i),this.leftOver=t.subarray(i,t.length)):(a=t.slice(0,i),this.leftOver=t.slice(i,t.length))),this.push({data:n.utf8decode(a),meta:e.meta})},r.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({
data:n.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},n.Utf8DecodeWorker=r,s.inherits(i,l),i.prototype.processChunk=function(e){this.push({data:n.utf8encode(e.data),meta:e.meta})},n.Utf8EncodeWorker=i},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(e,t,n){"use strict";function r(e){return e}function i(e,t){for(var n=0;n<e.length;++n)t[n]=255&e.charCodeAt(n);return t}function s(e){var t=65536,r=n.getTypeOf(e),i=!0
;if("uint8array"===r?i=u.applyCanBeUsed.uint8array:"nodebuffer"===r&&(i=u.applyCanBeUsed.nodebuffer),i)for(;t>1;)try{return u.stringifyByChunk(e,r,t)}catch(e){t=Math.floor(t/2)}return u.stringifyByChar(e)}function o(e,t){for(var n=0;n<e.length;n++)t[n]=e[n];return t}var a=e("./support"),l=e("./base64"),c=e("./nodejsUtils"),d=e("set-immediate-shim"),A=e("./external");n.newBlob=function(e,t){n.checkSupport("blob");try{return new Blob([e],{type:t})}catch(n){try{
var r=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return r.append(e),r.getBlob(t)}catch(e){throw new Error("Bug : can't construct the Blob.")}}};var u={stringifyByChunk:function(e,t,n){var r=[],i=0,s=e.length;if(s<=n)return String.fromCharCode.apply(null,e);for(;i<s;)"array"===t||"nodebuffer"===t?r.push(String.fromCharCode.apply(null,e.slice(i,Math.min(i+n,s)))):r.push(String.fromCharCode.apply(null,e.subarray(i,Math.min(i+n,s)))),i+=n;return r.join("")
},stringifyByChar:function(e){for(var t="",n=0;n<e.length;n++)t+=String.fromCharCode(e[n]);return t},applyCanBeUsed:{uint8array:function(){try{return a.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(e){return!1}}(),nodebuffer:function(){try{return a.nodebuffer&&1===String.fromCharCode.apply(null,c.allocBuffer(1)).length}catch(e){return!1}}()}};n.applyFromCharCode=s;var h={};h.string={string:r,array:function(e){return i(e,new Array(e.length))},
arraybuffer:function(e){return h.string.uint8array(e).buffer},uint8array:function(e){return i(e,new Uint8Array(e.length))},nodebuffer:function(e){return i(e,c.allocBuffer(e.length))}},h.array={string:s,array:r,arraybuffer:function(e){return new Uint8Array(e).buffer},uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return c.newBufferFrom(e)}},h.arraybuffer={string:function(e){return s(new Uint8Array(e))},array:function(e){return o(new Uint8Array(e),new Array(e.byteLength))
},arraybuffer:r,uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return c.newBufferFrom(new Uint8Array(e))}},h.uint8array={string:s,array:function(e){return o(e,new Array(e.length))},arraybuffer:function(e){return e.buffer},uint8array:r,nodebuffer:function(e){return c.newBufferFrom(e)}},h.nodebuffer={string:s,array:function(e){return o(e,new Array(e.length))},arraybuffer:function(e){return h.nodebuffer.uint8array(e).buffer},uint8array:function(e){
return o(e,new Uint8Array(e.length))},nodebuffer:r},n.transformTo=function(e,t){if(t||(t=""),!e)return t;n.checkSupport(e);var r=n.getTypeOf(t);return h[r][e](t)},n.getTypeOf=function(e){return"string"==typeof e?"string":"[object Array]"==={}.toString.call(e)?"array":a.nodebuffer&&c.isBuffer(e)?"nodebuffer":a.uint8array&&e instanceof Uint8Array?"uint8array":a.arraybuffer&&e instanceof ArrayBuffer?"arraybuffer":void 0},n.checkSupport=function(e){
if(!a[e.toLowerCase()])throw new Error(e+" is not supported by this platform")},n.MAX_VALUE_16BITS=65535,n.MAX_VALUE_32BITS=-1,n.pretty=function(e){var t,n,r="";for(n=0;n<(e||"").length;n++)r+="\\x"+((t=e.charCodeAt(n))<16?"0":"")+t.toString(16).toUpperCase();return r},n.delay=function(e,t,n){d((function(){e.apply(n||null,t||[])}))},n.inherits=function(e,t){var n=function(){};n.prototype=t.prototype,e.prototype=new n},n.extend=function(){var e,t,n={}
;for(e=0;e<arguments.length;e++)for(t in arguments[e])arguments[e].hasOwnProperty(t)&&void 0===n[t]&&(n[t]=arguments[e][t]);return n},n.prepareContent=function(e,t,r,s,o){return A.Promise.resolve(t).then((function(e){return a.blob&&(e instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf({}.toString.call(e)))&&"undefined"!=typeof FileReader?new A.Promise((function(t,n){var r=new FileReader;r.onload=function(e){t(e.target.result)},r.onerror=function(e){n(e.target.error)},
r.readAsArrayBuffer(e)})):e})).then((function(t){var c,d=n.getTypeOf(t);return d?("arraybuffer"===d?t=n.transformTo("uint8array",t):"string"===d&&(o?t=l.decode(t):r&&!0!==s&&(t=i(c=t,a.uint8array?new Uint8Array(c.length):new Array(c.length)))),t):A.Promise.reject(new Error("Can't read the data of '"+e+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))}))}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,"set-immediate-shim":54}],33:[function(e,t){
"use strict";function n(e){this.files=[],this.loadOptions=e}var r=e("./reader/readerFor"),i=e("./utils"),s=e("./signature"),o=e("./zipEntry"),a=(e("./utf8"),e("./support"));n.prototype={checkSignature:function(e){if(!this.reader.readAndCheckSignature(e)){this.reader.index-=4;var t=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+i.pretty(t)+", expected "+i.pretty(e)+")")}},isSignature:function(e,t){var n=this.reader.index;this.reader.setIndex(e)
;var r=this.reader.readString(4)===t;return this.reader.setIndex(n),r},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2)
;var e=this.reader.readData(this.zipCommentLength),t=a.uint8array?"uint8array":"array",n=i.transformTo(t,e);this.zipComment=this.loadOptions.decodeFileName(n)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),
this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e,t,n,r=this.zip64EndOfCentralSize-44;0<r;)e=this.reader.readInt(2),t=this.reader.readInt(4),n=this.reader.readData(t),this.zip64ExtensibleData[e]={id:e,length:t,value:n}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),
this.disksCount=this.reader.readInt(4),this.disksCount>1)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var e,t;for(e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(s.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(s.CENTRAL_FILE_HEADER);)(e=new o({
zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(e);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(s.CENTRAL_DIRECTORY_END)
;if(e<0)throw this.isSignature(0,s.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(e);var t=e;if(this.checkSignature(s.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),
this.diskNumber===i.MAX_VALUE_16BITS||this.diskWithCentralDirStart===i.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===i.MAX_VALUE_16BITS||this.centralDirRecords===i.MAX_VALUE_16BITS||this.centralDirSize===i.MAX_VALUE_32BITS||this.centralDirOffset===i.MAX_VALUE_32BITS){if(this.zip64=!0,(e=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(e),
this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,s.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),
this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var n=this.centralDirOffset+this.centralDirSize;this.zip64&&(n+=20,n+=12+this.zip64EndOfCentralSize);var r=t-n;if(r>0)this.isSignature(t,s.CENTRAL_FILE_HEADER)||(this.reader.zero=r);else if(r<0)throw new Error("Corrupted zip: missing "+Math.abs(r)+" bytes.")},prepareReader:function(e){this.reader=r(e)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},
t.exports=n},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utf8":31,"./utils":32,"./zipEntry":34}],34:[function(e,t){"use strict";function n(e,t){this.options=e,this.loadOptions=t}var r=e("./reader/readerFor"),i=e("./utils"),s=e("./compressedObject"),o=e("./crc32"),a=e("./utf8"),l=e("./compressions"),c=e("./support");n.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},readLocalPart:function(e){var t,n;if(e.skip(22),
this.fileNameLength=e.readInt(2),n=e.readInt(2),this.fileName=e.readData(this.fileNameLength),e.skip(n),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(t=function(e){for(var t in l)if(l.hasOwnProperty(t)&&l[t].magic===e)return l[t];return null
}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+i.pretty(this.compressionMethod)+" unknown (inner file : "+i.transformTo("string",this.fileName)+")");this.decompressed=new s(this.compressedSize,this.uncompressedSize,this.crc32,t,e.readData(this.compressedSize))},readCentralPart:function(e){this.versionMadeBy=e.readInt(2),e.skip(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),
this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4);var t=e.readInt(2);if(this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");e.skip(t),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readData(this.fileCommentLength)},
processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var e=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0===e&&(this.dosPermissions=63&this.externalFileAttributes),3===e&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var e=r(this.extraFields[1].value)
;this.uncompressedSize===i.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===i.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===i.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===i.MAX_VALUE_32BITS&&(this.diskNumberStart=e.readInt(4))}},readExtraFields:function(e){var t,n,r,i=e.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});e.index+4<i;)t=e.readInt(2),n=e.readInt(2),r=e.readData(n),
this.extraFields[t]={id:t,length:n,value:r};e.setIndex(i)},handleUTF8:function(){var e=c.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=a.utf8decode(this.fileName),this.fileCommentStr=a.utf8decode(this.fileComment);else{var t=this.findExtraFieldUnicodePath();if(null!==t)this.fileNameStr=t;else{var n=i.transformTo(e,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(n)}var r=this.findExtraFieldUnicodeComment();if(null!==r)this.fileCommentStr=r;else{
var s=i.transformTo(e,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(s)}}},findExtraFieldUnicodePath:function(){var e=this.extraFields[28789];if(e){var t=r(e.value);return 1!==t.readInt(1)||o(this.fileName)!==t.readInt(4)?null:a.utf8decode(t.readData(e.length-5))}return null},findExtraFieldUnicodeComment:function(){var e=this.extraFields[25461];if(e){var t=r(e.value);return 1!==t.readInt(1)||o(this.fileComment)!==t.readInt(4)?null:a.utf8decode(t.readData(e.length-5))}
return null}},t.exports=n},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(e,t){"use strict";var n=e("./stream/StreamHelper"),r=e("./stream/DataWorker"),i=e("./utf8"),s=e("./compressedObject"),o=e("./stream/GenericWorker"),a=function(e,t,n){this.name=e,this.dir=n.dir,this.date=n.date,this.comment=n.comment,this.unixPermissions=n.unixPermissions,this.dosPermissions=n.dosPermissions,this._data=t,
this._dataBinary=n.binary,this.options={compression:n.compression,compressionOptions:n.compressionOptions}};a.prototype={internalStream:function(e){var t=null,r="string";try{if(!e)throw new Error("No output type specified.");var s="string"===(r=e.toLowerCase())||"text"===r;"binarystring"!==r&&"text"!==r||(r="string"),t=this._decompressWorker();var a=!this._dataBinary;a&&!s&&(t=t.pipe(new i.Utf8EncodeWorker)),!a&&s&&(t=t.pipe(new i.Utf8DecodeWorker))}catch(e){(t=new o("error")).error(e)}
return new n(t,r,"")},async:function(e,t){return this.internalStream(e).accumulate(t)},nodeStream:function(e,t){return this.internalStream(e||"nodebuffer").toNodejsStream(t)},_compressWorker:function(e,t){if(this._data instanceof s&&this._data.compression.magic===e.magic)return this._data.getCompressedWorker();var n=this._decompressWorker();return this._dataBinary||(n=n.pipe(new i.Utf8EncodeWorker)),s.createWorkerFrom(n,e,t)},_decompressWorker:function(){
return this._data instanceof s?this._data.getContentWorker():this._data instanceof o?this._data:new r(this._data)}};for(var l=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],c=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},d=0;d<l.length;d++)a.prototype[l[d]]=c;t.exports=a},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(e,t){
(function(e){"use strict";function n(){var e,t;i=!0;for(var n=d.length;n;){for(t=d,d=[],e=-1;++e<n;)t[e]();n=d.length}i=!1}var r,i,s=e.MutationObserver||e.WebKitMutationObserver;if(s){var o=0,a=new s(n),l=e.document.createTextNode("");a.observe(l,{characterData:!0}),r=function(){l.data=o=++o%2}}else if(e.setImmediate||void 0===e.MessageChannel)r="document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script")
;t.onreadystatechange=function(){n(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(n,0)};else{var c=new e.MessageChannel;c.port1.onmessage=n,r=function(){c.port2.postMessage(0)}}var d=[];t.exports=function(e){1!==d.push(e)||i||r()}}).call(this,void 0!==n.g?n.g:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(e,t){"use strict";function n(){}function r(e){
if("function"!=typeof e)throw new TypeError("resolver must be a function");this.state=h,this.queue=[],this.outcome=void 0,e!==n&&a(this,e)}function i(e,t,n){this.promise=e,"function"==typeof t&&(this.onFulfilled=t,this.callFulfilled=this.otherCallFulfilled),"function"==typeof n&&(this.onRejected=n,this.callRejected=this.otherCallRejected)}function s(e,t,n){c((function(){var r;try{r=t(n)}catch(t){return d.reject(e,t)}
r===e?d.reject(e,new TypeError("Cannot resolve promise with itself")):d.resolve(e,r)}))}function o(e){var t=e&&e.then;if(e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof t)return function(){t.apply(e,arguments)}}function a(e,t){function n(t){i||(i=!0,d.reject(e,t))}function r(t){i||(i=!0,d.resolve(e,t))}var i=!1,s=l((function(){t(r,n)}));"error"===s.status&&n(s.value)}function l(e,t){var n={};try{n.value=e(t),n.status="success"}catch(e){n.status="error",n.value=e}return n}
var c=e("immediate"),d={},A=["REJECTED"],u=["FULFILLED"],h=["PENDING"];t.exports=r,r.prototype.finally=function(e){if("function"!=typeof e)return this;var t=this.constructor;return this.then((function(n){return t.resolve(e()).then((function(){return n}))}),(function(n){return t.resolve(e()).then((function(){throw n}))}))},r.prototype.catch=function(e){return this.then(null,e)},r.prototype.then=function(e,t){if("function"!=typeof e&&this.state===u||"function"!=typeof t&&this.state===A)return this
;var r=new this.constructor(n);return this.state!==h?s(r,this.state===u?e:t,this.outcome):this.queue.push(new i(r,e,t)),r},i.prototype.callFulfilled=function(e){d.resolve(this.promise,e)},i.prototype.otherCallFulfilled=function(e){s(this.promise,this.onFulfilled,e)},i.prototype.callRejected=function(e){d.reject(this.promise,e)},i.prototype.otherCallRejected=function(e){s(this.promise,this.onRejected,e)},d.resolve=function(e,t){var n=l(o,t);if("error"===n.status)return d.reject(e,n.value)
;var r=n.value;if(r)a(e,r);else{e.state=u,e.outcome=t;for(var i=-1,s=e.queue.length;++i<s;)e.queue[i].callFulfilled(t)}return e},d.reject=function(e,t){e.state=A,e.outcome=t;for(var n=-1,r=e.queue.length;++n<r;)e.queue[n].callRejected(t);return e},r.resolve=function(e){return e instanceof this?e:d.resolve(new this(n),e)},r.reject=function(e){var t=new this(n);return d.reject(t,e)},r.all=function(e){function t(e,t){r.resolve(e).then((function(e){o[t]=e,++a!==i||s||(s=!0,d.resolve(c,o))
}),(function(e){s||(s=!0,d.reject(c,e))}))}var r=this;if("[object Array]"!=={}.toString.call(e))return this.reject(new TypeError("must be an array"));var i=e.length,s=!1;if(!i)return this.resolve([]);for(var o=new Array(i),a=0,l=-1,c=new this(n);++l<i;)t(e[l],l);return c},r.race=function(e){if("[object Array]"!=={}.toString.call(e))return this.reject(new TypeError("must be an array"));var t,r=e.length,i=!1;if(!r)return this.resolve([]);for(var s=-1,o=new this(n);++s<r;)t=e[s],
this.resolve(t).then((function(e){i||(i=!0,d.resolve(o,e))}),(function(e){i||(i=!0,d.reject(o,e))}));return o}},{immediate:36}],38:[function(e,t){"use strict";var n={};(0,e("./lib/utils/common").assign)(n,e("./lib/deflate"),e("./lib/inflate"),e("./lib/zlib/constants")),t.exports=n},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(e,t,n){"use strict";function r(e){if(!(this instanceof r))return new r(e);this.options=o.assign({level:u,
method:p,chunkSize:16384,windowBits:15,memLevel:8,strategy:h,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new c,this.strm.avail_out=0;var n=s.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(n!==A)throw new Error(l[n]);if(t.header&&s.deflateSetHeader(this.strm,t.header),t.dictionary){var i
;if(i="string"==typeof t.dictionary?a.string2buf(t.dictionary):"[object ArrayBuffer]"===d.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(n=s.deflateSetDictionary(this.strm,i))!==A)throw new Error(l[n]);this._dict_set=!0}}function i(e,t){var n=new r(t);if(n.push(e,!0),n.err)throw n.msg||l[n.err];return n.result}var s=e("./zlib/deflate"),o=e("./utils/common"),a=e("./utils/strings"),l=e("./zlib/messages"),c=e("./zlib/zstream"),d={}.toString,A=0,u=-1,h=0,p=8
;r.prototype.push=function(e,t){var n,r,i=this.strm,l=this.options.chunkSize;if(this.ended)return!1;r=t===~~t?t:!0===t?4:0,"string"==typeof e?i.input=a.string2buf(e):"[object ArrayBuffer]"===d.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new o.Buf8(l),i.next_out=0,i.avail_out=l),1!==(n=s.deflate(i,r))&&n!==A)return this.onEnd(n),this.ended=!0,!1
;0!==i.avail_out&&(0!==i.avail_in||4!==r&&2!==r)||("string"===this.options.to?this.onData(a.buf2binstring(o.shrinkBuf(i.output,i.next_out))):this.onData(o.shrinkBuf(i.output,i.next_out)))}while((i.avail_in>0||0===i.avail_out)&&1!==n);return 4===r?(n=s.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===A):2!==r||(this.onEnd(A),i.avail_out=0,!0)},r.prototype.onData=function(e){this.chunks.push(e)},r.prototype.onEnd=function(e){
e===A&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},n.Deflate=r,n.deflate=i,n.deflateRaw=function(e,t){return(t=t||{}).raw=!0,i(e,t)},n.gzip=function(e,t){return(t=t||{}).gzip=!0,i(e,t)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(e,t,n){"use strict";function r(e){if(!(this instanceof r))return new r(e)
;this.options=o.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new d,this.strm.avail_out=0;var n=s.inflateInit2(this.strm,t.windowBits)
;if(n!==l.Z_OK)throw new Error(c[n]);this.header=new A,s.inflateGetHeader(this.strm,this.header)}function i(e,t){var n=new r(t);if(n.push(e,!0),n.err)throw n.msg||c[n.err];return n.result}var s=e("./zlib/inflate"),o=e("./utils/common"),a=e("./utils/strings"),l=e("./zlib/constants"),c=e("./zlib/messages"),d=e("./zlib/zstream"),A=e("./zlib/gzheader"),u={}.toString;r.prototype.push=function(e,t){var n,r,i,c,d,A,h=this.strm,p=this.options.chunkSize,f=this.options.dictionary,m=!1
;if(this.ended)return!1;r=t===~~t?t:!0===t?l.Z_FINISH:l.Z_NO_FLUSH,"string"==typeof e?h.input=a.binstring2buf(e):"[object ArrayBuffer]"===u.call(e)?h.input=new Uint8Array(e):h.input=e,h.next_in=0,h.avail_in=h.input.length;do{if(0===h.avail_out&&(h.output=new o.Buf8(p),h.next_out=0,h.avail_out=p),(n=s.inflate(h,l.Z_NO_FLUSH))===l.Z_NEED_DICT&&f&&(A="string"==typeof f?a.string2buf(f):"[object ArrayBuffer]"===u.call(f)?new Uint8Array(f):f,n=s.inflateSetDictionary(this.strm,A)),
n===l.Z_BUF_ERROR&&!0===m&&(n=l.Z_OK,m=!1),n!==l.Z_STREAM_END&&n!==l.Z_OK)return this.onEnd(n),this.ended=!0,!1;h.next_out&&(0!==h.avail_out&&n!==l.Z_STREAM_END&&(0!==h.avail_in||r!==l.Z_FINISH&&r!==l.Z_SYNC_FLUSH)||("string"===this.options.to?(i=a.utf8border(h.output,h.next_out),c=h.next_out-i,d=a.buf2string(h.output,i),h.next_out=c,h.avail_out=p-c,c&&o.arraySet(h.output,h.output,i,c,0),this.onData(d)):this.onData(o.shrinkBuf(h.output,h.next_out)))),0===h.avail_in&&0===h.avail_out&&(m=!0)
}while((h.avail_in>0||0===h.avail_out)&&n!==l.Z_STREAM_END);return n===l.Z_STREAM_END&&(r=l.Z_FINISH),r===l.Z_FINISH?(n=s.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===l.Z_OK):r!==l.Z_SYNC_FLUSH||(this.onEnd(l.Z_OK),h.avail_out=0,!0)},r.prototype.onData=function(e){this.chunks.push(e)},r.prototype.onEnd=function(e){e===l.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},
n.Inflate=r,n.inflate=i,n.inflateRaw=function(e,t){return(t=t||{}).raw=!0,i(e,t)},n.ungzip=i},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(e,t,n){"use strict";var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;n.assign=function(e){for(var t=[].slice.call(arguments,1);t.length;){var n=t.shift();if(n){
if("object"!=typeof n)throw new TypeError(n+"must be non-object");for(var r in n)n.hasOwnProperty(r)&&(e[r]=n[r])}}return e},n.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var i={arraySet:function(e,t,n,r,i){if(t.subarray&&e.subarray)e.set(t.subarray(n,n+r),i);else for(var s=0;s<r;s++)e[i+s]=t[n+s]},flattenChunks:function(e){var t,n,r,i,s,o;for(r=0,t=0,n=e.length;t<n;t++)r+=e[t].length;for(o=new Uint8Array(r),i=0,t=0,n=e.length;t<n;t++)s=e[t],
o.set(s,i),i+=s.length;return o}},s={arraySet:function(e,t,n,r,i){for(var s=0;s<r;s++)e[i+s]=t[n+s]},flattenChunks:function(e){return[].concat.apply([],e)}};n.setTyped=function(e){e?(n.Buf8=Uint8Array,n.Buf16=Uint16Array,n.Buf32=Int32Array,n.assign(n,i)):(n.Buf8=Array,n.Buf16=Array,n.Buf32=Array,n.assign(n,s))},n.setTyped(r)},{}],42:[function(e,t,n){"use strict";function r(e,t){if(t<65537&&(e.subarray&&o||!e.subarray&&s))return String.fromCharCode.apply(null,i.shrinkBuf(e,t))
;for(var n="",r=0;r<t;r++)n+=String.fromCharCode(e[r]);return n}var i=e("./common"),s=!0,o=!0;try{String.fromCharCode.apply(null,[0])}catch(e){s=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){o=!1}for(var a=new i.Buf8(256),l=0;l<256;l++)a[l]=l>=252?6:l>=248?5:l>=240?4:l>=224?3:l>=192?2:1;a[254]=a[254]=1,n.string2buf=function(e){var t,n,r,s,o,a=e.length,l=0
;for(s=0;s<a;s++)55296==(64512&(n=e.charCodeAt(s)))&&s+1<a&&56320==(64512&(r=e.charCodeAt(s+1)))&&(n=65536+(n-55296<<10)+(r-56320),s++),l+=n<128?1:n<2048?2:n<65536?3:4;for(t=new i.Buf8(l),o=0,s=0;o<l;s++)55296==(64512&(n=e.charCodeAt(s)))&&s+1<a&&56320==(64512&(r=e.charCodeAt(s+1)))&&(n=65536+(n-55296<<10)+(r-56320),s++),n<128?t[o++]=n:n<2048?(t[o++]=192|n>>>6,t[o++]=128|63&n):n<65536?(t[o++]=224|n>>>12,t[o++]=128|n>>>6&63,t[o++]=128|63&n):(t[o++]=240|n>>>18,t[o++]=128|n>>>12&63,
t[o++]=128|n>>>6&63,t[o++]=128|63&n);return t},n.buf2binstring=function(e){return r(e,e.length)},n.binstring2buf=function(e){for(var t=new i.Buf8(e.length),n=0,r=t.length;n<r;n++)t[n]=e.charCodeAt(n);return t},n.buf2string=function(e,t){var n,i,s,o,l=t||e.length,c=new Array(2*l);for(i=0,n=0;n<l;)if((s=e[n++])<128)c[i++]=s;else if((o=a[s])>4)c[i++]=65533,n+=o-1;else{for(s&=2===o?31:3===o?15:7;o>1&&n<l;)s=s<<6|63&e[n++],o--;o>1?c[i++]=65533:s<65536?c[i++]=s:(s-=65536,c[i++]=55296|s>>10&1023,
c[i++]=56320|1023&s)}return r(c,i)},n.utf8border=function(e,t){var n;for((t=t||e.length)>e.length&&(t=e.length),n=t-1;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+a[e[n]]>t?n:t}},{"./common":41}],43:[function(e,t){"use strict";t.exports=function(e,t,n,r){for(var i=65535&e|0,s=e>>>16&65535|0,o=0;0!==n;){n-=o=n>2e3?2e3:n;do{s=s+(i=i+t[r++]|0)|0}while(--o);i%=65521,s%=65521}return i|s<<16|0}},{}],44:[function(e,t){"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,
Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(e,t){"use strict";var n=function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}()
;t.exports=function(e,t,r,i){var s=n,o=i+r;e^=-1;for(var a=i;a<o;a++)e=e>>>8^s[255&(e^t[a])];return-1^e}},{}],46:[function(e,t,n){"use strict";function r(e,t){return e.msg=R[t],t}function i(e){return(e<<1)-(e>4?9:0)}function s(e){for(var t=e.length;--t>=0;)e[t]=0}function o(e){var t=e.state,n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(v.arraySet(e.output,t.pending_buf,t.pending_out,n,e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,
0===t.pending&&(t.pending_out=0))}function a(e,t){k._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,o(e.strm)}function l(e,t){e.pending_buf[e.pending++]=t}function c(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function d(e,t){
var n,r,i=e.max_chain_length,s=e.strstart,o=e.prev_length,a=e.nice_match,l=e.strstart>e.w_size-P?e.strstart-(e.w_size-P):0,c=e.window,d=e.w_mask,A=e.prev,u=e.strstart+z,h=c[s+o-1],p=c[s+o];e.prev_length>=e.good_match&&(i>>=2),a>e.lookahead&&(a=e.lookahead);do{if(c[(n=t)+o]===p&&c[n+o-1]===h&&c[n]===c[s]&&c[++n]===c[s+1]){s+=2,n++;do{}while(c[++s]===c[++n]&&c[++s]===c[++n]&&c[++s]===c[++n]&&c[++s]===c[++n]&&c[++s]===c[++n]&&c[++s]===c[++n]&&c[++s]===c[++n]&&c[++s]===c[++n]&&s<u);if(r=z-(u-s),
s=u-z,r>o){if(e.match_start=t,o=r,r>=a)break;h=c[s+o-1],p=c[s+o]}}}while((t=A[t&d])>l&&0!=--i);return o<=e.lookahead?o:e.lookahead}function A(e){var t,n,r,i,s,o,a,l,c,d,A=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=A+(A-P)){v.arraySet(e.window,e.window,A,A,0),e.match_start-=A,e.strstart-=A,e.block_start-=A,t=n=e.hash_size;do{r=e.head[--t],e.head[t]=r>=A?r-A:0}while(--n);t=n=A;do{r=e.prev[--t],e.prev[t]=r>=A?r-A:0}while(--n);i+=A}if(0===e.strm.avail_in)break;if(o=e.strm,
a=e.window,l=e.strstart+e.lookahead,c=i,d=void 0,(d=o.avail_in)>c&&(d=c),n=0===d?0:(o.avail_in-=d,v.arraySet(a,o.input,o.next_in,d,l),1===o.state.wrap?o.adler=y(o.adler,a,d,l):2===o.state.wrap&&(o.adler=w(o.adler,a,d,l)),o.next_in+=d,o.total_in+=d,d),e.lookahead+=n,e.lookahead+e.insert>=D)for(s=e.strstart-e.insert,e.ins_h=e.window[s],e.ins_h=(e.ins_h<<e.hash_shift^e.window[s+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[s+D-1])&e.hash_mask,e.prev[s&e.w_mask]=e.head[e.ins_h],
e.head[e.ins_h]=s,s++,e.insert--,!(e.lookahead+e.insert<D)););}while(e.lookahead<P&&0!==e.strm.avail_in)}function u(e,t){for(var n,r;;){if(e.lookahead<P){if(A(e),e.lookahead<P&&t===C)return q;if(0===e.lookahead)break}if(n=0,e.lookahead>=D&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+D-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-P&&(e.match_length=d(e,n)),
e.match_length>=D)if(r=k._tr_tally(e,e.strstart-e.match_start,e.match_length-D),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=D){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+D-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],
e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else r=k._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(a(e,!1),0===e.strm.avail_out))return q}return e.insert=e.strstart<D-1?e.strstart:D-1,t===x?(a(e,!0),0===e.strm.avail_out?Y:W):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?q:X}function h(e,t){for(var n,r,i;;){if(e.lookahead<P){if(A(e),e.lookahead<P&&t===C)return q;if(0===e.lookahead)break}if(n=0,
e.lookahead>=D&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+D-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=D-1,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-P&&(e.match_length=d(e,n),e.match_length<=5&&(e.strategy===S||e.match_length===D&&e.strstart-e.match_start>4096)&&(e.match_length=D-1)),e.prev_length>=D&&e.match_length<=e.prev_length){
i=e.strstart+e.lookahead-D,r=k._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-D),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+D-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=D-1,e.strstart++,r&&(a(e,!1),0===e.strm.avail_out))return q}else if(e.match_available){
if((r=k._tr_tally(e,0,e.window[e.strstart-1]))&&a(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return q}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=k._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<D-1?e.strstart:D-1,t===x?(a(e,!0),0===e.strm.avail_out?Y:W):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?q:X}function p(e,t,n,r,i){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=r,this.func=i}function f(){
this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=T,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,
this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new v.Buf16(2*L),this.dyn_dtree=new v.Buf16(2*(2*M+1)),this.bl_tree=new v.Buf16(2*(2*j+1)),s(this.dyn_ltree),s(this.dyn_dtree),s(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new v.Buf16(O+1),this.heap=new v.Buf16(2*F+1),s(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new v.Buf16(2*F+1),
s(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function m(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=I,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?N:Q,e.adler=2===t.wrap?0:1,t.last_flush=C,k._tr_init(t),E):r(e,G)}function g(e){var t,n=m(e);return n===E&&((t=e.state).window_size=2*t.w_size,s(t.head),
t.max_lazy_match=b[t.level].max_lazy,t.good_match=b[t.level].good_length,t.nice_match=b[t.level].nice_length,t.max_chain_length=b[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=D-1,t.match_available=0,t.ins_h=0),n}function _(e,t,n,i,s,o){if(!e)return G;var a=1;if(t===Z&&(t=6),i<0?(a=0,i=-i):i>15&&(a=2,i-=16),s<1||s>U||n!==T||i<8||i>15||t<0||t>9||o<0||o>B)return r(e,G);8===i&&(i=9);var l=new f;return e.state=l,l.strm=e,l.wrap=a,l.gzhead=null,
l.w_bits=i,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=s+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+D-1)/D),l.window=new v.Buf8(2*l.w_size),l.head=new v.Buf16(l.hash_size),l.prev=new v.Buf16(l.w_size),l.lit_bufsize=1<<s+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new v.Buf8(l.pending_buf_size),l.d_buf=1*l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=t,l.strategy=o,l.method=n,g(e)}
var b,v=e("../utils/common"),k=e("./trees"),y=e("./adler32"),w=e("./crc32"),R=e("./messages"),C=0,x=4,E=0,G=-2,Z=-1,S=1,B=4,I=2,T=8,U=9,F=286,M=30,j=19,L=2*F+1,O=15,D=3,z=258,P=z+D+1,N=42,V=103,Q=113,H=666,q=1,X=2,Y=3,W=4;b=[new p(0,0,0,0,(function(e,t){var n=65535;for(n>e.pending_buf_size-5&&(n=e.pending_buf_size-5);;){if(e.lookahead<=1){if(A(e),0===e.lookahead&&t===C)return q;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var r=e.block_start+n
;if((0===e.strstart||e.strstart>=r)&&(e.lookahead=e.strstart-r,e.strstart=r,a(e,!1),0===e.strm.avail_out))return q;if(e.strstart-e.block_start>=e.w_size-P&&(a(e,!1),0===e.strm.avail_out))return q}return e.insert=0,t===x?(a(e,!0),0===e.strm.avail_out?Y:W):(e.strstart>e.block_start&&(a(e,!1),e.strm.avail_out),q)
})),new p(4,4,8,4,u),new p(4,5,16,8,u),new p(4,6,32,32,u),new p(4,4,16,16,h),new p(8,16,32,32,h),new p(8,16,128,128,h),new p(8,32,128,256,h),new p(32,128,258,1024,h),new p(32,258,258,4096,h)],n.deflateInit=function(e,t){return _(e,t,T,15,8,0)},n.deflateInit2=_,n.deflateReset=g,n.deflateResetKeep=m,n.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?G:(e.state.gzhead=t,E):G},n.deflate=function(e,t){var n,d,u,h;if(!e||!e.state||t>5||t<0)return e?r(e,G):G;if(d=e.state,
!e.output||!e.input&&0!==e.avail_in||d.status===H&&t!==x)return r(e,0===e.avail_out?-5:G);if(d.strm=e,n=d.last_flush,d.last_flush=t,d.status===N)if(2===d.wrap)e.adler=0,l(d,31),l(d,139),l(d,8),d.gzhead?(l(d,(d.gzhead.text?1:0)+(d.gzhead.hcrc?2:0)+(d.gzhead.extra?4:0)+(d.gzhead.name?8:0)+(d.gzhead.comment?16:0)),l(d,255&d.gzhead.time),l(d,d.gzhead.time>>8&255),l(d,d.gzhead.time>>16&255),l(d,d.gzhead.time>>24&255),l(d,9===d.level?2:d.strategy>=2||d.level<2?4:0),l(d,255&d.gzhead.os),
d.gzhead.extra&&d.gzhead.extra.length&&(l(d,255&d.gzhead.extra.length),l(d,d.gzhead.extra.length>>8&255)),d.gzhead.hcrc&&(e.adler=w(e.adler,d.pending_buf,d.pending,0)),d.gzindex=0,d.status=69):(l(d,0),l(d,0),l(d,0),l(d,0),l(d,0),l(d,9===d.level?2:d.strategy>=2||d.level<2?4:0),l(d,3),d.status=Q);else{var p=T+(d.w_bits-8<<4)<<8;p|=(d.strategy>=2||d.level<2?0:d.level<6?1:6===d.level?2:3)<<6,0!==d.strstart&&(p|=32),p+=31-p%31,d.status=Q,c(d,p),0!==d.strstart&&(c(d,e.adler>>>16),c(d,65535&e.adler)),
e.adler=1}if(69===d.status)if(d.gzhead.extra){for(u=d.pending;d.gzindex<(65535&d.gzhead.extra.length)&&(d.pending!==d.pending_buf_size||(d.gzhead.hcrc&&d.pending>u&&(e.adler=w(e.adler,d.pending_buf,d.pending-u,u)),o(e),u=d.pending,d.pending!==d.pending_buf_size));)l(d,255&d.gzhead.extra[d.gzindex]),d.gzindex++;d.gzhead.hcrc&&d.pending>u&&(e.adler=w(e.adler,d.pending_buf,d.pending-u,u)),d.gzindex===d.gzhead.extra.length&&(d.gzindex=0,d.status=73)}else d.status=73
;if(73===d.status)if(d.gzhead.name){u=d.pending;do{if(d.pending===d.pending_buf_size&&(d.gzhead.hcrc&&d.pending>u&&(e.adler=w(e.adler,d.pending_buf,d.pending-u,u)),o(e),u=d.pending,d.pending===d.pending_buf_size)){h=1;break}h=d.gzindex<d.gzhead.name.length?255&d.gzhead.name.charCodeAt(d.gzindex++):0,l(d,h)}while(0!==h);d.gzhead.hcrc&&d.pending>u&&(e.adler=w(e.adler,d.pending_buf,d.pending-u,u)),0===h&&(d.gzindex=0,d.status=91)}else d.status=91;if(91===d.status)if(d.gzhead.comment){u=d.pending
;do{if(d.pending===d.pending_buf_size&&(d.gzhead.hcrc&&d.pending>u&&(e.adler=w(e.adler,d.pending_buf,d.pending-u,u)),o(e),u=d.pending,d.pending===d.pending_buf_size)){h=1;break}h=d.gzindex<d.gzhead.comment.length?255&d.gzhead.comment.charCodeAt(d.gzindex++):0,l(d,h)}while(0!==h);d.gzhead.hcrc&&d.pending>u&&(e.adler=w(e.adler,d.pending_buf,d.pending-u,u)),0===h&&(d.status=V)}else d.status=V;if(d.status===V&&(d.gzhead.hcrc?(d.pending+2>d.pending_buf_size&&o(e),
d.pending+2<=d.pending_buf_size&&(l(d,255&e.adler),l(d,e.adler>>8&255),e.adler=0,d.status=Q)):d.status=Q),0!==d.pending){if(o(e),0===e.avail_out)return d.last_flush=-1,E}else if(0===e.avail_in&&i(t)<=i(n)&&t!==x)return r(e,-5);if(d.status===H&&0!==e.avail_in)return r(e,-5);if(0!==e.avail_in||0!==d.lookahead||t!==C&&d.status!==H){var f=2===d.strategy?function(e,t){for(var n;;){if(0===e.lookahead&&(A(e),0===e.lookahead)){if(t===C)return q;break}if(e.match_length=0,
n=k._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(a(e,!1),0===e.strm.avail_out))return q}return e.insert=0,t===x?(a(e,!0),0===e.strm.avail_out?Y:W):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?q:X}(d,t):3===d.strategy?function(e,t){for(var n,r,i,s,o=e.window;;){if(e.lookahead<=z){if(A(e),e.lookahead<=z&&t===C)return q;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=D&&e.strstart>0&&(r=o[i=e.strstart-1])===o[++i]&&r===o[++i]&&r===o[++i]){s=e.strstart+z
;do{}while(r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&i<s);e.match_length=z-(s-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=D?(n=k._tr_tally(e,1,e.match_length-D),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=k._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(a(e,!1),0===e.strm.avail_out))return q}return e.insert=0,t===x?(a(e,!0),
0===e.strm.avail_out?Y:W):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?q:X}(d,t):b[d.level].func(d,t);if(f!==Y&&f!==W||(d.status=H),f===q||f===Y)return 0===e.avail_out&&(d.last_flush=-1),E;if(f===X&&(1===t?k._tr_align(d):5!==t&&(k._tr_stored_block(d,0,0,!1),3===t&&(s(d.head),0===d.lookahead&&(d.strstart=0,d.block_start=0,d.insert=0))),o(e),0===e.avail_out))return d.last_flush=-1,E}return t!==x?E:d.wrap<=0?1:(2===d.wrap?(l(d,255&e.adler),l(d,e.adler>>8&255),l(d,e.adler>>16&255),
l(d,e.adler>>24&255),l(d,255&e.total_in),l(d,e.total_in>>8&255),l(d,e.total_in>>16&255),l(d,e.total_in>>24&255)):(c(d,e.adler>>>16),c(d,65535&e.adler)),o(e),d.wrap>0&&(d.wrap=-d.wrap),0!==d.pending?E:1)},n.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==N&&69!==t&&73!==t&&91!==t&&t!==V&&t!==Q&&t!==H?r(e,G):(e.state=null,t===Q?r(e,-3):E):G},n.deflateSetDictionary=function(e,t){var n,r,i,o,a,l,c,d,u=t.length;if(!e||!e.state)return G
;if(2===(o=(n=e.state).wrap)||1===o&&n.status!==N||n.lookahead)return G;for(1===o&&(e.adler=y(e.adler,t,u,0)),n.wrap=0,u>=n.w_size&&(0===o&&(s(n.head),n.strstart=0,n.block_start=0,n.insert=0),d=new v.Buf8(n.w_size),v.arraySet(d,t,u-n.w_size,n.w_size,0),t=d,u=n.w_size),a=e.avail_in,l=e.next_in,c=e.input,e.avail_in=u,e.next_in=0,e.input=t,A(n);n.lookahead>=D;){r=n.strstart,i=n.lookahead-(D-1);do{n.ins_h=(n.ins_h<<n.hash_shift^n.window[r+D-1])&n.hash_mask,n.prev[r&n.w_mask]=n.head[n.ins_h],
n.head[n.ins_h]=r,r++}while(--i);n.strstart=r,n.lookahead=D-1,A(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=D-1,n.match_available=0,e.next_in=l,e.input=c,e.avail_in=a,n.wrap=o,E},n.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(e,t){"use strict";t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,
this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(e,t){"use strict";t.exports=function(e,t){var n,r,i,s,o,a,l,c,d,A,u,h,p,f,m,g,_,b,v,k,y,w,R,C,x;n=e.state,r=e.next_in,C=e.input,i=r+(e.avail_in-5),s=e.next_out,x=e.output,o=s-(t-e.avail_out),a=s+(e.avail_out-257),l=n.dmax,c=n.wsize,d=n.whave,A=n.wnext,u=n.window,h=n.hold,p=n.bits,f=n.lencode,m=n.distcode,g=(1<<n.lenbits)-1,_=(1<<n.distbits)-1;e:do{p<15&&(h+=C[r++]<<p,p+=8,h+=C[r++]<<p,p+=8),
b=f[h&g];t:for(;;){if(h>>>=v=b>>>24,p-=v,0==(v=b>>>16&255))x[s++]=65535&b;else{if(!(16&v)){if(0==(64&v)){b=f[(65535&b)+(h&(1<<v)-1)];continue t}if(32&v){n.mode=12;break e}e.msg="invalid literal/length code",n.mode=30;break e}k=65535&b,(v&=15)&&(p<v&&(h+=C[r++]<<p,p+=8),k+=h&(1<<v)-1,h>>>=v,p-=v),p<15&&(h+=C[r++]<<p,p+=8,h+=C[r++]<<p,p+=8),b=m[h&_];n:for(;;){if(h>>>=v=b>>>24,p-=v,!(16&(v=b>>>16&255))){if(0==(64&v)){b=m[(65535&b)+(h&(1<<v)-1)];continue n}e.msg="invalid distance code",n.mode=30
;break e}if(y=65535&b,p<(v&=15)&&(h+=C[r++]<<p,(p+=8)<v&&(h+=C[r++]<<p,p+=8)),(y+=h&(1<<v)-1)>l){e.msg="invalid distance too far back",n.mode=30;break e}if(h>>>=v,p-=v,y>(v=s-o)){if((v=y-v)>d&&n.sane){e.msg="invalid distance too far back",n.mode=30;break e}if(w=0,R=u,0===A){if(w+=c-v,v<k){k-=v;do{x[s++]=u[w++]}while(--v);w=s-y,R=x}}else if(A<v){if(w+=c+A-v,(v-=A)<k){k-=v;do{x[s++]=u[w++]}while(--v);if(w=0,A<k){k-=v=A;do{x[s++]=u[w++]}while(--v);w=s-y,R=x}}}else if(w+=A-v,v<k){k-=v;do{
x[s++]=u[w++]}while(--v);w=s-y,R=x}for(;k>2;)x[s++]=R[w++],x[s++]=R[w++],x[s++]=R[w++],k-=3;k&&(x[s++]=R[w++],k>1&&(x[s++]=R[w++]))}else{w=s-y;do{x[s++]=x[w++],x[s++]=x[w++],x[s++]=x[w++],k-=3}while(k>2);k&&(x[s++]=x[w++],k>1&&(x[s++]=x[w++]))}break}}break}}while(r<i&&s<a);r-=k=p>>3,h&=(1<<(p-=k<<3))-1,e.next_in=r,e.next_out=s,e.avail_in=r<i?i-r+5:5-(r-i),e.avail_out=s<a?a-s+257:257-(s-a),n.hold=h,n.bits=p}},{}],49:[function(e,t,n){"use strict";function r(e){
return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function i(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new h.Buf16(320),
this.work=new h.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function s(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=y,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new h.Buf32(C),t.distcode=t.distdyn=new h.Buf32(x),t.sane=1,t.back=-1,v):k}function o(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,s(e)):k}function a(e,t){var n,r
;return e&&e.state?(r=e.state,t<0?(n=0,t=-t):(n=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?k:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=n,r.wbits=t,o(e))):k}function l(e,t){var n,r;return e?(r=new i,e.state=r,r.window=null,(n=a(e,t))!==v&&(e.state=null),n):k}function c(e){if(E){var t;for(A=new h.Buf32(512),u=new h.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(g(_,e.lens,0,288,A,0,e.work,{bits:9}),
t=0;t<32;)e.lens[t++]=5;g(b,e.lens,0,32,u,0,e.work,{bits:5}),E=!1}e.lencode=A,e.lenbits=9,e.distcode=u,e.distbits=5}function d(e,t,n,r){var i,s=e.state;return null===s.window&&(s.wsize=1<<s.wbits,s.wnext=0,s.whave=0,s.window=new h.Buf8(s.wsize)),r>=s.wsize?(h.arraySet(s.window,t,n-s.wsize,s.wsize,0),s.wnext=0,s.whave=s.wsize):((i=s.wsize-s.wnext)>r&&(i=r),h.arraySet(s.window,t,n-r,i,s.wnext),(r-=i)?(h.arraySet(s.window,t,n-r,r,0),s.wnext=r,s.whave=s.wsize):(s.wnext+=i,
s.wnext===s.wsize&&(s.wnext=0),s.whave<s.wsize&&(s.whave+=i))),0}var A,u,h=e("../utils/common"),p=e("./adler32"),f=e("./crc32"),m=e("./inffast"),g=e("./inftrees"),_=1,b=2,v=0,k=-2,y=1,w=12,R=30,C=852,x=592,E=!0;n.inflateReset=o,n.inflateReset2=a,n.inflateResetKeep=s,n.inflateInit=function(e){return l(e,15)},n.inflateInit2=l,n.inflate=function(e,t){var n,i,s,o,a,l,A,u,C,x,E,G,Z,S,B,I,T,U,F,M,j,L,O,D,z=0,P=new h.Buf8(4),N=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]
;if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return k;(n=e.state).mode===w&&(n.mode=13),a=e.next_out,s=e.output,A=e.avail_out,o=e.next_in,i=e.input,l=e.avail_in,u=n.hold,C=n.bits,x=l,E=A,L=v;e:for(;;)switch(n.mode){case y:if(0===n.wrap){n.mode=13;break}for(;C<16;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if(2&n.wrap&&35615===u){n.check=0,P[0]=255&u,P[1]=u>>>8&255,n.check=f(n.check,P,2,0),u=0,C=0,n.mode=2;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&u)<<8)+(u>>8))%31){
e.msg="incorrect header check",n.mode=R;break}if(8!=(15&u)){e.msg="unknown compression method",n.mode=R;break}if(C-=4,j=8+(15&(u>>>=4)),0===n.wbits)n.wbits=j;else if(j>n.wbits){e.msg="invalid window size",n.mode=R;break}n.dmax=1<<j,e.adler=n.check=1,n.mode=512&u?10:w,u=0,C=0;break;case 2:for(;C<16;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if(n.flags=u,8!=(255&n.flags)){e.msg="unknown compression method",n.mode=R;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=R;break}
n.head&&(n.head.text=u>>8&1),512&n.flags&&(P[0]=255&u,P[1]=u>>>8&255,n.check=f(n.check,P,2,0)),u=0,C=0,n.mode=3;case 3:for(;C<32;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}n.head&&(n.head.time=u),512&n.flags&&(P[0]=255&u,P[1]=u>>>8&255,P[2]=u>>>16&255,P[3]=u>>>24&255,n.check=f(n.check,P,4,0)),u=0,C=0,n.mode=4;case 4:for(;C<16;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}n.head&&(n.head.xflags=255&u,n.head.os=u>>8),512&n.flags&&(P[0]=255&u,P[1]=u>>>8&255,n.check=f(n.check,P,2,0)),u=0,C=0,n.mode=5
;case 5:if(1024&n.flags){for(;C<16;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}n.length=u,n.head&&(n.head.extra_len=u),512&n.flags&&(P[0]=255&u,P[1]=u>>>8&255,n.check=f(n.check,P,2,0)),u=0,C=0}else n.head&&(n.head.extra=null);n.mode=6;case 6:if(1024&n.flags&&((G=n.length)>l&&(G=l),G&&(n.head&&(j=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Array(n.head.extra_len)),h.arraySet(n.head.extra,i,o,G,j)),512&n.flags&&(n.check=f(n.check,i,G,o)),l-=G,o+=G,n.length-=G),n.length))break e
;n.length=0,n.mode=7;case 7:if(2048&n.flags){if(0===l)break e;G=0;do{j=i[o+G++],n.head&&j&&n.length<65536&&(n.head.name+=String.fromCharCode(j))}while(j&&G<l);if(512&n.flags&&(n.check=f(n.check,i,G,o)),l-=G,o+=G,j)break e}else n.head&&(n.head.name=null);n.length=0,n.mode=8;case 8:if(4096&n.flags){if(0===l)break e;G=0;do{j=i[o+G++],n.head&&j&&n.length<65536&&(n.head.comment+=String.fromCharCode(j))}while(j&&G<l);if(512&n.flags&&(n.check=f(n.check,i,G,o)),l-=G,o+=G,j)break e
}else n.head&&(n.head.comment=null);n.mode=9;case 9:if(512&n.flags){for(;C<16;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if(u!==(65535&n.check)){e.msg="header crc mismatch",n.mode=R;break}u=0,C=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=w;break;case 10:for(;C<32;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}e.adler=n.check=r(u),u=0,C=0,n.mode=11;case 11:if(0===n.havedict)return e.next_out=a,e.avail_out=A,e.next_in=o,e.avail_in=l,n.hold=u,n.bits=C,2;e.adler=n.check=1,
n.mode=w;case w:if(5===t||6===t)break e;case 13:if(n.last){u>>>=7&C,C-=7&C,n.mode=27;break}for(;C<3;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}switch(n.last=1&u,C-=1,3&(u>>>=1)){case 0:n.mode=14;break;case 1:if(c(n),n.mode=20,6===t){u>>>=2,C-=2;break e}break;case 2:n.mode=17;break;case 3:e.msg="invalid block type",n.mode=R}u>>>=2,C-=2;break;case 14:for(u>>>=7&C,C-=7&C;C<32;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if((65535&u)!=(u>>>16^65535)){e.msg="invalid stored block lengths",n.mode=R;break}
if(n.length=65535&u,u=0,C=0,n.mode=15,6===t)break e;case 15:n.mode=16;case 16:if(G=n.length){if(G>l&&(G=l),G>A&&(G=A),0===G)break e;h.arraySet(s,i,o,G,a),l-=G,o+=G,A-=G,a+=G,n.length-=G;break}n.mode=w;break;case 17:for(;C<14;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if(n.nlen=257+(31&u),u>>>=5,C-=5,n.ndist=1+(31&u),u>>>=5,C-=5,n.ncode=4+(15&u),u>>>=4,C-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=R;break}n.have=0,n.mode=18;case 18:for(;n.have<n.ncode;){for(;C<3;){
if(0===l)break e;l--,u+=i[o++]<<C,C+=8}n.lens[N[n.have++]]=7&u,u>>>=3,C-=3}for(;n.have<19;)n.lens[N[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,O={bits:n.lenbits},L=g(0,n.lens,0,19,n.lencode,0,n.work,O),n.lenbits=O.bits,L){e.msg="invalid code lengths set",n.mode=R;break}n.have=0,n.mode=19;case 19:for(;n.have<n.nlen+n.ndist;){for(;I=(z=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,T=65535&z,!((B=z>>>24)<=C);){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if(T<16)u>>>=B,C-=B,n.lens[n.have++]=T;else{
if(16===T){for(D=B+2;C<D;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if(u>>>=B,C-=B,0===n.have){e.msg="invalid bit length repeat",n.mode=R;break}j=n.lens[n.have-1],G=3+(3&u),u>>>=2,C-=2}else if(17===T){for(D=B+3;C<D;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}C-=B,j=0,G=3+(7&(u>>>=B)),u>>>=3,C-=3}else{for(D=B+7;C<D;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}C-=B,j=0,G=11+(127&(u>>>=B)),u>>>=7,C-=7}if(n.have+G>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=R;break}for(;G--;)n.lens[n.have++]=j}}
if(n.mode===R)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=R;break}if(n.lenbits=9,O={bits:n.lenbits},L=g(_,n.lens,0,n.nlen,n.lencode,0,n.work,O),n.lenbits=O.bits,L){e.msg="invalid literal/lengths set",n.mode=R;break}if(n.distbits=6,n.distcode=n.distdyn,O={bits:n.distbits},L=g(b,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,O),n.distbits=O.bits,L){e.msg="invalid distances set",n.mode=R;break}if(n.mode=20,6===t)break e;case 20:n.mode=21;case 21:if(l>=6&&A>=258){
e.next_out=a,e.avail_out=A,e.next_in=o,e.avail_in=l,n.hold=u,n.bits=C,m(e,E),a=e.next_out,s=e.output,A=e.avail_out,o=e.next_in,i=e.input,l=e.avail_in,u=n.hold,C=n.bits,n.mode===w&&(n.back=-1);break}for(n.back=0;I=(z=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,T=65535&z,!((B=z>>>24)<=C);){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if(I&&0==(240&I)){for(U=B,F=I,M=T;I=(z=n.lencode[M+((u&(1<<U+F)-1)>>U)])>>>16&255,T=65535&z,!(U+(B=z>>>24)<=C);){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}u>>>=U,C-=U,n.back+=U}
if(u>>>=B,C-=B,n.back+=B,n.length=T,0===I){n.mode=26;break}if(32&I){n.back=-1,n.mode=w;break}if(64&I){e.msg="invalid literal/length code",n.mode=R;break}n.extra=15&I,n.mode=22;case 22:if(n.extra){for(D=n.extra;C<D;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}n.length+=u&(1<<n.extra)-1,u>>>=n.extra,C-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=23;case 23:for(;I=(z=n.distcode[u&(1<<n.distbits)-1])>>>16&255,T=65535&z,!((B=z>>>24)<=C);){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if(0==(240&I)){for(U=B,
F=I,M=T;I=(z=n.distcode[M+((u&(1<<U+F)-1)>>U)])>>>16&255,T=65535&z,!(U+(B=z>>>24)<=C);){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}u>>>=U,C-=U,n.back+=U}if(u>>>=B,C-=B,n.back+=B,64&I){e.msg="invalid distance code",n.mode=R;break}n.offset=T,n.extra=15&I,n.mode=24;case 24:if(n.extra){for(D=n.extra;C<D;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}n.offset+=u&(1<<n.extra)-1,u>>>=n.extra,C-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=R;break}n.mode=25;case 25:
if(0===A)break e;if(G=E-A,n.offset>G){if((G=n.offset-G)>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=R;break}G>n.wnext?(G-=n.wnext,Z=n.wsize-G):Z=n.wnext-G,G>n.length&&(G=n.length),S=n.window}else S=s,Z=a-n.offset,G=n.length;G>A&&(G=A),A-=G,n.length-=G;do{s[a++]=S[Z++]}while(--G);0===n.length&&(n.mode=21);break;case 26:if(0===A)break e;s[a++]=n.length,A--,n.mode=21;break;case 27:if(n.wrap){for(;C<32;){if(0===l)break e;l--,u|=i[o++]<<C,C+=8}if(E-=A,e.total_out+=E,n.total+=E,
E&&(e.adler=n.check=n.flags?f(n.check,s,E,a-E):p(n.check,s,E,a-E)),E=A,(n.flags?u:r(u))!==n.check){e.msg="incorrect data check",n.mode=R;break}u=0,C=0}n.mode=28;case 28:if(n.wrap&&n.flags){for(;C<32;){if(0===l)break e;l--,u+=i[o++]<<C,C+=8}if(u!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=R;break}u=0,C=0}n.mode=29;case 29:L=1;break e;case R:L=-3;break e;case 31:return-4;default:return k}return e.next_out=a,e.avail_out=A,e.next_in=o,e.avail_in=l,n.hold=u,n.bits=C,
(n.wsize||E!==e.avail_out&&n.mode<R&&(n.mode<27||4!==t))&&d(e,e.output,e.next_out,E-e.avail_out)?(n.mode=31,-4):(x-=e.avail_in,E-=e.avail_out,e.total_in+=x,e.total_out+=E,n.total+=E,n.wrap&&E&&(e.adler=n.check=n.flags?f(n.check,s,E,e.next_out-E):p(n.check,s,E,e.next_out-E)),e.data_type=n.bits+(n.last?64:0)+(n.mode===w?128:0)+(20===n.mode||15===n.mode?256:0),(0===x&&0===E||4===t)&&L===v&&(L=-5),L)},n.inflateEnd=function(e){if(!e||!e.state)return k;var t=e.state;return t.window&&(t.window=null),
e.state=null,v},n.inflateGetHeader=function(e,t){var n;return e&&e.state?0==(2&(n=e.state).wrap)?k:(n.head=t,t.done=!1,v):k},n.inflateSetDictionary=function(e,t){var n,r=t.length;return e&&e.state?0!==(n=e.state).wrap&&11!==n.mode?k:11===n.mode&&p(1,t,r,0)!==n.check?-3:d(e,t,r,r)?(n.mode=31,-4):(n.havedict=1,v):k},n.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(e,t){"use strict"
;var n=e("../utils/common"),r=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],i=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],s=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],o=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,a,l,c,d,A,u){
var h,p,f,m,g,_,b,v,k,y=u.bits,w=0,R=0,C=0,x=0,E=0,G=0,Z=0,S=0,B=0,I=0,T=null,U=0,F=new n.Buf16(16),M=new n.Buf16(16),j=null,L=0;for(w=0;w<=15;w++)F[w]=0;for(R=0;R<l;R++)F[t[a+R]]++;for(E=y,x=15;x>=1&&0===F[x];x--);if(E>x&&(E=x),0===x)return c[d++]=20971520,c[d++]=20971520,u.bits=1,0;for(C=1;C<x&&0===F[C];C++);for(E<C&&(E=C),S=1,w=1;w<=15;w++)if(S<<=1,(S-=F[w])<0)return-1;if(S>0&&(0===e||1!==x))return-1;for(M[1]=0,w=1;w<15;w++)M[w+1]=M[w]+F[w];for(R=0;R<l;R++)0!==t[a+R]&&(A[M[t[a+R]]++]=R)
;if(0===e?(T=j=A,_=19):1===e?(T=r,U-=257,j=i,L-=257,_=256):(T=s,j=o,_=-1),I=0,R=0,w=C,g=d,G=E,Z=0,f=-1,m=(B=1<<E)-1,1===e&&B>852||2===e&&B>592)return 1;for(;;){b=w-Z,A[R]<_?(v=0,k=A[R]):A[R]>_?(v=j[L+A[R]],k=T[U+A[R]]):(v=96,k=0),h=1<<w-Z,C=p=1<<G;do{c[g+(I>>Z)+(p-=h)]=b<<24|v<<16|k|0}while(0!==p);for(h=1<<w-1;I&h;)h>>=1;if(0!==h?(I&=h-1,I+=h):I=0,R++,0==--F[w]){if(w===x)break;w=t[a+A[R]]}if(w>E&&(I&m)!==f){for(0===Z&&(Z=E),g+=C,S=1<<(G=w-Z);G+Z<x&&!((S-=F[G+Z])<=0);)G++,S<<=1;if(B+=1<<G,
1===e&&B>852||2===e&&B>592)return 1;c[f=I&m]=E<<24|G<<16|g-d|0}}return 0!==I&&(c[g+I]=w-Z<<24|64<<16|0),u.bits=E,0}},{"../utils/common":41}],51:[function(e,t){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(e,t,n){"use strict";function r(e){for(var t=e.length;--t>=0;)e[t]=0}function i(e,t,n,r,i){this.static_tree=e,this.extra_bits=t,
this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=e&&e.length}function s(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function o(e){return e<256?D[e]:D[256+(e>>>7)]}function a(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function l(e,t,n){e.bi_valid>Z-n?(e.bi_buf|=t<<e.bi_valid&65535,a(e,e.bi_buf),e.bi_buf=t>>Z-e.bi_valid,e.bi_valid+=n-Z):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)}function c(e,t,n){l(e,n[2*t],n[2*t+1])}function d(e,t){
var n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}function A(e,t,n){var r,i,s=new Array(G+1),o=0;for(r=1;r<=G;r++)s[r]=o=o+n[r-1]<<1;for(i=0;i<=t;i++){var a=e[2*i+1];0!==a&&(e[2*i]=d(s[a]++,a))}}function u(e){var t;for(t=0;t<R;t++)e.dyn_ltree[2*t]=0;for(t=0;t<C;t++)e.dyn_dtree[2*t]=0;for(t=0;t<x;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*S]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function h(e){e.bi_valid>8?a(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,
e.bi_valid=0}function p(e,t,n,r){var i=2*t,s=2*n;return e[i]<e[s]||e[i]===e[s]&&r[t]<=r[n]}function f(e,t,n){for(var r=e.heap[n],i=n<<1;i<=e.heap_len&&(i<e.heap_len&&p(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!p(t,r,e.heap[i],e.depth));)e.heap[n]=e.heap[i],n=i,i<<=1;e.heap[n]=r}function m(e,t,n){var r,i,s,a,d=0;if(0!==e.last_lit)do{r=e.pending_buf[e.d_buf+2*d]<<8|e.pending_buf[e.d_buf+2*d+1],i=e.pending_buf[e.l_buf+d],d++,0===r?c(e,i,t):(c(e,(s=z[i])+w+1,t),0!==(a=U[s])&&l(e,i-=P[s],a),
c(e,s=o(--r),n),0!==(a=F[s])&&l(e,r-=H[s],a))}while(d<e.last_lit);c(e,S,t)}function g(e,t){var n,r,i,s=t.dyn_tree,o=t.stat_desc.static_tree,a=t.stat_desc.has_stree,l=t.stat_desc.elems,c=-1;for(e.heap_len=0,e.heap_max=E,n=0;n<l;n++)0!==s[2*n]?(e.heap[++e.heap_len]=c=n,e.depth[n]=0):s[2*n+1]=0;for(;e.heap_len<2;)s[2*(i=e.heap[++e.heap_len]=c<2?++c:0)]=1,e.depth[i]=0,e.opt_len--,a&&(e.static_len-=o[2*i+1]);for(t.max_code=c,n=e.heap_len>>1;n>=1;n--)f(e,s,n);i=l;do{n=e.heap[1],
e.heap[1]=e.heap[e.heap_len--],f(e,s,1),r=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=r,s[2*i]=s[2*n]+s[2*r],e.depth[i]=(e.depth[n]>=e.depth[r]?e.depth[n]:e.depth[r])+1,s[2*n+1]=s[2*r+1]=i,e.heap[1]=i++,f(e,s,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var n,r,i,s,o,a,l=t.dyn_tree,c=t.max_code,d=t.stat_desc.static_tree,A=t.stat_desc.has_stree,u=t.stat_desc.extra_bits,h=t.stat_desc.extra_base,p=t.stat_desc.max_length,f=0;for(s=0;s<=G;s++)e.bl_count[s]=0
;for(l[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<E;n++)(s=l[2*l[2*(r=e.heap[n])+1]+1]+1)>p&&(s=p,f++),l[2*r+1]=s,r>c||(e.bl_count[s]++,o=0,r>=h&&(o=u[r-h]),a=l[2*r],e.opt_len+=a*(s+o),A&&(e.static_len+=a*(d[2*r+1]+o)));if(0!==f){do{for(s=p-1;0===e.bl_count[s];)s--;e.bl_count[s]--,e.bl_count[s+1]+=2,e.bl_count[p]--,f-=2}while(f>0);for(s=p;0!==s;s--)for(r=e.bl_count[s];0!==r;)(i=e.heap[--n])>c||(l[2*i+1]!==s&&(e.opt_len+=(s-l[2*i+1])*l[2*i],l[2*i+1]=s),r--)}}(e,t),A(s,c,e.bl_count)}
function _(e,t,n){var r,i,s=-1,o=t[1],a=0,l=7,c=4;for(0===o&&(l=138,c=3),t[2*(n+1)+1]=65535,r=0;r<=n;r++)i=o,o=t[2*(r+1)+1],++a<l&&i===o||(a<c?e.bl_tree[2*i]+=a:0!==i?(i!==s&&e.bl_tree[2*i]++,e.bl_tree[2*B]++):a<=10?e.bl_tree[2*I]++:e.bl_tree[2*T]++,a=0,s=i,0===o?(l=138,c=3):i===o?(l=6,c=3):(l=7,c=4))}function b(e,t,n){var r,i,s=-1,o=t[1],a=0,d=7,A=4;for(0===o&&(d=138,A=3),r=0;r<=n;r++)if(i=o,o=t[2*(r+1)+1],!(++a<d&&i===o)){if(a<A)do{c(e,i,e.bl_tree)
}while(0!=--a);else 0!==i?(i!==s&&(c(e,i,e.bl_tree),a--),c(e,B,e.bl_tree),l(e,a-3,2)):a<=10?(c(e,I,e.bl_tree),l(e,a-3,3)):(c(e,T,e.bl_tree),l(e,a-11,7));a=0,s=i,0===o?(d=138,A=3):i===o?(d=6,A=3):(d=7,A=4)}}function v(e,t,n,r){l(e,(y<<1)+(r?1:0),3),function(e,t,n){h(e),a(e,n),a(e,~n),k.arraySet(e.pending_buf,e.window,t,n,e.pending),e.pending+=n}(e,t,n)}
var k=e("../utils/common"),y=0,w=256,R=w+1+29,C=30,x=19,E=2*R+1,G=15,Z=16,S=256,B=16,I=17,T=18,U=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],F=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],M=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],j=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],L=new Array(2*(R+2));r(L);var O=new Array(2*C);r(O);var D=new Array(512);r(D);var z=new Array(256);r(z);var P=new Array(29);r(P);var N,V,Q,H=new Array(C);r(H);var q=!1
;n._tr_init=function(e){q||(function(){var e,t,n,r,s,o=new Array(G+1);for(n=0,r=0;r<28;r++)for(P[r]=n,e=0;e<1<<U[r];e++)z[n++]=r;for(z[n-1]=r,s=0,r=0;r<16;r++)for(H[r]=s,e=0;e<1<<F[r];e++)D[s++]=r;for(s>>=7;r<C;r++)for(H[r]=s<<7,e=0;e<1<<F[r]-7;e++)D[256+s++]=r;for(t=0;t<=G;t++)o[t]=0;for(e=0;e<=143;)L[2*e+1]=8,e++,o[8]++;for(;e<=255;)L[2*e+1]=9,e++,o[9]++;for(;e<=279;)L[2*e+1]=7,e++,o[7]++;for(;e<=287;)L[2*e+1]=8,e++,o[8]++;for(A(L,R+1,o),e=0;e<C;e++)O[2*e+1]=5,O[2*e]=d(e,5)
;N=new i(L,U,w+1,R,G),V=new i(O,F,0,C,G),Q=new i(new Array(0),M,0,x,7)}(),q=!0),e.l_desc=new s(e.dyn_ltree,N),e.d_desc=new s(e.dyn_dtree,V),e.bl_desc=new s(e.bl_tree,Q),e.bi_buf=0,e.bi_valid=0,u(e)},n._tr_stored_block=v,n._tr_flush_block=function(e,t,n,r){var i,s,o=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1
;for(t=32;t<w;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),g(e,e.l_desc),g(e,e.d_desc),o=function(e){var t;for(_(e,e.dyn_ltree,e.l_desc.max_code),_(e,e.dyn_dtree,e.d_desc.max_code),g(e,e.bl_desc),t=x-1;t>=3&&0===e.bl_tree[2*j[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(s=e.static_len+3+7>>>3)<=i&&(i=s)):i=s=n+5,n+4<=i&&-1!==t?v(e,t,n,r):4===e.strategy||s===i?(l(e,2+(r?1:0),3),m(e,L,O)):(l(e,4+(r?1:0),3),function(e,t,n,r){var i;for(l(e,t-257,5),l(e,n-1,5),
l(e,r-4,4),i=0;i<r;i++)l(e,e.bl_tree[2*j[i]+1],3);b(e,e.dyn_ltree,t-1),b(e,e.dyn_dtree,n-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,o+1),m(e,e.dyn_ltree,e.dyn_dtree)),u(e),r&&h(e)},n._tr_tally=function(e,t,n){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&n,e.last_lit++,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(z[n]+w+1)]++,e.dyn_dtree[2*o(t)]++),e.last_lit===e.lit_bufsize-1},
n._tr_align=function(e){l(e,2,3),c(e,S,L),function(e){16===e.bi_valid?(a(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)}},{"../utils/common":41}],53:[function(e,t){"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(e,t){"use strict"
;t.exports="function"==typeof setImmediate?setImmediate:function(){var e=[].slice.apply(arguments);e.splice(1,0,0),setTimeout.apply(null,e)}},{}]},{},[10])(10)}},n={};e.n=t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},e.d=(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){
if("object"==typeof window)return window}}(),e.o=(e,t)=>({}.hasOwnProperty.call(e,t)),(()=>{"use strict";function t(e,t){return null===e||void 0===e&&t?"":void 0===e?void 0:`${e}`}function n(e){if(this.data="",this.read=0,"string"==typeof e)this.data=e;else if(Ci.isArrayBuffer(e)||Ci.isArrayBufferView(e)){var t=new Uint8Array(e);try{this.data=String.fromCharCode.apply(null,t)}catch(e){for(var r=0;r<t.length;++r)this.putByte(t[r])}
}else(e instanceof n||"object"==typeof e&&"string"==typeof e.data&&"number"==typeof e.read)&&(this.data=e.data,this.read=e.read);this._constructedStringLength=0}function r(e,t,n){for(var r,i,s,o,a,l,c,d,A,u,h,p,f,m=n.length();m>=64;){for(a=0;a<16;++a)t[a]=n.getInt32();for(;a<64;++a)r=((r=t[a-2])>>>17|r<<15)^(r>>>19|r<<13)^r>>>10,i=((i=t[a-15])>>>7|i<<25)^(i>>>18|i<<14)^i>>>3,t[a]=r+t[a-7]+i+t[a-16]|0;for(l=e.h0,c=e.h1,d=e.h2,A=e.h3,u=e.h4,h=e.h5,p=e.h6,f=e.h7,
a=0;a<64;++a)s=(l>>>2|l<<30)^(l>>>13|l<<19)^(l>>>22|l<<10),o=l&c|d&(l^c),r=f+((u>>>6|u<<26)^(u>>>11|u<<21)^(u>>>25|u<<7))+(p^u&(h^p))+Zi[a]+t[a],f=p,p=h,h=u,u=A+r|0,A=d,d=c,c=l,l=r+(i=s+o)|0;e.h0=e.h0+l|0,e.h1=e.h1+c|0,e.h2=e.h2+d|0,e.h3=e.h3+A|0,e.h4=e.h4+u|0,e.h5=e.h5+h|0,e.h6=e.h6+p|0,e.h7=e.h7+f|0,m-=64}}let i;Object.fromEntries?i=Object.fromEntries:(i=e=>[...e].reduce(((e,[t,n])=>(e[t]=n,e)),{}),Object.fromEntries=i),Object.defineProperties(Promise.prototype,{done:{value:function(e){
return this.then(((...t)=>e.apply(this,t)))},configurable:!0,enumerable:!0,writable:!1},fail:{value:function(e){return this.then((()=>{}),((...t)=>e.apply(this,t)))},configurable:!0,enumerable:!0,writable:!1},always:{value:function(e){return this.then(((...t)=>e.apply(this,t)),((...t)=>e.apply(this,t)))},configurable:!0,enumerable:!0,writable:!1}})
;const s=Promise,{AbortController:o,FileReader:a,TextDecoder:l,atob:c,btoa:d,clearInterval:A,clearTimeout:u,crypto:h,decodeURIComponent:p,encodeURIComponent:f,escape:m,location:g,setInterval:_,setTimeout:b,unescape:v}=self;let{fetch:k}=self;g.origin,g.host;const{addEventListener:y,removeEventListener:w}=self,R=y,{DOMParser:C,Notification:x,Image:E,Worker:G,alert:Z,confirm:S,document:B,screen:I,webkitNotifications:T}=self;let U;try{U=self.localStorage}catch(e){}const F=U
;let{XMLHttpRequest:M}=self;const j=(e,t)=>{null==t&&(t=[]);const n=new RegExp("(\\"+["/",".","+","?","|","(",")","[","]","{","}","\\"].concat(t).join("|\\")+")","g");return e.replace(n,"\\$1")},L=e=>j(e,["*"]),O={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","\\":"&bsol;"},D=e=>(({}.toString.apply(e).match(/\s([a-z|A-Z]+)/)||[])[1]),z=(e,t)=>{const n=D(e);if("Array"===n||"NodeList"===n){for(let n=0;n<e.length;n++)if(!1===t(e[n],n))return}else if("XPathResult"===n){let n=e.iterateNext(),r=0
;for(;n;){if(!1===t(n,r++))return;n=e.iterateNext()}}else for(const n in e)if(e.hasOwnProperty(n)&&!1===t(e[n],n))return},P=(e,t,n,r)=>{let i;if(Array.isArray(n)){const e={};n.forEach((t=>{e[t]=!0})),i=e}else i=n;return z(i||e,((n,s)=>{if(!i||i.hasOwnProperty(s)){let n;const o=e[s],a=D(o);if("Undefined"==a)return;if(i&&r&&(n=D(i[s]))&&n!==a&&("Array"===n||"Object"===n))return;"Object"==a?(t[s]={},P(o,t[s],i?i[s]:null)):"Array"==a?(t[s]=[],P(o,t[s])):t[s]=o}})),t},N=(e,t)=>{let n=!0
;if("object"!=typeof e||"object"!=typeof t||null===e||null===t)return e===t;for(const[r,i]of Object.entries(t))if(void 0!==i&&(n=void 0!==e[r]&&(null===i?null===e[r]:"object"==typeof i?N(e[r],i):e[r]===i),!n))break;return n},V=(e,t,n)=>{void 0===n&&(n="-");const r=t?/[:<>|~?*\x00-\x1F\uFDD0-\uFDEF"]/g:/[:<>|~?*\x00-\x1F\uFDD0-\uFDEF"\/\\]|^[.]|[.]$/g
;return!t&&/^((CON|PRN|AUX|NUL|CLOCK\$|COM[1-9]|LPT[1-9])(\..*)?|device(\..*)?|desktop.ini|thumbs.db)$/i.test(e)?(n||"_")+e.replace(r,n):e.replace(r,n)},Q=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(e=>{const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})),H=(e,t)=>{const n=[];for(let r=0,i=e.length;r<i;r+=t)n.push(e.slice(r,t+r));return n},q=e=>{let t,n,r=e.length;for(;0!==r;)n=Math.floor(Math.random()*r),r-=1,t=e[r],e[r]=e[n],e[n]=t;return e
},X=void 0,Y="display: none;";let W=0;const J=[],K=()=>{const e=["debug"],t=["log"],n=["warn","info"],r=["error"],i=[...e,...t,...n,...r],s=r;W>=80&&s.push(...e),W>=60&&s.push(...t),W>=30&&s.push(...n),i.forEach((e=>$[e]=s.includes(e)?console[e].bind(console):()=>{}))},$={set:e=>{W=e,J.forEach((e=>{e($,W)})),K()},get:()=>W,get verbose(){return($.debug||(()=>{})).bind(console)},debug:()=>{},log:()=>{},warn:()=>{},info:()=>{},error:()=>{},addChangeListener:e=>{J.push(e)}};K()
;const ee=e=>v(f(e)),te=e=>p(m(e)),ne=e=>{let t="";for(let n=0;n<e.length;n++)t+=String.fromCharCode(255&e.charCodeAt(n));return d(t)},re=e=>c(e),ie=(e,t)=>{try{let n,r;if("object"==typeof t?(n=t.encoding,r=t.array):n=t,!r&&n)return new l(n).decode(e);{let t=0;const r=[],i=e.byteLength;for(;t<i;t+=16384)r.push(String.fromCharCode.apply(null,new Uint8Array(e,t,Math.min(16384,i-t))));let s=r.join("");return n&&"utf-8"==n.toLowerCase()&&(s=te(s)),s}}catch(e){$.warn(e)}return null},se=(e,t)=>{try{
let n;n="object"==typeof t?t.encoding:t,n&&"utf-8"==n.toLowerCase()&&(e=ee(e));const r=new Uint8Array(e.length);for(let t=0;t<e.length;t++)r[t]=255&e.charCodeAt(t);return r.buffer}catch(e){$.warn(e)}return new Uint8Array(0).buffer},oe=(e,t)=>new Promise(((n,r)=>{const i=t&&t.encoding?"text/plain":"binary/octet-stream",s=new Blob([e],{type:i}),o=new a;o.onload=e=>{e.target?n(e.target.result):r(new Error("Could not convert array to string!"))},
t&&t.encoding?o.readAsText(s,t.encoding):o.readAsBinaryString(s)})),ae=(e,t)=>{const n=(e,t)=>e<<t|e>>>32-t,r=(e,t)=>{const n=2147483648&e,r=2147483648&t,i=1073741824&e,s=1073741824&t,o=(1073741823&e)+(1073741823&t);return i&s?2147483648^o^n^r:i|s?1073741824&o?3221225472^o^n^r:1073741824^o^n^r:o^n^r},i=(e,t,i,s,o,a,l)=>(e=r(e,r(r(((e,t,n)=>e&t|~e&n)(t,i,s),o),l)),r(n(e,a),t)),s=(e,t,i,s,o,a,l)=>(e=r(e,r(r(((e,t,n)=>e&n|t&~n)(t,i,s),o),l)),
r(n(e,a),t)),o=(e,t,i,s,o,a,l)=>(e=r(e,r(r(((e,t,n)=>e^t^n)(t,i,s),o),l)),r(n(e,a),t)),a=(e,t,i,s,o,a,l)=>(e=r(e,r(r(((e,t,n)=>t^(e|~n))(t,i,s),o),l)),r(n(e,a),t)),l=e=>{let t,n,r="",i="";for(n=0;n<=3;n++)t=e>>>8*n&255,i="0"+t.toString(16),r+=i.substr(i.length-2,2);return r};let c,d,A,u,h,p,f,m,g,_=[];for(t&&"utf-8"==t.toLowerCase()&&(e=ee(e)),_=(e=>{let t;const n=e.length,r=n+8,i=16*((r-r%64)/64+1),s=Array(i-1);let o=0,a=0;for(;a<n;)t=(a-a%4)/4,o=a%4*8,s[t]=s[t]|e.charCodeAt(a)<<o,a++
;return t=(a-a%4)/4,o=a%4*8,s[t]=s[t]|128<<o,s[i-2]=n<<3,s[i-1]=n>>>29,s})(e),p=1732584193,f=4023233417,m=2562383102,g=271733878,c=0;c<_.length;c+=16)d=p,A=f,u=m,h=g,p=i(p,f,m,g,_[c+0],7,3614090360),g=i(g,p,f,m,_[c+1],12,3905402710),m=i(m,g,p,f,_[c+2],17,606105819),f=i(f,m,g,p,_[c+3],22,3250441966),p=i(p,f,m,g,_[c+4],7,4118548399),g=i(g,p,f,m,_[c+5],12,1200080426),m=i(m,g,p,f,_[c+6],17,2821735955),f=i(f,m,g,p,_[c+7],22,4249261313),p=i(p,f,m,g,_[c+8],7,1770035416),
g=i(g,p,f,m,_[c+9],12,2336552879),m=i(m,g,p,f,_[c+10],17,4294925233),f=i(f,m,g,p,_[c+11],22,2304563134),p=i(p,f,m,g,_[c+12],7,1804603682),g=i(g,p,f,m,_[c+13],12,4254626195),m=i(m,g,p,f,_[c+14],17,2792965006),f=i(f,m,g,p,_[c+15],22,1236535329),p=s(p,f,m,g,_[c+1],5,4129170786),g=s(g,p,f,m,_[c+6],9,3225465664),m=s(m,g,p,f,_[c+11],14,643717713),f=s(f,m,g,p,_[c+0],20,3921069994),p=s(p,f,m,g,_[c+5],5,3593408605),g=s(g,p,f,m,_[c+10],9,38016083),m=s(m,g,p,f,_[c+15],14,3634488961),
f=s(f,m,g,p,_[c+4],20,3889429448),p=s(p,f,m,g,_[c+9],5,568446438),g=s(g,p,f,m,_[c+14],9,3275163606),m=s(m,g,p,f,_[c+3],14,4107603335),f=s(f,m,g,p,_[c+8],20,1163531501),p=s(p,f,m,g,_[c+13],5,2850285829),g=s(g,p,f,m,_[c+2],9,4243563512),m=s(m,g,p,f,_[c+7],14,1735328473),f=s(f,m,g,p,_[c+12],20,2368359562),p=o(p,f,m,g,_[c+5],4,4294588738),g=o(g,p,f,m,_[c+8],11,2272392833),m=o(m,g,p,f,_[c+11],16,1839030562),f=o(f,m,g,p,_[c+14],23,4259657740),p=o(p,f,m,g,_[c+1],4,2763975236),
g=o(g,p,f,m,_[c+4],11,1272893353),m=o(m,g,p,f,_[c+7],16,4139469664),f=o(f,m,g,p,_[c+10],23,3200236656),p=o(p,f,m,g,_[c+13],4,681279174),g=o(g,p,f,m,_[c+0],11,3936430074),m=o(m,g,p,f,_[c+3],16,3572445317),f=o(f,m,g,p,_[c+6],23,76029189),p=o(p,f,m,g,_[c+9],4,3654602809),g=o(g,p,f,m,_[c+12],11,3873151461),m=o(m,g,p,f,_[c+15],16,530742520),f=o(f,m,g,p,_[c+2],23,3299628645),p=a(p,f,m,g,_[c+0],6,4096336452),g=a(g,p,f,m,_[c+7],10,1126891415),m=a(m,g,p,f,_[c+14],15,2878612391),
f=a(f,m,g,p,_[c+5],21,4237533241),p=a(p,f,m,g,_[c+12],6,1700485571),g=a(g,p,f,m,_[c+3],10,2399980690),m=a(m,g,p,f,_[c+10],15,4293915773),f=a(f,m,g,p,_[c+1],21,2240044497),p=a(p,f,m,g,_[c+8],6,1873313359),g=a(g,p,f,m,_[c+15],10,4264355552),m=a(m,g,p,f,_[c+6],15,2734768916),f=a(f,m,g,p,_[c+13],21,1309151649),p=a(p,f,m,g,_[c+4],6,4149444226),g=a(g,p,f,m,_[c+11],10,3174756917),m=a(m,g,p,f,_[c+2],15,718787259),f=a(f,m,g,p,_[c+9],21,3951481745),p=r(p,d),f=r(f,A),m=r(m,u),g=r(g,h)
;return(l(p)+l(f)+l(m)+l(g)).toLowerCase()},le=e=>{let t;t=e.split(",")[0].includes("base64")?c(e.split(",")[1]):v(e.split(",")[1]);const n=e.split(",")[0].split(":")[1].split(";")[0],r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return new Blob([r],{type:n})},ce=e=>new Promise((t=>{const n=new a;n.onload=e=>{var n;t((null===(n=e.target)||void 0===n?void 0:n.result)||void 0)},n.readAsDataURL(e)})),de=async(e,t)=>new Promise((n=>{const r=new a;r.onload=()=>{
n(r.result||"")},r.onerror=e=>{$.warn(`unable to decode data ${e}`),n("")},t?r.readAsText(e,t):r.readAsBinaryString(e)})),Ae=(e,t)=>ne(e+"_"+t).replace(/[^0-9a-zA-Z_-]/g,""),ue=e=>{const t={};let n=window.location.search.replace(/^\?/,"");const r=window.location.hash.replace(/^#/,"");n?e&&r&&(n=n+"&"+r):n=r;const i=n.split("&");let s;for(let e=0;e<i.length;e++){if(s=i[e].split("="),2!=s.length){const e=s[0],t=s.slice(1).join("=");s=[e,t]}t[s[0]]=decodeURIComponent(s[1])}return t},he=e=>{
setTimeout((()=>{alert(e)}),1)},pe=(e,t)=>{setTimeout((()=>{const n=confirm(e);t&&t(n)}),1)},fe=(e,t,n,r,i,s)=>{let o;try{let a=e+"_"+Ae(n,r);if(null!=i&&(a+="_"+i),o=document.getElementById(a),o&&s){const t=document.createElement(e);t.setAttribute("id",a);const n=o.parentNode;n.insertBefore(t,o),n.removeChild(o),o=t}else o?o.inserted=!0:(o=document.createElement(e),o.setAttribute("id",a));t&&o.setAttribute("class",t),o.__removeChild||(o.__removeChild=o.removeChild,o.removeChild=e=>{
delete e.inserted,o.__removeChild(e)}),o.__appendChild||(o.__appendChild=o.appendChild,o.appendChild=(e,t)=>{"Array"!==D(e)&&(e=[e]),e.forEach((e=>{(!e.parentElement&&!e.inserted||t)&&o.__appendChild(e)}))}),o.__insertBefore||(o.__insertBefore=o.insertBefore,o.insertBefore=(e,t)=>{e.parentElement||e.inserted||o.__insertBefore(e,t)})}catch(e){console.log("options: Error:"+e.message)}return o},me=(e,t,n,r,i)=>fe(e,null,t,n,r,i),ge=globalThis,{chrome:_e,browser:be}=ge;let ve,ke,ye,we,Re
;const Ce=()=>{if(void 0!==ke)return ke;try{ke=-1!=navigator.userAgent.indexOf("Mac OS X")}catch(e){}return ke};we=()=>{if(void 0!==ve)return ve;try{const e=navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./);e&&(ve=parseInt(e[2]))}catch(e){}return ve},Re=()=>{if(void 0!==ye)return ye;try{ye=-1!=navigator.userAgent.search(/Android|Mobile/)}catch(e){}return ye};const xe=(we(),{CLOSE_ALLOWED:!0,MIN_DELAY:Ce()?150:0}),Ee={USE:null,DEFAULT:"chromeStorage",SECURE:!1,NO_WARNING:!1
},Ge=["chrome-extension:"];[].concat(["chrome"]);const Ze=e=>{window.setTimeout((()=>location.reload()),e||0)},Se=(()=>{const e={getInternalPathRegexp:function(e,t){const n=new RegExp("(\\"+["/",".","+","?","|","(",")","[","]","{","}","\\"].join("|\\")+")","g"),r=Ge[0]+"//"+Se.id+"/";return new RegExp(r.replace(n,"\\$1")+"([a-zA-Z"+(e?"\\/":"")+"]*)"+(t||"").replace(n,"\\$1"))},getInternalPageRegexp:function(){return Se.getInternalPathRegexp(!1,".html")},
getPlatformInfo:e=>_e.runtime.getPlatformInfo(e),getBrowserInfo:e=>{e({name:"Chrome",version:`${we()}`,vendor:"unknown"})}};return Object.defineProperty(e,"lastError",{get:()=>_e.runtime.lastError,enumerable:!0}),Object.defineProperty(e,"id",{get:()=>_e.runtime.id,enumerable:!0}),Object.defineProperty(e,"short_id",{get:()=>e.id.replace(/[^0-9a-zA-Z]/g,"").substr(0,4),enumerable:!0}),e})(),Be=(()=>{let e;return Object.defineProperties({},{inIncognitoContext:{
get:()=>(void 0===e&&(e=_e.extension.inIncognitoContext),e),set:t=>{e=t},enumerable:!0},...Object.getOwnPropertyDescriptors({getURL:function(e){return _e.runtime.getURL(e)},sendMessage:(e,t)=>_e.runtime.sendMessage(e,t),onMessage:{addListener:e=>_e.runtime.onMessage.addListener(e)},connect:e=>_e.runtime.connect({name:e}),urls:{prepareForReport:function(e){return e}}})})})(),Ie=e=>{const t=(()=>{let e,t;const n=[],r=[],i=e=>{r.push(e),s()},s=()=>{if(void 0!==e){let n;for(;r.length;)n=r.shift(),
void 0!==n.state&&n.state!==e||"function"==typeof n.f&&n.f.call(o,t)}},o={promise:()=>o,done:e=>(i({state:!0,f:e}),o),fail:e=>(i({state:!1,f:e}),o),always:e=>(i({f:e}),o),progress:e=>(e&&n.push(e),o),then:(e,n,r)=>Ie((i=>{const s=(e,n)=>(...r)=>{const s=e?e(t):void 0,o=s&&"function"==typeof s.promise?s.promise():null,a=s&&"function"==typeof s.then?s:null
;if(o)o.done((e=>i.resolve(e))).fail(((...e)=>i.reject(e[0]))).progress(((...e)=>i.notify(...e)));else if(a)a.then(((...e)=>i.resolve(...e)),(e=>i.reject(e)));else{const t=e?[s]:r;i[n](t[0])}};o.done(s(e,"resolve")),o.fail(s(n,"reject")),o.progress(s(r,"notify"))})).promise(),each:e=>{const t=Ie();return o.then((n=>{const r=Array.isArray(n)?n:[n];Ie.when(r.map((t=>e(t)))).then(t.resolve)})),t.promise()},iterate:e=>{const t=Ie();return o.then((n=>{
const r=(Array.isArray(n)?n:[n]).map((t=>()=>e(t)));Ie.onebyone(r,!0).done((e=>{t.resolve(e)})).fail(t.reject)})),t.promise()}};return{get:()=>o,try_resolve:n=>(void 0===e&&(e=!0,t=n),s(),o),try_reject:n=>(void 0===e&&(e=!1,t=n),s(),o),do_notify:e=>((e=>{n.forEach((t=>t(e)))})(e),o)}})(),n={promise:()=>t.get(),resolve:e=>t.try_resolve(e),reject:e=>t.try_reject(e),notify:e=>t.do_notify(e),
consume:e=>(e&&e.promise?e.promise().done((e=>n.resolve(e))).fail(((...e)=>n.reject(e[0]))).progress((e=>n.notify(e))):e&&e.then?e.then((e=>n.resolve(e)),((...e)=>n.reject(e[0]))):$.warn("promise: incompatible object given to consume()",e),n.promise())};return e&&e(n),n};Ie.Pledge=(...e)=>{const t=Ie();return t.resolve(...e),t.promise()},Ie.Breach=(...e)=>{const t=Ie();return t.reject(e[0]),t.promise()},Ie.onebyone=(e,t=!0)=>{const n=[],r=Ie();let i=0;const s=()=>{if(i<e.length){const o=(0,
e[i++])();o&&o.promise?o.promise().done((e=>{n.push(e),s()})).fail((()=>{if(n.push(null),t)return r.reject();s()})):o&&o.then?o.then((e=>{n.push(e),s()}),(()=>{if(n.push(null),t)return r.reject();s()})):(n.push(o),s())}else r.resolve(n)};return s(),r.promise()},Ie.or=e=>{let t;const n=Ie(),r=()=>{e.length?(t=e.shift(),t&&Ie.Pledge().then(t).done(n.resolve).fail(r)):n.reject()};return r(),n.promise()},Ie.sidebyside=e=>{e=Array.isArray(e)?e:[e];const t=Ie();let n=e.length;return n?e.forEach((e=>{
e&&e.promise&&e.promise().always((()=>{0==--n&&t.resolve()}))})):t.resolve(),t.promise()},Ie.when=e=>{e=Array.isArray(e)?e:[e];const t=Ie();let n=e.length;const r=[];return n?e.forEach((e=>{let i;if(e&&e.promise)i=e.promise();else{if(!e.then)return void $.warn("promise: incompatible object given to when()",e);i=e}i.fail((()=>{t.reject(),n=-1})).done((e=>{r.push(e),0==--n&&t.resolve(r)}))})):t.resolve(r),t.promise()};let Te=[];const Ue={},Fe={},Me=(e,t)=>{const n="string"==typeof e?[e]:e,r=()=>{
n.every((e=>!!je[e]))?t():Te.push(r)};n.forEach((e=>{if(void 0===je[e]&&void 0===Ue[e]){const t=b((()=>{Fe[e]||(delete Ue[e],Le(e),De(Be.getURL(e+".js"),(()=>{Oe(e)})))}),0);Ue[e]=()=>{u(t),delete Ue[e]}}})),r()},je={},Le=e=>{Fe[e]=!0},Oe=e=>{if(!je[e]){let t;je[e]=!0,delete Fe[e],(t=Ue[e])&&t(),(()=>{const e=Te;Te=[];for(const t of e)t()})()}},De=(e,t)=>{let n=1;const r=()=>{0==--n&&t&&t()};("string"==typeof e?[e]:e).forEach((e=>{n++;try{!function(e,t){{const n=document.createElement("script")
;n.setAttribute("src",e),t&&(n.onload=()=>t(!0),n.onerror=()=>t(!1)),(document.head||document.body||document.documentElement||document).appendChild(n)}}(e,(t=>{t||$.warn("registry: self.load "+e+" failed! "),r()}))}catch(t){$.warn("registry: self.load "+e+" failed! ",t),r()}})),r()};class ze{constructor(){this.events={}}on(e,t,n){const r=void 0===n||Array.isArray(n)?n:[n],{events:i}=this;let s=i[e];return s||(s=[],i[e]=s),s.push({listener:t,filter:r}),()=>this.off(e,t)}once(e,t,n){
const r=void 0===n||Array.isArray(n)?n:[n],i=this.on(e,((...e)=>(i(),t.bind(this)(...e))),r);return i}off(e,t){const n=this.events[e];n&&(this.events[e]=n.filter((e=>e.listener!==t)))}has(e,t){const n=this.events[e];return!!n&&n.some((e=>e.listener===t))}emit(e,...t){const n=this.events[e];if(n)for(const e of n){const{listener:n,filter:r}=e;if((!r||N(t,r))&&!0===n(...t))return!0}return!1}listening(e,t){const n=this.events[e];if(n){if(t){const e=Array.isArray(t)?t:[t]
;return n.filter((t=>!t.filter||N(t.filter,e))).length}return n.length}return 0}}const Pe=e=>new s((t=>He(t,e)));let Ne=0;const Ve=(e,t)=>{const n=Date.now();if(Ne+e<n)return new s((e=>b((()=>{Ne=Date.now(),e()}),t||0)))},Qe=async function(e,...t){await Ve(1e3),e.apply(this,t)},He=function(e,t){return t?b.apply(this,[e,t]):(Qe.apply(this,[e]),0)},qe=e=>{const t=[],n=[],r=()=>{let i;if(n.length<e.threads&&t.length&&(i=t.shift())){const e=i.fn();let t;if(void 0!==e.catch){const n=Ie()
;e.then(n.resolve).catch(n.reject),t=n.promise()}else t=e;n.push(t),t.always((()=>{let e;(e=n.indexOf(t))>-1&&n.splice(e,1),r()})),i.p.consume(t)}};return{add:function(e){const n=Ie();return t.push({fn:e,p:n}),r(),n.promise()}}},Xe=e=>{const t={},{retimeout_on_get:n,timeout:r,check_interval:i,auto_remove_mutex:s}=Object.assign({retimeout_on_get:!1,timeout:300,check_interval:120},e||{});let o;const a=()=>{l(),o=b(d,1e3*i)},l=()=>{o&&u(o),o=null},c=e=>{const n=t[e];return delete t[e],
n?n.value:void 0},d=()=>{o=null;const e=Date.now()-1e3*r,n=Object.entries(t).filter((([t,n])=>n.ts<e));n.length?(s?s.acquire():Ie.Pledge()).then((()=>{n.forEach((([e,t])=>{const n=t&&t.d;n&&n.resolve([e,t.value]),c(e)})),s&&s.release(),a()})):a()};return r&&a(),{set:(e,n)=>{const r=Ie();return t[e]={value:n,ts:Date.now(),d:r},r.promise()},get:(e,r)=>{const i=t[e];return i?(n&&(i.ts=Date.now()),i.value):r},remove:c,removeAll:()=>{Object.keys(t).map((e=>c(e)))}}},Ye=new class{constructor(e=1){
if(this.waiting=[],e<0)throw new Error("Count must not be negative");this._count=e}acquire(){if(this._count>0)return this._count--,Ie.Pledge();{const e=Ie();return this.waiting.push(e.resolve),e.promise()}}release(){if(this.waiting.length>0){const e=this.waiting.shift();e&&e(void 0)}else this._count++}get count(){return this._count}}(1),We=new ze,Je={};let Ke=[];We.on("download",(({id:e,url:t,error:n})=>{const r=Je[e];r?(r.state=n?"interrupted":"complete",Ke.forEach((e=>{e(r)})),b((()=>{
delete Je[e]}),6e4)):console.warn("download event for unknown id",e,t)}));const $e={notDetermined:void 0,denied:"denied",authorized:"granted",provisional:"granted"};let et=[],tt=[],nt=[],rt=$e.notDetermined;We.on("notification",(({id:e,clicked:t,error:n})=>{n?console.warn("notifications: error",e,n):(t&&et.forEach((function(t){t(e)})),tt.forEach((function(t){t(e)})))})),We.on("permission",(({permission:e})=>{rt=$e[e],nt.forEach((function(e){e(rt)}))}));let it=!0;(async()=>{await null,it=!1})()
;const st=Object.defineProperties({},{...Object.getOwnPropertyDescriptors(Be),...Object.getOwnPropertyDescriptors({onConnect:{addListener:function(e){return _e.runtime.onUserScriptConnect.addListener(e),_e.runtime.onConnect.addListener(e)}},onConnectExternal:{addListener:function(e){return _e.runtime.onConnectExternal.addListener(e)}},onMessageExternal:{addListener:function(e){return _e.runtime.onMessageExternal.addListener(e)}},onMessage:{...Be.onMessage,addListener:e=>{
const t=(t,n,r)=>e(t,n,r);_e.runtime.onUserScriptMessage.addListener(t),Be.onMessage.addListener(t)}}}),inIncognitoContext:{get:function(){return Be.inIncognitoContext}},manifest:{get:function(){return _e.runtime.getManifest()}}}),ot=Object.defineProperties({},{...Object.getOwnPropertyDescriptors(Se),...Object.getOwnPropertyDescriptors({onInstalled:{addListener:function(e){_e.runtime.onInstalled&&_e.runtime.onInstalled.addListener(e)}},onUpdateAvailable:{addListener:function(e){
_e.runtime.onUpdateAvailable&&_e.runtime.onUpdateAvailable.addListener(e)}},setUninstallURL:e=>{_e.runtime.setUninstallURL&&_e.runtime.setUninstallURL(e)},getContexts:async e=>{if("getContexts"in _e.runtime)return await _e.runtime.getContexts(e)}})}),at=function(e,t){let n=1;const r=()=>_e.tabs.query({active:!0,lastFocusedWindow:!0},(e=>{_e.runtime.lastError&&n-- >0?b(r,1):t&&t(e[0])}));return r()},lt=function(e,t,n,r){return"function"==typeof n&&(r=n,n={}),_e.tabs.sendMessage(e,t,n,r)
},ct=(_e&&_e.windows&&_e.windows.getAll,_e&&_e.webRequest&&_e.webRequest.OnBeforeSendHeadersOptions&&_e.webRequest.OnBeforeSendHeadersOptions.hasOwnProperty("EXTRA_HEADERS"),!_e||_e.webNavigation,_e&&_e.webNavigation&&(_e.webNavigation.onHistoryStateUpdated||_e.webNavigation.onReferenceFragmentUpdated),_e&&_e.action,_e&&_e.declarativeContent&&_e.declarativeContent.onPageChanged&&(_e.declarativeContent.PageStateMatcher,_e.declarativeContent.RequestContentScript),
be&&be.contentScripts&&be.contentScripts.register,be&&be.userScripts&&be.userScripts.register,(()=>{const e=_e;e&&e.scripting&&e.scripting.executeScript})(),_e&&_e.declarativeNetRequest&&_e.declarativeNetRequest.updateSessionRules&&_e.declarativeNetRequest.getSessionRules,(()=>{const e=be||_e;e&&e.storage&&e.storage.session})(),{cache:{},isAllowed:function(e){return _e.extension.isAllowedFileSchemeAccess?_e.extension.isAllowedFileSchemeAccess(e):e(!1)},get:async function(e,t,n){try{
const n=await k(e),r=await n.blob();t(await new Response(r).arrayBuffer())}catch(e){n(e.message)}}}),dt=(Object.defineProperties({},{...Object.getOwnPropertyDescriptors({isDefaultStoreId:e=>!(void 0!==e),getIdFromStoreId:()=>{}}),...Object.getOwnPropertyDescriptors({getAll:function(e,t){return _e.cookies.getAll(e,t)},remove:function(e,t){return _e.cookies.remove(e,t)},set:function(e,t){return _e.cookies.set(e,t)}})}),_e&&_e.commands&&_e.commands.onCommand,(()=>{const e=_e&&_e.notifications
;e&&e.getPermissionLevel&&e.onPermissionLevelChanged&&e.onClicked})(),_e&&_e.contextMenus&&_e.contextMenus.create&&_e.contextMenus.update&&_e.contextMenus.remove,(()=>{const e=_e&&_e.permissions&&!!_e.permissions.getAll;return e?{supported:e,getAll:t=>{if(e)return _e.permissions.getAll(t);t&&t({permissions:[],origins:[]})},contains:(t,n)=>{if(e)return _e.permissions.contains(t,n);n&&n(!1)},request:(t,n)=>{if(e)return _e.permissions.request(t,n);n&&n(!1)},remove:(t,n)=>{
if(e)return _e.permissions.remove(t,n);n&&n(!1)},onAdded:{addListener:t=>{if(e&&_e.permissions.onAdded)return _e.permissions.onAdded.addListener(t)}},onRemoved:{addListener:t=>{if(e&&_e.permissions.onRemoved)return _e.permissions.onRemoved.addListener(t)}}}:{supported:!1}})()),At={native_support:!0,getMessage:function(...e){return _e.i18n.getMessage(...e)},getUILanguage:function(){return _e.i18n.getUILanguage?_e.i18n.getUILanguage():navigator.language},getAcceptLanguages:function(e){
return _e.i18n.getAcceptLanguages?_e.i18n.getAcceptLanguages(e):e([])}};!_e||!_e.alarms||_e.alarms.create;let ut=!1;(async()=>{Ee.USE=Ee.DEFAULT;try{F&&(Ee.NO_WARNING="nowarning"===F.getItem("#brokenprofile"),Ee.USE=F.getItem("#storage")||Ee.DEFAULT)}catch(e){console.warn("prep: error at storage type detection",e)}ct.isAllowed((e=>{ut=e}))})();const ht="en";let pt={},ft={},mt=null;const gt=[],_t=async e=>{const t=`_locales/${e}/messages.json`,n=await(e=>{let t;const n=Be.getURL(e)
;return Ie((async({resolve:r})=>{try{if(M){const i=new M;M.onlyasync?(i.open("GET",n),i.onload=()=>{r(i.responseText)},i.onerror=()=>{r()},i.send(null)):(i.open("GET",n,!1),i.send(null),t=i.responseText,t||$.warn("registry: content of "+e+" is null!"),r(t))}else r(await k(n).then((e=>e.text())))}catch(e){$.log("getRaw:"+e),r()}})).promise()})(t);try{if(n)return JSON.parse(n)}catch(e){}$.warn("i18n: parsing locale "+e+" failed!")},bt=async e=>{e=e.concat(ht);let t=-1,n=null;const r=async()=>{
if(t++,t<e.length){const i=e[t];if(!i||!yt.includes(i))return r();try{const e=await _t(i);if(!e)throw new Error("locale parsing failed!");n=i,pt=e}catch(e){return r()}if(!wt&&i!=ht)try{const e=await _t(ht);ft=e||{}}catch(e){}}};return await r(),n},vt=e=>e?e.replace(/-/g,"_").split("_").map(((e,t)=>t?e.toUpperCase():e.toLowerCase())).join("_"):e,kt=(e,t)=>{let n,r;return t=t||(mt?[mt,mt.split("_")[0]].concat(gt).filter((e=>e)):gt),z(t,((t,i)=>{const s=Number(i);if(z(e,((e,i)=>{
const o=Number(i),a=vt(e),l=a.split(/_/)[0];if(a==t)return r=o,!1;l==t&&(void 0===n||s<n)&&(r=o,n=s)})),void 0!==r)return!1})),void 0===r?r:e[r]},yt=[{value:"ar",name:"Arabic - ‎‫العربية‬‎"},{value:"be",name:"Belarusian - беларуская"},{value:"cs",name:"Czech - čeština"},{value:"da",name:"Danish - dansk"},{value:"de",name:"German - Deutsch"},{value:"el",name:"Hellenic (Greek) - Ελληνικά"},{value:"en",name:"English"},{value:"es",name:"Spanish - español"},{value:"fr",name:"French - français"},{
value:"hi",name:"Hindi - हिन्दी"},{value:"hr",name:"Croatian - hrvatski"},{value:"hu",name:"Hungarian - magyar"},{value:"id",name:"Indonesian - Indonesia"},{value:"it",name:"Italian - italiano"},{value:"ja",name:"Japanese - 日本語"},{value:"ko",name:"Korean - 한국어"},{value:"mk",name:"Macedonian - македонски"},{value:"nb",name:"Norwegian - norsk"},{value:"nl",name:"Dutch - Nederlands"},{value:"pl",name:"Polish - polski"},{value:"pt_BR",name:"Portuguese (Brazil) - português (Brasil)"},{value:"pt_PT",
name:"Portuguese (Portugal) - português (Portugal)"},{value:"ru",name:"Russian - русский"},{value:"sk",name:"Slovak - slovenčina"},{value:"sr",name:"Serbian - српски"},{value:"tr",name:"Turkish - Türkçe"},{value:"uk",name:"Ukrainian - українська"},{value:"vi",name:"Vietnamese - Tiếng Việt"},{value:"zh_CN",name:"Chinese (Simplified) - 中文（简体中文）"},{value:"zh_TW",name:"Chinese (Traditional) - 中文（繁體）"}].map((e=>e.value)),wt=!(void 0===At)&&At.native_support,Rt=(e,...t)=>{let n
;return mt&&(n=pt[e]||ft[e])?((e,t)=>{let n,r=e.message;return n=1==t.length&&Array.isArray(t[0])?t[0]:t,e.placeholders&&Object.entries(e.placeholders).forEach((([e,t])=>{try{const i=Number(t.content.replace(/^\$/,""))-1;let s;i<n.length?(s=n[i],r=r.replace("$"+e+"$",s)):$.log('i18n: invalid argument count on processing "'+r+'" with args '+JSON.stringify(n))}catch(e){$.log('i18n: error processing "'+r+'" with args '+JSON.stringify(n))}})),r})(n,t):(wt&&(n=((e,...t)=>{const n=[],r=e=>{
for(let t=0;t<e.length;t++)Array.isArray(e[t])?r(e[t]):n.push(String(e[t]))};return r(t),At.getMessage(e,n)})(e,...t))||(n=function(e,...t){let n=e;1==t.length&&Array.isArray(t[0])&&(t=t[0]);const r=new RegExp("(^|_)0[a-zA-Z]+0(_|$)");for(let e=0;e<t.length;e++){const i=n.match(r);if(!i){$.log("i18n: getMessage(): wrong argument count!!!");break}n=n.replace(r,(i[1]?" ":"")+t[e]+(i[2]?" ":""))}return n.replace(/_/g," ")}(e,...t),$.warn("i18n: missing translation"+n)),n)},Ct=(e,t)=>{let n
;const r=e[t];if(e){let i=e[t+"_i18n"]||{};r&&(i={en:r,...i});const s=kt(Object.keys(i));void 0!==s&&(n=i[s])}return n||r},xt=async e=>{let t=e;if("null"===e&&(t=null),t&&(t=vt(t)),!t&&wt)mt=t;else if(t!==mt)return bt([t,...gt,mt].filter((e=>e))).then((t=>{mt=t,mt!=e&&$.log("i18n: retrieving locale "+t+" failed!")}))},Et=kt,Gt=(e,t)=>{for(let n=0,r=e.length;n<r;n++)if(e[n]==t)return!0;return!1},Zt=(e,t)=>{const n=document.querySelectorAll(t);let r=e.parentNode;for(;r&&!Gt(n,r);)r=r.parentNode
;return r},St=e=>e&&void 0!==e.nodeType,Bt=e=>e&&void 0!==e.tagName,It=(...e)=>{const n=e.length>1?e:e[0];if(null==n?void 0:n.queryHelper)return n;if("function"==typeof n)return"loading"!=window.document.readyState?n(null):window.addEventListener("DOMContentLoaded",n),It([]);if("string"==typeof n){let e=[];if("<"==n.trim()[0]){let t=n;try{if(-1!=t.indexOf("<script")){const n=/<script[^>]*>[^<]*<\/script>/g,r=/([^\r\n\t\f\v= '"]+)(?:=(["'])?((?:.(?!\2?\s+(?:\S+)=|\2))+.)\2?)?/g,i=t.match(n)
;i&&i.length&&(t=t.replace(n,""),i.forEach((t=>{const n=t.match(r);if(!n||!n.length)return;const i={};if(n.slice(1,-1).forEach((e=>{const t=e.split("="),n=t.shift()||e;i[n]=(t.join("=")||"").replace(/^"|"$/g,"")})),!i.src)return void console.error(`ssjq: unable to parse "${t}"`,i);const s=document.createElement("script");["src","async"].forEach((e=>{void 0!==i[e]&&s.setAttribute(e,i[e])})),e.push(s)})))}const n=/^<([^>]+)>$/.exec(t);n&&2==n.length&&(t=`${t}</${n[1].split(" ")[0]}>`)
;const r=(new DOMParser).parseFromString(t,"text/html"),i=[].slice.call(r.body.children);e=i.concat(e)}catch(t){console.error(`ssjq: ${t}`),e=[]}}else{const t=document.querySelectorAll(n);e=[].slice.call(t)}return It(e)}if(Array.isArray(n)){const e=[].concat(...n.map((e=>Array.isArray(e)?e:"string"==typeof e?It(e):[e])).filter((e=>e))),r={},i=Object.assign(e,{queryHelper:!0,append:(...t)=>(t.forEach((t=>{const n=e[0];St(n)&&It(t).forEach((e=>n.appendChild(It(e)[0])))})),It(e)),appendTo:t=>{
const n=It(t);return e.filter(St).forEach((e=>n.append(e))),It(e)},insertBefore:t=>{const n=[...e];if(e.length){const r=It(t)[0],i=null==r?void 0:r.parentNode;i&&e.forEach((e=>{const t=It(e)[0];t&&(i.insertBefore(t,r),n.push(t))}))}return It(n)},remove:()=>(e.filter(St).forEach((e=>{var t;return null===(t=e.parentNode)||void 0===t?void 0:t.removeChild(e)})),It([])),replaceWith:t=>{const n=e[0];if(n){const e=(e=>e&&e.queryHelper)(n)?n[0]:n,r=It(t),i=r.shift()
;return void 0!==i&&Bt(e)&&(e.replaceWith(i),r.forEach((e=>{var t;null===(t=i.parentNode)||void 0===t||t.insertBefore(e,i.nextSibling)}))),It(e)}return It(e)},prevAll:t=>{var n;const r=e[0];if(!r)return It([]);const i=null===(n=It(r).parent())||void 0===n?void 0:n.children(t);if(!i||!i.length)return It([]);const s=[];for(let e=0;e<i.length;e++){const t=i[e];if(t==r)break;s.push(t)}return It(s.reverse())},nextAll:t=>{var n;const r=e[0];if(!r)return It([])
;const i=null===(n=It(r).parent())||void 0===n?void 0:n.children(t);if(!i||!i.length)return It([]);const s=[];let o=!1;for(let e=0;e<i.length;e++){const t=i[e];o&&s.push(t),t==r&&(o=!0)}return It(s)},addClass:t=>{const n=t.trim().split(" ");return e.filter(Bt).forEach((e=>n.forEach((t=>e.classList.add(t))))),It(e)},removeClass:t=>{const n=t.trim().split(" ");return e.filter(Bt).forEach((e=>n.forEach((t=>e.classList.remove(t))))),It(e)},toggleClass:(t,n)=>(t.trim().split(" ").forEach((t=>{
!0===n?i.addClass(t):!1===n?i.removeClass(t):e.filter(Bt).forEach((e=>e.classList.toggle(t)))})),It(e)),hasClass:t=>!!e.filter((e=>Bt(e)&&e.classList.contains(t))).length,is:t=>{const n=e[0];if(n&&Bt(n))return":visible"==t?"none"!==window.getComputedStyle(n).display:":checked"==t?1==n.checked:void 0},attr:(t,n)=>{if(e.length){const r=(t,n)=>{null===n?e.filter(Bt).forEach((e=>e.removeAttribute(t))):e.filter(Bt).forEach((e=>e.setAttribute(t,n.toString())))};if("string"==typeof t){if(void 0===n){
const n=e[0];return Bt(n)&&n.getAttribute(t)||void 0}r(t,n)}else for(const e of Object.keys(t))r(e,t[e])}return It(e)},prop:(t,n)=>{if(e.length){const r=(t,n)=>{null===n?e.filter(Bt).forEach((e=>{delete e[t],e.removeAttribute(t)})):e.forEach((e=>e[t]=n))};if("string"==typeof t){if(void 0===n)return e[0][t];r(t,n)}else for(const e of Object.keys(t))r(e,t[e])}return It(e)},
text:n=>void 0===n?e.length?e.filter(Bt).map((e=>e.innerText)).join(""):void 0:(e.length&&null!=n&&e.filter(Bt).forEach((e=>e.innerText=t(n))),It(e)),html:n=>{if(e.length){if(void 0===n)return e.filter(Bt).map((e=>e.innerHTML)).join("");e.filter(Bt).forEach((e=>e.innerHTML=t(n)))}return It(e)},closest:t=>{const n=[];for(const r of e){const e=Bt(r)&&Zt(r,t);e&&n.push(e)}return It(n)},parent:()=>{const t=e[0];return Bt(t)?It(t.parentNode):It([])},children:t=>{const n=e[0];if(Bt(n))if(t){
if(n.querySelectorAll){const e=n.querySelectorAll(t);return It([].slice.call(e))}}else if(n.children)return It([].slice.call(n.children));return It([])},find:t=>{let n=[];return e.forEach((e=>{It(e).children(t).forEach((e=>{const r=It(e).find(t).toArray();n=[...n,e,...r]}))})),It(n)},each:t=>(e.forEach(((e,n)=>t(n,e))),It(e)),not:t=>{const n=It(t);return It(e.filter((e=>-1===n.indexOf(It(e)[0]))))},first:()=>It(e.slice(0,1)),toArray:()=>[...e],bind:(t,n)=>(t.split(" ").forEach((t=>{
(r[t]||(r[t]=[])).push(n),e.forEach((e=>e.addEventListener(t,n)))})),It(e)),unbind:t=>(t.split(" ").forEach((t=>{r[t]&&(r[t].forEach((n=>{e.forEach((e=>{e.removeEventListener(t,n)}))})),r[t]=[])})),It(e)),value:n=>{if(void 0===n){let t;return e.reverse().some((e=>{if(!e.disabled)return"checkbox"!=e.type||1==e.checked?(t=e.value,!0):void 0})),t}{const r=e.length?e[e.length-1]:void 0
;return r&&("checkbox"==r.type?r.value==n&&(r.checked=!0):"select-one"==r.type?r.value=t(n):((e=>e&&void 0!==e.type)(r)&&"text"==r.type&&(r.value=t(n)),r.setAttribute("value",t(n)))),It(e)}},data:(n,r)=>{const i=e[0];return Bt(i)&&i.dataset?void 0===r?i.dataset[n]:(null===r?delete i.dataset[n]:i.dataset[n]=t(r),It(e)):void 0===r?It(e):void 0},offset:()=>{const t=e[0];return Bt(t)&&(null==t?void 0:t.getBoundingClientRect())||{left:-1,top:-1,right:-1,bottom:-1,x:-1,y:-1,height:-1,width:-1}},
height:()=>{const t=e[0];return t&&((e=>e&&void 0!==e.document)(t)?window.innerHeight:t.offsetHeight)||0},scrollTop:()=>{const t=e[0];return t&&(t.scrollTop||t.pageYOffset)||0},get:t=>e[t],on:(t,n)=>(t.split(" ").forEach((t=>e.forEach((e=>null==e?void 0:e.addEventListener(t,n))))),It(e)),off:(t,n)=>(t.split(" ").forEach((t=>e.forEach((e=>null==e?void 0:e.removeEventListener(t,n))))),It(e)),trigger:(t,n)=>{const r=new Event(t,{bubbles:!0,cancelable:!0});return n&&Object.assign(r,n),
e.forEach((e=>{if(["focus","blur"].includes(t)){const n=e[t];"function"==typeof n&&n.apply(e,[])}e.dispatchEvent(r)})),It(e)},toggle:t=>(e.forEach((e=>{const n=It(e);(void 0===t?n.is(":visible"):!t)?n.hide():n.show()})),It(e)),hide:()=>(e.filter(Bt).forEach((e=>{var t;const n=null===(t=null==e?void 0:e.style)||void 0===t?void 0:t.display;n&&-1==n.indexOf("none")&&(e.backuped_display=n),It(e).attr("style","display: none !important")})),It(e)),fadeOut:async t=>new Promise((n=>{
e.filter(Bt).forEach((e=>{e.style.opacity="1",e.style.transition=`opacity ${t||150}ms`,setTimeout((()=>{e.style.opacity="0"}),1)})),setTimeout((()=>{e.filter(Bt).forEach((e=>{e.style.transition="",e.style.opacity=""})),i.hide(),n(It(e))}),(t||150)+1)})),show:()=>(e.filter(Bt).forEach((e=>{e.style.display=e.backuped_display||""})),It(e)),fadeIn:async t=>new Promise((n=>{e.filter(Bt).forEach((e=>{e.style.opacity="0",e.style.transition=`opacity ${t||150}ms`,setTimeout((()=>{e.style.opacity="1"}),1)
})),i.show(),setTimeout((()=>{e.filter(Bt).forEach((e=>{e.style.transition="",e.style.opacity=""})),n(It(e))}),(t||150)+1)})),animate:(t,n)=>new Promise((r=>{const i=e[0];i.current_action&&window.clearInterval(i.current_action),i.current_action=window.setInterval((()=>{if(void 0!==t.scrollTop){const n=i===window?document.documentElement:i,s=n.scrollTop;n.scrollTop<t.scrollTop?n.scrollTop=n.scrollTop+3:n.scrollTop=n.scrollTop-3,
(n.scrollTop===s||Math.abs(n.scrollTop-t.scrollTop)<=3)&&(n.scrollTop=t.scrollTop,window.clearInterval(i.current_action),delete i.current_action,r(It(e))),window.getComputedStyle(n)}else e.forEach((e=>{if(void 0!==t.height){const n=It(e).get(0);n&&n.style&&(n.style.height=`${t.height}px`)}})),r(It(e))}),void 0===n?100:n)})),serialize:()=>{if(1==e.length&&(t=e[0])&&"FORM"==t.tagName)return It(e[0]).find("input, textarea, select, button").map((e=>{const t=e.name,n=It(e).value()
;if("string"==typeof t&&"string"==typeof n)return`${t}=${encodeURIComponent(n)}`})).filter((e=>"string"==typeof e)).join("&");var t}});return i}return It(void 0===n||null==n?[]:[n])};let Tt=null,Ut=null;const Ft=(e,t)=>{t||(t={});const n="ct",r="0",i=me("div",n,r,"p"),s=fe("div","curbg",n,r,"c"),o=fe("div",t.fixed?"curmiddle_fixed":"curmiddle_relative",n,r,"d");i.inserted||(i.setAttribute("class","curouter hide"),i.setAttribute("style","z-index: 10000"));const a=((e,t,n,r)=>{r||(r="")
;const i=fe("table","curtable",t,n,"table"+r),s=fe("tr","",t,n,"tr2"+r),o=fe("td","curtableoutertd",t,n,"td1"+r),a=fe("td","curtableinner",t,n,"td2"+r),l=fe("td","curtableoutertd",t,n,"td3"+r);return s.appendChild(o),s.appendChild(a),s.appendChild(l),i.appendChild(s),e&&a.appendChild(e),i})(e,n,r);return o.appendChild(a),i.appendChild(o),i.appendChild(s),i.show=()=>{i.setAttribute("class","curouter block")},i.hide=()=>{i.setAttribute("class","curouter hide")},i.remove=()=>{
i.parentNode&&i.parentNode.removeChild(i)},i.setText=e=>{i.text=e},i.print=e=>{i.text&&(i.text.textContent=e)},document.body.appendChild(i),i},Mt=()=>{const e=document.createElement("div");e.setAttribute("class","curcontainer");const t=document.createElement("div");t.setAttribute("class","curwaithead");const n=document.createElement("div");return n.setAttribute("class","curwaitmsg"),e.appendChild(t),e.appendChild(n),{head:t,outer:e,inner:n}},jt={wait:e=>{if(document.body){
if(void 0===e&&(e=Rt("Please_wait___")),null===Ut){Tt&&(Tt.remove(),Tt=null);const t=e=>{const t=Mt(),n=t.inner,r=document.createElement("div");return r.textContent=e,r.setAttribute("class","curtext"),It(n).append(It('<div class="lds-css ng-scope"><div class="lds-dual-ring"><div></div><div></div></div></div>')).append(r),{all:t.outer,text:r}};Ut=0;const n=t(e);return Tt=Ft(n.all,{fixed:!0}),Tt.setText(n.text),Tt.show(),!0}return 0===Ut&&(void 0===e&&(e=Rt("Please_wait___")),Tt)?(e&&Tt.print(e),
Tt.show(),!0):void 0}},hide:()=>{0===Ut&&(Tt&&Tt.hide(),Ut=null)},dialog:(e,t)=>{if(!document.body)return;if(1===Ut)return!1;if(null!==Ut)return;Tt&&(Tt.remove(),Tt=null),Ut=1;const n=Mt();if(n.inner.appendChild(e),Tt=Ft(n.outer),Tt.show(),t){const e=document.createElement("div");e.setAttribute("class","curclose"),n.head.appendChild(e),e.addEventListener("click",(()=>t())),Tt.addEventListener("keydown",(e=>{27==e.keyCode&&(t(),e.preventDefault())}),!0),Tt.setAttribute("tabindex",0)}return!0},
hideDialog:()=>{1===Ut&&(Tt&&Tt.hide(),Ut=null)}};let Lt=null,Ot=2;const Dt=(e,t)=>{let n=e,r=t;const i=()=>{null!=Lt&&window.clearTimeout(Lt),Lt=null},s=e=>{e&&(i(),n&&(n(e),n=null))};Lt=window.setTimeout((()=>{if(i(),Ot-- >0&&n&&r)return Dt(n,r),void(n=r=null);r&&r()}),5e3);const o={method:"ping"};try{st.sendMessage(o,s)}catch(e){}},zt=Dt,Pt=[{name:Rt("Default"),layout:"default",value:"default"},{name:Rt("Default_Light"),layout:"default",theme:"light",value:"default#light"},{
name:Rt("Default_Dark"),layout:"default",theme:"dark",value:"default#dark"},{name:Rt("Default_Darker"),layout:"default",theme:"darker",footer:'Theme by <a href="https://github.com/narcolepticinsomniac" target="blank">narcolepticinsomniac</a> from the <a href="https://github.com/openstyles/stylus" target="blank">Stylus</a> project.',value:"default#darker"}],Nt={default:Rt("Default"),monokai:"Monokai",solarized:"Solarized","mdn-like":"MDN-like",eclipse:"Eclipse",railscasts:"RailsCasts",
zenburn:"ZenBurn"},Vt={
unknown:"data:image/png;base64,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",
uso:"data:image/png;base64,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",
gf:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3ggEBCQHM3fXsAAAAVdJREFUOMudkz2qwkAUhc/goBaGJBgUtBCZyj0ILkpwAW7Bws4yO3AHLiCtEFD8KVREkoiFxZzX5A2KGfN4F04zMN+ce+5c4LMUgDmANYBnrnV+plBSi+FwyHq9TgA2LQpvCiEiABwMBtzv95RSfoNEHy8DYBzHrNVqVEr9BWKcqNFoxF6vx3a7zc1mYyC73a4MogBg7vs+z+czO50OW60Wt9stK5UKp9Mpj8cjq9WqDTBHnjAdxzGQZrPJw+HA31oulzbAWgLoA0CWZVBKIY5jzGYzdLtdE9DlcrFNrY98zobqOA6TJKHW2jg4nU5sNBpFDp6mhVe5rsvVasUwDHm9Xqm15u12o+/7Hy0gD8KatOd5vN/v1FozTVN6nkchxFuI6hsAAIMg4OPxMJCXdtTbR7JJCMEgCJhlGUlyPB4XfumozInrupxMJpRSRtZlKoNYl+m/6/wDuWAjtPfsQuwAAAAASUVORK5CYII=",
gh:"data:image/png;base64,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",
gl:"data:image/png;base64,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",
bb:"data:image/png;base64,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********************************/3I5SS6N3mf9/wh31qVwSyHl/6rzQrZZn0oncKoI4iRjyKg7GyaxPJbNIXmS6ELL1X3FVlFSxl0UR1EGpZzVFfSc9qZJA/Yc9YPsw69h5wYG+6I2GXEZshEeFvTkFqGPTrYH6gCAJ5HyvgMXbz15PIN57rHDKsg1i1CzCLrl4RkgxpkZshEeF/jcyK+3tpBVGMIPnnjp4Mq96YcSOn78ayrNhTyCgu+Z8FeIhJKIiMGI/L4pDjbTCVNiryuSb40efbsu49IZXMtv8rDMursAbhEEkFuEjnuOHraI4N0oCKTXEOEZpoL4CjhDk3IKpyzdgMVEDmZGEbmSiIZoT7XLiM2IgDCIN1NOC4opNMPkK8zBNWLSWqLnOnayO12lnZi5PgtEozxKLBsObvz0bAlUypqiEqqjQWjkN0RyN0jQCOLd5A5VRHPUlgVziGA1W27C34cjpEqJ0E3GcAHVM+jFNLCb9+KHz4MW8AJ2H1Xq3WvXiGy4Jq5vC3CImJ6mEqqiQaqmcJmiI5lzlQAzpCyOcT35Fk+oL/7BxLpU1Zpwp4fhj37WHFkXsmOWdPGnJeo85iX+dHfOnWdF/nBnFkmQyH/iXL7lEAYpRmFu4kduphKpUXyqJyigufWHqDIN2ZOZadEmPurpxad2538oiL1yhdANkPvAvX3KJAvpIgsooLgmkjiEWsP6wxWAJlVFcEkidGdvXTUniPFuDJJRFZRSXBFInfBOjkLvsCjVMQllURnFJIHWmVb49wa+s7qpxCISyqIzikkCqjaPFYbYGScwFqDmClgRiOCmOozNIik7LQmVJIDUXBhHi/7FhCPS2fxoqSwKpuYOHSB1ibavbpwfWk1wkgVTcHs+gsrb5hhEIhPtMHKYpCaTybBBbx41AII7zHaa6D1USiHNM4tKzjUCg+M3ZKCsJpHIWR0k+dveBNArO8EpGWUkgDdZH98Qgd/NhEAqiJspKAmkyneiz5oB7EwgFNZpClAQSW7d8ci5WuCt7ci5WoiBqSgJpNyFk+vXrYUQscMuArKiGgjI6h+Yb5om6ncdJ726UUAel9NsGLyOU/Xhq8L5jbhL+F0VQR0Yo09u5QTCDVfF7xSG9QzQhPCqgCOrIAFOD4N/4dG/4Xw76v/ugbWhRB4ERG+FRAUVkhLLB7Ip4Bv95N44DaW/deej61EFIRP3Pe3GIjfAyxJ1LZLET9A8zIsOSMoprzGLvhEslREIwxENIRFUQc0MSSNveaOU3xvlOWb5h4+7TBNFlG87g8gYBEANhEAnBEE/zXkcSSPl8IweUiAjAhIgL2nDkyOnShsstnV0f60MaGqI5GqVpBBCxfIdrH+lBEkiTIAc8vM95eOJgGrNgNTHkkvedOZ5bxZob9gp2qvGmoxKqokKqpXKaoCGao1Ga1j6ogySQbt1S7ziJlRIs9mPFFmORycvWzw/d6rv24OodJwitmnOhnNCZeeVNRdXm6qYbnJJszfzLl1yiAMUozC3cyO1UQlVUSLVUThPD9SeNJJD+fGLXFa8V66GWImImz55gCd8Y78fp4z+aEkSAMDIf+JcvuUQBilHYeiAmlVCVDcZIAhmWWyJ/5als/VLiIwkk8//apQMZAAAAgEH+1gd5WwgJhECcAoFACIRACAQCIRACIRACgUAIhEAIBAIhEAIhEAiEQAiEQCAQAiEQAkEyEVOTAEOkBQAAAABJRU5ErkJggg==",
ouj:"data:image/x-icon;base64,AAABAAEAQEAAAAEACAAoFgAAFgAAACgAAABAAAAAgAAAAAEACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwTwoAclEOAHVWAABzUhAAdlcAAHdYAAB0UxIAdVQTAHhZAgB2VRUAe1sJAH1dDgB3XB0AeF0eAHpfIQB7YCIAfGEjAH1iJAB+YyYAf2QnAIFmKQCEah8AgmcrAIVpLgCHbSQAhmovAIhuJQCJbyYAh3EzAIhyNACJczYAi3U4AIx2OQCNdzoAjng7AI95PACQej0Aknw/AJN9QQCXgUUAlYROAJaFTwCXhlAAnItWAKCPWgChkFsAo5FcAJ2RYQCekmIAn5NjAKKWZgCkmGgAp5pyAKqdbQCrnXUArJ52AK+hegCpoX4AraWCALOqhwC0q4gAtayJALatigC3r4sAuLCMALmxjQC3tJQAuLWVALm2lgC7uJgAvbqaAL67mwC/vJwAwb6eAMbBqQDHwqoAwsOuAMXGsgDHyLQAyMm1AM7PugDP0LsA0NG8ANLTvwDT1MAAztXEANDWxQDR18YA0tnHANTbyQDV3MoA19zTANfezQDZ4M8A2t/VANvg1gDc4dcA3eLYAN7j2QDf5NoA4OXbAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRjYl9dXFlZWVlcXV9iY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY2FcVU9JQDo2NDQ2OkBJTlVcYWNkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRiXVZMPTIpIRoUDg0NDhQaISgyPUxWXWJkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGNfVEk0JxgLCAIDAQAAAAABAwIFCxgnNElTX2NkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGJZSzQkEQUDAQAAAAAAAAAAAAAAAAEDBREjNEpZYmRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY2BSPykUBQEAAAAAAAAAAAAAAAAAAAAAAAABBBMoP1JfY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY11NNB0MAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAADDB00TV1jZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY11MMBYEAQAAAAAAAAEDBwIJBAQJAgcDAQAAAAAAAAEEFC5MW2NkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY11MLBMHAAAAAAABAwcIERccHyIiHxwXEQgHAwEAAAAAAAcTLExdY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGBNMBMHAAAAAAABCQ4YJi84PURGRkQ+OC8mGA4JAQAAAAAABxMuTV9kZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGJSNBYHAAAAAAEDChgqOEVNUlhcW1tcWFJNRTkqGAoGAQAAAAAHFjRRYmRkZGRkZGRkZGRkZGRkZGRkZGRkZGNZPx0EAAAAAAECDyM0SFBcYGJiY2RkY2JiYFxRSTQjDwIBAAAAAAQdP1ljZGRkZGRkZGRkZGRkZGRkZGRkZGRfSykMAQAAAAEEFCk7TlphY2RkZGRkZGRkZGRjYVpPPCkUBAEAAAABDChKX2RkZGRkZGRkZGRkZGRkZGRkZGRiVDQUAwAAAAECFCtDUl5iZGRkZGRkZGRkZGRkZGRiXlNDKxQCAQAAAAMTNFNiZGRkZGRkZGRkZGRkZGRkZGRkXUkkBQAAAAADDypDVGBjZGRkZGRkZGRkZGRkZGRkZGNgVUMqEAYAAAAABSRJXWRkZGRkZGRkZGRkZGRkZGRkY1Y1EgMAAAABCiM8UmBjZGRkZGRkZGRkZGRkZGRkZGRkY2BTPCMKAQAAAAERNFZjZGRkZGRkZGRkZGRkZGRkZGFMJwgAAAABCRg0Tl5jZGRkZGRkZGRkZGRkZGRkZGRkZGRjXk80GAkBAAAABSdMYWRkZGRkZGRkZGRkZGRkZGRcPRgHAAAAAw4qSFpiZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGJaSCoOAwAAAAMYPVxkZGRkZGRkZGRkZGRkZGRjVTMOAQAAAAcYOFBhZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkYVE5GAcAAAABCzJVY2RkZGRkZGRkZGRkZGRkYk8pCAAAAAEIJkVaYmRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGNcRSYIAQAAAAgpTmJkZGRkZGRkZGRkZGRkZGBJIgQAAAADES1NX2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkYE0vEQMAAAACIUlfZGRkZGRkZGRkZGRkZGRdQRsHAAAABhc4UmJkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGJSOBcHAAAABxpAXWRkZGRkZGRkZGRkZGRkXDoUAwAAAAccPVdiZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRiWD4cAgAAAAEUOlxkZGRkZGRkZGRkZGRkZFo2DwEAAAACH0RcY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY1xEIAkAAAABDjZZZGRkZGRkZGRkZGRkZGRZNA0BAAAACSJGW2NkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRbRiIJAAAAAA00WWRkZGRkZGRkZGRkZGRkWTQNAQAAAAkiRltjZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkW0YiCQAAAAANNFlkZGRkZGRkZGRkZGRkZFo3DwEAAAACH0RcY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY1xEIAkAAAABDzZZZGRkZGRkZGRkZGRkZGRcOhQDAAAABxw9V2JkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGJYPRwCAAAAARQ6XGRkZGRkZGRkZGRkZGRkXUEbBwAAAAYXOFJiZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRiUjgXBwAAAAcaQF1kZGRkZGRkZGRkZGRkZGBJIgQAAAADES1NX2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkYE0vEQMAAAACIklfZGRkZGRkZGRkZGRkZGRiTykIAAAAAQgmRVpiZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY1xFJggBAAAACClOYmRkZGRkZGRkZGRkZGRkY1UzDgEAAAAHGDhQYGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGFQORgHAAAAAQsyVWNkZGRkZGRkZGRkZGRkZGRcPhoHAAAAAQ4qSFpiZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGJaSCoOAwAAAAMYPVxkZGRkZGRkZGRkZGRkZGRkYUwnCAAAAAEJGTROXmNkZGRkZGRkZGRkZGRkZGRkZGRkZGNeTjQYCQEAAAAFJ0xhZGRkZGRkZGRkZGRkZGRkZGNWNRIDAAAAAQgjO1JgY2RkZGRkZGRkZGRkZGRkZGRkZGNgUzwjCgEAAAABETRWY2RkZGRkZGRkZGRkZGRkZGRkXUkkBQAAAAADDylCVGBjZGRkZGRkZGRkZGRkZGRkZGNgVEMpDwMAAAAABSRJXWRkZGRkZGRkZGRkZGRkZGRkZGJUNRQDAAAAAQIUK0NSXmJkZGRkZGRkZGRkZGRkZGJeUkMrFAIBAAAAAxM0VGJkZGRkZGRkZGRkZGRkZGRkZGRkX0spDAEAAAABBBQpO05aYWJkZGRkZGRkZGRkYmFaTjspFAQBAAAAAQwoS19kZGRkZGRkZGRkZGRkZGRkZGRkZGNZQB4EAAAAAAECDyI0R1BaYGJiY2NjY2JiYFxQSDQjDwIBAAAAAAQdP1ljZGRkZGRkZGRkZGRkZGRkZGRkZGRkYlI0FQIAAAAAAQMIGCo4RU1SV1xbW1xXUk1FOSoYCAMBAAAAAAcWNFJiZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRgTTEUBwAAAAAAAQkOGSUtOD1ERkZEPTgvJhgOCQEAAAAAAAcTME1gZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY15MLxQHAAAAAAABAwcIERccHyIiHxwXEQgHAwEAAAAAAAcTLExeY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRjXUwxFQUBAAAAAAAAAQMGBwIJCQIHBgMBAAAAAAAAAQQWMExdY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGNeTTQeDAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwwdNE1dY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY2BSQCkUBQMAAAAAAAAAAAAAAAAAAAAAAAADBRMpP1JgY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkYllLNSQSCAcBAAAAAAAAAAAAAAAAAQcIEiQ0SlliZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRjX1RJNScaDggCBwMBAQEBAwcCCA4aJzVJVF9jZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRiXVZMPjMqIhsUDwsLDxQbIikzPkxWXWJkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRjYVxVT0lBOjc0NDc6QElPVVxhY2RkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkY2JgXVxaWVlaXF1gYmNkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZGRkZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
usty:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+PHRpdGxlPjlDMzAwNDI3LTg4Q0QtNDI2RC05QkZELUFEMUU2RUI2RjRDNjwvdGl0bGU+PGRlZnM+PHBhdGggZD0iTTAgNS42MjNBNS42MjMgNS42MjMgMCAwIDEgNS42MjMgMGgxMi43NTRBNS42MjMgNS42MjMgMCAwIDEgMjQgNS42MjN2MTIuNzU0QTUuNjIzIDUuNjIzIDAgMCAxIDE4LjM3NyAyNEg1LjYyM0E1LjYyMyA1LjYyMyAwIDAgMSAwIDE4LjM3N1Y1LjYyM3oiIGlkPSJhIi8+PC9kZWZzPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PG1hc2sgaWQ9ImIiIGZpbGw9IiNmZmYiPjx1c2UgeGxpbms6aHJlZj0iI2EiLz48L21hc2s+PHVzZSBmaWxsLW9wYWNpdHk9IjAiIGZpbGw9IiNGRkYiIHhsaW5rOmhyZWY9IiNhIi8+PHBhdGggZmlsbD0iIzJFQ0M3MSIgbWFzaz0idXJsKCNiKSIgZD0iTTExLjI1IDB2MTQuNjI1SDBWMHoiLz48cGF0aCBmaWxsPSIjRTc0QzNDIiBtYXNrPSJ1cmwoI2IpIiBkPSJNMjQgMHY3Ljg3NUgxMi43NVYweiIvPjxwYXRoIGZpbGw9IiNGMzlDMTIiIG1hc2s9InVybCgjYikiIGQ9Ik0xMS4yNSAxNi4xMjVWMjRIMHYtNy44NzV6Ii8+PHBhdGggZmlsbD0iIzM0OThEQiIgbWFzaz0idXJsKCNiKSIgZD0iTTI0IDkuNTYzdjE0LjYyNEgxMi43NVY5LjU2M3oiLz48L2c+PC9zdmc+"
},Qt={images:{origin:e=>Vt["gst"==e?"gh":e]||Vt.unknown,brand:e=>"data:image/svg+xml,"+encodeURI({
tampermonkey:"<?xml version='1.0' encoding='utf-8'?><svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 400'><g id='XMLID_273_'><g id='XMLID_78_'><path id='XMLID_83_' class='st0' d='M304.8,0H95.2C42.6,0,0,42.6,0,95.2v209.6C0,357.4,42.6,400,95.2,400h209.6 c52.6,0,95.2-42.6,95.2-95.2V95.2C400,42.6,357.4,0,304.8,0z M106.3,375C61.4,375,25,338.6,25,293.8c0-44.9,36.4-81.3,81.3-81.3 c44.9,0,81.3,36.4,81.3,81.3C187.5,338.6,151.1,375,106.3,375z M293.8,375c-44.9,0-81.3-36.4-81.3-81.3 c0-44.9,36.4-81.3,81.3-81.3c44.9,0,81.3,36.4,81.3,81.3C375,338.6,338.6,375,293.8,375z'/></g><g id='XMLID_67_' class='st2'><path id='XMLID_74_' class='st3' d='M304.8,0H95.2C42.6,0,0,42.6,0,95.2v209.6C0,357.4,42.6,400,95.2,400h209.6 c52.6,0,95.2-42.6,95.2-95.2V95.2C400,42.6,357.4,0,304.8,0z M106.3,375C61.4,375,25,338.6,25,293.8c0-44.9,36.4-81.3,81.3-81.3 c44.9,0,81.3,36.4,81.3,81.3C187.5,338.6,151.1,375,106.3,375z M293.8,375c-44.9,0-81.3-36.4-81.3-81.3 c0-44.9,36.4-81.3,81.3-81.3c44.9,0,81.3,36.4,81.3,81.3C375,338.6,338.6,375,293.8,375z'/></g></g></svg>",
monkey:"\n<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 59.16 69.57'>\n    <g transform='translate(-110.73,-61.49)'>\n        <path d='m 130.15,99.28 c 2.9,-1.3 4.93,-4.22 4.94,-7.6 0,-4.61 -3.74,-8.35 -8.35,-8.35 -4.61,0 -8.35,3.74 -8.35,8.35 0,4.6 3.74,8.34 8.35,8.34 0.72,0 1.41,-0.1 2.08,-0.27 0.41,-0.18 0.85,-0.33 1.32,-0.46 z m -7.55,-6.13 c 0,-2.3 1.87,-4.17 4.18,-4.17 0.58,0 1.15,0.12 1.65,0.34 -0.11,0.31 -0.18,0.65 -0.18,1.0 0,1.53 1.19,2.79 2.7,2.91 -0.04,2.26 -1.89,4.09 -4.17,4.09 -2.3,0 -4.18,-1.87 -4.18,-4.17' style='fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 146.01,83.31 c -4.61,0 -8.35,3.74 -8.35,8.35 0,3.35 1.97,6.23 4.82,7.56 0.55,0.14 1.08,0.32 1.56,0.54 0.63,0.15 1.28,0.24 1.96,0.24 4.6,0 8.34,-3.74 8.35,-8.35 0,-4.61 -3.74,-8.35 -8.35,-8.35 z m 0.04,14.01 c -2.3,0 -4.17,-1.87 -4.17,-4.17 0,-2.3 1.87,-4.17 4.17,-4.17 0.51,0 1.01,0.09 1.46,0.26 -0.13,0.33 -0.21,0.7 -0.21,1.08 0,1.61 1.3,2.92 2.92,2.92 -0.05,2.26 -1.9,4.08 -4.17,4.08' style='fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 144.48,101.56 c -0.88,-0.89 -2.66,-1.31 -4.61,-1.49 0.0,-0.06 0.02,-0.13 0.02,-0.2 0,-0.14 -0.02,-0.28 -0.05,-0.41 -0.06,-0.24 -0.18,-0.46 -0.33,-0.65 -0.31,-0.37 -0.77,-0.62 -1.3,-0.62 -0.48,0 -0.92,0.2 -1.23,0.53 -0.16,0.17 -0.28,0.38 -0.36,0.61 -0.05,0.17 -0.09,0.35 -0.09,0.54 0,0.02 0.0,0.04 0.0,0.06 -0.03,0 -0.07,-7.94e-4 -0.11,-7.94e-4 -0.05,0 -0.11,0 -0.18,0 0.0,-0.02 0.0,-0.04 0.0,-0.06 0,-0.19 -0.03,-0.37 -0.09,-0.54 -0.07,-0.23 -0.2,-0.44 -0.36,-0.61 -0.31,-0.33 -0.74,-0.53 -1.23,-0.53 -0.53,0 -1.0,0.25 -1.32,0.63 -0.15,0.19 -0.26,0.41 -0.32,0.65 -0.03,0.13 -0.05,0.26 -0.05,0.4 0,0.07 0.01,0.14 0.02,0.21 -1.76,0.17 -3.61,0.58 -4.53,1.49 -0.29,0.28 -0.48,0.62 -0.55,1.02 -0.02,0.1 -0.03,0.22 -0.03,0.33 0,3.35 3.73,5.89 8.68,5.89 4.78,0 8.68,-2.64 8.68,-5.89 0,-0.11 -0.02,-0.22 -0.03,-0.33 -0.06,-0.39 -0.25,-0.73 -0.53,-1.02 z m -7.01,-1.7 c 0,-0.21 0.09,-0.4 0.24,-0.53 0.12,-0.11 0.28,-0.18 0.47,-0.18 0.2,0 0.38,0.08 0.51,0.22 0.12,0.12 0.2,0.3 0.2,0.49 0,0.04 -0.01,0.08 -0.02,0.13 -0.06,0.33 -0.34,0.58 -0.69,0.58 -0.36,0 -0.66,-0.28 -0.7,-0.64 -0.0,-0.02 -0.01,-0.05 -0.01,-0.07 z m -3.68,0 c 0,-0.19 0.07,-0.36 0.19,-0.49 0.13,-0.13 0.31,-0.22 0.52,-0.22 0.18,0 0.34,0.07 0.47,0.18 0.14,0.13 0.24,0.31 0.24,0.53 0,0.02 -0.01,0.04 -0.01,0.07 -0.03,0.35 -0.33,0.64 -0.7,0.64 -0.35,0 -0.63,-0.25 -0.69,-0.58 -0.0,-0.04 -0.02,-0.08 -0.02,-0.13 z m -0.16,6.82 c -0.93,0 -1.7,-0.76 -1.7,-1.7 h 0.49 0.49 c 0,0.39 0.32,0.72 0.72,0.72 0.39,0 0.72,-0.32 0.72,-0.72 h 0.49 0.49 c 0,0.93 -0.76,1.7 -1.7,1.7' style='fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 115.82,89.87 c -1.14,-0.47 -2.52,-0.26 -3.29,0.5 -0.36,0.36 -0.7,0.99 -0.3,1.93 0.45,1.07 1.22,2.33 2.01,3.29 0.99,1.2 1.64,1.49 1.87,1.47 0.13,-0.01 0.27,-0.27 0.37,-0.67 l 0.02,0.0 c -0.44,-1.11 -0.69,-2.32 -0.69,-3.6 v -2.93' style='fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 160.15,90.37 c -0.75,-0.75 -2.09,-0.96 -3.23,-0.52 v 2.95 c 0,1.29 -0.25,2.52 -0.71,3.65 0.09,0.36 0.22,0.6 0.35,0.61 0.23,0.01 0.88,-0.27 1.87,-1.47 0.78,-0.95 1.56,-2.21 2.01,-3.29 0.39,-0.94 0.05,-1.57 -0.3,-1.93' style='fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 130.96,93.23 c -1.51,-0.11 -2.7,-1.37 -2.7,-2.91 0,-0.35 0.06,-0.69 0.18,-1.0 -0.5,-0.22 -1.06,-0.34 -1.65,-0.34 -2.3,0 -4.18,1.87 -4.18,4.17 0,2.3 1.87,4.17 4.18,4.17 2.28,0 4.13,-1.82 4.17,-4.09' style='fill:#282a2b;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 146.05,97.32 c 2.27,0 4.12,-1.81 4.17,-4.08 -1.61,-0.0 -2.92,-1.31 -2.92,-2.92 0,-0.38 0.07,-0.75 0.21,-1.08 -0.45,-0.17 -0.95,-0.26 -1.46,-0.26 -2.3,0 -4.17,1.87 -4.17,4.17 0,2.3 1.87,4.17 4.17,4.17' style='fill:#282a2b;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 146.29,102.92 c 0,-0.11 -0.0,-0.22 -0.01,-0.33 h 0.86 c 3.46,0 6.5,-1.81 8.23,-4.53 0.03,-0.05 0.07,-0.1 0.1,-0.16 0.3,0.33 0.66,0.46 0.96,0.48 0.04,0.0 0.08,0.0 0.13,0.0 0.83,0 1.79,-0.65 2.86,-1.95 0.86,-1.04 1.71,-2.43 2.21,-3.61 0.52,-1.24 0.31,-2.46 -0.58,-3.37 -1.01,-1.01 -2.65,-1.38 -4.15,-0.97 V 86.44 71.27 c 1e-5,-5.4 -4.37,-9.78 -9.78,-9.78 h -21.53 c -5.4,0 -9.78,4.37 -9.78,9.78 v 15.21 2.0 c -1.52,-0.42 -3.19,-0.07 -4.21,0.95 -0.89,0.9 -1.11,2.12 -0.58,3.37 0.5,1.18 1.34,2.56 2.21,3.61 1.07,1.29 2.03,1.95 2.86,1.95 0.04,0 0.08,-0.0 0.13,-0.0 0.31,-0.02 0.68,-0.15 1.0,-0.52 0.03,0.05 0.06,0.1 0.1,0.15 1.73,2.74 4.77,4.58 8.26,4.58 h 0.86 c -0.01,0.11 -0.01,0.22 -0.01,0.33 0,4.06 4.26,7.12 9.92,7.12 5.46,0 9.91,-3.19 9.91,-7.12 z m 13.86,-12.55 c 0.36,0.36 0.7,0.99 0.3,1.93 -0.45,1.07 -1.22,2.33 -2.01,3.29 -0.99,1.2 -1.64,1.49 -1.87,1.47 -0.12,-0.01 -0.26,-0.25 -0.35,-0.61 0.45,-1.12 0.71,-2.35 0.71,-3.65 v -2.95 c 1.13,-0.43 2.47,-0.22 3.23,0.52 z m -14.14,-7.05 c 4.61,0 8.35,3.74 8.35,8.35 -0.01,4.61 -3.75,8.35 -8.35,8.35 -0.67,0 -1.33,-0.08 -1.96,-0.24 -0.47,-0.22 -1.0,-0.4 -1.56,-0.54 -2.84,-1.33 -4.82,-4.21 -4.82,-7.56 0,-4.61 3.74,-8.35 8.35,-8.35 z m -29.52,13.09 c -0.09,0.39 -0.23,0.65 -0.37,0.67 -0.23,0.01 -0.88,-0.27 -1.87,-1.47 -0.78,-0.95 -1.56,-2.21 -2.01,-3.29 -0.39,-0.94 -0.05,-1.57 0.3,-1.93 0.76,-0.76 2.14,-0.97 3.29,-0.5 v 2.93 c 0,1.27 0.25,2.48 0.69,3.6 z m 1.9,-4.72 c 0,-4.61 3.74,-8.35 8.35,-8.35 4.61,0 8.35,3.74 8.35,8.35 -0.0,3.38 -2.03,6.3 -4.94,7.6 -0.46,0.12 -0.91,0.27 -1.32,0.46 -0.66,0.17 -1.36,0.27 -2.08,0.27 -4.61,0 -8.35,-3.74 -8.35,-8.34 z m 9.29,11.24 c 0,-0.11 0.01,-0.22 0.03,-0.33 0.07,-0.39 0.26,-0.73 0.55,-1.02 0.91,-0.9 2.76,-1.31 4.53,-1.49 -0.0,-0.06 -0.02,-0.13 -0.02,-0.21 0,-0.14 0.02,-0.27 0.05,-0.4 0.06,-0.24 0.17,-0.46 0.32,-0.65 0.31,-0.38 0.78,-0.63 1.32,-0.63 0.48,0 0.92,0.2 1.23,0.53 0.16,0.17 0.28,0.38 0.36,0.61 0.05,0.17 0.09,0.35 0.09,0.54 0,0.02 -0.0,0.04 -0.0,0.06 0.06,0 0.12,0 0.18,0 0.03,0 0.07,7.94e-4 0.11,7.94e-4 -0.0,-0.02 -0.0,-0.04 -0.0,-0.06 0,-0.19 0.03,-0.37 0.09,-0.54 0.07,-0.23 0.2,-0.44 0.36,-0.61 0.31,-0.32 0.74,-0.53 1.23,-0.53 0.52,0 0.99,0.24 1.3,0.62 0.15,0.19 0.27,0.41 0.33,0.65 0.03,0.13 0.05,0.27 0.05,0.41 0,0.07 -0.01,0.13 -0.02,0.2 1.94,0.18 3.73,0.59 4.61,1.49 0.28,0.28 0.46,0.62 0.53,1.02 0.01,0.11 0.03,0.21 0.03,0.33 0,3.24 -3.89,5.89 -8.68,5.89 -4.95,0 -8.68,-2.53 -8.68,-5.89' style='fill:#282a2b;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 134.49,100.58 c 0.37,0 0.66,-0.28 0.7,-0.64 0.0,-0.02 0.01,-0.04 0.01,-0.07 0,-0.21 -0.09,-0.4 -0.24,-0.53 -0.12,-0.11 -0.29,-0.18 -0.47,-0.18 -0.2,0 -0.38,0.08 -0.52,0.22 -0.12,0.12 -0.19,0.3 -0.19,0.49 0,0.04 0.01,0.08 0.02,0.13 0.06,0.33 0.34,0.58 0.69,0.58' style='fill:#282a2b;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 138.18,100.58 c 0.35,0 0.62,-0.25 0.69,-0.58 0.0,-0.04 0.02,-0.08 0.02,-0.13 0,-0.19 -0.07,-0.36 -0.2,-0.49 -0.13,-0.13 -0.31,-0.22 -0.51,-0.22 -0.18,0 -0.34,0.07 -0.47,0.18 -0.15,0.13 -0.24,0.31 -0.24,0.53 0,0.02 0.01,0.05 0.01,0.07 0.04,0.35 0.33,0.64 0.7,0.64' style='fill:#282a2b;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 134.33,104.97 c -3.4e-4,0.39 -0.32,0.72 -0.72,0.72 -0.39,0 -0.72,-0.32 -0.72,-0.72 h -0.49 -0.49 c 0,0.93 0.76,1.7 1.7,1.7 0.93,0 1.7,-0.76 1.7,-1.7 h -0.49 -0.49' style='fill:#282a2b;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n        <path d='m 165.85,106.2 -0.22,-0.22 c -4.1,-4.1 -10.78,-4.1 -14.88,0 -1.78,1.78 -2.77,4.16 -2.77,6.68 0,2.52 0.98,4.9 2.77,6.68 l 0.08,0.08 c 1.66,1.66 3.85,2.5 6.04,2.5 2.19,-2.7e-4 4.37,-0.83 6.04,-2.5 2.45,-2.45 2.45,-6.44 0,-8.9 -0.57,-0.57 -1.5,-0.57 -2.08,0 -0.57,0.57 -0.57,1.5 0,2.08 1.3,1.3 1.3,3.42 0,4.73 -2.18,2.18 -5.73,2.18 -7.92,0 l -0.08,-0.08 c -1.22,-1.22 -1.9,-2.86 -1.9,-4.6 0,-1.73 0.67,-3.37 1.9,-4.6 2.95,-2.95 7.76,-2.95 10.71,0 l 0.22,0.22 c 4.23,4.23 4.23,11.13 0,15.37 -2.87,2.86 -6.68,4.45 -10.74,4.45 h -4.03 c 0.06,-0.28 0.11,-0.59 0.11,-0.94 0,-4.96 -7.31,-7.35 -7.23,-10.18 0.19,-6.94 -9.38,-7.21 -9.58,-0.27 -0.19,6.97 -1.11,14.35 6.29,14.35 0.64,0 1.26,-0.0 1.86,-0.0 0.03,0.0 0.07,0.01 0.11,0.01 h 12.46 c 4.84,0 9.4,-1.88 12.82,-5.31 5.38,-5.38 5.38,-14.15 0,-19.54' style='fill:#282a2b;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.03' />\n    </g>\n</svg>",
webdav:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 640 512'><path d='M537.585 226.56C541.725 215.836 544 204.184 544 192c0-53.019-42.981-96-96-96-19.729 0-38.065 5.954-53.316 16.159C367.042 64.248 315.288 32 256 32c-88.366 0-160 71.634-160 160 0 2.728.07 5.439.204 8.133C40.171 219.845 0 273.227 0 336c0 79.529 64.471 144 144 144h368c70.692 0 128-57.308 128-128 0-61.93-43.983-113.586-102.415-125.44z'/></svg>",
yandex:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 256 512'><path d='M153.1 315.8L65.7 512H2l96-209.8c-45.1-22.9-75.2-64.4-75.2-141.1C22.7 53.7 90.8 0 171.7 0H254v512h-55.1V315.8h-45.8zm45.8-269.3h-29.4c-44.4 0-87.4 29.4-87.4 114.6 0 82.3 39.4 108.8 87.4 108.8h29.4V46.5z'/></svg>",
firefox:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 496 512'><path d='M496 262.5C497.6 380.8 388.9 504 248 504c-106.7 0-190.9-62.5-229.8-151.7-43-97.7-5.7-251.6 70.3-320.3l-2.7 69.7c3.9-5 32.5-6.4 37.1 0C139 71 190.7 48 232.1 47.2c-15.8 13.3-52.2 61.6-49.2 86.2 20.2 6.4 51.1 6.6 67.4 7.7 5 2.8 4.1 19.6-5.8 33.4 0 0-13 18-48.1 24.3l2.7 41.1-37-7.4c-12.4 31.5 17.4 59.4 48.4 54.2 34.3-5.8 45.9-32 70-30.6 23.8 1.4 33.2 14.6 30.1 27.1 0 0-3.9 14.9-29.6 12.4-21.8 34.5-50.4 53.5-97.3 49.4 71.3 59.1 168.3 5.5 192.6-42.8 24.3-48.1 8-122.1-16.3-142.3 28.7 12.4 43.7 27.6 54.2 55.5 5.5-61.9-22.9-132.1-73.8-173.3C436 70.1 494.3 144.2 496 262.5z'/></svg>",
chrome:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 496 512'><path d='M131.5 217.5L55.1 100.1c47.6-59.2 119-91.8 192-92.1 42.3-.3 85.5 10.5 124.8 33.2 43.4 25.2 76.4 61.4 97.4 103L264 133.4c-58.1-3.4-113.4 29.3-132.5 84.1zm32.9 38.5c0 46.2 37.4 83.6 83.6 83.6s83.6-37.4 83.6-83.6-37.4-83.6-83.6-83.6-83.6 37.3-83.6 83.6zm314.9-89.2L339.6 174c37.9 44.3 38.5 108.2 6.6 157.2L234.1 503.6c46.5 2.5 94.4-7.7 137.8-32.9 107.4-62 150.9-192 107.4-303.9zM133.7 303.6L40.4 120.1C14.9 159.1 0 205.9 0 256c0 124 90.8 226.7 209.5 244.9l63.7-124.8c-57.6 10.8-113.2-20.8-139.5-72.5z'/></svg>",
onedrive:"<svg xmlns='http://www.w3.org/2000/svg' id='Layer_1' data-name='Layer 1' viewBox='0 0 24 24'><title>Artboard 1</title><g id='Templates'><path d='M17,10.57a3,3,0,0,1,1.18.23,3.11,3.11,0,0,1,1,.64,2.82,2.82,0,0,1,.65,1,3,3,0,0,1-1.6,3.95,3.08,3.08,0,0,1-1.16.23H8a4,4,0,0,1-1.56-.31,4,4,0,0,1,0-7.38A4,4,0,0,1,8,8.57a3.54,3.54,0,0,1,.73.07,4.63,4.63,0,0,1,.72-.87,4.72,4.72,0,0,1,.89-.65,4.58,4.58,0,0,1,1-.41,4.79,4.79,0,0,1,1.13-.14,4.37,4.37,0,0,1,1.64.3,4.55,4.55,0,0,1,1.36.84,4.39,4.39,0,0,1,1,1.27A4.66,4.66,0,0,1,17,10.57Z'/></g></svg>",
gdrive:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><path d='M339 314.9L175.4 32h161.2l163.6 282.9H339zm-137.5 23.6L120.9 480h310.5L512 338.5H201.5zM154.1 67.4L0 338.5 80.6 480 237 208.8 154.1 67.4z'/></svg>",
dropbox:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><path d='M256 87.2l-151.9 93.9L0 97.5 150.6 0 256 87.2zM0 265.3l150.6 98.3 105.4-88L104.1 181 0 265.3zm256 10.3l105.4 88L512 265.3l-104.1-84.2L256 275.6zM512 97.5L361.4 0 256 87.2l151.9 93.9L512 97.5zM256.3 294.6l-105.7 87.7-45.2-29.5V385l150.9 90.5L407.2 385v-32.2L362 382.3l-105.7-87.7z'/></svg>",
instagram:"<svg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='5 4 44 44' style='enable-background:new 5 4 44 44;' xml:space='preserve'><style type='text/css'>.st0{fill:none;}</style><g><rect x='-0.2' y='0.1' class='st0' width='53.8' height='53.4'/><path d='M48.1,26.3c0,4.3,0,7.2-0.1,8.8c-0.2,3.9-1.3,6.9-3.5,9s-5.1,3.3-9,3.5c-1.6,0.1-4.6,0.1-8.8,0.1c-4.3,0-7.2,0-8.8-0.1 c-3.9-0.2-6.9-1.3-9-3.5c-2.1-2.1-3.3-5.1-3.5-9c-0.1-1.6-0.1-4.6-0.1-8.8s0-7.2,0.1-8.8c0.2-3.9,1.3-6.9,3.5-9 c2.1-2.1,5.1-3.3,9-3.5c1.6-0.1,4.6-0.1,8.8-0.1c4.3,0,7.2,0,8.8,0.1c3.9,0.2,6.9,1.3,9,3.5s3.3,5.1,3.5,9 C48,19.1,48.1,22,48.1,26.3z M28.8,8.7c-1.3,0-2,0-2.1,0c-0.1,0-0.8,0-2.1,0c-1.3,0-2.3,0-2.9,0c-0.7,0-1.6,0-2.7,0.1 c-1.1,0-2.1,0.1-2.9,0.3c-0.8,0.1-1.5,0.3-2,0.5c-0.9,0.4-1.7,0.9-2.5,1.6c-0.7,0.7-1.2,1.5-1.6,2.5c-0.2,0.5-0.4,1.2-0.5,2 s-0.2,1.7-0.3,2.9c0,1.1-0.1,2-0.1,2.7c0,0.7,0,1.7,0,2.9c0,1.3,0,2,0,2.1s0,0.8,0,2.1c0,1.3,0,2.3,0,2.9c0,0.7,0,1.6,0.1,2.7 c0,1.1,0.1,2.1,0.3,2.9s0.3,1.5,0.5,2c0.4,0.9,0.9,1.7,1.6,2.5c0.7,0.7,1.5,1.2,2.5,1.6c0.5,0.2,1.2,0.4,2,0.5 c0.8,0.1,1.7,0.2,2.9,0.3s2,0.1,2.7,0.1c0.7,0,1.7,0,2.9,0c1.3,0,2,0,2.1,0c0.1,0,0.8,0,2.1,0c1.3,0,2.3,0,2.9,0 c0.7,0,1.6,0,2.7-0.1c1.1,0,2.1-0.1,2.9-0.3c0.8-0.1,1.5-0.3,2-0.5c0.9-0.4,1.7-0.9,2.5-1.6c0.7-0.7,1.2-1.5,1.6-2.5 c0.2-0.5,0.4-1.2,0.5-2c0.1-0.8,0.2-1.7,0.3-2.9c0-1.1,0.1-2,0.1-2.7c0-0.7,0-1.7,0-2.9c0-1.3,0-2,0-2.1s0-0.8,0-2.1 c0-1.3,0-2.3,0-2.9c0-0.7,0-1.6-0.1-2.7c0-1.1-0.1-2.1-0.3-2.9c-0.1-0.8-0.3-1.5-0.5-2c-0.4-0.9-0.9-1.7-1.6-2.5 c-0.7-0.7-1.5-1.2-2.5-1.6c-0.5-0.2-1.2-0.4-2-0.5c-0.8-0.1-1.7-0.2-2.9-0.3c-1.1,0-2-0.1-2.7-0.1C31.1,8.7,30.1,8.7,28.8,8.7z  M34.4,18.5c2.1,2.1,3.2,4.7,3.2,7.8s-1.1,5.6-3.2,7.8c-2.1,2.1-4.7,3.2-7.8,3.2c-3.1,0-5.6-1.1-7.8-3.2c-2.1-2.1-3.2-4.7-3.2-7.8 s1.1-5.6,3.2-7.8c2.1-2.1,4.7-3.2,7.8-3.2C29.7,15.3,32.3,16.3,34.4,18.5z M31.7,31.3c1.4-1.4,2.1-3.1,2.1-5s-0.7-3.7-2.1-5.1 c-1.4-1.4-3.1-2.1-5.1-2.1c-2,0-3.7,0.7-5.1,2.1s-2.1,3.1-2.1,5.1s0.7,3.7,2.1,5c1.4,1.4,3.1,2.1,5.1,2.1 C28.6,33.4,30.3,32.7,31.7,31.3z M39.9,13c0.5,0.5,0.8,1.1,0.8,1.8c0,0.7-0.3,1.3-0.8,1.8c-0.5,0.5-1.1,0.8-1.8,0.8 s-1.3-0.3-1.8-0.8c-0.5-0.5-0.8-1.1-0.8-1.8c0-0.7,0.3-1.3,0.8-1.8c0.5-0.5,1.1-0.8,1.8-0.8S39.4,12.5,39.9,13z'/></g></svg>",
facebook:"<svg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 30 30' style='enable-background:new 0 0 30 30;' xml:space='preserve'><style type='text/css'>.f{}.c{fill:none;}</style><g><circle class='c' cx='15' cy='15' r='13' stroke='black' stroke-width='3'/><path class='f' d='M16.4,23.9v-8.1h2.7l0.4-3.2h-3.1v-2c0-0.9,0.3-1.5,1.6-1.5l1.7,0V6.2c-0.3,0-1.3-0.1-2.4-0.1c-2.4,0-4.1,1.5-4.1,4.2v2.3h-2.7v3.2h2.7v8.1H16.4z'/></g></svg>",
edge:'\n<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256">\n    <defs>\n        <radialGradient id="b" cx="161.8" cy="68.9" r="95.4" gradientTransform="matrix(1 0 0 -.95 0 248.8)" gradientUnits="userSpaceOnUse">\n            <stop offset=".7" stop-opacity="0"/>\n            <stop offset=".9" stop-opacity=".5"/>\n            <stop offset="1"/>\n        </radialGradient>\n        <radialGradient id="d" cx="-340.3" cy="63" r="143.2" gradientTransform="matrix(.15 -.99 -.8 -.12 176.6 -125.4)" gradientUnits="userSpaceOnUse">\n            <stop offset=".8" stop-opacity="0"/>\n            <stop offset=".9" stop-opacity=".5"/>\n            <stop offset="1"/>\n        </radialGradient>\n        <radialGradient id="e" cx="113.4" cy="570.2" r="202.4" gradientTransform="matrix(-.04 1 2.13 .08 -1179.5 -106.7)" gradientUnits="userSpaceOnUse">\n            <stop offset="0" stop-color="#35c1f1"/>\n            <stop offset=".1" stop-color="#34c1ed"/>\n            <stop offset=".2" stop-color="#2fc2df"/>\n            <stop offset=".3" stop-color="#2bc3d2"/>\n            <stop offset=".7" stop-color="#36c752"/>\n        </radialGradient>\n        <radialGradient id="f" cx="376.5" cy="568" r="97.3" gradientTransform="matrix(.28 .96 .78 -.23 -303.8 -148.5)" gradientUnits="userSpaceOnUse">\n            <stop offset="0" stop-color="#66eb6e"/>\n            <stop offset="1" stop-color="#66eb6e" stop-opacity="0"/>\n        </radialGradient>\n        <linearGradient id="a" x1="63.3" y1="84" x2="241.7" y2="84" gradientTransform="matrix(1 0 0 -1 0 266)" gradientUnits="userSpaceOnUse">\n            <stop offset="0" stop-color="#0c59a4"/>\n            <stop offset="1" stop-color="#114a8b"/>\n        </linearGradient>\n        <linearGradient id="c" x1="157.3" y1="161.4" x2="46" y2="40.1" gradientTransform="matrix(1 0 0 -1 0 266)" gradientUnits="userSpaceOnUse">\n            <stop offset="0" stop-color="#1b9de2"/>\n            <stop offset=".2" stop-color="#1595df"/>\n            <stop offset=".7" stop-color="#0680d7"/>\n            <stop offset="1" stop-color="#0078d4"/>\n        </linearGradient>\n    </defs>\n    <path d="M235.7 195.5a93.7 93.7 0 0 1-10.6 4.7 101.9 101.9 0 0 1-35.9 6.4c-47.3 0-88.5-32.5-88.5-74.3a31.5 31.5 0 0 1 16.4-27.3c-42.8 1.8-53.8 46.4-53.8 72.5 0 74 68.1 81.4 82.8 81.4 7.9 0 19.8-2.3 27-4.6l1.3-.4a128.3 128.3 0 0 0 66.6-52.8 4 4 0 0 0-5.3-5.6Z" transform="translate(-4.6 -5)" style="fill:url(#a)"/>\n    <path d="M235.7 195.5a93.7 93.7 0 0 1-10.6 4.7 101.9 101.9 0 0 1-35.9 6.4c-47.3 0-88.5-32.5-88.5-74.3a31.5 31.5 0 0 1 16.4-27.3c-42.8 1.8-53.8 46.4-53.8 72.5 0 74 68.1 81.4 82.8 81.4 7.9 0 19.8-2.3 27-4.6l1.3-.4a128.3 128.3 0 0 0 66.6-52.8 4 4 0 0 0-5.3-5.6Z" transform="translate(-4.6 -5)" style="isolation:isolate;opacity:.35;fill:url(#b)"/>\n    <path d="M110.3 246.3A79.2 79.2 0 0 1 87.6 225a80.7 80.7 0 0 1 29.5-120c3.2-1.5 8.5-4.1 15.6-4a32.4 32.4 0 0 1 25.7 13 31.9 31.9 0 0 1 6.3 18.7c0-.2 24.5-79.6-80-79.6-43.9 0-80 41.6-80 78.2a130.2 130.2 0 0 0 12.1 56 128 128 0 0 0 156.4 67 75.5 75.5 0 0 1-62.8-8Z" transform="translate(-4.6 -5)" style="fill:url(#c)"/>\n    <path d="M110.3 246.3A79.2 79.2 0 0 1 87.6 225a80.7 80.7 0 0 1 29.5-120c3.2-1.5 8.5-4.1 15.6-4a32.4 32.4 0 0 1 25.7 13 31.9 31.9 0 0 1 6.3 18.7c0-.2 24.5-79.6-80-79.6-43.9 0-80 41.6-80 78.2a130.2 130.2 0 0 0 12.1 56 128 128 0 0 0 156.4 67 75.5 75.5 0 0 1-62.8-8Z" transform="translate(-4.6 -5)" style="opacity:.41;fill:url(#d);isolation:isolate"/>\n    <path d="M157 153.8c-.9 1-3.4 2.5-3.4 5.6 0 2.6 1.7 5.2 4.8 7.3 14.3 10 41.4 8.6 41.5 8.6a59.6 59.6 0 0 0 30.3-8.3 61.4 61.4 0 0 0 30.4-52.9c.3-22.4-8-37.3-11.3-43.9C228 28.8 182.3 5 132.6 5a128 128 0 0 0-128 126.2c.5-36.5 36.8-66 80-66 3.5 0 23.5.3 42 10a72.6 72.6 0 0 1 30.9 29.3c6.1 10.6 7.2 24.1 7.2 29.5s-2.7 13.3-7.8 19.9Z" transform="translate(-4.6 -5)" style="fill:url(#e)"/>\n    <path d="M157 153.8c-.9 1-3.4 2.5-3.4 5.6 0 2.6 1.7 5.2 4.8 7.3 14.3 10 41.4 8.6 41.5 8.6a59.6 59.6 0 0 0 30.3-8.3 61.4 61.4 0 0 0 30.4-52.9c.3-22.4-8-37.3-11.3-43.9C228 28.8 182.3 5 132.6 5a128 128 0 0 0-128 126.2c.5-36.5 36.8-66 80-66 3.5 0 23.5.3 42 10a72.6 72.6 0 0 1 30.9 29.3c6.1 10.6 7.2 24.1 7.2 29.5s-2.7 13.3-7.8 19.9Z" transform="translate(-4.6 -5)" style="fill:url(#f)"/>\n</svg>',
android:'\n<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-147 -70 294 345">\n    <g fill="#a4c639">\n        <use stroke-width="14.4" xlink:href="#b" stroke="#FFF"/>\n        <use xlink:href="#a" transform="scale(-1,1)"/>\n        <g id="a" stroke="#FFF" stroke-width="7.2">\n            <rect rx="6.5" transform="rotate(29)" height="86" width="13" y="-86" x="14"/>\n            <rect id="c" rx="24" height="133" width="48" y="41" x="-143"/>\n            <use y="97" x="85" xlink:href="#c"/>\n        </g>\n        <g id="b">\n            <ellipse cy="41" rx="91" ry="84"/>\n            <rect rx="22" height="182" width="182" y="20" x="-91"/>\n        </g>\n    </g>\n    <g stroke="#FFF" stroke-width="7.2" fill="#FFF">\n        <path d="m-95 44.5h190"/><circle cx="-42" r="4"/><circle cx="42" r="4"/>\n    </g>\n</svg>',
safari:'\n<svg class="svg-inline--fa fa-safari fa-w-16 min-w1 w-auto" aria-hidden="true" data-fa-processed="" data-prefix="fab" data-icon="safari" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">\n    <path fill="currentColor" d="M236.9 256.8c0-9.1 6.6-17.7 16.3-17.7 8.9 0 17.4 6.4 17.4 16.1 0 9.1-6.4 17.7-16.1 17.7-9 0-17.6-6.7-17.6-16.1zM504 256c0 137-111 248-248 248S8 393 8 256 119 8 256 8s248 111 248 248zm-26.6 0c0-122.3-99.1-221.4-221.4-221.4S34.6 133.7 34.6 256 133.7 477.4 256 477.4 477.4 378.3 477.4 256zm-72.5 96.6c0 3.6 13 10.2 16.3 12.2-27.4 41.5-69.8 71.4-117.9 83.3l-4.4-18.5c-.3-2.5-1.9-2.8-4.2-2.8-1.9 0-3 2.8-2.8 4.2l4.4 18.8c-13.3 2.8-26.8 4.2-40.4 4.2-36.3 0-72-10.2-103-29.1 1.7-2.8 12.2-18 12.2-20.2 0-1.9-1.7-3.6-3.6-3.6-3.9 0-12.2 16.6-14.7 19.9-41.8-27.7-72-70.6-83.6-119.6l19.1-4.2c2.2-.6 2.8-2.2 2.8-4.2 0-1.9-2.8-3-4.4-2.8L62 294.5c-2.5-12.7-3.9-25.5-3.9-38.5 0-37.1 10.5-73.6 30.2-104.9 2.8 1.7 16.1 10.8 18.3 10.8 1.9 0 3.6-1.4 3.6-3.3 0-3.9-14.7-11.3-18-13.6 28.2-41.2 71.1-70.9 119.8-81.9l4.2 18.5c.6 2.2 2.2 2.8 4.2 2.8s3-2.8 2.8-4.4L219 61.7c12.2-2.2 24.6-3.6 37.1-3.6 37.1 0 73.3 10.5 104.9 30.2-1.9 2.8-10.8 15.8-10.8 18 0 1.9 1.4 3.6 3.3 3.6 3.9 0 11.3-14.4 13.3-17.7 41 27.7 70.3 70 81.7 118.2l-15.5 3.3c-2.5.6-2.8 2.2-2.8 4.4 0 1.9 2.8 3 4.2 2.8l15.8-3.6c2.5 12.7 3.9 25.7 3.9 38.7 0 36.3-10 72-28.8 102.7-2.8-1.4-14.4-9.7-16.6-9.7-2.1 0-3.8 1.7-3.8 3.6zm-33.2-242.2c-13 12.2-134.2 123.7-137.6 129.5l-96.6 160.5c12.7-11.9 134.2-124 137.3-129.3l96.9-160.7z">\n    </path>\n</svg>',
apple:'\n<svg class="svg-inline--fa fa-apple fa-w-14 min-w1 w-auto" aria-hidden="true" data-fa-processed="" data-prefix="fab" data-icon="apple" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">\n    <path fill="currentColor" d="M247.2 137.6c-6.2 1.9-15.3 3.5-27.9 4.6 1.1-56.7 29.9-96.6 88-110.1 9.3 41.6-26.1 94.1-60.1 105.5zm121.3 72.7c6.4-9.4 16.6-19.9 30.6-31.7-22.3-27.6-48.1-44.3-85.1-44.3-35.4 0-65.2 18.2-87 18.2-18.5 0-51.9-16.1-84.5-16.1-69.6 0-106.5 68.1-106.5 139C36 354.2 95.7 480 156.2 480c23.8 0 45.2-18 73.5-18 29.3 0 52.8 17.2 80.3 17.2 46 0 88.6-77.5 102-119.7-46.8-14.3-84.4-90.2-43.5-149.2z">\n    </path>\n</svg>\n'
}[e]).replace(/#|%20|%0A/g,(e=>"#"==e?"%23":" ")),empty:"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="},getLayouts:()=>Pt.map((e=>{const{name:t,theme:n,layout:r}=e;return{name:t,value:n?[r,n].join("#"):r}})),getLayoutByValue:e=>{let t;return Pt.some((n=>{if(n.value===e)return t=n,!0})),t},getEditorThemes:()=>Object.keys(Nt).map((e=>({name:Nt[e]||e,value:e})))},{getLayoutByValue:Ht}=Qt;let qt;const Xt=(()=>{const e=F;let t,n;return{cache:function(){
const t=document.documentElement,n=location.pathname;if(e&&t&&n)try{const r=JSON.parse(e.getItem("background")||"{}");r[n]={class:t.getAttribute("class")},e.setItem("background",JSON.stringify(r))}catch(e){}},reset:function(){n=!0;const e=document.documentElement;"string"==typeof t&&e&&e.setAttribute("class",t)},restore:function(){if(n)return;const r=location.pathname;if(e&&r)try{let n;const i=e.getItem("background");if(i&&(n=JSON.parse(i))&&(n=n[r])&&n.class){const e=document.documentElement
;if(!e)return;t=e.getAttribute("class")||"",e.setAttribute("class",t+" "+n.class)}}catch(e){}}}})(),Yt=e=>{const t=()=>{e(),Xt.restore()};Jt?document.body?t():window.addEventListener("DOMContentLoaded",t):(Wt=Wt||[]).push((()=>{Yt(e)})),qt&&(window.clearTimeout(qt),qt=void 0)};let Wt,Jt=!1;const Kt=e=>{Jt=!0,Wt&&(Wt.forEach((e=>{e()})),Wt=void 0),Xt.restore(),zt((t=>{if(t&&t.pong){let e,n=t.config.layout;window.matchMedia("(prefers-color-scheme: dark)").matches&&!n.includes("#")&&(n+="#dark")
;const r=Ht(n);if(!r)return void $.warn(`Unknown layout ${n}`);const{theme:i,footer:s}=r;i&&"light"!==i?(s&&window.setTimeout((()=>{const e=document.querySelectorAll(".footer")[0];e&&(e.innerHTML=s)}),0),e=i):e="default";const o="chromium",a="page_"+location.pathname.substr(1).split(".")[0].replace(/[^a-z0-9]/gi,"_");Xt.reset(),document.documentElement.classList.add(Re()?"mobile":"desktop",e,o,a),window.setTimeout(Xt.cache,500)}e.suc&&e.suc()}),e.fail)
},$t="aaa\naarp\nabb\nabbott\nabogado\nac\nacademy\naccenture\naccountant\naccountants\naco\nactive\nactor\nad\nadac\nads\nadult\nae\naeg\naero\naf\nafl\nag\nagency\nai\naig\nairforce\nairtel\nal\nalibaba\nalipay\nallfinanz\nalsace\nam\namica\namsterdam\nan\nanalytics\nandroid\nao\napartments\napp\napple\naq\naquarelle\nar\naramco\narchi\narmy\narpa\narte\nas\nasia\nassociates\nat\nattorney\nau\nauction\naudi\naudio\nauthor\nauto\nautos\naw\nax\naxa\naz\nazure\nba\nbaidu\nband\nbank\nbar\nbarcelona\nbarclaycard\nbarclays\nbargains\nbauhaus\nbayern\nbb\nbbc\nbbva\nbcn\nbd\nbe\nbeats\nbeer\nbentley\nberlin\nbest\nbet\nbf\nbg\nbh\nbharti\nbi\nbible\nbid\nbike\nbing\nbingo\nbio\nbiz\nbj\nbl\nblack\nblackfriday\nbloomberg\nblue\nbm\nbms\nbmw\nbn\nbnl\nbnpparibas\nbo\nboats\nboehringer\nbom\nbond\nboo\nbook\nboots\nbosch\nbostik\nbot\nboutique\nbq\nbr\nbradesco\nbridgestone\nbroadway\nbroker\nbrother\nbrussels\nbs\nbt\nbudapest\nbugatti\nbuild\nbuilders\nbusiness\nbuy\nbuzz\nbv\nbw\nby\nbz\nbzh\nca\ncab\ncafe\ncal\ncall\ncamera\ncamp\ncancerresearch\ncanon\ncapetown\ncapital\ncar\ncaravan\ncards\ncare\ncareer\ncareers\ncars\ncartier\ncasa\ncash\ncasino\ncat\ncatering\ncba\ncbn\ncc\ncd\nceb\ncenter\nceo\ncern\ncf\ncfa\ncfd\ncg\nch\nchanel\nchannel\nchat\ncheap\nchloe\nchristmas\nchrome\nchurch\nci\ncipriani\ncircle\ncisco\ncitic\ncity\ncityeats\nck\ncl\nclaims\ncleaning\nclick\nclinic\nclinique\nclothing\ncloud\nclub\nclubmed\ncm\ncn\nco\ncoach\ncodes\ncoffee\ncollege\ncologne\ncom\ncommbank\ncommunity\ncompany\ncompare\ncomputer\ncomsec\ncondos\nconstruction\nconsulting\ncontact\ncontractors\ncooking\ncool\ncoop\ncorsica\ncountry\ncoupons\ncourses\ncr\ncredit\ncreditcard\ncreditunion\ncricket\ncrown\ncrs\ncruises\ncsc\ncu\ncuisinella\ncv\ncw\ncx\ncy\ncymru\ncyou\ncz\ndabur\ndad\ndance\ndate\ndating\ndatsun\nday\ndclk\nde\ndealer\ndeals\ndegree\ndelivery\ndell\ndeloitte\ndelta\ndemocrat\ndental\ndentist\ndesi\ndesign\ndev\ndiamonds\ndiet\ndigital\ndirect\ndirectory\ndiscount\ndj\ndk\ndm\ndnp\ndo\ndocs\ndog\ndoha\ndomains\ndoosan\ndownload\ndrive\ndubai\ndurban\ndvag\ndz\nearth\neat\nec\nedeka\nedu\neducation\nee\neg\neh\nemail\nemerck\nenergy\nengineer\nengineering\nenterprises\nepson\nequipment\ner\nerni\nes\nesq\nestate\net\neu\neurovision\neus\nevents\neverbank\nexchange\nexpert\nexposed\nexpress\nfage\nfail\nfairwinds\nfaith\nfamily\nfan\nfans\nfarm\nfashion\nfast\nfeedback\nferrero\nfi\nfilm\nfinal\nfinance\nfinancial\nfirestone\nfirmdale\nfish\nfishing\nfit\nfitness\nfj\nfk\nflickr\nflights\nflorist\nflowers\nflsmidth\nfly\nfm\nfo\nfoo\nfootball\nford\nforex\nforsale\nforum\nfoundation\nfox\nfr\nfresenius\nfrl\nfrogans\nfrontier\nfund\nfurniture\nfutbol\nfyi\nga\ngal\ngallery\ngallup\ngame\ngarden\ngb\ngbiz\ngd\ngdn\nge\ngea\ngent\ngenting\ngf\ngg\nggee\ngh\ngi\ngift\ngifts\ngives\ngiving\ngl\nglass\ngle\nglobal\nglobo\ngm\ngmail\ngmo\ngmx\ngn\ngold\ngoldpoint\ngolf\ngoo\ngoog\ngoogle\ngop\ngot\ngov\ngp\ngq\ngr\ngrainger\ngraphics\ngratis\ngreen\ngripe\ngroup\ngs\ngt\ngu\ngucci\nguge\nguide\nguitars\nguru\ngw\ngy\nhamburg\nhangout\nhaus\nhdfcbank\nhealth\nhealthcare\nhelp\nhelsinki\nhere\nhermes\nhiphop\nhitachi\nhiv\nhk\nhm\nhn\nhockey\nholdings\nholiday\nhomedepot\nhomes\nhonda\nhorse\nhost\nhosting\nhoteles\nhotmail\nhouse\nhow\nhr\nhsbc\nht\nhu\nhyundai\nibm\nicbc\nice\nicu\nid\nie\nifm\niinet\nil\nim\nimmo\nimmobilien\nin\nindustries\ninfiniti\ninfo\ning\nink\ninstitute\ninsurance\ninsure\nint\ninternational\ninvestments\nio\nipiranga\niq\nir\nirish\nis\niselect\nist\nistanbul\nit\nitau\niwc\njaguar\njava\njcb\nje\njetzt\njewelry\njlc\njll\njm\njmp\njo\njobs\njoburg\njot\njoy\njp\njprs\njuegos\nkaufen\nkddi\nke\nkfh\nkg\nkh\nki\nkia\nkim\nkinder\nkitchen\nkiwi\nkm\nkn\nkoeln\nkomatsu\nkp\nkpn\nkr\nkrd\nkred\nkw\nky\nkyoto\nkz\nla\nlacaixa\nlamborghini\nlamer\nlancaster\nland\nlandrover\nlanxess\nlasalle\nlat\nlatrobe\nlaw\nlawyer\nlb\nlc\nlds\nlease\nleclerc\nlegal\nlexus\nlgbt\nli\nliaison\nlidl\nlife\nlifeinsurance\nlifestyle\nlighting\nlike\nlimited\nlimo\nlincoln\nlinde\nlink\nlive\nliving\nlixil\nlk\nloan\nloans\nlol\nlondon\nlotte\nlotto\nlove\nlr\nls\nlt\nltd\nltda\nlu\nlupin\nluxe\nluxury\nlv\nly\nma\nmadrid\nmaif\nmaison\nmakeup\nman\nmanagement\nmango\nmarket\nmarketing\nmarkets\nmarriott\nmba\nmc\nmd\nme\nmed\nmedia\nmeet\nmelbourne\nmeme\nmemorial\nmen\nmenu\nmeo\nmf\nmg\nmh\nmiami\nmicrosoft\nmil\nmini\nmk\nml\nmm\nmma\nmn\nmo\nmobi\nmobily\nmoda\nmoe\nmoi\nmom\nmonash\nmoney\nmontblanc\nmormon\nmortgage\nmoscow\nmotorcycles\nmov\nmovie\nmovistar\nmp\nmq\nmr\nms\nmt\nmtn\nmtpc\nmtr\nmu\nmuseum\nmutuelle\nmv\nmw\nmx\nmy\nmz\nna\nnadex\nnagoya\nname\nnatura\nnavy\nnc\nne\nnec\nnet\nnetbank\nnetwork\nneustar\nnew\nnews\nnexus\nnf\nng\nngo\nnhk\nni\nnico\nnikon\nninja\nnissan\nnl\nno\nnokia\nnorton\nnowruz\nnp\nnr\nnra\nnrw\nntt\nnu\nnyc\nnz\nobi\noffice\nokinawa\nom\nomega\none\nong\nonl\nonline\nooo\noracle\norange\norg\norganic\norigins\nosaka\notsuka\novh\npa\npage\npamperedchef\npanerai\nparis\npars\npartners\nparts\nparty\npe\npet\npf\npg\nph\npharmacy\nphilips\nphoto\nphotography\nphotos\nphysio\npiaget\npics\npictet\npictures\npid\npin\nping\npink\npizza\npk\npl\nplace\nplay\nplaystation\nplumbing\nplus\npm\npn\npohl\npoker\nporn\npost\npr\npraxi\npress\npro\nprod\nproductions\nprof\npromo\nproperties\nproperty\nprotection\nps\npt\npub\npw\npwc\npy\nqa\nqpon\nquebec\nquest\nracing\nre\nread\nrealtor\nrealty\nrecipes\nred\nredstone\nredumbrella\nrehab\nreise\nreisen\nreit\nren\nrent\nrentals\nrepair\nreport\nrepublican\nrest\nrestaurant\nreview\nreviews\nrexroth\nrich\nricoh\nrio\nrip\nro\nrocher\nrocks\nrodeo\nroom\nrs\nrsvp\nru\nruhr\nrun\nrw\nrwe\nryukyu\nsa\nsaarland\nsafe\nsafety\nsakura\nsale\nsalon\nsamsung\nsandvik\nsandvikcoromant\nsanofi\nsap\nsapo\nsarl\nsas\nsaxo\nsb\nsbs\nsc\nsca\nscb\nschaeffler\nschmidt\nscholarships\nschool\nschule\nschwarz\nscience\nscor\nscot\nsd\nse\nseat\nsecurity\nseek\nselect\nsener\nservices\nseven\nsew\nsex\nsexy\nsfr\nsg\nsh\nsharp\nshell\nshia\nshiksha\nshoes\nshow\nshriram\nsi\nsingles\nsite\nsj\nsk\nski\nskin\nsky\nskype\nsl\nsm\nsmile\nsn\nsncf\nso\nsoccer\nsocial\nsoftbank\nsoftware\nsohu\nsolar\nsolutions\nsony\nsoy\nspace\nspiegel\nspreadbetting\nsr\nsrl\nss\nst\nstada\nstar\nstarhub\nstatefarm\nstatoil\nstc\nstcgroup\nstockholm\nstorage\nstudio\nstudy\nstyle\nsu\nsucks\nsupplies\nsupply\nsupport\nsurf\nsurgery\nsuzuki\nsv\nswatch\nswiss\nsx\nsy\nsydney\nsymantec\nsystems\nsz\ntab\ntaipei\ntaobao\ntatamotors\ntatar\ntattoo\ntax\ntaxi\ntc\ntci\ntd\nteam\ntech\ntechnology\ntel\ntelefonica\ntemasek\ntennis\ntf\ntg\nth\nthd\ntheater\ntheatre\ntickets\ntienda\ntiffany\ntips\ntires\ntirol\ntj\ntk\ntl\ntm\ntmall\ntn\nto\ntoday\ntokyo\ntools\ntop\ntoray\ntoshiba\ntours\ntown\ntoyota\ntoys\ntp\ntr\ntrade\ntrading\ntraining\ntravel\ntravelers\ntravelersinsurance\ntrust\ntrv\ntt\ntube\ntui\ntushu\ntv\ntvs\ntw\ntz\nua\nubs\nug\nuk\num\nunicom\nuniversity\nuno\nuol\nus\nuy\nuz\nva\nvacations\nvana\nvc\nve\nvegas\nventures\nverisign\nversicherung\nvet\nvg\nvi\nviajes\nvideo\nvillas\nvin\nvip\nvirgin\nvision\nvista\nvistaprint\nviva\nvlaanderen\nvn\nvodka\nvolkswagen\nvote\nvoting\nvoto\nvoyage\nvu\nwales\nwalter\nwang\nwanggou\nwatch\nwatches\nweather\nweatherchannel\nwebcam\nweber\nwebsite\nwed\nwedding\nweir\nwf\nwhoswho\nwien\nwiki\nwilliamhill\nwin\nwindows\nwine\nwme\nwolterskluwer\nwork\nworks\nworld\nws\nwtc\nwtf\nxbox\nxerox\nxin\n测试\nकॉम\nपरीक्षा\n佛山\n慈善\n集团\n在线\n한국\n点看\nคอม\nভারত\n八卦\n‏موقع‎\nবাংলা\n公益\n公司\n移动\n我爱你\nмосква\nиспытание\nқаз\nонлайн\nсайт\n联通\nсрб\nбел\n‏קום‎\n时尚\n테스트\n淡马锡\nорг\nनेट\n삼성\nசிங்கப்பூர்\n商标\n商店\n商城\nдети\nмкд\n‏טעסט‎\nею\nポイント\n新闻\n工行\n‏كوم‎\n中文网\n中信\n中国\n中國\n娱乐\n谷歌\nభారత్\nලංකා\n购物\n測試\nભારત\nभारत\n‏آزمایشی‎\nபரிட்சை\n网店\nसंगठन\n餐厅\n网络\nком\nукр\n香港\n诺基亚\nδοκιμή\n飞利浦\n‏إختبار‎\n台湾\n台灣\n手表\n手机\nмон\n‏الجزائر‎\n‏عمان‎\n‏ارامكو‎\n‏ایران‎\n‏امارات‎\n‏بازار‎\n‏پاکستان‎\n‏الاردن‎\n‏موبايلي‎\n‏بھارت‎\n‏المغرب‎\n‏السعودية‎\n‏سودان‎\n‏همراه‎\n‏عراق‎\n‏مليسيا‎\n澳門\n닷컴\n政府\n‏شبكة‎\n‏بيتك‎\nგე\n机构\n组织机构\n健康\nไทย\n‏سورية‎\nрус\nрф\n珠宝\n‏تونس‎\n大拿\nみんな\nグーグル\nελ\n世界\nਭਾਰਤ\n网址\n닷넷\nコム\n游戏\nvermögensberater\nvermögensberatung\n企业\n信息\n‏مصر‎\n‏قطر‎\n广东\nஇலங்கை\nஇந்தியா\nհայ\n新加坡\n‏فلسطين‎\nテスト\n政务\nxperia\nxxx\nxyz\nyachts\nyahoo\nyamaxun\nyandex\nye\nyodobashi\nyoga\nyokohama\nyoutube\nyt\nza\nzara\nzero\nzip\nzm\nzone\nzuerich\nzw".split("\n").join("|"),en="ac.cn\nac.jp\nac.uk\nad.jp\nah.cn\naichi.jp\nakita.jp\naomori.jp\nasn.au\nbj.cn\nchiba.jp\nco.cc\nco.ck\nco.fk\nco.gg\nco.im\nco.in\nco.ir\nco.je\nco.jp\nco.kr\nco.ma\ncom.ac\ncom.af\ncom.ag\ncom.ai\ncom.al\ncom.ar\ncom.au\ncom.aw\ncom.az\ncom.ba\ncom.bb\ncom.bh\ncom.bi\ncom.bm\ncom.bo\ncom.br\ncom.bs\ncom.bt\ncom.by\ncom.bz\ncom.ci\ncom.cm\ncom.cn\ncom.co\ncom.cu\ncom.cw\ncom.cy\ncom.de\ncom.dm\ncom.do\ncom.dz\ncom.ec\ncom.ee\ncom.eg\ncom.es\ncom.et\ncom.fr\ncom.ge\ncom.gh\ncom.gi\ncom.gl\ncom.gn\ncom.gp\ncom.gr\ncom.gt\ncom.gu\ncom.gy\ncom.hk\ncom.hn\ncom.hr\ncom.ht\ncom.im\ncom.io\ncom.iq\ncom.is\ncom.jo\ncom.kg\ncom.ki\ncom.km\ncom.kp\ncom.ky\ncom.kz\ncom.la\ncom.lb\ncom.lc\ncom.lk\ncom.lr\ncom.lv\ncom.ly\ncom.mg\ncom.mk\ncom.ml\ncom.mo\ncom.ms\ncom.mt\ncom.mu\ncom.mv\ncom.mw\ncom.mx\ncom.my\ncom.na\ncom.nf\ncom.ng\ncom.ni\ncom.nr\ncom.om\ncom.pa\ncom.pe\ncom.pf\ncom.ph\ncom.pk\ncom.pl\ncom.pr\ncom.ps\ncom.pt\ncom.py\ncom.qa\ncom.re\ncom.ro\ncom.ru\ncom.rw\ncom.sa\ncom.sb\ncom.sc\ncom.sd\ncom.se\ncom.sg\ncom.sh\ncom.sl\ncom.sn\ncom.so\ncom.st\ncom.sv\ncom.sy\ncom.tj\ncom.tm\ncom.tn\ncom.to\ncom.tr\ncom.tt\ncom.tw\nco.mu\ncom.ua\ncom.ug\ncom.uy\ncom.uz\ncom.vc\ncom.ve\ncom.vi\ncom.vn\ncom.vu\ncom.ws\ncom.zm\nconf.au\nco.nz\nco.rw\nco.th\nco.tj\nco.tt\nco.tv\nco.tz\nco.ug\nco.uk\nco.us\nco.ve\nco.yu\nco.za\nco.zm\nco.zw\ncq.cn\ncsiro.au\nde.net\ndk.org\ned.jp\nedu.au\nedu.cn\nedu.uk\nehime.jp\neu.org\nfukui.jp\nfukuoka.jp\nfukushima.jp\ngb.net\ngd.cn\ngifu.jp\ngo.jp\ngov.au\ngov.cn\ngov.jp\ngov.uk\ngr.jp\ngs.cn\ngunma.jp\ngx.cn\ngz.cn\nhb.cn\nhe.cn\nhi.cn\nhiroshima.jp\nhk.cn\nhl.cn\nhn.cn\nhokkaido.jp\nhyogo.jp\nibaraki.jp\nid.au\ninfo.au\nishikawa.jp\niwate.jp\njl.cn\njs.cn\nkagawa.jp\nkagoshima.jp\nkanagawa.jp\nkanazawa.jp\nkawasaki.jp\nkitakyushu.jp\nkobe.jp\nkochi.jp\nkumamoto.jp\nkyoto.jp\nlg.jp\nln.cn\nltd.uk\nmatsuyama.jp\nme.uk\nmie.jp\nmiyagi.jp\nmiyazaki.jp\nmo.cn\nmod.uk\nnagano.jp\nnagasaki.jp\nnagoya.jp\nnara.jp\nne.jp\nnet.au\nnet.cn\nnet.jp\nnet.uk\nnhs.uk\nnic.uk\nniigata.jp\nnm.cn\nnx.cn\noita.jp\nokayama.jp\nokinawa.jp\norg.au\norg.cn\norg.jp\norg.uk\nor.jp\nosaka.jp\notc.au\noz.au\nplc.uk\npolice.uk\nqh.cn\nsaga.jp\nsaitama.jp\nsapporo.jp\nsc.cn\nsch.uk\nsendai.jp\nsh.cn\nshiga.jp\nshimane.jp\nshizuoka.jp\nsn.cn\nsx.cn\ntakamatsu.jp\ntelememo.au\ntj.cn\ntochigi.jp\ntokushima.jp\ntokyo.jp\ntottori.jp\ntoyama.jp\ntw.cn\nuk.net\nutsunomiya.jp\nwakayama.jp\nxj.cn\nxz.cn\nyamagata.jp\nyamaguchi.jp\nyamanashi.jp\nyn.cn\nyokohama.jp\nzj.cn".split("\n").join("|"),tn=(".("+[$t,en].join("|")+")").replace(/\./gi,"\\."),nn=Xe({
timeout:180,check_interval:120,retimeout_on_get:!0}),rn=e=>e.split("").map((e=>{const t=e.toLowerCase(),n=e.toUpperCase();return t!=n?"["+t+n+"]":e})).join(""),sn="://",on=(e,t)=>{let{scheme:n,host:r,path:i}=(e=>{let t,n,r,i;const s="/",o=e.replace(/\$$/,"").split(sn);o.length<2?(t="",n=e):(t=o[0].replace(/^\^/,""),n=o.slice(1).join(sn));const a=n.split(s);if(i=a.length<2?"/":s+a.slice(1).join(s),r=a[0],
"http*"===t||t.match(/\*|http|https|file|ftp/)||($.warn('uri: override scheme "'+t+'" with "*"'),t="*"),"file"===t)r="";else{const e=r,t=r.match(/\*$|(\*\.)?[^/*]+/);r=(t?t[0]:"").replace(/:[0-9]*$/,""),r!==e&&$.warn('uri: override host "'+e+'" with "'+r+'"')}return i&&i.substr(0,1)===s||($.warn('uri: prefix path "'+i+'" with "/"'),i=s+i),{scheme:t,host:r,path:i}})(e);return n="http*"===n?"https?":j(n).replace(/\*/gi,"[^:/#?]*"),r=j(r).replace(/\*\\\./gi,"(*\\.)?").replace(/\*/gi,"[^#?/]*"),
i=j(i).replace(/\*/gi,".*"),r=r.replace(new RegExp(j(".tld")+"$"),tn),t?(n=n.toLowerCase(),r=r.toLowerCase()):(n=rn(n),r=rn(r)),r+="(:[0-9]{1,5})?","^"+n+j(sn)+r+i+"$"},an=["protocol","hostname","origin"],ln=["port"],cn=["pathname"],dn=["search","hash"];let An;const un={protocol:"",scheme:"",origin:"",pathname:"",hostname:"",port:void 0,search:"",hash:""},hn=(e,t)=>{let n=Object.assign({},un);if(null==e);else if(["data:","view-source:"].some((t=>e.startsWith(t)))){n.origin="null"
;const t=e.indexOf(":");n.protocol=e.substr(0,t+1),n.pathname=e.substr(t+1)}else{let r,i;try{r=new URL(e)}catch(t){try{const t="http:";i=Q(),r=new URL(e||"/",`${t}//${i}/${i}`)}catch(e){i=void 0}}if(r){let{protocol:s,origin:o,pathname:a,hostname:l,port:c,search:d,hash:A,username:u,password:h}=r;if(i&&(s=o="",l===i&&(l=""),a=a.replace(`/${i}`,"")),n={protocol:s,origin:o,pathname:a,hostname:l,port:parseInt(c)||void 0,search:d,hash:A,username:u,password:h},!t&&s){
if(0!==e.toLowerCase().indexOf(s))if(e.startsWith("//"))n.origin="",n.protocol="";else{if(!["/","?","#"].includes(e[0]))return hn("/"+e);An=An||hn("",!0),[...dn,...cn].forEach((e=>{An[e]===n[e]&&(n[e]="")})),an.forEach((e=>{n[e]=""})),ln.forEach((e=>{n[e]=void 0}))}["tampermonkey:"].includes(n.protocol)&&(n.pathname=((n.hostname?"/"+n.hostname:"")+(n.pathname||"")).replace(/^\/+/,"/"),n.hostname="")}}}return 0===n.port&&(n.port=void 0),Object.defineProperties(n,{domain:{get:function(){
const e=n.hostname.split("."),t=e.pop();let r=`${e.pop()}.${t}`;return fn(r)&&(r=`${e.pop()}.${r}`),r}},scheme:{get:function(){return n.protocol.replace(/:$/,"")}}}),n};let pn;const fn=e=>(pn=pn||new RegExp("^("+en.replace(/\./g,"\\.")+")$"),!!e.match(pn)),mn=e=>{if(null==e)return;let t=nn.get(e);return t||(t=hn(e),nn.set(e,t),t)
},gn=e=>Object.entries(e).map((([e,t])=>void 0===t?null:f(e)+"="+f(t))).filter((e=>e)).join("&"),_n="mtm_visitor",bn="default",vn="pageview",kn="script_update",yn="script",wn="cloud",Rn="event",Cn="pageview",xn="ping",En="https://a.tampermonkey.net/matomo.php",Gn=F;let Zn,Sn,Bn,In=null;const Tn=()=>({url:En,siteId:4,tracker:{[bn]:{enabled:Vn(4,1)},[yn]:{enabled:!0},[kn]:{enabled:Vn(4,10)},[wn]:{enabled:Vn(4,5e-4)}}}),Un=()=>({url:En,siteId:5,tracker:{[bn]:{enabled:Vn(5,10)},[yn]:{enabled:!0},
[kn]:{enabled:Vn(5,10)},[wn]:{enabled:Vn(5,.001)}}}),Fn=()=>({url:En,siteId:6,tracker:{[bn]:{enabled:Vn(6,50)},[yn]:{enabled:!0},[kn]:{enabled:Vn(6,10)},[wn]:{enabled:Vn(6,.01)}}}),Mn={default:Un,gcal:Un,iikm:Tn,fcmf:Tn,saap:()=>({url:En,siteId:7,tracker:{[bn]:{enabled:!0},[yn]:{enabled:!0},[kn]:{enabled:Vn(7,10)},[wn]:{enabled:Vn(7,.01)}}}),fire:Fn,firb:Fn,dhdg:()=>({url:En,siteId:3,tracker:{[bn]:{enabled:Vn(3,1)},[yn]:{enabled:!0},[kn]:{enabled:Vn(3,10)},[wn]:{enabled:Vn(3,5e-4)}}}),mfdh:Un,
heif:()=>({url:"http://a.userscript.grobilan:8081/matomo.php",siteId:2,tracker:{[bn]:{enabled:!0}}})};let jn;const Ln=[{msg:"a disconnected port"},{msg:"Function.prototype.apply: Arguments list has wrong type",url:"event_bindings"},{msg:"Script error."}],On=e=>[...Array(e)].map((()=>Math.floor(16*Math.random()).toString(16))).join(""),Dn=e=>{if(!Gn)return;const t=[e.uuid,e.createTs,e.visitCount,e.currentVisitTs,e.lastVisitTs].join(".");Gn.setItem(_n,t)},zn=e=>{
const t=e||bn,n=jn.tracker[t]||jn.tracker[bn];return n.enabled?{url:jn.url,siteId:jn.siteId,options:n}:null},Pn=On(6),Nn=async(e,t,n)=>{if(!e)return;const r=(()=>{if(!Gn)return;const e=Gn.getItem(_n);if(!e)return;const t=e.split(".");if(t.length>=5){t.unshift("0");const[e,n,r,i,s,o]=t;return{createdNow:!1,newVisitor:e,uuid:n,createTs:r,visitCount:i,currentVisitTs:s,lastVisitTs:o}}})()||(()=>{const e=Math.floor(Date.now()/1e3).toString(),t={createdNow:!0,newVisitor:"1",uuid:On(16),createTs:e,
visitCount:"0",currentVisitTs:e,lastVisitTs:""};return Dn(t),t})();let i;const s=new Date,o={idsite:e.siteId,rec:1,action_name:B?B.title:g.href||Se.short_id,url:g.href,_id:r.uuid,rand:On(4),apiv:1,h:s.getHours(),m:s.getMinutes(),s:s.getSeconds(),cookie:1,pv_id:Pn},a={...o,_idts:Number(r.createTs),_idvc:Number(r.visitCount),_viewts:Number(r.lastVisitTs),res:I?`${I.width}x${I.height}`:"0x0"};if(t==Cn){const e=Sn?{gt_ms:Sn}:{},t={...o,...a,...e,new_visit:1};Dn((e=>{
const t=Math.floor(Date.now()/1e3).toString();return e.newVisitor="0",e.visitCount=(Number(e.visitCount)+1).toString(),e.lastVisitTs=e.currentVisitTs,e.currentVisitTs=t,e})(r)),i=t}else if(t==Rn){if(!n)return;i={...o,ca:1,e_c:n.category,e_a:n.action,e_n:n.name,e_v:n.value}}else{if(t!=xn)return;i={...o,...a,ping:1}}i=Object.assign(o,i);const l=`${e.url}?${gn(i)}`;if(B){const e=B.createElement("img");e.src=l,e.onload=()=>{var t;null===(t=e.parentNode)||void 0===t||t.removeChild(e)},e.onerror=()=>{
var t;null===(t=e.parentNode)||void 0===t||t.removeChild(e)},(B.body||B.head||B.documentElement).appendChild(e)}else try{await k(l)}catch(e){$.warn("stats:",e)}},Vn=(e,t)=>{let n=100*Math.random()<t;if(Gn)try{let r,i;const s=["wsr",e,t].join("_"),o=Date.now(),a=864e7;if(r=Gn.getItem(s)){try{i=JSON.parse(r)}catch(e){}(!i||i.ts+a<o)&&(i={ts:o,w:n})}else i={ts:o,w:n};n=i.w,Gn.setItem(s,JSON.stringify(i))}catch(e){}return n};let Qn=Ie();const Hn=()=>null===In&&Qn?Qn.promise():Ie.Pledge(!!In);let qn
;const Xn=(e,t)=>{qn=`${(null==t?void 0:t.version)||""} `;const n=Se.short_id;jn=(Mn[n]||Mn[bn])(),R("error",(t=>{const{message:n,filename:r,lineno:i,colno:s,error:o}=t;((t,n,r,i,s)=>{let o="";if(s)try{o=s.stack||""}catch(e){}Kn(t.toString(),qn+e+"@"+Be.urls.prepareForReport(n||""),[r+":"+i,o].join(";"))})(n,r,i,s,o)}));const r=t=>{let n="";try{n=t.stack}catch(e){}
Kn("CSP violation of "+t.effectiveDirective,qn+e+"@"+Be.urls.prepareForReport(t.documentURI),[t.sourceFile," -> ",t.lineNumber+":"+t.columnNumber,n].join(";"))};B?B.addEventListener("securitypolicyviolation",(e=>r(e))):R("securitypolicyviolation",(e=>r(e))),(null==t?void 0:t.started)&&(Sn=Date.now()-t.started.getTime())},Yn=e=>{In=e,Qn&&(Qn.resolve(In),Qn=void 0),In?Wn():Bn&&(A(Bn),Bn=void 0)},Wn=()=>{Zn&&(Nn(zn(vn),Cn),Zn=!1),Bn=_((()=>Nn(zn(vn),xn)),864e5)},Jn=async(e,t,n)=>{
if(!await Hn())return;let r="",i="";"init"===t?(i="Initialized",r=e):"error"===t&&(i="Error",r=e+" -> "+n),Nn(zn(wn),Rn,{category:"Cloud",action:i,name:r})},Kn=async(e,t,n)=>{if(!await Hn())return;void 0===n&&(t+=" "+g.href,n="");let r=!1;for(const n of Ln){if(!n.msg&&!n.url)return;n.msg&&-1==e.indexOf(n.msg)||n.url&&-1==t.indexOf(n.url)||(r=!0)}r||Nn(zn("error"),Rn,{category:"Error",action:e,name:t+":"+n})};var $n=e(2462),er=e.n($n);const tr=(()=>{let e,t,n=!1;const r=()=>{const e=Ie()
;return t=e,e};return{write:()=>{const t=Ie();return n=!1,e=new(er()),t.resolve(e),t.promise()},open:i=>{const s=r();return n=!0,er().loadAsync(i).then((t=>{e=t,s.resolve(e)}),(e=>{t&&t.reject(e),s.reject(e)})),s.promise()},entries:()=>{const t=r(),n=e.files,i=Object.keys(n).map((e=>{const t=n[e];if(t&&!t.dir)return{filename:t.name}})).filter((e=>e));return t.resolve(i),t.promise()},get:t=>{const n=r(),i=e.file(t.filename);return i?i.async("arraybuffer").then((e=>{n.resolve(e)})):n.resolve(),
n.promise()},put:(t,n,i)=>{const s=r();try{e.file(t,n,{date:i?new Date(i):void 0}),s.resolve()}catch(e){s.reject(e)}return s.promise()},end:()=>{const t=r();return n?t.reject():e.generateAsync({type:"blob",compression:"DEFLATE",comment:"Created by Tampermonkey"}).then((e=>t.resolve(e)),(e=>t.reject(e))),t.promise()}}})(),nr={zip:{create:function(e,t){const n=Ie();return Ie.Pledge().then((()=>tr.write())).then((()=>{const t=Ie(),r={},i=(e,t)=>{let n=[e,t].join(".");if(r[n]){let s;do{
s=e+" ("+r[n]+")",n=[s,t].join("."),r[n]++}while(r[n]);return i(s,t)}return r[n]=1,n},s=e.length,o=()=>{if(!e.length)return t.resolve();const r=e.shift();if(!r)return t.resolve();const a=r.meta.name.replace(/[\\/$*?|]/g,"-"),l=i(a,"user.js"),c=i(a,"options.json"),d=i(a,"storage.json"),A={options:r.options,settings:r.settings,meta:r.meta},u=JSON.stringify(A),h=r.storage?JSON.stringify(r.storage):null;n.notify("Zip: "+Math.floor((s-e.length)/s*100)+"%"),
tr.put(l,r.source,r.meta.modified).then((()=>tr.put(c,u))).then((()=>h?tr.put(d,h):Ie.Pledge())).then((()=>{if(!r.resources.length&&!r.requires.length)return Ie.Pledge();const e=[];return["resources","requires"].forEach((t=>{const n=r[t];n&&n.length&&n.forEach((n=>{if(void 0===n.source)return;const r=n.meta.name.replace(/[\\/$*?|]/g,"-"),i=ae(t+n.meta.url),s=[l,i,r].join("-"),o=JSON.stringify(n.meta),a=se(n.source,{encoding:"resources"==t?void 0:"UTF-8"
}),c=tr.put(s,a).then((()=>tr.put(s+"."+t+".json",o)));e.push(c)}))})),Ie.when(e)})).fail((()=>{$.log("porter: add to zip failed")})).always((async()=>{await Ve(1e3),o()}))};return o(),t.promise()})).then((()=>t?tr.put("Tampermonkey.global.json",JSON.stringify(t)):Ie.Pledge())).then((()=>tr.end())).done((e=>{n.resolve(e)})).fail((()=>{n.reject()})),n.promise()},read:function(e){const t=Ie();let n;return Ie.Pledge().then((()=>tr.open(e))).then((()=>tr.entries())).then((e=>{
const r=Ie(),i={},s={},o=e.length,a=()=>{const l=e.shift();if(l)tr.get(l).done((e=>{let t=l.filename.match(/((.*)\.(storage\.json)|(.*)\.(options\.json)|(.*)\.(global\.json)|(.*)\.(user\.js)|(.*)\.user\.js-[0-9a-f]+-.*\.(resources\.json)|(.*)\.user\.js-[0-9a-f]+-.*\.(requires\.json))$/);if(t&&(t=t.slice(1).filter((e=>e))),!e||!t||t.length<3)s[l.filename]=e;else try{const r=t[1],s=t[2],o=ie(e,"UTF-8");if("global.json"==s)n=JSON.parse(o);else{const e=i[r]||{resources:{},requires:{}};i[r]=e,
"user.js"==s?e.source=o:"options.json"==s?e.options=JSON.parse(o):"resources.json"==s?e.resources[l.filename]={name:l.filename,data:JSON.parse(o)}:"requires.json"==s?e.requires[l.filename]={name:l.filename,data:JSON.parse(o)}:"storage.json"==s&&(e.storage=JSON.parse(o))}}catch(e){$.warn("porter: read from zip failed",e)}})).always((async()=>{t.notify("Zip: "+Math.floor((o-e.length)/o*100)+"%"),await Ve(1e3),a()}));else{const e=[];for(const[t,n]of Object.entries(i)){
const{options:r,source:i,storage:o}=n;if(!i){$.warn(`porter: invalid script ${t}`);continue}const a={requires:[],resources:[]};["resources","requires"].forEach((e=>{for(const t of Object.values(n[e])){const n=t.name.replace("."+e+".json",""),r=s[n];r&&a[e].push({meta:t.data,source:ie(r,{encoding:"resources"==e?void 0:"UTF-8"})||void 0})}}));const l={...a,...r,source:i,storage:o};e.push(l)}r.resolve({scripts:e,global_settings:n})}};return a(),r.promise()})).done((e=>{t.resolve(e)})).fail((()=>{
t.reject()})),t.promise()}},json:{create:function(e,t){const n=Ie(),r={created_by:"Tampermonkey",version:"1",scripts:[],settings:t};return e.forEach((e=>{const t={name:e.meta.name,options:e.options,storage:e.storage,enabled:e.settings.enabled,position:e.settings.position,file_url:e.meta.file_url,uuid:e.meta.uuid,source:ne(ee(e.source))};["resources","requires"].forEach((n=>{const r=e[n];if(!r||!r.length)return;const i=t[n]=[];r.forEach((e=>{if(void 0===e.source)return
;const t=e.meta,n=ne(ee(e.source));i.push({meta:t,source:n})}))})),r.scripts.push(t)})),n.resolve(JSON.stringify(r)),n.promise()},read:function(e){const t=Ie(),n=(e,r)=>{if(e.trim()){let i=null;try{i=JSON.parse(e);const n=e=>{if(e&&e.length)return e.map((e=>({meta:e.meta,source:e.source?te(re(e.source)):e.source})))},r=i.scripts.map((e=>({meta:{uuid:e.uuid,name:e.name,file_url:e.file_url},settings:{enabled:e.enabled,position:e.position},options:e.options,storage:e.storage,
source:te(re(e.source)),resources:n(e.resources)||[],requires:n(e.requires)||[]})));return t.resolve({scripts:r,global_settings:i.settings})}catch(t){if(!r){const t="<body>",r="</body>";if(-1!=e.indexOf(t)){const i=e.indexOf(t),s=e.lastIndexOf(r);if(-1!=i&&-1!=s)return e=e.substr(i+t.length,s-(i+t.length)),n(e,!0)}}}}t.reject()};return n(e),t.promise()}}},rr=nr
;var ir=e(8039),sr=e.n(ir),or=e(70),ar=e.n(or),lr=e(8754),cr=e.n(lr),dr=e(1763),Ar=e.n(dr),ur=e(8295),hr=e.n(ur),pr=e(230),fr=e.n(pr),mr=e(3880),gr=e.n(mr),_r=e(4350),br=e.n(_r),vr=e(1933),kr=e.n(vr),yr=e(2215),wr=e.n(yr),Rr=e(2672),Cr=e.n(Rr),xr=e(3316),Er=e.n(xr);const Gr={rules:{"userscripts/filename-user":sr(),"userscripts/no-invalid-grant":ar(),"userscripts/no-invalid-headers":cr(),"userscripts/no-invalid-metadata":Ar(),"userscripts/require-name":hr(),"userscripts/require-description":fr(),
"userscripts/require-version":gr(),"userscripts/require-attribute-space-prefix":br(),"userscripts/use-homepage-and-url":kr(),"userscripts/use-download-and-update-url":wr(),"userscripts/align-attributes":Cr(),"userscripts/avoid-regexp-include":Er()}};let Zr;const Sr=we();let Br=!0,Ir=!!G;if(F){let e;try{e=F.getItem("lint_worker")||""}catch(e){}"false"==e&&(Ir=!1)}Zr=Sr>=94?2022:Sr>=86?2021:Sr>=83?2020:Sr>=64?2018:Sr>=59?2017:Sr>=47?2015:5,Sr<77||Sr>100?(Br=!1,
$.warn("hint: disable inline ESLint config due to web worker CSP issues","https://bugs.chromium.org/p/chromium/issues/detail?id=777076","https://bugs.chromium.org/p/chromium/issues/detail?id=159303")):Ir||(Br=!1,$.warn("hint: disable inline ESLint config because web workers are unavailable and this extension's CSP doesn't allow unsafe eval, which is required for ESLint's dynamic reconfigration"));const Tr={"userscripts/no-invalid-grant":1,"userscripts/no-invalid-headers":1,
"userscripts/no-invalid-metadata":[2,{top:"optional"}],"userscripts/require-name":[2,"required"],"userscripts/require-description":[1,"required"],"userscripts/require-version":[1,"required"],"userscripts/require-attribute-space-prefix":1,"userscripts/use-homepage-and-url":0,"userscripts/use-download-and-update-url":1,"userscripts/avoid-regexp-include":1},Ur={env:{es6:Zr>=2015,browser:!0},parserOptions:{ecmaVersion:Zr,sourceType:"script",ecmaFeatures:{globalReturn:!0},allowAwaitOutsideFunction:!0
},rules:{curly:[1,"multi-line"],"dot-location":0,"dot-notation":[1,{allowKeywords:!0}],"no-caller":1,"no-case-declarations":2,"no-div-regex":0,"no-empty-pattern":2,"no-eq-null":0,"no-eval":1,"no-extra-bind":1,"no-fallthrough":1,"no-implicit-globals":2,"no-implied-eval":1,"no-lone-blocks":1,"no-loop-func":1,"no-multi-spaces":1,"no-multi-str":1,"no-native-reassign":1,"no-octal-escape":2,"no-octal":2,"no-proto":1,"no-redeclare":2,"no-return-assign":1,"no-sequences":1,"no-undef":1,
"no-useless-call":1,"no-useless-concat":1,"no-with":1}
},Fr={},Mr=["uneval","unsafeWindow","GM_info","GM","GM_addStyle","GM_addElement","GM_cookie","GM_deleteValue","GM_listValues","GM_getValue","GM_download","GM_log","GM_registerMenuCommand","GM_unregisterMenuCommand","GM_openInTab","GM_setValue","GM_addValueChangeListener","GM_removeValueChangeListener","GM_xmlhttpRequest","GM_webRequest","GM_getTab","GM_saveTab","GM_getTabs","GM_setClipboard","GM_notification","GM_getResourceText","GM_getResourceURL"];let jr;const Lr={};let Or
;const Dr=e=>e.map((e=>{const{message:t}=e;if(!Br){let n;t&&(n=t.match(/Configuration for rule "([^"]+)"[\s\S]*evaluate a string as JavaScript[\s\S]*/))&&(e.message=`Rule "${n[1]}": ESLint inline configuration is not supported by this browser.`,e.severity=1)}return e})).filter((e=>e)),zr={},Pr=e=>{const t=null==e?void 0:e.keys,n={keys:t,default:()=>[],set:(e,t)=>(e.push(t),e)};return(null==t?void 0:t.length)&&t.forEach((e=>zr[e]=n)),n},Nr=e=>{const t=null==e?void 0:e.keys,n={keys:t,
default:()=>null,set:(t,n,r)=>{const i=(null==e?void 0:e.convert)?e.convert(n,r):n;return null===t?i:t}};return(null==t?void 0:t.length)&&t.forEach((e=>zr[e]=n)),n},Vr=e=>{const t=null==e?void 0:e.keys,n={default:()=>null,set:()=>!0};return(null==t?void 0:t.length)&&t.forEach((e=>zr[e]=n)),n},Qr=e=>{const t=null==e?void 0:e.keys,n={keys:t,default:()=>({}),set:(t,n,r,i)=>{i=i?vt(i):"default";const s=(null==e?void 0:e.convert)?e.convert(n,r):n;return t[i]=t[i]||s,t}}
;return(null==t?void 0:t.length)&&t.forEach((e=>zr[e]=n)),n},Hr={name:Qr({convert:e=>null==e?void 0:e.replace(/\s\s+/g," ")}),version:Nr({convert:e=>(null==e?void 0:e.replace(/\s/g,""))||e}),grants:Pr({keys:["grant"]}),icon:Nr({keys:["icon","iconURL","iconUrl","defaulticon"]}),icon64:Nr({keys:["icon64","iconURL64"]}),supportURL:Nr({keys:["supportURL","supportUrl"]}),fileURL:Nr(),downloadURL:Nr({keys:["downloadURL","downloadUrl"]}),updateURL:Nr({keys:["updateURL","updateUrl"]}),namespace:Nr({
convert:e=>""===e?null:e}),author:Nr(),copyright:Nr(),homepage:Nr({keys:["homepage","homepageURL","homepageUrl","website","source"]}),description:Qr(),includes:Pr({keys:["include"]}),excludes:Pr({keys:["exclude"]}),matches:Pr({keys:["match"]}),requires:Pr({keys:["require"]}),resources:(e=>{const t=null==e?void 0:e.keys,n={keys:t,default:()=>({}),set:(t,n,r)=>{const i=n.match(/^(\S*)\s+(.*)/);if(i&&void 0===t[i[1]]){const n=(null==e?void 0:e.convert)?e.convert(i[2],r):i[2];t[i[1]]=n}return t}}
;return(null==t?void 0:t.length)&&t.forEach((e=>zr[e]=n)),n})({keys:["resource"]}),sandbox:Nr({keys:["sandbox","inject-into"],convert:(e,t)=>null!==e&&"inject-into"===t?{auto:"DOM",content:"DOM",page:null}[e]||null:e}),tags:Pr({keys:["tag"]}),noframes:Vr(),unwrap:Vr(),connects:Pr({keys:["connect","connect-src","domain"]}),webRequest:Pr(),"run-at":Nr(),"run-in":Pr(),antifeatures:(e=>{const t=null==e?void 0:e.keys,n={keys:t,default:()=>({}),set:(t,n,r,i)=>{var s;i=i?vt(i):"default"
;const o=n.match(/^(\S*)\s+(.*)/);if(o){const n=t[s=o[1]]||(t[s]={}),a=(null==e?void 0:e.convert)?e.convert(o[2],r):o[2];n[i]=a}return t}};return(null==t?void 0:t.length)&&t.forEach((e=>zr[e]=n)),n})({keys:["antifeature"]})},qr={};Object.entries(Hr).forEach((([e,t])=>{t.keys?t.keys.forEach((t=>{qr[t]=e})):qr[e]=e}));const Xr=(()=>{const e=e=>{const t=e.split(".");return t.slice(0,3).concat([t.slice(3).join(".")]).concat([0,0,0,0]).slice(0,4).map((e=>{
const t=e.toString().match(/((?:-?[0-9]+)?)([^0-9]*)((?:[0-9]+)?)(.*)/);return t?[Number(t[1]),t[2],Number(t[3]),t[4]]:[]})).reduce(((e,t)=>e.concat(t)))},t=(n,r)=>{const i=Array.isArray(n)?n:e(n),s=Array.isArray(r)?r:e(r);for(let e=0;e<16;e++){const n=i[e],r=s[e];if(e%2==1){if(!n&&r)return t.eNEWER;if(n&&!r)return t.eOLDER;const e=n.match(/\w/g)||[],i=r.match(/\w/g)||[];for(let n=0;n<Math.min(e.length,i.length);n++){if(e[n].charCodeAt(0)>i[n].charCodeAt(0))return t.eNEWER
;if(e[n].charCodeAt(0)<i[n].charCodeAt(0))return t.eOLDER}if(e.length>i.length)return t.eNEWER;if(e.length<i.length)return t.eOLDER}else{if(Number(n)>Number(r))return t.eNEWER;if(Number(n)<Number(r))return t.eOLDER}}return t.eEQUAL};return t.eERROR=-2,t.eOLDER=-1,t.eEQUAL=0,t.eNEWER=1,t})(),Yr="try to take over the world!",Wr="This script was deleted from Greasy Fork, and due to its negative effects, it has been automatically removed from your browser.";var Jr;new class{constructor(){
this.regexps=["https?://userscripts\\.org/scripts/(source|version)/([0-9]{1,9})\\.user\\.js","https?://userscripts-mirror\\.org/scripts/(source|version)/([0-9]{1,9})\\.user\\.js"],this.domains=["userscripts.org","userscripts-mirror.org"]}test(e){const t=this.regexps.reduce(((t,n)=>t||e.match(new RegExp(n))),null);if(t&&3==t.length)return{id:t[2],token:"uso",meta_url:!0,url:"http://userscripts-mirror.org/scripts/show/"+t[2],code_url:"http://userscripts-mirror.org/scripts/review/"+t[2],
issue_url:"http://contactbyweb.com/userscripts-mirror"}}updates(e){return!1}},new class{constructor(){this.regexps=["https?://greasyfork\\.org/(?:[^/]+/)?scripts/([^/-]+).*/code.*\\.user\\.js","https?://update.greasyfork\\.org/(?:[^/]+/)?scripts/([^/-]+).*/.*\\.user\\.js"],this.domains=["greasyfork.org","update.greasyfork.org"]}test(e){const t=this.regexps.reduce(((t,n)=>t||e.match(new RegExp(n))),null);if(t&&2==t.length){const e=t[1];return{id:e,token:"gf",meta_url:!0,
url:"https://greasyfork.org/scripts/"+e,issue_url:"https://greasyfork.org/scripts/"+e+"/feedback",code_url:"https://greasyfork.org/scripts/"+e+"/code"}}}async converts(e){let t,n=e;if(n){const e=n.description.trim();e===Yr?n.description="":e===Wr&&(n=null,t="deleted by hoster")}return{script:n,warning:t}}updates(e){
const t=new RegExp("https?://greasyfork\\.org/scripts/[^/]+/code/.*\\.user\\.js.*version=[0-9]+.*"),n=new RegExp("https?://update\\.greasyfork\\.org/scripts/[^/]+/[^/]+/[^/]+\\.user\\.js");return!e.match(n)&&!e.match(t)}},new class{constructor(){this.regexps=["https?://sleazyfork\\.org/(?:[^/]+/)?scripts/([^/-]+).*/code.*\\.user\\.js","https?://update.sleazyfork\\.org/(?:[^/]+/)?scripts/([^/-]+).*/.*\\.user\\.js"],this.domains=["sleazyfork.org","update.sleazyfork.org"]}test(e){
const t=this.regexps.reduce(((t,n)=>t||e.match(new RegExp(n))),null);if(t&&2==t.length){const e=t[1];return{id:e,token:"sf",meta_url:!0,url:"https://sleazyfork.org/scripts/"+e,issue_url:"https://sleazyfork.org/scripts/"+e+"/feedback",code_url:"https://sleazyfork.org/scripts/"+e+"/code"}}}async converts(e){let t,n=e;if(n){const e=n.description.trim();e===Yr?n.description="":e===Wr&&(n=null,t="deleted by hoster")}return{script:n,warning:t}}updates(e){
const t=new RegExp("https?://sleazyfork\\.org/scripts/([^/]+)/code/.*\\.user\\.js.*version=[0-9]+.*"),n=new RegExp("https?://update\\.sleazyfork\\.org/scripts/[^/]+/[^/]+/[^/]+\\.user\\.js");return!e.match(n)&&!e.match(t)}},new class{constructor(){this.regexps=["https?://openuserjs\\.org/install/([^/]+)+/(.+?)(?:\\.min)?\\.user\\.js"],this.domains=["openuserjs.org"]}test(e){const t=this.regexps.reduce(((t,n)=>t||e.match(new RegExp(n))),null);if(t&&3==t.length)return t.shift(),{id:t.join("/"),
token:"ouj",meta_header:!0,url:"https://openuserjs.org/scripts/"+t[0]+"/"+t[1],issue_url:"https://openuserjs.org/scripts/"+t[0]+"/"+t[1]+"/issues",code_url:"https://openuserjs.org/scripts/"+t[0]+"/"+t[1]+"/source"}}async converts(e){const t=e;return t&&t.description.trim()===Yr&&(t.description=""),{script:t,warning:void 0}}updates(e){return!0}},new class{constructor(){
this.regexps=["https?://raw\\.githubusercontent\\.com/([^/]+)/([^/]+)/[^/]+/(.*)\\.user\\.js","https?://github\\.com/([^/]+)/([^/]+)/raw/[^/]+/(.*)\\.user\\.js","https?://raw.github\\.com/([^/]+)/([^/]+)/[^/]+/(.*)\\.user\\.js","https?://github\\.com/([^/]+)/([^/]+)/releases/latest/download/(.*)\\.user\\.js","https?://github\\.com/([^/]+)/([^/]+)/releases/download/[^/]+/(.*)\\.user\\.js","https?://(([^\\.]+).github\\.io)/(.*)\\.user\\.js"],
this.domains=["github.com","githubusercontent.com","github.io"]}test(e){let t;const n=this.regexps.slice(0,-1).reduce(((t,n)=>t||e.match(new RegExp(n))),null),r=this.regexps.slice(-1).reduce(((t,n)=>t||e.match(new RegExp(n))),null);if(n&&4==n.length||r&&4==r.length){let e;if(n){n.shift();const[r,i,s]=n;e=[r,i].join("/"),t=[r,i].concat(i==s?[]:s).join("/")}else{if(!r)throw new Error("Should never happen!");{r.shift();const[n,i,s]=r;e=[i,n].join("/"),t=[i,n].concat(n==s?[]:s).join("/")}}return{
id:t,token:"gh",url:"https://github.com/"+e,issue_url:"https://github.com/"+e+"/issues"}}}updates(e){return!0}},new class{constructor(){this.regexps=["https?://gist\\.github\\.com/([^/]+)/([^/]+)/raw/(.*/)?.*\\.user\\.js","https?://gist\\.githubusercontent\\.com/([^/]+)/([^/]+)/raw/(.*/)?.*\\.user\\.js"],this.domains=["gist.github.com","gist.githubusercontent.com"]}test(e){const t=this.regexps.reduce(((t,n)=>t||e.match(new RegExp(n))),null);if(t&&4==t.length){const e=t[1],n=t[2],r=`${e}/${n}`
;if(n&&r)return{id:r,token:"gst",url:`https://gist.github.com/${r}`,issue_url:`https://gist.github.com/${r}#new_comment_field`,code_url:`https://gist.github.com/${r}`}}}updates(e){const t=this.regexps.reduce(((t,n)=>t||e.match(new RegExp(n))),null);return!(!t||null!=t[3])}},new class{constructor(){this.regexps=["https?://gitlab\\.com/([^/]+)(?:/([^/]+))?/([^/-]{1,1}[^/]+)/?(?:-/)?(snippets/[^/]+/)?(raw|wikis)/(?:[^/]+/)?(.*?)\\.user\\.js"],this.domains=["gitlab.com"]}test(e){
const t=this.regexps.reduce(((t,n)=>t||e.match(new RegExp(n))),null);if(t&&7==t.length){const[,e,n,r,,,i]=t,s=[e].concat([n,r].filter((e=>e))).join("/");return{id:[s].concat(r==i?[]:i).join("/"),token:"gl",url:"https://gitlab.com/"+s,issue_url:"https://gitlab.com/"+s+"/issues"}}}updates(e){return!0}},new class{constructor(){this.regexps=["https?://bitbucket\\.org/([^/]+)/([^/]+)/raw/[^/]+/(.*)\\.user\\.js","https?://bitbucket\\.org/([^/]+)/([^/]+)/downloads/(.*)\\.user\\.js"],
this.domains=["bitbucket.org"]}test(e){const t=this.regexps.reduce(((t,n)=>t||e.match(new RegExp(n))),null);if(t&&4==t.length){t.shift();const[e,n,r]=t,i=[e,n].concat(n==r?[]:r).join("/"),s=[e,n].join("/");return{id:i,token:"bb",url:"https://bitbucket.org/"+s,issue_url:"https://bitbucket.org/"+s+"/issues"}}}updates(e){return!0}},new class{constructor(){this.regexps=["https?://userstyles\\.org/styles/userjs/([^/]+)/.*\\.user\\.js"],this.domains=["userstyles.org"]}test(e){
const t=this.regexps.reduce(((t,n)=>t||e.match(new RegExp(n))),null);if(t&&2==t.length)return t.shift(),{id:t[0],token:"usty",url:"https://userstyles.org/styles/"+t[0],issue_url:"https://forum.userstyles.org/post/discussion?Discussion/StyleID="+t[0]}}async converts(e){let t,n,r,i;const s=[{tag:"includes",re:/(?: \|\| \(new RegExp\("\^)([^$]+)(?:\$"\)\)\.test\(document\.location\.href\))/g,idx:1,value:e=>"/^"+e.replace(/\\\\(.)/g,"\\$1")+"$/"},{tag:"includes",
re:/(?: \|\| \(document\.location\.href\.indexOf\(")([^"]+)"(?:\) == 0\))/g,idx:1,value:e=>e+"*"},{tag:"matches",re:/(?: \|\| \(document\.domain == ")([^"]+)"(?: \|\| document\.domain\.substring\(document\.domain\.indexOf\("[^"]+"\) \+ 1\) == "[^"]+"\))/g,idx:1,value:e=>"*."+e}],o=new RegExp("(?:if \\(false)("+s.map((e=>"(?:"+e.re.source+")")).join("|")+")+","g")
;return e.textContent&&0===e.includes.length&&0===e.matches.length&&0===e.excludes.length&&e.options&&e.options.override&&e.options.override.orig_includes&&0===e.options.override.orig_includes.length&&e.options.override.orig_matches&&0===e.options.override.orig_matches.length&&(n=e.textContent.match(o))&&(n.forEach((t=>{s.forEach((n=>{for(;r=n.re.exec(t);)if(r.length>n.idx){const t=n.value(r[n.idx]);e.options.override["orig_"+n.tag].push(t),e[n.tag].push(t),i=!0}}))})),i&&(t="includes added")),
0===e.grant.length&&(e.grant.push("GM_addStyle"),t="includes added"),{script:e,info:t}}updates(e){return!0}},new class{constructor(){this.regexps=["https://static\\.iitc\\.me/build/release/(plugins/)?(.*)\\.user\\.js","https://iitc\\.app/build/release/(plugins/)?(.*)\\.user\\.js","https?://socialfixer\\.com/socialfixer\\.user\\.js","https?://www\\.fbpurity\\.com/.*\\.user\\.js"],this.domains=["static.iitc.me","iitc.app","socialfixer.com","fbpurity.com"]}test(e){
const[t,n,r,i]=this.regexps,s=e.match(new RegExp(t));if(s&&3===s.length){s.shift();const[e,t]=s,n="iitc-project/ingress-intel-total-conversion";return{id:[n].concat(e?[e,t]:[]).join("/").replace(/\/\//g,"/"),token:"gh",url:"https://github.com/"+n,issue_url:"https://github.com/"+n+"/issues"}}const o=e.match(new RegExp(n));if(o&&3===o.length){o.shift();const[e,t]=o,n="IITC-CE/ingress-intel-total-conversion";return{id:[n].concat(e?[e,t]:[]).join("/").replace(/\/\//g,"/"),token:"gh",
url:"https://github.com/"+n,issue_url:"https://github.com/"+n+"/issues"}}return e.match(new RegExp(r))?{id:"socialfixer",token:"web",url:"http://socialfixer.com",issue_url:"https://www.facebook.com/groups/SocialFixerUsersSupport/"}:e.match(new RegExp(i))?{id:"fbpurity",token:"web",url:"https://www.fbpurity.com/",issue_url:"https://www.facebook.com/fluffbustingpurity"}:void 0}updates(e){return!0}},function(e){e[e.OBJ_URL=1]="OBJ_URL",e[e.BLOB=2]="BLOB",e[e.DATA_URI=4]="DATA_URI",
e[e.BINARY=8]="BINARY"}(Jr||(Jr={}));const Kr=0+Jr.BINARY,$r=e=>void 0!==e.objUrl,ei=e=>void 0!==e.blob,ti=e=>void 0!==e.dataUri,ni=e=>void 0!==e.binary;class ri{constructor(e,t){if($r(e))this.objUrl=e.objUrl.url,this.type=e.objUrl.type;else if(ei(e))this.blob=e.blob;else if(ti(e))this.dataUri=e.dataUri;else{if(!ni(e))throw new Error("incompatible TransferableData");this.binary=e.binary}this.kinds=t||Kr}dispose(){this.objUrl&&URL.revokeObjectURL(this.objUrl)}async toTransferableData(){
if(this.kinds&Jr.OBJ_URL){if(!this.objUrl&&(!this.blob&&this.dataUri&&(this.blob=le(this.dataUri)),this.blob&&(this.objUrl=URL.createObjectURL(this.blob)),!this.objUrl))throw new Error("incomplete Transferable");return{objUrl:{url:this.objUrl,type:this.type}}}if(this.kinds&Jr.BLOB){if(!this.blob&&(this.objUrl?this.blob=await k(this.objUrl).then((e=>e.blob())):this.dataUri&&(this.blob=le(this.dataUri)),!this.blob))throw new Error("incomplete Transferable");return{blob:this.blob}}
if(this.kinds&Jr.DATA_URI){if(!this.dataUri){let e=this.blob;if(!e&&this.objUrl&&(e=await k(this.objUrl).then((e=>e.blob()))),e&&(this.dataUri=await ce(e)),!this.dataUri)throw new Error("incomplete Transferable")}return{dataUri:this.dataUri}}if(this.kinds&Jr.BINARY){if(!this.binary){let e=this.blob;if(!e&&this.objUrl&&(e=await k(this.objUrl).then((e=>e.blob()))),e&&(this.binary=await de(e)),!this.binary)throw new Error("incomplete Transferable")}return{binary:this.binary,
type:"application/octet-stream"}}throw new Error("incompatible Transferable")}get tryObjectUrl(){return this.objUrl}get tryBlob(){return this.blob}get tryDataUri(){return this.dataUri}get tryBinary(){return this.binary}async toBlob(){if(this.blob)return this.blob;if(!this.objUrl){if(this.dataUri)return le(this.dataUri);if(this.binary)return new Blob([se(this.binary)],{type:"application/octet-stream"});throw new Error("incompatible Transferable")}try{return await(await k(this.objUrl)).blob()
}catch(e){return}}async toDataUri(){if(this.dataUri)return this.dataUri;{const e=await this.toBlob();if(!e)throw new Error("incompatible Transferable");return await ce(e)}}static fromTransferableData(e){return e&&(ei(e)||$r(e)||ti(e)||ni(e))?new ri(e):void 0}}const ii={};We.on("xhr",(function(e){let t,n;void 0!==(t=e.id)&&void 0!==(n=ii[t])&&n.handle(e)&&delete ii[t]}));class si{constructor(e){this.readyState=si.UNSENT,this.status=0,this.statusText="",this.responseType="",this.async=!0,
this.timeout=-1,this.canceled=!1,this.loaded=0,this.headers={},this.onreadystatechange=null,this.onopen=null,this.onload=null,this.onloadstart=null,this.onprogress=null,this.onabort=null,this.onerror=null,this.ontimeout=null,this.upload={onprogress:null,loaded:0,total:void 0},this.open=function(e,t,n=!0,r,i){if(this.method=e,this.url=t,this.async=n,this.user=r,this.password=i,this.responseURL=t,!this.async)throw new Error("Synchronous requests are not supported");this.onopen&&this.onopen(this)},
this.send=async function(e){this.mime_type&&(this.request_headers||(this.request_headers={}),this.request_headers["content-type"]||(this.request_headers["content-type"]=this.mime_type)),(this.user||this.password)&&(this.request_headers||(this.request_headers={}),this.request_headers.Authorization="Basic "+d((this.user||"")+":"+(this.password||"")));const t=this.method||"GET",n=this.url;if(!n)throw new Error("No URL specified");const r={method:t,url:n,headers:this.request_headers,
timeout:this.timeout>0?this.timeout:void 0,anonymous:"withCredentials"in this?!this.withCredentials:this.options&&!!this.options.anonymous},i=e=>{this.method&&"POST"==this.method.toUpperCase()&&(this.request_headers||(this.request_headers={}),this.request_headers["content-type"]||(this.request_headers["content-type"]=e))};if(e){let t,n="";if(e instanceof Blob){t=e.size;const s=await de(e);""===s?(this.readyState=0,this.statusText="data read error",this.onerror&&this.onerror(this)):(r.data=ne(s),
i(n))}else t=e.length,n="text/plain",r.data=ne(ee(e));i(n),this.upload&&(this.upload.loaded=0,this.upload.total=t)}(async e=>{await Ye.acquire();try{let t;const n=await Promise.race([new Promise(((n,r)=>{t=b((()=>{t=void 0,r(`timeout: ${e.method}`)}),15e3)})),be.runtime.sendNativeMessage("application",e)]);return void 0!==t&&u(t),n}catch(e){throw console.error("sendNativeMessage",e),e}finally{Ye.release()}})({method:"xhr",id:this.id,details:r}),
e&&this.upload&&this.upload.onprogress&&this.upload.onprogress.apply(this,[this.upload])},this.abort=function(){this.canceled=!0,delete ii[this.id],this.onabort&&this.onabort(this)},this.overrideMimeType=function(e){this.mime_type=e},this.getAllResponseHeaders=function(){const e=this.headers;return Object.keys(e).map((function(t){const n=e[t];return n&&n.includes(",")?n.split(",").map((function(e){return t+":"+e.trimStart()})).join("\r\n"):t+":"+n})).join("\r\n")},
this.getResponseHeader=function(e){return this.headers[e]},this.setRequestHeader=function(e,t){null!==t&&(this.request_headers||(this.request_headers={}),this.request_headers[e.trim().toLowerCase()]=t.trim())},this.toString=function(){return"[object XMLHttpRequest]"},this.handle=function(e){const t=e.data,n=()=>{this.onprogress&&this.onprogress(this)};if(1==t.readyState&&"loaded"in t)this.upload&&this.upload.onprogress&&(this.upload.loaded=t.loaded||0,this.upload.total=t.total,
this.upload.onprogress.apply(this,[this.upload]));else if(t.readyState!=this.readyState){if(t.timeout)return void(this.ontimeout&&this.ontimeout.apply(this,[this]));if(Object.assign(this,t),this.onreadystatechange&&this.onreadystatechange(this),4==t.readyState){const e=t.status;return e>=400?this.onload&&this.onload.apply(this,[this]):e>=300||(e>=200?this.onload&&this.onload.apply(this,[this]):e>=100||0===e&&this.onerror&&this.onerror.apply(this,[this])),!0}
t.readyState>1?n():1==t.readyState&&this.onloadstart&&this.onloadstart.apply(this,[this])}else 3==t.readyState&&(Object.assign(this,t),n())},this.id=Date.now()+(Math.random()+1).toString(36).substring(7),this.options=e,ii[this.id]=this}get lengthComputable(){return null!=this.total}get target(){return this}get response(){const e=ai(this.responseData);if(null==e)return null
;const t=this.responseType?this.responseType.toLowerCase():"",n=(this.headers?this.headers["content-type"]:null)||"binary/octet-stream";return oi(e,t,n)}get responseText(){const e=ai(this.responseData);return null==e?null:oi(e,this.mime_type?"binary":"text")}get responseXML(){const e=ai(this.responseData);if(null==e)return null;const t=(this.headers?this.headers["content-type"]:null)||"";return oi(e,"document",t.split(";")[0])}}si.UNSENT=0,si.OPENED=1,si.HEADERS_RECEIVED=2,si.LOADING=3,
si.DONE=4,si.toString=function(){return"[XMLHttpRequest]"};const oi=function(e,t,n){let r;if("arraybuffer"==t)r=se(e);else if("blob"==t)r=new Blob([se(e)],{type:n});else if("json"==t)r=JSON.parse(te(e));else{if("document"==t)return"text/xml"==n&&C?(new C).parseFromString(e,n):null;if("text"==t||n&&(n.match(/charset=(utf[-_]?8)/i)||"text/html"==n))try{r=te(e)}catch(t){r=e}else r=e}return r},ai=e=>{try{if(void 0===e)return;return re(e)}catch(e){return void console.warn("XmlHttpRequest:",e)}}
;var li;!function(e){e.TESTING="TESTING",e.AUDIO_PLAYBACK="AUDIO_PLAYBACK",e.IFRAME_SCRIPTING="IFRAME_SCRIPTING",e.DOM_SCRAPING="DOM_SCRAPING",e.BLOBS="BLOBS",e.DOM_PARSER="DOM_PARSER",e.USER_MEDIA="USER_MEDIA",e.DISPLAY_MEDIA="DISPLAY_MEDIA",e.WEB_RTC="WEB_RTC",e.CLIPBOARD="CLIPBOARD",e.LOCAL_STORAGE="LOCAL_STORAGE",e.WORKERS="WORKERS",e.BATTERY_STATUS="BATTERY_STATUS",e.MATCH_MEDIA="MATCH_MEDIA",e.GEOLOCATION="GEOLOCATION"}(li||(li={}));let ci={},di=()=>s.resolve({done:()=>{}})
;const Ai=["internal","user-agent","accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","date","dnt","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],ui={"cache-control":"no-cache",pragma:"no-cache"},hi={"cache-control":"max-age=0, must-revalidate"},pi=e=>{const t={},n={};return Object.keys(e).forEach((r=>{let i=r,s=e[r];var o;o=r.toLowerCase(),
Ai.includes(o)||0===o.indexOf("proxy-")||0===o.indexOf("sec-")?n[i]=s:t[i]=s})),{...Object.keys(n).length?{forbidden:n}:{},...Object.keys(t).length?{regular:t}:{}}},fi=e=>({responseText:"",response:null,readyState:4,responseHeaders:"",status:0,statusText:"",error:e=e||"Forbidden"}),mi=e=>e&&"object"==typeof e&&"type"in e&&"value"in e,gi=e=>{if("Blob"===e.type)return new Blob([se(e.value)]);if("File"===e.type){if(!e.name)return;return new File([se(e.value)],e.name,{type:e.meta,
lastModified:e.lastModified||Date.now()})}if("FormData"==e.type){const t=new FormData;return Object.keys(e.value).forEach((n=>{const r="Array"===e.value[n].type,i=gi(e.value[n]),s=r?i:[i];s.forEach(((e,r)=>{t.append(n,s[r])}))})),t}if("URLSearchParams"===e.type)return new URLSearchParams(e.value);if("Array"===e.type||"Object"===e.type){let t,n,r;"Object"===e.type?(r=Object.keys(e.value),n=e=>e<r.length?r[e]:null,t={}):(n=t=>t<e.value.length?t:null,t=[])
;for(let r,i=0;null!==(r=n(i));i++)t[r]=gi(e.value[r]);return t}return e.value},_i=e=>{const t={};return e&&e.split("\n").forEach((e=>{const n=e.match(/^([^:]+): ?(.*)/);if(n){const e=n[1].toLowerCase();t[e]=(void 0!==t[e]?t[e]+",":"")+(n[2]||"").replace(/,/g,"%2C")}})),t},bi=(e,t,n)=>{const r={...t||{}},i=async(e,t)=>{const n=r[e];n&&n("function"==typeof t?await t():t)},s=async(e,t)=>{r[e]&&(await i(e,t),r[e]=void 0)};if(!(n=n||{}).internal&&(o=e.url,
!["https:","http:","data:","blob"].some((e=>o.startsWith(e))))){$.warn("xhr: invalid scheme of url:",e.url);const t=fi("Invalid scheme");return s("onerror",t),void s("ondone",t)}var o;const a=void 0!==e.responseType?e.responseType.toLowerCase():void 0,l=e.redirect&&"follow"!==e.redirect,c=!ci.mozAnon&&e.anonymous||"stream"==a||l||!e.partialSize&&a||"typified"===e.data_type,d=e.fetch||!1;return c||d?yi(e,r,n,e.retries||0,s,i):wi(e,r,n,e.retries||0,s,i)
},vi="tm-finalurl"+Se.short_id.toLowerCase(),ki="tm-setcookie"+Se.short_id.toLowerCase(),yi=(e,t,n,r,i,s)=>{const a=C,c=!e.redirect||"follow"===e.redirect;let A,h,p,f;const m=(t,n,r)=>{const i=[];let s;const o=r||t.headers;if(o){const n=[],r=[];o.forEach(((e,t)=>{const o=t.toLowerCase();o.startsWith(ki)?n.push(e):"set-cookie"===o?r.push(e):o===vi?s=e:i.push(o+":"+e)})),s=c?s||t.url:e.url,(n.length?n:r).forEach((e=>i.push("set-cookie:"+e)))}const a=void 0===n?4:n,l=t.status||0,d=t.statusText||""
;return{readyState:a,responseHeaders:i.join("\r\n"),finalUrl:s,status:l,statusText:d}},g=e=>{let t;f&&f(),h||(p?(t=m({status:408,statusText:"Request Timeout"}),i("ontimeout")):"AbortError"==(null==e?void 0:e.name)?(t=fi("aborted"),i("onabort",t)):(t=m({status:408,statusText:(null==e?void 0:e.message)||"Request Timeout"}),i("onerror",t)),h=!0,i("ondone",t))},_=e.responseType?e.responseType.toLowerCase():"";if("document"==_&&!a)return void g({name:"Error",message:"response type not supported"})
;const v=async(e,t,r)=>{let i;if(!r){const t=e;let r;n.no_blob,r=Jr.BINARY;const i=new ri({blob:t},r);return s("onpartial",{tfd:await i.toTransferableData()}),void b((()=>i.dispose()),3e5)}i=e;const o=H(i,t);o.forEach(((e,t)=>{s("onpartial",{partial:e,index:t,length:o.length})}))};let y=!1;const w=e=>{e&&(p=!0),R?R.abort():p?g():g({name:"AbortError",message:"Aborted by user"})};let R;return(async()=>{try{const x={};x.method=e.method||"GET";const E=mn(e.url);let G,Z;if(E){let t=!1
;E.username&&(e.user=e.user||E.username,t=!0),E.password&&(e.password=e.password||E.password,t=!0),G=t?(p=E,((C=C||{}).noprotocol?"":["about:"].includes(p.protocol)?p.protocol:p.protocol+"//")+(C.auth&&(p.username||p.password)?((e,t)=>{const n=e=>e.replace(/[^A-Za-z0-9-._~!$&'()*+,;=]/g,(e=>"%"+e.charCodeAt(0).toString(16).toUpperCase()));return n(e)+(t?":"+n(t):"")+"@"
})(p.username||"",p.password)+"@":"")+p.hostname+(!C.noport&&p.port&&p.port>0?":"+p.port:"")+(C.nopath?"":C.nofile?p.pathname.replace(/\/[^/]*$/,"/"):p.pathname+(C.nosearch?"":p.search))):e.url}else G=e.url;if(e.headers){const{forbidden:t,regular:n}=pi(e.headers);Z=n,t&&$.warn("xhr: setting forbidden headers in non-bg environment is not supported!",t)}if(c||(x.redirect=e.redirect,$.warn("xhr: setting redirect in non-bg environment is not supported!",e.redirect)),!f){
const{done:e}=await di(G,x.method);f=()=>{e(),f=void 0}}e.nocache?x.cache="reload":e.revalidate&&(x.cache="default",Z=Z||{},Z={...Z,...hi}),e.anonymous?x.credentials="omit":x.credentials="include",e.user&&e.password&&(Z=Z||{},Z.Authorization="Basic "+d(e.user+":"+e.password)),Z&&(x.headers=new Headers(Z)),void 0!==e.data&&null!==e.data&&(mi(e.data)&&"typified"===e.data_type?x.body=gi(e.data):"string"==typeof e.data?x.body=e.data:x.body=JSON.stringify(e.data)),R=o?new o:void 0,
R&&(x.signal=R.signal),i("onloadstart",m({status:0,statusText:""},1)),k(G,x).then((async o=>{var c;let d;if(A&&(u(A),A=null),f&&f(),h)return;let p=m(o,void 0,d);if(p.status>0&&(p.status<200||p.status>=399)&&r>0)return void yi(e,t,n,r-1,i,s);const{partialSize:g,overrideMimeType:b,responseType:k}=e;if(o.ok)if(s("onreadystatechange",m(o,2,d)),"stream"==_){let t;if(t=null===(c=o.body)||void 0===c?void 0:c.getReader())for(;;){const{done:n,value:r}=await t.read();if(r){const t=new Blob([r])
;await v(t,parseInt(e.partialSize),!1)}if(n)break}}else if(g){let t;["arraybuffer","blob"].includes(_)||void 0!==b?(p.response=await o.blob(),t=!1):(p.response=await o.text(),t=!0),p=await(async(t,n)=>{if(e.partialSize){const r=t.response;["response","responseText","responseXML"].forEach((e=>{delete t[e]})),!y&&r&&(y=!0,await v(r,parseInt(e.partialSize),n))}return t})(p,t)}else if(void 0!==k){let e
;if("arraybuffer"==_)p.response=await o.arrayBuffer();else if("blob"==_)p.response=await o.blob();else if("document"==_){e=(_i(p.responseHeaders)["content-type"]||"text/xml").toString().split(";")[0];const t=new a;p.response=t.parseFromString(await o.text(),e)}else if("json"==_){const e=await o.text();p.response=JSON.parse(e)}else $.warn("xhr: responseType",_," is not implemented!"),p.responseText=p.response=await o.text()}else if(void 0!==b&&l){
const e=await o.arrayBuffer(),t=(b.toLowerCase().match(/charset=([^;]+)/)||[])[1];p.responseText=p.response=new l(t).decode(e)}else{const e=await o.text();p.responseText=p.response=e}else if(p.responseXML=void 0,"follow"!==e.redirect&&"opaqueredirect"==o.type){const e=m(o,2,d);e.responseText=e.response=p.responseText=p.response=void 0,e.status=p.status=301,s("onreadystatechange",e)}else{s("onreadystatechange",m(o,2,d));try{if("stream"==_){const t=await o.text(),n=new Blob([t])
;await v(n,parseInt(e.partialSize),!1)}else p.responseText=p.response=await o.text()}catch(e){}}i("onreadystatechange",p),i("onload",p),i("ondone",p)})).catch((async e=>{g(e)})),void 0!==e.timeout&&null!==e.data&&(A=b((()=>{A=null,w(!0)}),e.timeout))}catch(e){f&&f();const t=e;$.error(t.message,t.stack);const n=fi(t.message);i("onerror",n),i("ondone",n)}var p,C})(),{abort:()=>w()}},wi=(e,t,n,r,i,s)=>{const o=e.responseType?e.responseType.toLowerCase():"";let a,l;e.anonymous&&(a=ci.mozAnon?{
mozAnon:!0}:{anonymous:!0});const c=M;if(!c)throw new Error("internal error: XMLHttpRequest not available!");const d=new c(a),A=t=>{let n="",r=e.url;if(d.readyState>=2){let e;n=d.getAllResponseHeaders(),n&&(n=n.replace(/tm-finalurl[0-9a-zA-Z]*: .*[\r\n]{1,2}/i,""),n=n.replace(/tm-setcookie[0-9a-zA-Z]+-[0-9]+:/i,"set-cookie:")),(e=d.getResponseHeader(vi)||d.responseURL)&&(r=e)}const i={readyState:d.readyState,responseHeaders:n,finalUrl:r,status:d.readyState>=2?d.status:0,
statusText:d.readyState>=2?d.statusText:""};return t&&4==d.readyState?d.responseType?(i.responseXML=void 0,i.responseText=void 0,i.responseType=d.responseType,i.response=d.response):(i.responseXML=d.responseXML||void 0,i.responseText=d.responseText,i.response=d.response):(i.responseXML=void 0,i.responseText="",i.response=void 0),i};let u=!1;const h=async t=>{if(e.partialSize){const r=t.response,i=!["arraybuffer","blob"].includes(o);["response","responseText","responseXML"].forEach((e=>{
delete t[e]})),!u&&r&&(u=!0,await(async(e,t,r)=>{let i;if(!r){const t=e;let r;n.no_blob,r=Jr.BINARY;const i=new ri({blob:t},r);return s("onpartial",{tfd:await i.toTransferableData()}),void b((()=>i.dispose()),3e5)}i=e;const o=H(i,t);o.forEach(((e,t)=>{s("onpartial",{partial:e,index:t,length:o.length})}))})(r,parseInt(e.partialSize),i))}return t},p=qe({threads:1}),f=e=>t=>p.add((()=>e(t)));return(async()=>{e.method=e.method||"GET";const{done:a}=await di(e.url,e.method),c=async e=>{
await i("ondone",e),a()},u={onload:f((async()=>{let o=A(!0);o.status>0&&(o.status<200||o.status>=300)&&r>0?wi(e,t,n,r-1,i,s):(e.partialSize&&(o=await h(o)),await i("onload",o),4==o.readyState&&await c(o))})),onerror:f((async()=>{const o=A();4==o.readyState&&200!=o.status&&0!=o.status&&r>0?wi(e,t,n,r-1,i,s):(await i("onerror",o),await c(o))})),onloadstart:f((async()=>{await s("onloadstart",(()=>A()))})),onreadystatechange:f((async()=>{await s("onreadystatechange",(async()=>{let e=A()
;return e=await h(e),e}))})),onprogress:f((async e=>{await s("onprogress",(async()=>{let t=A();return t=await h(t),g(e,t)}))})),ontimeout:f((async()=>{const e=A();await i("ontimeout"),await c(e)})),onabort:f((async()=>{const e=fi("aborted");await i("onabort"),await c(e)}))},p={onuploadprogress:f((async e=>{await s("onuploadprogress",(async()=>g(e)))}))},m=0==Object.keys(u).concat(["ondone"]).filter((e=>!!t[e])).length;if(m)throw new Error("Synchronous XHR is not supported anymore")
;const g=(e,t)=>{let n,r,i,s,o,a;try{if(e.lengthComputable||e.total>0)n=e.loaded,r=e.total;else if(t){const i=!d.responseType||["","text"].includes(d.responseType)?d.responseText:null;let s=Number(_i(t.responseHeaders)["content-length"]||"");const o=t.readyState>2&&i?i.length:0;0==s&&(s=-1),n=e.loaded||o,r=e.total||s}s=e.lengthComputable,i=n,o=n,a=r}catch(e){}return Object.assign(t||{},{lengthComputable:s,loaded:i,done:n,position:o,total:r,totalSize:a})
},_=["ontimeout","onload","onerror","onabort"];Object.keys(u).forEach((e=>{(t[e]||_.includes(e))&&(d[e]=u[e])}));const b={onuploadprogress:"onprogress"};d.upload&&Object.keys(p).forEach((e=>{const n=b[e];n&&t[e]&&(d.upload[n]=p[e])}));try{let t;if(d.open(e.method,e.url,!m,e.user,e.password),e.headers){const{forbidden:n,regular:r}=pi(e.headers);t={...r,...n}}(e.nocache||e.revalidate)&&(t=t||{},e.nocache?t={...t,...ui}:e.revalidate&&(t={...t,...hi})),t&&Object.keys(t).forEach((e=>{try{
d.setRequestHeader(e,t[e])}catch(n){$.warn("xhr: rejected header",e,t[e])}})),void 0!==e.overrideMimeType&&d.overrideMimeType(e.overrideMimeType),e.partialSize?["arraybuffer","blob"].includes(o)?d.responseType=e.responseType="blob":delete e.responseType:void 0!==e.responseType&&(l=e.responseType.toLowerCase(),"xml"!=l&&"headers"!=l&&"stream"!=l&&(d.responseType=l)),void 0!==e.timeout&&(d.timeout=e.timeout),
void 0!==e.data?mi(e.data)&&"typified"===e.data_type?d.send(gi(e.data)):"string"==typeof e.data?d.send(e.data):d.send(JSON.stringify(e.data)):d.send()}catch(e){const t=e;$.error(t.stack);const n=fi(t.message);i("onerror",n),await c(n)}})(),{abort:function(){d.abort()}}};var Ri={},Ci=Ri.util=Ri.util||{};Ci.isArrayBuffer=function(e){return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer},Ci.isArrayBufferView=function(e){return e&&Ci.isArrayBuffer(e.buffer)&&void 0!==e.byteLength},
Ci.ByteBuffer=n,Ci.ByteStringBuffer=n,Ci.ByteStringBuffer.prototype._optimizeConstructedString=function(e){this._constructedStringLength+=e,this._constructedStringLength>4096&&(this.data.substr(0,1),this._constructedStringLength=0)},Ci.ByteStringBuffer.prototype.length=function(){return this.data.length-this.read},Ci.ByteStringBuffer.prototype.isEmpty=function(){return this.length()<=0},Ci.ByteStringBuffer.prototype.putByte=function(e){return this.putBytes(String.fromCharCode(e))},
Ci.ByteStringBuffer.prototype.fillWithByte=function(e,t){e=String.fromCharCode(e);for(var n=this.data;t>0;)1&t&&(n+=e),(t>>>=1)>0&&(e+=e);return this.data=n,this._optimizeConstructedString(t),this},Ci.ByteStringBuffer.prototype.putBytes=function(e){return this.data+=e,this._optimizeConstructedString(e.length),this},Ci.ByteStringBuffer.prototype.putString=function(e){return this.putBytes(Ci.encodeUtf8(e))},Ci.ByteStringBuffer.prototype.putInt16=function(e){
return this.putBytes(String.fromCharCode(e>>8&255)+String.fromCharCode(255&e))},Ci.ByteStringBuffer.prototype.putInt24=function(e){return this.putBytes(String.fromCharCode(e>>16&255)+String.fromCharCode(e>>8&255)+String.fromCharCode(255&e))},Ci.ByteStringBuffer.prototype.putInt32=function(e){return this.putBytes(String.fromCharCode(e>>24&255)+String.fromCharCode(e>>16&255)+String.fromCharCode(e>>8&255)+String.fromCharCode(255&e))},Ci.ByteStringBuffer.prototype.putInt16Le=function(e){
return this.putBytes(String.fromCharCode(255&e)+String.fromCharCode(e>>8&255))},Ci.ByteStringBuffer.prototype.putInt24Le=function(e){return this.putBytes(String.fromCharCode(255&e)+String.fromCharCode(e>>8&255)+String.fromCharCode(e>>16&255))},Ci.ByteStringBuffer.prototype.putInt32Le=function(e){return this.putBytes(String.fromCharCode(255&e)+String.fromCharCode(e>>8&255)+String.fromCharCode(e>>16&255)+String.fromCharCode(e>>24&255))},Ci.ByteStringBuffer.prototype.putInt=function(e,t){var n=""
;do{t-=8,n+=String.fromCharCode(e>>t&255)}while(t>0);return this.putBytes(n)},Ci.ByteStringBuffer.prototype.putSignedInt=function(e,t){return e<0&&(e+=2<<t-1),this.putInt(e,t)},Ci.ByteStringBuffer.prototype.putBuffer=function(e){return this.putBytes(e.getBytes())},Ci.ByteStringBuffer.prototype.getByte=function(){return this.data.charCodeAt(this.read++)},Ci.ByteStringBuffer.prototype.getInt16=function(){var e=this.data.charCodeAt(this.read)<<8^this.data.charCodeAt(this.read+1)
;return this.read+=2,e},Ci.ByteStringBuffer.prototype.getInt24=function(){var e=this.data.charCodeAt(this.read)<<16^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2);return this.read+=3,e},Ci.ByteStringBuffer.prototype.getInt32=function(){var e=this.data.charCodeAt(this.read)<<24^this.data.charCodeAt(this.read+1)<<16^this.data.charCodeAt(this.read+2)<<8^this.data.charCodeAt(this.read+3);return this.read+=4,e},Ci.ByteStringBuffer.prototype.getInt16Le=function(){
var e=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8;return this.read+=2,e},Ci.ByteStringBuffer.prototype.getInt24Le=function(){var e=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16;return this.read+=3,e},Ci.ByteStringBuffer.prototype.getInt32Le=function(){var e=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16^this.data.charCodeAt(this.read+3)<<24
;return this.read+=4,e},Ci.ByteStringBuffer.prototype.getInt=function(e){var t=0;do{t=(t<<8)+this.data.charCodeAt(this.read++),e-=8}while(e>0);return t},Ci.ByteStringBuffer.prototype.getSignedInt=function(e){var t=this.getInt(e),n=2<<e-2;return t>=n&&(t-=n<<1),t},Ci.ByteStringBuffer.prototype.getBytes=function(e){var t;return e?(e=Math.min(this.length(),e),t=this.data.slice(this.read,this.read+e),this.read+=e):0===e?t="":(t=0===this.read?this.data:this.data.slice(this.read),this.clear()),t},
Ci.ByteStringBuffer.prototype.bytes=function(e){return void 0===e?this.data.slice(this.read):this.data.slice(this.read,this.read+e)},Ci.ByteStringBuffer.prototype.at=function(e){return this.data.charCodeAt(this.read+e)},Ci.ByteStringBuffer.prototype.setAt=function(e,t){return this.data=this.data.substr(0,this.read+e)+String.fromCharCode(t)+this.data.substr(this.read+e+1),this},Ci.ByteStringBuffer.prototype.last=function(){return this.data.charCodeAt(this.data.length-1)},
Ci.ByteStringBuffer.prototype.copy=function(){var e=Ci.createBuffer(this.data);return e.read=this.read,e},Ci.ByteStringBuffer.prototype.compact=function(){return this.read>0&&(this.data=this.data.slice(this.read),this.read=0),this},Ci.ByteStringBuffer.prototype.clear=function(){return this.data="",this.read=0,this},Ci.ByteStringBuffer.prototype.truncate=function(e){var t=Math.max(0,this.length()-e);return this.data=this.data.substr(this.read,t),this.read=0,this},
Ci.ByteStringBuffer.prototype.toHex=function(){for(var e="",t=this.read;t<this.data.length;++t){var n=this.data.charCodeAt(t);n<16&&(e+="0"),e+=n.toString(16)}return e},Ci.ByteStringBuffer.prototype.toString=function(){return Ci.decodeUtf8(this.bytes())},Ci.createBuffer=function(e,t){return t=t||"raw",void 0!==e&&"utf8"===t&&(e=Ci.encodeUtf8(e)),new Ci.ByteBuffer(e)},Ci.fillString=function(e,t){for(var n="";t>0;)1&t&&(n+=e),(t>>>=1)>0&&(e+=e);return n},Ci.encodeUtf8=function(e){
return unescape(encodeURIComponent(e))},Ci.decodeUtf8=function(e){return decodeURIComponent(escape(e))};var xi=Ri.sha256=Ri.sha256||{};Ri.md=Ri.md||{},Ri.md.algorithms=Ri.md.algorithms||{},Ri.md.sha256=Ri.md.algorithms.sha256=xi,xi.create=function(){Gi||(Ei=String.fromCharCode(128),Ei+=Ri.util.fillString(String.fromCharCode(0),64),
Zi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],
Gi=!0);var e=null,t=Ri.util.createBuffer(),n=new Array(64),i={algorithm:"sha256",blockLength:64,digestLength:32,messageLength:0,messageLength64:[0,0],start:function(){return i.messageLength=0,i.messageLength64=[0,0],t=Ri.util.createBuffer(),e={h0:1779033703,h1:3144134277,h2:1013904242,h3:2773480762,h4:1359893119,h5:2600822924,h6:528734635,h7:1541459225},i}};return i.start(),i.update=function(s,o){return"utf8"===o&&(s=Ri.util.encodeUtf8(s)),i.messageLength+=s.length,
i.messageLength64[0]+=s.length/4294967296>>>0,i.messageLength64[1]+=s.length>>>0,t.putBytes(s),r(e,n,t),(t.read>2048||0===t.length())&&t.compact(),i},i.digest=function(){var s=Ri.util.createBuffer();s.putBytes(t.bytes()),s.putBytes(Ei.substr(0,64-(i.messageLength64[1]+8&63))),s.putInt32(i.messageLength64[0]<<3|i.messageLength64[0]>>>28),s.putInt32(i.messageLength64[1]<<3);var o={h0:e.h0,h1:e.h1,h2:e.h2,h3:e.h3,h4:e.h4,h5:e.h5,h6:e.h6,h7:e.h7};r(o,n,s);var a=Ri.util.createBuffer()
;return a.putInt32(o.h0),a.putInt32(o.h1),a.putInt32(o.h2),a.putInt32(o.h3),a.putInt32(o.h4),a.putInt32(o.h5),a.putInt32(o.h6),a.putInt32(o.h7),a},i};var Ei=null,Gi=!1,Zi=null;Ci.hasWideChar=function(e){for(var t=0;t<e.length;t++)if(e.charCodeAt(t)>>>8)return!0;return!1};const Si=function(e,t){var n=Ri.md.sha256.create();return n.update(e,"UTF-8"==t||void 0===t&&Ci.hasWideChar(e)?"utf8":void 0),n.digest().toHex()},Bi=e=>{if(!C)throw new Error("DOMParser not available")
;return(new C).parseFromString(e,"text/xml")},Ii=e=>{var t;let n;return e&&(n=(null===(t=e.firstChild)||void 0===t?void 0:t.nextSibling)?e.firstChild.nextSibling:e.firstChild),n},Ti=(e,t)=>{let n,r;if((n=e.getElementsByTagNameNS("*",t)[0])&&(r=n.firstChild))return r.nodeValue||void 0},Ui=async e=>{if(C){const t=Bi(e);let n;if(!t||!(n=Ii(t))||!n.childNodes)return;return(async e=>{const t=[],n=e.getElementsByTagNameNS("*","response");for(let e=0;e<n.length;e++){const r=n[e];let i=Ti(r,"href")
;if(null==i)continue;i=i.replace(/\/$/,"");const s=r.getElementsByTagNameNS("*","propstat")[0].getElementsByTagNameNS("*","prop")[0],o=Ti(s,"getlastmodified"),a=Ti(s,"getetag"),l=parseInt(Ti(s,"getcontentlength")||"");if(s.getElementsByTagNameNS("*","resourcetype")[0].getElementsByTagNameNS("*","collection")[0]);else{const e={etag:a,name:i,id:i,modifiedTime:new Date(o||0).getTime(),size:l>=0?l:void 0,removed:-1==l};t.push(e)}}return t})(n)}throw new Error("DOMParser not available")
},Fi=async e=>{if(C){const t=Bi(e);let n;if(!t||!(n=Ii(t))||!n.childNodes)return;return(e=>Ti(e,"td:cursor"))(n)}throw new Error("DOMParser not available")};let Mi;const ji=qe({threads:1}),Li=(e,t)=>{const n=(e?e.split("/"):[]).concat(t?[t]:[]).join("/");return n?("/"==n.substr(0,1)?"":"/")+n:""};let Oi=()=>({get:()=>Ie.Breach(),set:()=>Ie.Breach(),delete:()=>Ie.Breach(),clear:()=>Ie.Breach()});const Di=e=>{const t=(e.match(/[\dA-F]{2}/gi)||[]).map((e=>parseInt(e,16)))
;return new Uint8Array(t).buffer},zi=e=>{const t={type:e,request:e=>{const t=()=>{const t=Ie(),n=e=>{$.log("rest service: request failed",e),t.reject(e)},r=Object.assign({},e),i="headers"===r.responseType;return i&&delete r.responseType,bi(r,{onload:e=>{if([200,201,204,207].includes(e.status)){let n;n=i?_i(e.responseHeaders):e.response,t.resolve({result:n})}else n(e)},onerror:n,ontimeout:()=>n("timed out"),onprogress:e=>t.notify(e),onuploadprogress:e=>t.notify(e)},{internal:!0}),t.promise()}
;return e.no_queue?t():ji.add(t)},error:t=>{const n=t;let r;if(void 0!==n.status){r=n.status.toString();try{r=r+" | "+n.responseText}catch(e){}}else r=t;Jn(e,"error","request: "+r)},wait:e=>(...t)=>e(...t).then((e=>e),(e=>(e=>Ie.Breach(e?e.responseText||e.statusText:void 0))(e))),changes:(()=>{let e;return{listen:()=>(e||(e=Ie(),t.watch&&t.watch.start()),e.promise()),notify:t=>{e.notify(t)},stop:()=>(t.watch&&t.watch.stop(),Ie.Pledge())}})()};return t},Pi=e=>{const t=Object.assign({},e),n=t.type
;if(void 0===n)throw new Error("Internal error");let r,i,s=[];const o=e=>{const t={client_id:d.config.client_id,redirect_uri:d.config.redirect_uri,state:i,scope:d.config.scope,refresh_token:e};return d.config.redirect_uri+"?"+gn(t)},a=e=>{let t,n;if(e&&0===e.indexOf(d.config.redirect_uri)&&(n=mn(e))&&("string"!=typeof(r=n)&&(r=r.search?r.search.substring(1):r.hash?r.hash.substring(1):""),t=r.split("&").reduce(((e,t)=>{const n=t.split("=");return e[p(n[0])]=p(n[1]),e
}),{}))&&(t.access_token||t.error)&&t.state===i)return{uid:t.uid,access_token:t.access_token,refresh_token:t.refresh_token,error:t.error};var r};let l;const c={};Object.defineProperty(c,"credentials",{get:()=>(l||(l=Oi(e.type)),l),enumerable:!1});const d=Object.assign(c,e,{...t,config:{...t.config,auth_prefix:"Bearer"},request:e=>Ie((async({resolve:t,reject:n})=>{if(!e.no_auth){const t=(await d.credentials.get()).access_token;if(!t)return void n("Authentication failed");e.headers=e.headers||{},
e.headers.Authorization=d.config.auth_prefix+" "+t}t()})).promise().then((()=>t.request(e))),oauth:{run:()=>{if(r)return r;let e=Ie();const t=r=e.promise();i="!!"+n+"-"+Q();const{redirect_uri:s,refresh_supported:l}=d.config,c=void 0,A=async(t,n)=>{const r=e;e=void 0,await d.credentials.set(n),null==r||r.resolve(),t&&t.close()},u=e=>Ie((({resolve:t})=>{bi({url:e,responseType:"json"},{ondone:e=>t(e)},{internal:!0})})).promise();return d.credentials.get().then((async e=>{const t=e.refresh_token
;if(l&&t){let e,r=1e3,i=4;const s=[200,400,401,403,404,405,406,408,412],a=o(t);for(;!e;)e=await u(a),e&&s.includes(e.status)||!(--i>=0)||(await Pe(r),r=Math.min(3*r,36e5),e=void 0);if(200==e.status){const t=e.response;if(t){if(!t.error&&t.access_token){const{access_token:e,refresh_token:n,basic_auth:r,uid:i}=t;return void A(null,{access_token:e,refresh_token:n,basic_auth:r,uid:i})}Jn(n,"error","auth refresh token use: "+(t.error||"!access_token"))}
}else Jn(n,"error",`auth refresh token use failed with status ${e.status}`)}})).then((()=>{if(!e)return;if(!l)return Ie.Pledge();const t=Ie(),r=Mi({url:o(),filter:c});return r.promise.progress((t=>{let i;e&&(i=a(t.url))&&(i.error||!i.access_token?(Jn(n,"error","auth refresh: "+(i.error||"!access_token")),d.config.refresh_supported=!1):A(r,i))})).always(t.resolve),t.promise()})).then((()=>{if(!e)return;const t=Ie(),r=Mi({url:d.config.request_uri+"?"+gn({response_type:d.config.response_type,
client_id:d.config.client_id,redirect_uri:d.config.redirect_uri,state:i,scope:d.config.scope}),filter:c});return r.promise.progress((t=>{let i;e&&(i=a(t.url))&&(i.error||!i.access_token?Jn(n,"error","auth: "+(i.error||"!access_token")):A(r,i))})).always(t.resolve),t.promise()})).done((()=>{r=void 0,e&&e.reject("auth_failed")})),t},revoke:()=>d.credentials.get().then((e=>{const r=e.access_token;return r?(i=i||"!!"+n+"-"+Q(),t.request({method:"GET",retry_auth:!1,url:`${Vi}/revoke?`+gn({token:r,
state:i})}).then((()=>d.oauth.reset()),(()=>d.oauth.reset()))):Ie.Breach()})),reset:()=>d.credentials.clear()},wait:e=>t.wait(((...t)=>d.credentials.get().then((n=>{if(n.access_token)return e(...t);{const n=Ie();return s.push((()=>n.consume(e(...t)))),d.oauth.run().done((()=>{s.forEach((e=>e())),s=[]})).fail((e=>{n.reject(e)})),n.promise()}}))))});return d},Ni=e=>{let t,n,r,i;const s=Ie();let o,a,l,c,d,A=null,h=null
;const f=Object.assign({},e),m=e=>f.wait(((...t)=>v.init().then((()=>e(...t))))),g=(e,t)=>{const n=t||{};return n.set_current_list&&(o={}),v.request({method:"PROPFIND",url:e,headers:{"Content-Type":"text/xml; charset=UTF-8",Depth:void 0!==n.depth?n.depth:1}}).then((async e=>{const t=e.result;if(null==t)return;const r=await Ui(t);if(void 0===r)return;const i=_(r);if(n.set_current_list){const e=await Fi(t);e&&(a=e),i.forEach((e=>{o[e.id]=e}))}return i
})).then((e=>void 0===e?Ie.Breach("not found"):e))},_=e=>e.map((e=>{const{id:i,...s}=e,o=i.replace(new RegExp("^("+[j(n+t)+"/?",j(r+t)+"/?"].join("|")+")"),"");let a;try{a=p(o)}catch(e){a=i}return{id:o,...s,name:a}})),v=Object.assign(e,{...e,config:{...e.config,watch_interval:36e5,root:void 0,path:void 0},request:e=>(e.fetch=!0,e.anonymous=!0,f.request(e).then((e=>e),(t=>{const n=(null==t?void 0:t.status)||-1;return!t||[429,500].includes(n)?(e.backoff=2*(e.backoff||1e3),
Pe(e.backoff).then((()=>v.request(e)))):404==n?Ie.Pledge(t):(v.error(t),Ie.Breach(t))}))),init:()=>{if(i)return i;t=Li(v.config.root,v.config.path),n=v.config.url||"","/"==n.slice(-1)&&(n=n.slice(0,-1));const e=mn(n);if(!e)return Ie.Breach("invalid url");r=e.pathname,"/"==r.slice(-1)&&(r=r.slice(0,-1));const o=Ie(),a=i=o.promise();return v.request({method:"OPTIONS",url:`${n}/`,responseType:"headers"}).done((e=>{const t=e.result;let n
;t&&(n=t["access-control-allow-methods"])&&n.includes("EDITOR")&&(d=!0)})).always((()=>{g(`${n}${t}/`,{depth:0}).done((()=>{o.resolve(),s.resolve()})).fail((()=>{const e=[];Ie.onebyone(t.split("/").filter((e=>e)).map((t=>{e.push(t);const r=e.join("/");return()=>v.request({method:"MKCOL",url:`${n}/${r}/`,headers:{"Content-Type":"text/xml; charset=UTF-8"}}).then((e=>e),(e=>{if(405==((null==e?void 0:e.status)||-1))return Ie.Pledge(e)}))}))).done((()=>{o.resolve(),s.resolve()})).fail((()=>{i=void 0,
o.reject()}))}))})),a},list:m((e=>g(`${n}${t}/`,{set_current_list:!0}).then((t=>{const n={};return t.map((t=>{if(!e){if(n[t.id])return;n[t.id]=!0}return{name:t.name,id:t.id,size:t.size||0,etag:t.etag,modified:t.modifiedTime,precision:1e3,removed:t.removed}})).filter((e=>e))})))),get:m((e=>{const r=e.id||e;return v.request({method:"GET",url:n+Li(t,r),headers:{"Content-Type":"text/xml; charset=UTF-8"},responseType:"arraybuffer"}).then((e=>{const t=e.result;return new Blob([t])}))})),
put:m(((e,r,i)=>{const s=e.id||e;let o,a,l;const c={"Content-Type":"application/octet-stream"};let d=!1;return i&&i.lastModified&&(d=null===h,o=i.lastModified,l=new Date(i.lastModified).toISOString(),a=i.lastModified/1e3,(h||d)&&(c["X-OC-Mtime"]=a)),v.request({method:"PUT",url:n+Li(t,s),headers:c,data_type:"typified",data:{type:"raw",value:r},responseType:"headers"}).then((e=>{const r=e.result;if(r&&d){let e
;h=!("accepted"!=r["x-oc-mtime"]&&(!r.date||!(e=new Date(r.date).getTime())||e!=o&&e!=Math.floor(a)))}if(!h&&!A&&l){const e=()=>{$.warn("WebDAV: no way to set file modification date! This might cause redundant up and downloads."),A=!0};return v.request({method:"PROPPATCH",url:n+Li(t,s),headers:{"Content-Type":"text/xml; charset=UTF-8"},
data:['<?xml version="1.0"?>','<d:propertyupdate xmlns:d="DAV:">',"<d:set>","<d:prop>",`<d:getlastmodified>${l}</d:getlastmodified>`,"</d:prop>","</d:set>","</d:propertyupdate>"].join("")}).then((async t=>{const n=t.result;if(null===n)return;const r=await(async e=>{if(C){const t=Bi(e);if(!t)return;return(e=>{var t;let n,r,i,s
;if((n=e.childNodes[0])&&(r=n.getElementsByTagNameNS("*","status")[0])&&(i=null===(t=r.firstChild)||void 0===t?void 0:t.nodeValue)&&(s=i.match(/HTTP\/[0-9.]+ ([1-5][0-9][0-9])/)))return[parseInt(s[1])]})(t)}throw new Error("DOMParser not available")})(n);r&&403===r[0]&&e()}),(()=>(e(),Ie.Pledge())))}}))})),delete:m((e=>{const r=e.id||e;return v.request({method:"DELETE",url:n+Li(t,r),headers:{"Content-Type":"text/xml; charset=UTF-8"}})})),watch:{start:()=>{if(l)return;let e=100;const r=l=Q()
;let i=null;const d=()=>{if(c=void 0,l)if(!1===i){const e=o;g(`${n}${t}/`,{set_current_list:!0}).then((()=>{e&&(Object.keys(e).forEach((t=>{const n=o[t],r=e[t];n?r.modifiedTime!=n.modifiedTime&&f.changes.notify({time:n.modifiedTime,name:n.name,id:n.id}):f.changes.notify({time:Date.now(),name:r.name,id:r.id,removed:!0})})),Object.keys(o).forEach((t=>{if(!e[t]){const e=o[t];f.changes.notify({time:e.modifiedTime,name:e.name,id:e.id})}})))})).fail((e=>{$.warn("WebDAV: file changes check failed",e)
})).always((()=>{c=b(d,v.config.watch_interval)}))}else((e,t)=>{const n={"Content-Type":"text/xml; charset=UTF-8",Depth:1,Timeout:90};return t&&(n.Cursor=t),v.request({method:"SUBSCRIBE",url:e,headers:n,no_queue:!0}).then((async e=>{const t=e.result;if(null===t)return Ie.Pledge({changes:[],cursor:a});const n=await Ui(t);return void 0!==n?{changes:_(n),cursor:await Fi(t)}:void 0})).then((e=>void 0===e?Ie.Breach():e))})(`${n}${t}/`,a).then((t=>{i=!0;const n=t.changes;a=t.cursor,e=100,
n.forEach((e=>{f.changes.notify({time:e.modifiedTime,name:e.name,id:e.id,removed:e.removed})}))}),(()=>{if(null!==i)return e*=2,Pe(e);i=!1})).always(d)},A=()=>!l||l!==r;s.promise().then((()=>{A()||m((()=>A()?Ie.Breach():(d(),Ie.Pledge())))()}))},stop:()=>{l=void 0,c&&(u(c),c=void 0)}},getRemoteUrl:e=>{if(d)return n+Li(t,e)+"?method=editor#bypass=true"},getRemoteDomains:()=>{var e;return[((null===(e=mn(n))||void 0===e?void 0:e.origin)||"").replace(/^.*:\/\//,"")]}});return v
},Vi="https://accounts.tampermonkey.net/e/oauth2/v1",Qi={drive:()=>{let e;F&&(e=parseInt(F.getItem("gdrive_poll_interval")||""))||(e=18e5);const t=Pi(zi("drive")),n=Object.assign({},t),r="appDataFolder";let i,s;const o=Object.assign(t,{...t,config:{...t.config,redirect_uri:`${Vi}/auth`,refresh_supported:!0,request_uri:"https://accounts.google.com/o/oauth2/v2/auth",client_id:"************-********************************.apps.googleusercontent.com",
scope:"https://www.googleapis.com/auth/drive.appdata",response_type:"token",watch_interval:e},request:e=>n.request(e).then((e=>e),(t=>{const n=(null==t?void 0:t.status)||-1;return!t||[403,429,500].includes(n)?(e.backoff=2*(e.backoff||1e3),Pe(e.backoff).then((()=>o.request(e)))):[400,401].includes(n)?($.warn("Google Drive: authentication error",t),o.credentials.delete("access_token").then((()=>{if(!e.retry_auth)return e.retry_auth=!0,o.oauth.run().fail((e=>o.error(e))).then((()=>o.request(e)))
}))):404==n?Ie.Pledge(t):(o.error(t),Ie.Breach(t))})),list:n.wait((e=>{let t=[];const n=Ie(),i=e=>"https://www.googleapis.com/drive/v3/files?"+gn({spaces:r,pageToken:e,orderBy:"modifiedTime desc",fields:"nextPageToken, files(id, size, name, modifiedTime, md5Checksum)",pageSize:500}),s=e=>{o.request({method:"GET",url:e,headers:{"Content-Type":"application/json"}}).then((e=>{const r=e.result,o=r?JSON.parse(r):{files:[]};if(t=t.concat(o.files),o.nextPageToken)return s(i(o.nextPageToken))
;n.resolve(t)}))};return s(i()),n.promise().then((t=>{const n={};return t.map((t=>{if(!e){if(n[t.name])return;n[t.name]=!0}return{name:t.name,size:t.size||0,id:t.id,md5:t.md5Checksum,modified:new Date(t.modifiedTime).getTime()}})).filter((e=>e))}))})),get:n.wait((e=>{const t=e.id||e;return o.request({method:"GET",url:"https://www.googleapis.com/drive/v3/files/"+t+"?"+gn({spaces:r,alt:"media"}),responseType:"arraybuffer"}).then((e=>{const t=e.result;return new Blob([t])}))})),
put:n.wait(((e,t,n)=>{const i=e,s=i.name||e,a=i.id,l=Q();return Ie.Pledge().then((()=>{if(t)return de(t)})).then((e=>{const t=n&&n.lastModified?new Date(n.lastModified).toISOString():void 0,i=[];return i.push("--"+l),i.push("Content-Type: application/json"),i.push(""),i.push(JSON.stringify({name:s,parents:a?void 0:[r],modifiedTime:t})),i.push("--"+l),e&&(i.push("Content-Type: application/octet-stream"),i.push("Content-Transfer-Encoding: base64"),i.push(""),i.push(ne(e)),i.push("--"+l+"--")),
i.push(""),o.request({method:a||!e?"PATCH":"POST",url:"https://www.googleapis.com/"+(e?"upload/":"")+"drive/v3/files"+(a?"/"+a:"")+"?"+gn({uploadType:"multipart"}),headers:{"Content-Type":"multipart/related; boundary="+l},data:i.join("\r\n")})}))})),delete:n.wait((e=>{const t=e.id||e;return o.request({method:"DELETE",url:"https://www.googleapis.com/drive/v3/files/"+t+"?"+gn({spaces:r}),headers:{"Content-Type":"application/json"}})})),revoke:()=>o.oauth.revoke(),compare:(e,t)=>{const n=Ie();let r
;return(r=e.md5)&&r==ae(t,"utf-8")?n.resolve(!0):n.resolve(!1),n.promise()},watch:{start:()=>{if(i)return;let e;i=!0;const t=()=>{s=null,i&&o.request({method:"GET",url:"https://www.googleapis.com/drive/v3/changes/?"+gn({pageToken:e,spaces:r,pageSize:1e3,includeRemoved:!0}),headers:{"Content-Type":"application/json"}}).then((t=>{const n=t.result;if(!i)return Ie.Breach();const r=n?JSON.parse(n):{};if(!(e=r.newStartPageToken))return $.warn("Google Drive: watch token error",n),o.watch.stop()
;r.nextPageToken&&$.warn("Google Drive: too much changes",n),(r.changes||[]).forEach((e=>{let t;const n=e.file;"file"===e.type&&n&&(t=Date.parse(e.time),isNaN(t)&&(t=Date.now()),o.changes.notify({id:n.id,time:t,name:n.name,removed:e.removed}))}))})).fail((e=>{$.warn("Google Drive: file changes check failed",e)})).always((()=>{s=b(t,o.config.watch_interval)}))};n.wait((()=>i?o.request({method:"GET",url:"https://www.googleapis.com/drive/v3/changes/startPageToken",headers:{
"Content-Type":"application/json"}}).then((n=>{const r=n.result,i=r?JSON.parse(r):{};if(!(e=i.startPageToken))return $.warn("Google Drive: watch token error",r),o.watch.stop();t()})):Ie.Breach()))()},stop:()=>{i=!1,s&&(u(s),s=null)}},getRemoteUrl:void 0});return o},dropbox:e=>{const t=e.path||"";let n;F&&(n=parseInt(F.getItem("dropbox_poll_interval")||""))||(n=18e5);const r=Pi(zi("dropbox")),i=Object.assign({},r);let s,o,a,l,c=!0;const d=e=>{let n=[];const r=Ie(),i=e=>{A.request({method:"POST",
url:"https://api.dropboxapi.com/2/files/list_folder"+(e?"/continue":""),headers:{"Content-Type":"application/json"},data:{path:e?void 0:Li(t),cursor:e}}).then((e=>{const t=e.result,s=t?JSON.parse(t):{entries:[]};if(n=n.concat(s.entries),s.has_more&&s.cursor)return i(s.cursor);r.resolve({list:n,cursor:s.cursor})})).fail(r.reject)};return c?(c=!1,A.put(".version",new Blob([Hi])).then((()=>{i(e)})).fail(r.reject)):i(e),r.promise()},A=Object.assign(r,{...r,config:{...r.config,
redirect_uri:`${Vi}/auth`,request_uri:"https://www.dropbox.com/oauth2/authorize",client_id:"gq3auc9yym0e21y",response_type:"token",watch_interval:n?Number(n):0},request:e=>i.request(e).then((e=>e),(t=>{const n=(null==t?void 0:t.status)||-1;return!t||[500,429].includes(n)?(e.backoff=2*(e.backoff||1e3),Pe(e.backoff).then((()=>A.request(e)))):[401].includes(n)?($.warn("Dropbox: authentication error",t),A.credentials.delete("access_token").then((()=>{if(!e.retry_auth)return e.retry_auth=!0,
A.oauth.run().fail((e=>A.error(e))).then((()=>A.request(e)))}))):(A.error(t),Ie.Breach(t))})),list:i.wait((e=>d().then((t=>{const n={};return o=t.cursor,t.list.map((t=>{if(!e){if(n[t.name])return;n[t.name]=!0}return{name:t.name,size:t.size,dropbox_hash:t.content_hash,modified:new Date(t.client_modified).getTime(),precision:1e3}})).filter((e=>e))})).always((()=>{s&&o&&(s(),s=null)})))),get:i.wait((e=>{const n=e.name||e;return A.request({method:"POST",
url:"https://content.dropboxapi.com/2/files/download",headers:{"Dropbox-API-Arg":JSON.stringify({path:Li(t,n)})},responseType:"arraybuffer"}).then((e=>{const t=e.result;return new Blob([t])}))})),put:i.wait(((e,n,r)=>{const i=e.name||e,s=r&&r.lastModified?(new Date(r.lastModified).toISOString().match(/[^:]*:[^:]*:[^:.a-zA_Z]*/)||"")[0]+"Z":void 0;return A.request({method:"POST",url:"https://content.dropboxapi.com/2/files/upload",headers:{"Dropbox-API-Arg":JSON.stringify({path:Li(t,i),
client_modified:s,mode:"overwrite"}),"Content-Type":"application/octet-stream"},data_type:"typified",data:{type:"raw",value:n}})})),delete:i.wait((e=>{const n=e.name||e;return A.request({method:"POST",url:"https://api.dropboxapi.com/2/files/delete",headers:{"Content-Type":"application/json"},data:{path:Li(t,n)}})})),revoke:()=>A.credentials.get().then((e=>e.access_token?i.request({method:"POST",url:"https://api.dropboxapi.com/2/auth/token/revoke"}).always((()=>A.oauth.reset())):Ie.Breach())),
compare:(e,t)=>{const n=Ie(),r=4194304,i=se(t,{encoding:"utf-8"}),s=[],o=i.byteLength;let a=1;const l=async()=>{if(0==--a){let t=new ArrayBuffer(0);s.forEach((e=>{e&&(t=((e,t)=>{const n=new Uint8Array(e.byteLength+t.byteLength);return n.set(new Uint8Array(e),0),n.set(new Uint8Array(t),e.byteLength),n.buffer})(t,e))}));const r=await oe(t),i=Si(r,"ASCII");n.resolve(i==e.dropbox_hash)}};for(let e=0,t=0;t<o;t+=r,e++)(e=>{s.push(null),a++,oe(i.slice(t,t+Math.min(r,o-t))).then((t=>{
const n=Si(t,"ASCII");s[e]=Di(n),l()}),(()=>{$.warn("Dropbox: unable to calculate SHA-256 hashes"),n.reject()}))})(e);return l(),n.promise()},watch:{start:()=>{if(a)return;a=!0;let e=0;const t=()=>{if(l=null,e=0,a)return o?void A.request({method:"POST",url:"https://notify.dropboxapi.com/2/files/list_folder/longpoll",headers:{"Content-Type":"application/json"},no_auth:!0,no_queue:!0,data:{cursor:o,timeout:180}}).then((t=>{const n=t.result;if(!a)return Ie.Breach();const r=n?JSON.parse(n):{}
;return r.backoff&&(e=1e3*r.backoff),r.changes?Pe(6e4).then((()=>d(o))).then((e=>(o=e.cursor)?e.list:($.warn("Dropbox: watch token error",n),A.watch.stop()))):null})).then((e=>{e&&e.forEach((e=>{let t;const n=e[".tag"];["file","deleted"].includes(n)&&(t=Date.parse(e.server_modified),A.changes.notify({id:e.id,time:t,name:e.name,removed:"deleted"==n}))}))})).fail((e=>{$.warn("Dropbox: file changes check failed",e)})).always((()=>{l=b(t,e+A.config.watch_interval)
})):($.warn("Dropbox: watch token error",o),A.watch.stop())};i.wait((()=>a?(o?t():s=t,Ie.Pledge()):Ie.Breach()))()},stop:()=>{a=!1,l&&(u(l),l=null)}},getRemoteDomains:()=>["dropbox.com","dropboxapi.com"],getRemoteUrl:void 0});return A},onedrive:e=>{const t=e.path||"";let n,r;n=18e5;const i=Pi(zi("onedrive")),s=Object.assign({},i);let o,a;const l=e=>{const n=Ie();let i=[];const s=o=>{c.request({method:"GET",url:o||"https://api.onedrive.com/v1.0/drive/special/approot:"+Li(t)+":/children",headers:{
"Content-Type":"application/json"}}).then((t=>{const o=t.result,a=o?JSON.parse(o):{value:[]};if(i=i.concat(a.value.map((e=>{var t,n,r;const i=(null===(t=null==e?void 0:e.fileSystemInfo)||void 0===t?void 0:t.lastModifiedDateTime)||0,s=(null===(r=null===(n=null==e?void 0:e.file)||void 0===n?void 0:n.hashes)||void 0===r?void 0:r.sha1Hash)||void 0;return{id:e.id,name:e.name,size:e.size,sha1:s,modified:new Date(i).getTime()}}))),a["@odata.nextLink"])return s(a["@odata.nextLink"])
;e.set_current_list&&(r=i),n.resolve(i)})).fail((e=>n.reject(e)))};return s(),n.promise()},c=Object.assign(i,{...i,config:{...i.config,redirect_uri:`${Vi}/auth`,refresh_supported:!0,request_uri:"https://login.live.com/oauth20_authorize.srf",client_id:"000000004C1A3122",response_type:"token",scope:"onedrive.appfolder",watch_interval:18e5},request:e=>s.request(e).then((e=>e),(t=>{const n=(null==t?void 0:t.status)||-1;return!t||[429,500].includes(n)?(e.backoff=2*(e.backoff||1e3),
Pe(e.backoff).then((()=>c.request(e)))):[401,403].includes(n)?($.warn("OneDrive: authentication error",t),c.credentials.delete("access_token").then((()=>{if(!e.retry_auth)return e.retry_auth=!0,c.oauth.run().fail((e=>c.error(e))).then((()=>c.request(e)))}))):404==n?Ie.Pledge(t):(c.error(t),Ie.Breach(t))})),list:s.wait((()=>l({set_current_list:!0}))),get:s.wait((e=>{const n=e.name||e;return c.request({method:"GET",url:"https://api.onedrive.com/v1.0/drive/special/approot:"+Li(t,f(n))+":/content",
responseType:"arraybuffer"}).then((e=>{const t=e.result;return new Blob([t])}))})),put:s.wait(((e,n,r)=>{const i=e.name||e,s=f(i.replace(/[#%<>:"|?*/\\]/g,"-"));return c.request({method:"PUT",url:"https://api.onedrive.com/v1.0/drive/special/approot:"+Li(t,s)+":/content",headers:{"Content-Type":"application/octet-stream"},data_type:"typified",data:{type:"raw",value:n}}).then((e=>{const t=r&&r.lastModified?new Date(r.lastModified).toISOString():void 0;if(!t)return e
;const n=e.result,i=JSON.parse(n);return c.request({method:"PATCH",url:"https://api.onedrive.com/v1.0/drive/items/"+i.id,headers:{"Content-Type":"application/json"},data:JSON.stringify({fileSystemInfo:{lastModifiedDateTime:t}})})}))})),delete:s.wait((e=>{const n=e.name||e;return c.request({method:"DELETE",url:"https://api.onedrive.com/v1.0/drive/special/approot:"+Li(t,f(n))})})),revoke:()=>c.oauth.revoke(),watch:{start:()=>{if(o)return;o=!0;const e=()=>{if(a=null,!o)return;const t=r;l({
set_current_list:!0}).then((()=>{if(!t)return;const e={},n={};r.forEach((t=>{e[t.id]=t})),t.forEach((e=>{n[e.id]=e})),Object.keys(n).forEach((t=>{const r=e[t],i=n[t];r?i.modified!=r.modified&&c.changes.notify({time:r.modified,name:r.name}):c.changes.notify({time:Date.now(),name:i.name,removed:!0})})),Object.keys(e).forEach((t=>{if(!n[t]){const n=e[t];c.changes.notify({time:n.modified,name:n.name})}}))})).fail((e=>{$.warn("OneDrive: file changes check failed",e)})).always((()=>{
a=b(e,c.config.watch_interval)}))};s.wait((()=>o?(e(),Ie.Pledge()):Ie.Breach()))()},stop:()=>{o=!1,a&&(u(a),a=null)}},getRemoteDomains:()=>["onedrive.live.com"],getRemoteUrl:void 0});return c},yandex:()=>{let e;e=18e5;const t=Pi(zi("yandex")),n=Ni(t),r=Object.assign({},n);let i;const s=Object.assign(n,{...t,...n,config:{...n.config,root:"/Programs/Tampermonkey",url:"https://webdav.yandex.ru",redirect_uri:`${Vi}/auth`,refresh_supported:!0,request_uri:"https://oauth.yandex.com/authorize",
client_id:"a591fcd2ccd248f0baa84222898065f4",response_type:"token",auth_prefix:"OAuth",watch_interval:18e5},init:()=>{if(i)return i;const e=Ie();return i=e.promise(),s.request({method:"GET",url:"https://cloud-api.yandex.net/v1/disk/"}).done((e=>{const t=e.result,n=t?JSON.parse(t):{}
;n.total_space&&n.used_space&&(n.used_space+5e7>n.total_space?$.warn("Yandex: low disk space warning, only "+(n.total_space-n.used_space)+" bytes available"):$.log("Yandex: "+(n.total_space-n.used_space)+" bytes on disk available!"))})).always((()=>{e.consume(r.init())})),i},getRemoteDomains:()=>["disk.yandex.com"],getRemoteUrl:void 0,list:e=>r.list(e).then((e=>e.map((e=>(e.md5=e.etag,e))))),request:e=>(()=>{const t=e.headers=e.headers||{};if(t["X-Requested-With"]="XMLHttpRequest",
"PUT"==e.method&&mi(e.data)&&"raw"==e.data.type&&e.data.value){const n=Ie();return de(e.data.value).then((e=>{t.Etag=ae(e),t.Sha256=Si(e,"ASCII")})).always(n.resolve),n.promise()}return Ie.Pledge()})().then((()=>r.request(e))).then((e=>e),(t=>{const n=(null==t?void 0:t.status)||-1;return[401].includes(n)?($.warn("Yandex: authentication error",t),s.credentials.delete("access_token").then((()=>{if(!e.retry_auth)return e.retry_auth=!0,s.oauth.run().fail((e=>s.error(e))).then((()=>r.request(e)))
}))):t})),compare:(e,t)=>{const n=Ie();let r;return(r=e.md5)&&r==ae(t,"utf-8")?n.resolve(!0):n.resolve(!1),n.promise()},revoke:()=>s.oauth.revoke()});return s},webdav:e=>{let t;t=18e5;const n=e.url;if(n){const t=n.toLowerCase();t.startsWith("webdav")?e.url=n.replace(/^webdav/i,"http"):t.startsWith("http")||(e.url=`http://${n}`)}const r=Ni((e=>{const t=Object.assign({},e);if(void 0===t.type)throw new Error("Internal error");const n=Object.assign(e,{...t,config:{...t.config},credentials:{
get:()=>Ie.Pledge({basic_auth:n.config.basic_auth}),set:()=>Ie.Breach(),delete:e=>Ie((({resolve:t,reject:r})=>{"basic_auth"===e?(delete n.config[e],t()):r()})).promise(),clear:()=>Ie.Breach()},request:e=>Ie((async({resolve:t,reject:r})=>{if(!e.no_auth){const t=(await n.credentials.get()).basic_auth;if(!t)return void r("Authentication failed");e.headers=e.headers||{},e.headers.Authorization="Basic "+t}t()})).promise().then((()=>t.request(e))).then((e=>e),(t=>{if(!e.no_auth){
const e=(null==t?void 0:t.status)||-1;[401].includes(e)&&($.warn("basic auth: authentication error",t),n.credentials.delete("basic_auth"))}return t})),wait:e=>t.wait(((...t)=>e(...t)))});return n})(zi("webdav")));return Object.assign(r,{...r,config:{...r.config,root:"Tampermonkey",watch_interval:18e5,...e}})}};let Hi;Object.keys(Qi);const qi=new ze,Xi={images:{origin:Qt.images.origin,brand:Qt.images.brand,get:function(e){return{about:"info-circle blue",bug:"bug",button_ok:"check green",
cancel:"times red",check:"badge-check",clock:"clock green",cloud:"cloud",critical:"exclamation-triangle orange",contrib:"heart",db:"database grey",delete:"trash-alt",download:"spinner rotate",edit:"edit",edit_add:"plus-square",editor_cancel:"undo",enabler:"angle-right",enabler_enabled:"angle-down",error:"bell red",exit:"times",filesave:"save",filter:"filter",flag:"flag",encrypted:"lock orange",save_to_disk:"download",help:"question-square",home:"home",import:"upload",incognito:"eye-slash",
info:"info-square",no_script:"info",lock:"cog",menu_cmd:"cogs",mobile:"mobile-alt",no:"minus-circle red",no_domain:"thumbs-down red",question_mark:"question-circle",resources:"cloud purple",script_add:"plus-square",script_cancel:"industry-alt",script_download:"file-code purple",script_search:"search",tags:"tags",trash:"trash-alt",permissionless:"star-exclamation green",update:"sync",utilities:"cog",web:"globe blue",windowlist:"window-restore grey",yes_domain:"thumbs-up green"}[e]||""}},
formatBytes:(e,t)=>{if(0==e)return"0 Byte";const n=void 0===t?3:t,r=Math.floor(Math.log(e)/Math.log(1e3));return(0===t?Math.round:e=>e)(e/Math.pow(1e3,r)).toFixed(n)+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][r]}},Yi="monkey_clip",Wi=Xi.images,Ji=new ze,Ki=[{id:"L001",get question(){return Rt("There_are_no_active_scripts__Do_you_want_to_search_for_userscripts_")},tags:["FirstSteps"],url:(e=>{const t=e.www;return`${e.protocol}//${t?t+".":""}${e.domain}`})({protocol:"https:",www:"www",
api:"api",domain:"userscript.zone"})+"?"+gn({utm_source:"ext",utm_medium:"clip".substr(0,3),utm_campaign:Se.short_id})},{id:"Q101",get question(){return Rt("Do_you_need_help_working_with_Tampermonkey_")},tags:["General","FirstSteps"]},{id:"Q102",get question(){return Rt("Do_you_need_help_installing_new_scripts_to_Tampermonkey_")},tags:["ScriptManagement","FirstSteps"]},{id:"Q103",get question(){return Rt("Would_you_like_to_know_how_to_overwrite_or_extend_a_script_s_includes_and_or_excludes_")},
tags:["ScriptManagement","Customization"]},{id:"Q105",get question(){return Rt("Do_you_need_help_syncing_all_your_installed_scripts_to_another_browser_")},tags:["ScriptManagement","Sync"]},{id:"Q106",get question(){return Rt("Would_you_like_to_learn_how_to_export_and_import_your_scripts_")},tags:["ScriptManagement","Sync"]},{id:"Q204",get question(){return Rt("Do_you_want_to_know_how_to_allow_Tampermonkey_access_to_local_file_URIs_")},tags:["ExtensionSettings","SecurityPrivacy"]},{id:"Q303",
get question(){return Rt("One_of_your_scripts_is_blacklisted__Would_you_like_to_know_why_")},tags:["Blacklisted"]},{id:"Q400",get question(){return Rt("Do_you_need_help_viewing_and_editing_values_stored_by_a_userscript_")},tags:["ScriptManagement","Customization","ScriptSettings"]},{id:"Q402",get question(){return Rt("Do_you_want_to_use_an_external_editor_to_edit_your_scripts__nWould_you_like_to_know_how_to_set_this_up_")},tags:["ScriptManagement","Customization"]},{id:"Q404",get question(){
return Rt("Are_you_unsure_about_what__sandbox_value_to_use_")},tags:["ScriptManagement","Customization"]},{id:"Q600",get question(){return Rt("Do_you_need_help_finding_Tampermonkey_s_console_output_")},tags:["Troubleshooting"]
}].filter((e=>!!e)),$i=['<div class="monkey-clip">','<div class="speech-bubble">','<div class="header"></div>','<button class="close" type="button"><svg width="1em" height="1em" viewBox="0 0 24 24" fill="none" class="frame-11g4mt0 e13lhb5m0"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.293 6.293a1 1 0 0 1 1.414 0L12 10.586l4.293-4.293a1 1 0 1 1 1.414 1.414L13.414 12l4.293 4.293a1 1 0 0 1-1.414 1.414L12 13.414l-4.293 4.293a1 1 0 0 1-1.414-1.414L10.586 12 6.293 7.707a1 1 0 0 1 0-1.414Z" fill="currentColor"></path></svg></button><a class="yes" href="#">','<div class="help-text"></div>','</a><div class="clickable disable"></div>',"</div>",`<img src="${Wi.brand("monkey")}" alt="Tampermonkey Bot" class="helper-image">`,"</div>"]
;let es;const ts=e=>{if(F)try{F.setItem(Yi,JSON.stringify(e))}catch(e){console.error(e)}},ns=(()=>{let e;const t=[],n=(()=>{if(F)try{const e=F.getItem(Yi);return e?JSON.parse(e):{enabled:!0,lastShown:0,shown:{}}}catch(e){console.error(e)}return{enabled:!1,lastShown:0,shown:{}}})(),r=e=>{const r=Object.keys(n.shown);return(e=>{const t=new Set;return e.forEach((e=>{Ki.filter((t=>t.tags.includes(e))).forEach((e=>t.add(e)))})),Array.from(t)})(e).filter((({id:e})=>!t.includes(e)&&!r.includes(e)))
},i={init:()=>{if(!es){if(!n.enabled)return;es=It($i.join("")).hide(),It("body").append(es),Ji.on("numberOfScripts",(e=>{const t=[];0!==e.count&&0!==e.enabled||t.push("FirstSteps"),e.blacklisted>0&&t.push("Blacklisted");const n=q(r(t))[0];n&&i.show(n)})),Ji.on("scriptEditorOpened",(e=>{if("new-user-script"==e.uuid)return;const t=q(r(["Customization","ScriptSettings"]))[0];t&&i.show(t)})),Ji.on("storageEditorOpened",(()=>{const e=q(r(["ScriptSettings"]))[0];e&&i.show(e)})),
es.find(".header").text(Rt("0name0_Your_service_bot",Rt("Tam"))),es.find(".yes").on("click",(function(e){const t=this.getAttribute("href");t&&(window.open(t,"_blank"),e.preventDefault()),i.close()}));const t=es.find(".no").text(Rt("Close")),s=es.find(".close").attr("title",Rt("Close"));It(t,s).on("click",(t=>{t.preventDefault(),e&&(n.shown[e]={date:Date.now()},ts(n)),i.close()})),es.find(".disable").text(Rt("Disable")).on("click",(e=>{e.preventDefault(),n.enabled=!1,ts(n),i.close()})),
window.setTimeout((()=>{const e=r(["General","Sync","ExtensionSettings","SecurityPrivacy","Troubleshooting"]),t=q(e)[0];t&&i.show(t)}),1e3)}},show:({id:r,url:i,question:s})=>{e||n.enabled&&(n.lastShown=Date.now(),ts(n),t.push(r),e=r,es.find(".help-text").text(s),es.find(".yes").attr("href",i||`https://www.tampermonkey.net/faq.php#${e}`),es.fadeIn(1e3))},close:()=>{e=void 0,es.fadeOut(1e3)}};return i})(),rs={glog(e){if(e<1)throw new Error("glog("+e+")");return rs.LOG_TABLE[e]},gexp(e){
for(;e<0;)e+=255;for(;e>=256;)e-=255;return rs.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)};for(let e=0;e<8;e++)rs.EXP_TABLE[e]=1<<e;for(let e=8;e<256;e++)rs.EXP_TABLE[e]=rs.EXP_TABLE[e-4]^rs.EXP_TABLE[e-5]^rs.EXP_TABLE[e-6]^rs.EXP_TABLE[e-8];for(let e=0;e<255;e++)rs.LOG_TABLE[rs.EXP_TABLE[e]]=e;class is{constructor(e,t){if(null==e.length)throw new Error(e.length+"/"+t);let n=0;for(;n<e.length&&0===e[n];)n++;this.num=new Array(e.length-n+t)
;for(let t=0;t<e.length-n;t++)this.num[t]=e[t+n]}get(e){return this.num[e]}getLength(){return this.num.length}multiply(e){const t=new Array(this.getLength()+e.getLength()-1);for(let n=0;n<this.getLength();n++)for(let r=0;r<e.getLength();r++)t[n+r]^=rs.gexp(rs.glog(this.get(n))+rs.glog(e.get(r)));return new is(t,0)}mod(e){if(this.getLength()-e.getLength()<0)return this;const t=rs.glog(this.get(0))-rs.glog(e.get(0)),n=new Array(this.getLength());for(let e=0;e<this.getLength();e++)n[e]=this.get(e)
;for(let r=0;r<e.getLength();r++)n[r]^=rs.gexp(rs.glog(e.get(r))+t);return new is(n,0).mod(e)}}const ss={
PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],
G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo(e){let t=e<<10;for(;ss.getBCHDigit(t)-ss.getBCHDigit(ss.G15)>=0;)t^=ss.G15<<ss.getBCHDigit(t)-ss.getBCHDigit(ss.G15);return(e<<10|t)^ss.G15_MASK},getBCHTypeNumber(e){let t=e<<12;for(;ss.getBCHDigit(t)-ss.getBCHDigit(ss.G18)>=0;)t^=ss.G18<<ss.getBCHDigit(t)-ss.getBCHDigit(ss.G18);return e<<12|t},getBCHDigit(e){let t=0;for(;0!==e;)t++,e>>>=1;return t},getPatternPosition:e=>ss.PATTERN_POSITION_TABLE[e-1],getMask(e,t,n){switch(e){case 0:
return(t+n)%2==0;case 1:return t%2==0;case 2:return n%3==0;case 3:return(t+n)%3==0;case 4:return(Math.floor(t/2)+Math.floor(n/3))%2==0;case 5:return t*n%2+t*n%3==0;case 6:return(t*n%2+t*n%3)%2==0;case 7:return(t*n%3+(t+n)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial(e){let t=new is([1],0);for(let n=0;n<e;n++)t=t.multiply(new is([1,rs.gexp(n)],0));return t},getLengthInBits(e,t){if(t>=1&&t<10)switch(e){case 1:return 10;case 2:return 9;case 4:case 8:return 8
;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case 1:return 12;case 2:return 11;case 4:return 16;case 8:return 10;default:throw new Error("mode:"+e)}else{if(!(t<41))throw new Error("type:"+t);switch(e){case 1:return 14;case 2:return 13;case 4:return 16;case 8:return 12;default:throw new Error("mode:"+e)}}},getLostPoint(e){const t=e.getModuleCount();let n=0;for(let r=0;r<t;r++)for(let i=0;i<t;i++){let s=0;const o=e.isDark(r,i)
;for(let n=-1;n<=1;n++)if(!(r+n<0||t<=r+n))for(let a=-1;a<=1;a++)i+a<0||t<=i+a||0===n&&0===a||o===e.isDark(r+n,i+a)&&s++;s>5&&(n+=3+s-5)}for(let r=0;r<t-1;r++)for(let i=0;i<t-1;i++){let t=0;e.isDark(r,i)&&t++,e.isDark(r+1,i)&&t++,e.isDark(r,i+1)&&t++,e.isDark(r+1,i+1)&&t++,0!==t&&4!==t||(n+=3)}for(let r=0;r<t;r++)for(let i=0;i<t-6;i++)e.isDark(r,i)&&!e.isDark(r,i+1)&&e.isDark(r,i+2)&&e.isDark(r,i+3)&&e.isDark(r,i+4)&&!e.isDark(r,i+5)&&e.isDark(r,i+6)&&(n+=40)
;for(let r=0;r<t;r++)for(let i=0;i<t-6;i++)e.isDark(i,r)&&!e.isDark(i+1,r)&&e.isDark(i+2,r)&&e.isDark(i+3,r)&&e.isDark(i+4,r)&&!e.isDark(i+5,r)&&e.isDark(i+6,r)&&(n+=40);let r=0;for(let n=0;n<t;n++)for(let i=0;i<t;i++)e.isDark(i,n)&&r++;return n+=Math.abs(100*r/t/t-50)/5*10,n}};class os{getLength(){return this.data.length}write(e){for(let t=0;t<this.data.length;t++)e.put(this.data.charCodeAt(t),8)}constructor(e){this.mode=4,this.data=e}}class as{constructor(){this.buffer=[],this.length=0}get(e){
const t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)}put(e,t){for(let n=0;n<t;n++)this.putBit(1==(e>>>t-n-1&1))}getLengthInBits(){return this.length}putBit(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}}class ls{constructor(e,t){this.totalCount=e,this.dataCount=t}static getRSBlocks(e,t){const n=this.getRsBlockTable(e,t)
;if(void 0===n)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);const r=n.length/3,i=[];for(let e=0;e<r;e++){const t=n[3*e],r=n[3*e+1],s=n[3*e+2];for(let e=0;e<t;e++)i.push(new ls(r,s))}return i}static getRsBlockTable(e,t){switch(t){case 1:return this.RS_BLOCK_TABLE[4*(e-1)];case 0:return this.RS_BLOCK_TABLE[4*(e-1)+1];case 3:return this.RS_BLOCK_TABLE[4*(e-1)+2];case 2:return this.RS_BLOCK_TABLE[4*(e-1)+3];default:return}}}
ls.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]]
;const cs=ls;class ds{constructor(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=[],this.moduleCount=0,this.dataCache=null,this.dataList=[]}addData(e){const t=new os(e);this.dataList.push(t),this.dataCache=null}isDark(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]}getModuleCount(){return this.moduleCount}make(){if(this.typeNumber<1){let e;for(e=1;e<40;e++){const t=cs.getRSBlocks(e,this.errorCorrectLevel),n=new as
;let r=0;for(let e=0;e<t.length;e++)r+=t[e].dataCount;for(let t=0;t<this.dataList.length;t++){const r=this.dataList[t];n.put(r.mode,4),n.put(r.getLength(),ss.getLengthInBits(r.mode,e)),r.write(n)}if(n.getLengthInBits()<=8*r)break}this.typeNumber=e}this.makeImpl(!1,this.getBestMaskPattern())}makeImpl(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(let e=0;e<this.moduleCount;e++){this.modules[e]=new Array(this.moduleCount)
;for(let t=0;t<this.moduleCount;t++)this.modules[e][t]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=ds.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)}setupPositionProbePattern(e,t){
for(let n=-1;n<=7;n++)if(!(e+n<=-1||this.moduleCount<=e+n))for(let r=-1;r<=7;r++)t+r<=-1||this.moduleCount<=t+r||(this.modules[e+n][t+r]=n>=0&&n<=6&&(0===r||6===r)||r>=0&&r<=6&&(0===n||6===n)||n>=2&&n<=4&&r>=2&&r<=4)}getBestMaskPattern(){let e=0,t=0;for(let n=0;n<8;n++){this.makeImpl(!0,n);const r=ss.getLostPoint(this);(0===n||e>r)&&(e=r,t=n)}return t}createMovieClip(e,t,n){const r=e.createEmptyMovieClip(t,n);this.make();for(let e=0;e<this.modules.length;e++){const t=1*e
;for(let n=0;n<this.modules[e].length;n++){const i=1*n;this.modules[e][n]&&(r.beginFill(0,100),r.moveTo(i,t),r.lineTo(i+1,t),r.lineTo(i+1,t+1),r.lineTo(i,t+1),r.endFill())}}return r}setupTimingPattern(){for(let e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(let e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)}setupPositionAdjustPattern(){const e=ss.getPatternPosition(this.typeNumber)
;for(let t=0;t<e.length;t++)for(let n=0;n<e.length;n++){const r=e[t],i=e[n];if(null==this.modules[r][i])for(let e=-2;e<=2;e++)for(let t=-2;t<=2;t++)this.modules[r+e][i+t]=-2===e||2===e||-2===t||2===t||0===e&&0===t}}setupTypeNumber(e){const t=ss.getBCHTypeNumber(this.typeNumber);for(let n=0;n<18;n++){const r=!e&&1==(t>>n&1);this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=r}for(let n=0;n<18;n++){const r=!e&&1==(t>>n&1);this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=r}}
setupTypeInfo(e,t){const n=this.errorCorrectLevel<<3|t,r=ss.getBCHTypeInfo(n);for(let t=0;t<15;t++){const n=!e&&1==(r>>t&1);t<6?this.modules[t][8]=n:t<8?this.modules[t+1][8]=n:this.modules[this.moduleCount-15+t][8]=n}for(let t=0;t<15;t++){const n=!e&&1==(r>>t&1);t<8?this.modules[8][this.moduleCount-t-1]=n:t<9?this.modules[8][15-t-1+1]=n:this.modules[8][15-t-1]=n}this.modules[this.moduleCount-8][8]=!e}mapData(e,t){let n=-1,r=this.moduleCount-1,i=7,s=0
;for(let o=this.moduleCount-1;o>0;o-=2)for(6===o&&o--;;){for(let n=0;n<2;n++)if(null==this.modules[r][o-n]){let a=!1;s<e.length&&(a=1==(e[s]>>>i&1)),ss.getMask(t,r,o-n)&&(a=!a),this.modules[r][o-n]=a,i--,-1===i&&(s++,i=7)}if(r+=n,r<0||this.moduleCount<=r){r-=n,n=-n;break}}}static createData(e,t,n){const r=cs.getRSBlocks(e,t),i=new as;for(let t=0;t<n.length;t++){const r=n[t];i.put(r.mode,4),i.put(r.getLength(),ss.getLengthInBits(r.mode,e)),r.write(i)}let s=0
;for(let e=0;e<r.length;e++)s+=r[e].dataCount;if(i.getLengthInBits()>8*s)throw new Error("code length overflow. ("+i.getLengthInBits()+">"+8*s+")");for(i.getLengthInBits()+4<=8*s&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(!1);for(;!(i.getLengthInBits()>=8*s||(i.put(ds.PAD0,8),i.getLengthInBits()>=8*s));)i.put(ds.PAD1,8);return ds.createBytes(i,r)}static createBytes(e,t){let n=0,r=0,i=0;const s=new Array(t.length),o=new Array(t.length);for(let a=0;a<t.length;a++){
const l=t[a].dataCount,c=t[a].totalCount-l;r=Math.max(r,l),i=Math.max(i,c),s[a]=new Array(l);for(let t=0;t<s[a].length;t++)s[a][t]=255&e.buffer[t+n];n+=l;const d=ss.getErrorCorrectPolynomial(c),A=new is(s[a],d.getLength()-1).mod(d);o[a]=new Array(d.getLength()-1);for(let e=0;e<o[a].length;e++){const t=e+A.getLength()-o[a].length;o[a][e]=t>=0?A.get(t):0}}let a=0;for(let e=0;e<t.length;e++)a+=t[e].totalCount;const l=new Array(a);let c=0
;for(let e=0;e<r;e++)for(let n=0;n<t.length;n++)e<s[n].length&&(l[c++]=s[n][e]);for(let e=0;e<i;e++)for(let n=0;n<t.length;n++)e<o[n].length&&(l[c++]=o[n][e]);return l}static createCanvas(e){const t=Object.assign({width:256,height:256,correctLevel:2,background:"#ffffff",foreground:"#000000"},e),n=new ds(-1,t.correctLevel);n.addData(t.text),n.make();const r=document.createElement("canvas");r.width=t.width,r.height=t.height;const i=r.getContext("2d")
;if(!i)throw new Error("Unable to get canvas context");const s=t.width/n.getModuleCount(),o=t.height/n.getModuleCount();for(let e=0;e<n.getModuleCount();e++)for(let r=0;r<n.getModuleCount();r++){i.fillStyle=n.isDark(e,r)?t.foreground:t.background;const a=Math.ceil((r+1)*s)-Math.floor(r*s),l=Math.ceil((e+1)*o)-Math.floor(e*o);i.fillRect(Math.round(r*s),Math.round(e*o),a,l)}return r}static setCanvas(e,t){const n=document.getElementById(e)
;if(!n)throw new Error("Unable to find element by the given id!");n.innerHTML="",n.appendChild(ds.createCanvas(t))}}ds.PAD0=236,ds.PAD1=17;const As=Xi.images,us=(e,t,n,r,i)=>{const s=(r.uuid?r.uuid:"")+r.id;t.title=e;const o=bs({after:{image:i||"help"},name:r.name,id:s});o&&n.appendChild(o)},hs=(e,t,n,r,i,s)=>{const o=fe("i","far fa-"+e,t,n,r,!0);if(i&&(o.title=i),o.key=n,o.name=t,s){const e=o.getAttribute("class")||"";o.setAttribute("class",e+" clickable"),o.addEventListener("click",s,!0)}
return o},ps=(e,t,n)=>{const{onClick:r,onAuxClick:i,onDelete:s,disabled:o,onAdd:a,color:l,class:c}=n||{},{bg:d,fg:A,bc:u}=((e,t)=>{const n=t||(e=>Si(e))(e).substring(0,6),r=(i=n,(299*parseInt(i.substr(0,2),16)+587*parseInt(i.substr(2,2),16)+114*parseInt(i.substr(4,2),16))/1e3>=128?"000000":"FFFFFF");var i;const s=(e=>{const t=(299*parseInt(e.substr(0,2),16)+587*parseInt(e.substr(2,2),16)+114*parseInt(e.substr(4,2),16))/1e3;return t>=220||t<=50?"6d6d6d":""})(n);return{bg:`#${n}`,fg:`#${r}`,
bc:s?`#${s}`:void 0}})(e,l),h=fe("span","tag"+(o?" disabled":"")+(c?" "+c:""),t.uuid,"tag"+t.id,e);if(h.setAttribute("style",`background-color: ${d}; color: ${A}; ${u?"border: 1px solid "+u:""}`),h.textContent=e,o&&h.setAttribute("title",Rt("This_tag_is_not_part_of_the_system_tag_list_")),!h.inserted&&r&&h.addEventListener("click",r),!h.inserted&&r&&h.addEventListener("auxclick",i),a){const n=fe("i","far fa-"+As.get("edit_add"),t.uuid,"tag_add",e);n.setAttribute("title",Rt("Add_Tag_to_System")),
h.appendChild(n),n.inserted||n.addEventListener("click",a)}if(s){const n=fe("i","far fa-"+As.get("delete"),t.uuid,"tag_delete",e);n.setAttribute("title",Rt("Delete")),h.appendChild(n),n.inserted||n.addEventListener("click",s)}return h},fs=(e,t,n)=>{const r=(t.uuid?t.uuid:"")+t.id,i=me("div",t.name,r,"input");i.key=t.id;const s=me("input",t.name,r,"input",!0),o=e.split("##");s.name=t.name,s.uuid=t.uuid,s.key=t.id,s.oldvalue=t.value,s.value=null!=t.value?t.value:"",
s.type=t.password?"password":"text",s.setAttribute("spellcheck","false"),t.placeholder&&s.setAttribute("placeholder",t.placeholder),n&&!s.inserted&&s.addEventListener("change",n);const a=fe("span","optiondesc",t.name,r,"s1"),l=me("span",t.name,r,"s2");return a.textContent=o[0]+":",o.length>1&&(l.textContent=o[1]),i.appendChild(a),i.appendChild(s),i.appendChild(l),t.enabledBy&&i.setAttribute("name","enabled_by_"+t.enabledBy),{elem:i,input:s}},ms=(e,t,n,r,i)=>{
const s=(t.uuid?t.uuid:"")+t.id,o=me("div",t.name,s,"outer_dd");o.key=t.id;const a=me("select",t.name,s,"dd",!0);let l=!1,c=t.array&&t.value?t.value.join(","):t.value;if(n&&Object.keys(n).forEach((e=>{const r=me("option",t.name,n[e].name,"dd"+s,!0);r.textContent=n[e].name.replace(/(?:&#x([a-fA-F0-9]+);|&#([0-9]+);)/g,((e,t,n)=>t?String.fromCharCode(parseInt(t,16)):String.fromCharCode(parseInt(n,10))));const i=n[e].value,o=r.value=t.array&&i?i.join(","):i;r.warning=n[e].warning,l|=!!n[e].warning,
n[e].enabledBy&&r.setAttribute("name","enabled_by_"+n[e].enabledBy),t.enabler&&n[e].enable&&r.setAttribute("enables",JSON.stringify(n[e].enable)),o==c&&(r.selected=!0),a.appendChild(r)})),a.key=t.id,a.name=t.name,a.uuid=t.uuid,a.reload=t.reload,a.warning=t.warning,a.oldvalue=t.value,a.valtype="native",a.array=t.array,a.inserted||(r&&a.addEventListener("change",r),l&&i&&a.addEventListener("change",i)),null!==e){const n=fe("span","optiondesc",t.name,s,"inner_dd");n.textContent=e+": ",
o.appendChild(n)}return o.appendChild(a),t.desc&&us(t.desc,o,o,t),t.enabledBy&&o.setAttribute("name","enabled_by_"+t.enabledBy),{elem:o,select:a}},gs=(e,t,n)=>{const r=(t.uuid?t.uuid:"")+t.id;let i=null;return i=fe("input","button",t.name,r,"bu",!0),i.name=t.name,i.uuid=t.uuid,i.key=t.id,i.type="button",i.value=t.name,i.data=t.data,i.reload=t.reload,i.ignore=t.ignore||t.reload,i.warning=t.warning,t.enabledBy&&i.setAttribute("name","enabled_by_"+t.enabledBy),
!i.inserted&&n&&i.addEventListener("click",n),i},_s=(e,t,n,r)=>{let i=null;return i=fe("input","button",e,t,"bu",!0),i.name=e,i.key=t,i.type="button",i.value=n,!i.inserted&&r&&i.addEventListener("click",r),i},bs=(e,t)=>{const n=(e.uuid?e.uuid:"")+e.id;let r,i,s;if(i=e.after||e.validation){if(s=e.validation?"validation":"help",t&&(s+=" clickable"),r=fe("span",s,e.name,n,s,!0),i.image){const e=hs(As.get(i.image),n,"after_icon"),t=[];i.opacity&&t.push("opacity: "+i.opacity),
e.setAttribute("style",t.join(";")),r.appendChild(e)}r&&(t&&r.addEventListener("click",t),i.msg&&r.setAttribute("title",i.msg))}return r},vs=e=>({"&":"&amp;","<":"&lt;",">":"&gt;"}[e]||e),ks={getInfoFromUrl:e=>{let t;if(-1!=e.search(/\/\^?(http(s|s\?|\.\+)?|\.\*):\/\/(\.\*)*\$?\/?$/)||-1!=e.search(/htt(ps|p):\/\/(\*\/\*|\*)*$/)||-1!=e.search(/\*:\/\/(\*\/\*|\*)*$/)||"*"==e)return{dom:"*",tld:"*"};0==e.search(/\//)?(t=e,t=t.replace(/\([^\)]*\)[\?|\+|\*|\{[^\}]*]*/g,""),
t=t.replace(/\[[^\]]*\][\?|\+|\*|\{[^\}]*]*/g,""),t=t.replace(/^\/|\/$|\^|\$|\\\?.*|#.*|\?|\(|\)|\+|\\|\.\*|/g,"")):t=e,t=t.replace(/^\*:\/\//,"http://"),0!=t.search("http")&&(t="http://"+t);const n=t.split("/");if(n.length<3)return null;const r=n[2].split(".");if(r.length<2)return null;const i=r[r.length-1].split(":")[0];let s=r[r.length-2];"*"!==s&&(s=s.replace(/\*/g,""));const o=[];for(let e=r.length-3;e>=0&&-1==r[e].search("\\*");e--)o.push(r[e]);return{tld:i,dom:s,subdom:o.reverse()}},
getValue:e=>{let t=e.value;if("native"===e.valtype)if("undefined"===t)t=void 0;else if("null"===t)t=null;else try{t=JSON.parse(t)}catch(e){}return t},safeTagsReplace:e=>e.replace(/[&<>]/g,vs),addClass:(e,t)=>{let n=e.getAttribute("class")||"";const r=new RegExp("[ \t]*"+t+"[ \t]*");-1==n.search(r)&&(n=(n?n+" ":"")+t),e.setAttribute("class",n)},toggleClass:(e,t)=>{let n=e.getAttribute("class")||"";const r=new RegExp("[ \t]*"+t+"[ \t]*");n=-1!=n.search(r)?n.replace(r,""):(n?n+" ":"")+t,
e.setAttribute("class",n)},setHelp:us,createAfterIcon:bs,createEnabler:(e,t,n,r,i)=>{const s=r.append,o=r.disabled,a=r.title,l=fe("div",(o?"":"clickable ")+"enabler "+e,t,n+"_enabler",s,"wrap");l.appendChild([fe("i","far fa-toggle-on on",t,n+"toggle-on"),fe("i","far fa-toggle-on fa-flip-horizontal off greyed",t,n+"toggle-off")]),r.onf&&l.appendChild(fe("i","far fa-toggle-off fa-flip-horizontal onf",t,n+"toggle-onf")),a&&(l.title=a),l.key=n,l.uuid=t;const c="enabler_enabled"
;return o||l.addEventListener("click",(e=>(It(l).hasClass(c)?It(l).removeClass(c):It(l).addClass(c),i&&window.setTimeout((()=>{i.call(l)}),100),e.stopPropagation(),e.preventDefault(),!1)),!0),l},createImage:(e,t,n,r,i,s)=>{const o=fe("img","icon16",t,n,r,!0);if(o.setAttribute("src",e),o.key=n,i&&(o.title=i),t&&(o.name=t),s){const e=o.getAttribute("class")||"";o.setAttribute("class",e+" clickable"),o.addEventListener("click",s,!0)}return o},createIcon:hs,createFileInput:(e,t,n)=>{
const r=fe("input","import","file",null,null,!0);return r.inserted||(r.type="file",n&&r.addEventListener("change",n)),r},createNamedSettings:(e,t,n)=>{const r=(t.uuid?t.uuid:"")+t.id,i=fe("div","settingsta",t.name,r,"named_wrapper"),s=fe("div","named",t.name,r,"named_settings"),o=[],a=me("span",t.name,r,"s1");return e&&(a.textContent=e+":"),t.desc&&us(t.desc,a,a,t),i.appendChild(a),i.appendChild(s),i.key=t.id,t.value.forEach((e=>{
const i=fe("div","",t.name+e.name,r,"named",!0),a=fe("div","",t.name+e.name,r,"named_label",!0);a.textContent=e.name,i.appendChild(a);const l=me("textarea",t.name+e.name,r,"textarea",!0);l.setAttribute("spellcheck","false"),l.name=t.name,l.key=t.id,l.named_name=e.name,l.uuid=t.uuid,l.named=!0,l.oldvalue=e.value||"",l.value=e.value||"",n&&!l.inserted&&l.addEventListener("change",n),i.appendChild(l),s.appendChild(i),o.push(l)})),{elem:i,textareas:o,label:a}},createTextarea:(e,t,n)=>{
const r=(t.uuid?t.uuid:"")+t.id,i=me("div",t.name,r,"textarea");i.key=t.id;const s=fe("textarea","settingsta",t.name,r,"textarea",!0);s.setAttribute("spellcheck","false"),s.name=t.name,s.key=t.id,s.uuid=t.uuid,s.json=t.json,s.array=t.array,s.oldvalue=t.value,void 0===t.value||null===t.value?s.value="":t.array?s.value=t.value.join("\n"):t.json?s.value=JSON.stringify(t.value,null,4):s.value=t.value,n&&!s.inserted&&s.addEventListener("change",n);const o=me("span",t.name,r,"s1")
;return e&&(o.textContent=e+":"),t.desc&&us(t.desc,o,o,t),i.appendChild(o),i.appendChild(s),{elem:i,textarea:s,label:o}},createTag:ps,createTagEditor:(e,t,n,r)=>{const{onTagClick:i,onChange:s,onTagAdd:o,tagsToAdd:a}=r||{},l=(t.uuid?t.uuid:"")+t.id,c=fe("div",t.name,l,"tag_editor");c.key=t.id;const d=fe("textarea","hidden",t.name,l,"textarea",!0);d.setAttribute("spellcheck","false"),d.name=t.name,d.key=t.id,d.uuid=t.uuid,d.oldvalue=t.value,d.value=JSON.stringify(t.value,null,4)
;const A=fe("span","optiondesc",t.name,l,"s1");e&&(A.textContent=e+":"),t.desc&&us(t.desc,A,A,t);const u=fe("div","tag_editor",t.name,l,"tag_editor",!0),h=Object.entries(n).sort((([e],[t])=>{const n=e.toUpperCase(),r=t.toUpperCase();return n<r?-1:n>r?1:0})),p=(e,r)=>{const{color:a,add:l,click:c,disabled:A}=r,h=ps(e,{...t,id:"editor"+e},{onClick:c?()=>i&&i(e):void 0,class:c?"clickable":void 0,onDelete:()=>{delete n[e],d.value=JSON.stringify(n,null,4),h.remove(),s&&s(n)},
onAdd:l?()=>o&&o(e):void 0,disabled:A,color:a});u.appendChild(h)},f=a?ms(Rt("Add_Tag"),{...t,id:"new_tag",value:a[0]||""},a.reduce(((e,t)=>(e[t]={name:t,value:t},e)),{}),null,null):fs(Rt("New_Tag"),{...t,id:"new_tag",value:""}),m=me("input","new_tag_add_button",l,"editor"+t.name,!0);return a&&0==a.length?m.setAttribute("disabled","disabled"):m.removeAttribute("disabled"),m.type="button",m.value=Rt("Add"),m.addEventListener("click",(function(){const e=a?f.select:f.input,t=e.value
;t&&(n[t]?alert(Rt("Tag_Already_Exists")):(a||(e.value=""),p(t,{click:!!i,add:!!o}),n[t]={},d.value=JSON.stringify(n,null,4),s&&s(n)))})),f.elem.appendChild(m),(()=>{for(const[e,t]of h)p(e,t)})(),c.appendChild(A),c.appendChild(u),c.appendChild(f.elem),c.appendChild(d),{elem:c,textarea:d,label:A}},createFileSelect:(e,t,n)=>{const r=(t.uuid?t.uuid:"")+t.id,i=me("input",t.name,r,"file");if(i.inserted||(i.type="file",i.addEventListener("change",(e=>{n(e.target.files)}),!1)),e){
const n=me("div",t.name,r,"input"),s=me("span",t.name,r,"s1");return s.textContent=e+":",n.appendChild(s),n.appendChild(i),{elem:n,input:i}}return{elem:i}},createInput:fs,createColorChooser:(e,t,n)=>{const r=(t.uuid?t.uuid:"")+t.id,i=fs(e,t,n),s=function(){n&&n.apply(this,arguments);const e=(this.value.match(/[a-fA-F0-9]+/)||"")[0];e&&[3,6].includes(e.length)&&o.setAttribute("style","background-color: #"+e+";")};i.input.inserted||i.input.addEventListener("keyup",s)
;const o=fe("span","color_choosed",t.name,r,"col");return i.col=o,i.elem.appendChild(i.col),s.call(i.input),i},createPassword:(e,t,n)=>{const r=fs(e,t,n);return r.input.setAttribute("type","password"),r},createCheckbox:(e,t,n)=>{const r=(t.uuid?t.uuid:"")+t.id,i=fe("div","checkbox",t.name,r,"cb1");i.key=t.id;const s=me("input",t.name,r,"cb",!0);s.title=t.desc?t.desc:"",s.name=t.name,s.uuid=t.uuid,s.key=t.id,s.reload=t.reload,s.warning=t.warning,s.oldvalue=t.enabled,s.checked=t.enabled,
s.type="checkbox",s.valtype="boolean",n&&!s.inserted&&s.addEventListener("click",n);const o=fe("span","checkbox_desc",t.name,r,"cb2");return o.textContent=e,t.desc&&us(t.desc,i,o,t),i.appendChild(s),i.appendChild(o),{elem:i,input:s,span:o}},createDropDown:ms,createImageButton:(e,t,n,r,i)=>{let s=null,o=null,a=null;return s=fe("button","imgbutton button",e,t,"bu",!0),o=fe("div","imgbutton_container",e,t,"bu_container"),o.appendChild(s),s.uuid=e,s.key=t,
a=fe("i","imgbutton_image far fa-"+r,e,t,"bu_img",!0),s.appendChild(a),s.title=a.title=a.alt=n,!s.inserted&&i&&s.addEventListener("click",i),o},createImageTextButton:(e,t,n,r,i)=>{const s=fe("button","button imgtextbutton",e,t+n,"itb",!0);if(s.key=t,s.uuid=e,r){const i=hs(As.get(r),e,t+n+"itb",r);s.appendChild(i)}const o=me("span",e,t+n,"itb");return o.textContent=n,s.appendChild(o),i&&s.addEventListener("click",i),s},createButton:function(e,t){
return"Object"===D(t)?gs.apply(this,arguments):_s.apply(this,arguments)},createPosition:(e,t,n)=>{const r=(t.uuid?t.uuid:"")+t.id,i=me("div",t.name,r,"pos1"),s=me("select",t.name,r,"pos",!0);for(let e=1;e<=t.posof;e++){const n=me("option",t.name,r,"opt"+e);n.textContent=e,e==t.pos&&(n.selected=!0),s.appendChild(n)}s.key=t.id,s.uuid=t.uuid,s.name=t.name,s.valtype="native",n&&!s.inserted&&s.addEventListener("change",n);const o=fe("span","optiondesc",t.name,r,"pos2");return o.textContent=e,
i.appendChild(o),i.appendChild(s),i},createSearchBox:e=>{const t=fe("div","searchbox","search_inner"),n=fe("div","searchbox_mv","search_inner_mv"),r=fe("input","searchbox_input","search_input"),i=fe("input","searchbox_button","search_button");r.type="text",r.setAttribute("spellcheck","false"),r.value=e,i.type="button",i.value="Go";const s=()=>{const e=r.value;window.location=window.location.origin+window.location.pathname+"?url="+encodeURIComponent(e)};return i.addEventListener("click",s),
r.addEventListener("keyup",(e=>{e&&13==e.keyCode&&s()})),n.appendChild(r),n.appendChild(i),t.appendChild(n),t},createSocialButtons:()=>{const e=[],t=fe("a","","github_link");t.href="https://github.com/derjanb",t.target="_blank";const n=fe("img","icon16","github");n.setAttribute("src",As.origin("gh")),t.appendChild(n),e.push(t);const r=fe("a","","facebook_link");r.href="https://www.facebook.com/tampermonkey",r.target="_blank";const i=fe("img","icon16","facebook")
;i.setAttribute("src",As.brand("facebook")),r.appendChild(i),e.push(r);const s=fe("a","","insta_link");s.href="https://www.instagram.com/der_jan_b/",s.target="_blank";const o=fe("img","icon16","instagram");return o.setAttribute("src",As.brand("instagram")),s.appendChild(o),e.push(s),e},createGobalHint:(e,t)=>{
const{id:n,class:r,text:i,qr:s,title:o,image:a,instant:l,onclick:c,onclose:d,onbuttonclick:A,timeout:u,delay:h,done:p,buttons:f}=e,m=!!s,g="global_hint_"+(r||"warning")+(c?" clickable":""),_=n||Date.now(),b=fe(m?"table":"span",[m?"table noborder":"simple","global_hint",g].join(" "),"globalhint",_),v=me("span","globalhint_c",_),k=me("span","globalhint_t",_);let y,w;if(m){const e=me("tr","globalhint_tr",_);b.appendChild(e)
;const t=fe("td","qr","globalhint_td1",_),n=fe("td","text","globalhint_td2",_),r=me("td","globalhint_td2",_);e.appendChild([t,n,r]),n.appendChild(v),y=me("span","globalhint_mi",_),t.appendChild(y),w=r}else b.appendChild(v),y=w=v;if(o&&(k.title=o),a&&v.appendChild(hs(As.get(a),"globalhint_"+_,"icon"+_)),k.innerHTML=(e=>{const t=new RegExp(`[${Object.keys(O).map((e=>L(e))).join("")}]`,"g");return e.replace(t,(e=>O[e]||e))})(i).split("\n").join("<br>"),c&&!k.inserted&&k.addEventListener("click",c),
v.appendChild(k),s){const e=fe("div","qr","globalhint_qr",_),{img:t,...n}=s;(async(e,t)=>{let{size:n,text:r,img:i,csize:s,bsize:o}=t;if(!r)return;const a=n||225,l=n||225;s=s||75,o=o||0;const c=ds.createCanvas({width:l,height:a,correctLevel:i?3:1,background:"#ffffff",foreground:"#000000",text:r});if(i){const e=c.getContext("2d");if(e){let t,n;o&&(t=(l-s-o)/2,e.fillStyle="#ffffff",e.fillRect(t,t,s+o,s+o)),n=i.startsWith("data:image/svg")?await new Promise((e=>{const t=new Image;t.src=i,
t.onload=()=>e(createImageBitmap(t,{resizeWidth:s,resizeHeight:s}))})):await fetch(i).then((e=>e.blob())).then((e=>createImageBitmap(e))),t=(l-s)/2,e.drawImage(n,t,t,s,s)}}e.append(c)})(e,{...n,...t?{img:As.brand(t)}:{}}),y.appendChild(e),c&&!k.inserted&&e.addEventListener("click",c)}f&&f.forEach((e=>{const{text:t,id:n}=e,r=me(m?"a":"button","globalhint_b",n);r.textContent=t,A&&!r.inserted&&r.addEventListener("click",(t=>{A(e),C(),t.stopPropagation()}),!0),v.appendChild(r)}));const R=It(b)
;l||(R.hide(),window.setTimeout((()=>{R.fadeIn(500)}),h||1));const C=()=>{l?(R.hide(),R.remove()):R.fadeOut(500).then((()=>{R.remove()})),p&&p(b)};if(u&&window.setTimeout(C,u),!u||u>18e5){const e=fe("span","close","gh_close",_);w.appendChild(e),e.addEventListener("click",(e=>{C(),d&&d(),e.stopPropagation()}),!0)}return R.appendTo(t||document.body),R},isScrolledIntoView:(e,t,n)=>{
const r=It(e),i=It(t),s=n&&n.padding&&n.padding.top||0,o=n&&n.padding&&n.padding.bottom||0,a=i.height(),l=r.offset().top-i.offset().top;return l+r.height()<=a-o&&l>=0+s}},ys=ks,ws={},Rs={create:(e,t,n,r)=>{const i=(o=/[0-9a-zA-Z]/g,(s=e)&&o?(s.match(o)||[]).join(""):"");var s,o;let a=!1;null==n&&(n={tv:"tv",tv_table:"tv_table",tr_tabs:"tr_tabs",tr_content:"tr_content",td_content:"td_content",td_tabs:"td_tabs",tv_tabs_fill:"tv_tabs_fill",tv_tabs_table:"tv_tabs_table",
tv_tabs_align:"tv_tabs_align",tv_contents:"tv_contents",tv_tab_selected:"tv_tab tv_selected",tv_tab_close:"tv_tab_close",tv_tab:"tv_tab",tv_content:"tv_content"});const l=fe("div",n.tv,"main"+i),c=fe("table",n.tv_table+" noborder","main_table"+i);c.inserted?a=!0:(ws[i]={},ws[i].g_entries={},ws[i].g_selectedId=null)
;const d=fe("tr",n.tr_tabs,"tabs"+t.id+i),A=fe("td",n.td_tabs,"pages"+i),u=fe("div",n.tv_tabs_fill,"tv_tabs_fill"+i),h=fe("div",n.tv_tabs_table,"tv_tabs_table"+i),p=fe("div",n.tv_tabs_align,"tv_tabs_align"+i),f=fe("tr",n.tr_content,"content"+t.id+i),m=fe("td",n.td_content,"content"+i),g=fe("div",n.tv_contents,"tv_content"+i),_=me("tfoot","tv_footer_t"+i),b=me("tr","tv_footer_tr"+i),v=me("td","tv_footer_td"+i);_.appendChild(b),b.appendChild(v),h.appendChild(p),u.appendChild(h),A.appendChild(u),
d.appendChild(A),c.appendChild(d),m.appendChild(g),f.appendChild(m),c.appendChild(f),c.appendChild(_),l.appendChild(c),t.appendChild(l);const k=(e,t,n)=>{t?e.setAttribute("style",n?void 0:X):e.setAttribute("style",n?"position:absolute; left: -20000px; top: -200000px; width: 1px; height: 1px;":Y),e.setAttribute("vis",t.toString())},y=(e,t)=>{const n=e.getId();if(ws[i].g_entries[n]){if(t==ws[i].g_entries[n].visible)return;ws[i].g_entries[n].visible=t,k(ws[i].g_entries[n].tab,t)}},w=(e,t)=>{
e&&k(e.content,t,!1)},R=e=>{if(null===e)return null;const t=Object.keys(ws[i].g_entries);for(let n,r=0;n=t[r];r++){const t=ws[i].g_entries[n];if(t.entry.getId()==e)return t.entry}return null},C=e=>{e.hide();const t=e.getId(),n=ws[i].g_entries[t];if(n){n.tab.parentNode.removeChild(n.tab),n.content.parentNode.removeChild(n.content);const e=(e=>{const t=Object.keys(ws[i].g_entries);for(let n,r=0;n=t[r];r++)if(ws[i].g_entries[n].tab.id==e.id)return n;return null})(n.tab);e&&delete ws[i].g_entries[e]
}else console.log("tv: WARN: tab not part of tabview!")};let x=null;return a?x=ws[i].tv:(x={getTabById:R,getSelectedTab:function(){return R(ws[i].g_selectedId)},getAllTabs:function(){const e=It(p).find("div[tvid]");let t,n;const r=[];return e.each((i=>{(t=It(e.get(i)).attr("tvid"))&&(n=R(t))&&r.push(n)})),r},getNextTab:function(){const e=It(p).find("div[tvid]");let t;return ws[i].g_selectedId&&e.each(((n,r)=>{t||It(r).attr("tvid")===ws[i].g_selectedId&&(t=It(e.get(n+1)).attr("tvid"))})),
R(t||e.first().attr("tvid"))},getPreviousTab:function(){const e=It(p).find("div[tvid]");let t;return ws[i].g_selectedId&&e.each(((n,r)=>{t||It(r).attr("tvid")===ws[i].g_selectedId&&(t=It(e.get(n-1)).attr("tvid"))})),R(t||e.last().attr("tvid"))},removeTab:C,appendTab:function(e,t,n,r,i){return this.insertTab(void 0,e,t,n,r,i)},insertTab:function(e,t,s,o,a,l){null===e&&(e=p.firstChild);const c="tab_"+t,d=me("div",c,"content"+i),A=void 0!==d.inserted&&1==d.inserted,u=me("div",c,"head_text"+i)
;if(s.appendChild(u),l){const e=fe("div",n.tv_tab_close,c,"tv_close"+i,"tab_close");e.inserted||(e.addEventListener("click",(()=>l())),s.addEventListener("auxclick",(e=>{e&&1==e.button&&l()}))),s.appendChild(e)}if(A){const e=(e=>{const t=Object.keys(ws[i].g_entries);for(let n,r=0;n=t[r];r++){const t=ws[i].g_entries[n];if(t.tab.id==e.id)return t}return null})(d);if(e)return e.entry;console.log("tv: WARN: old tab, but not in tabs collection!")}let h;const f=t,m=e=>{
""!=e.target.className&&e.target.className==n.tv_tab_close||h.select()};return d.setAttribute("tvid",t),d.addEventListener("click",m),s.addEventListener("click",m),d.setAttribute("name","tabview_tab"+i),d.setAttribute("class",n.tv_tab),d.appendChild(s),e?p.insertBefore(d,e):p.appendChild(d),o.setAttribute("name","tabview_content"+i),o.setAttribute("class",n.tv_content),g.appendChild(o),h={getId:function(){return f},isVisible:function(){return"true"==d.getAttribute("vis")},isSelected:function(){
return ws[i].g_selectedId==this.getId()},isCloseable:function(){return!!l},modified:function(){return It(s).hasClass("modified")},remove:function(){C(this)},hide:function(){(e=>{const t=e.getId(),n=t==ws[i].g_selectedId;if(ws[i].g_entries[t]?y(e,!1):console.log("tv: WARN: tab not part of tabview!"),n){let e=null,t=null;for(const n in ws[i].g_entries)ws[i].g_entries[n].visible&&(e=ws[i].g_entries[n],t||e.closable||(t=e));r||e.closable||(e=t),e?e.entry.select():(ws[i].g_selectedId=null,
console.log("tv: WARN: selected tab set, but entry collection empty!"))}})(this)},show:function(){(e=>{const t=e.getId();ws[i].g_entries[t]?y(e,!0):console.log("tv: WARN: tab not part of tabview!")})(this)},select:function(e){if(!e&&h.isSelected())return;const t=Ie();a&&a(t.promise()),(e=>{if(e.getId()==ws[i].g_selectedId)return;const t=e.getId();ws[i].g_selectedId&&w(ws[i].g_entries[ws[i].g_selectedId],!1),Object.keys(ws[i].g_entries).forEach((e=>{const r=ws[i].g_entries[e]
;r.entry.getId()==t?r.visible?r.selected||(r.tab.setAttribute("class",n.tv_tab_selected),w(r,!0),r.selected=!0):console.log("tv: WARN: tab selected but not visible!"):r.selected&&(r.tab.setAttribute("class",n.tv_tab),w(r,!1),r.selected=!1)})),ws[i].g_selectedId=t})(this),t.resolve()},setHeading:function(e,t,n){const r=s.firstChild;if(t&&e.length>t){const i=Math.round(t/2);r.textContent=e.substr(0,i)+"..."+e.substr(i-t),r.title=n||e}else r.textContent=e},toggleClass:function(e,t){
It(s,g).toggleClass(e,t)},close:function(){l&&l()}},ws[i].g_entries[f]={entry:h,tab:d,content:o,closable:null!=l},w(ws[i].g_entries[f],!1),h.show(),h},setFooter:function(e){v.appendChild(e)}},ws[i].tv=x),x}},Cs=Xe({timeout:10800,check_interval:300,retimeout_on_get:!0}),xs=qe({threads:5}),Es={},Gs=async e=>{let t,n;const r=Es[e];if(r)return await r;const i=xs.add((async()=>(n=Cs.get(e),n&&(t=await n.toBlob()),t||(t=await Zs(e)),t&&(n||(n=new ri({blob:t}),Cs.set(e,n))),delete Es[e],n)))
;return Es[e]=i,await i},Zs=async e=>(/^data:image\/svg|^chrome:|\.svgz?([#?].*)?$/.test(e),await(async e=>{try{const t=await k(e);if(t.ok&&200===t.status)return await t.blob()}catch(e){}})(e)),Ss=e=>{const t=(typeof e)[0];if("o"===t)try{e=t+JSON.stringify(e)}catch(n){console.error("Storage: setValue ERROR: "+n.message),e=t+JSON.stringify({})}else e=t+e;return e},Bs=new ze;window.requestFileSystem||(window.requestFileSystem=window.webkitRequestFileSystem)
;const Is=Xi.images,Ts=window.CodeMirror,Us=window.MirrorFrame,Fs="chrome";window.setTimeout=He,window.Hinter={hint:(e,t)=>{const n=Ie(),r=n.promise(),i=t||{},s=i.options||Fr;let o;if(i.config?(o=JSON.parse(JSON.stringify(i.config)),"object"!=typeof o&&(o={})):o=JSON.parse(JSON.stringify(Ur)),i.userscript||i.external){const e={};z(Mr,(t=>{e[t]="writeable"})),o.globals={...e,...o.globals}}if(i.userscript&&(o.rules={...Tr,...o.rules}),e)if(Ir&&G){const t=G,i=()=>{const e=new t("lint.js")
;e.onmessage=e=>{const t=e.data,{results:n,id:r}=t;let i,s;r&&(i=Lr[r])&&(s=i.d)&&(delete Lr[r],n?s.resolve(Dr(n)):s.reject(t.error||"Unknown error")),a(!!r)};const n={method:"base_uri",value:Be.getURL("/")};return e.postMessage(n),Or=void 0,e},a=e=>{if(jr=jr||i(),e)Or=void 0;else if(Or)return;let t,n=Date.now();if(Object.keys(Lr).forEach((e=>{Lr[e].ts<=n&&(t=Lr[e],n=t.ts)})),t){Or=t.id;const e={method:t.method,id:t.id,config:t.c,options:t.o,text:t.t};jr.postMessage(e)}};r.abort=()=>{
jr&&(jr.terminate(),jr=void 0,a())},jr||i();const l=Q(),c={method:"lint",id:l,d:n,t:e,c:o,o:s,ts:Date.now()};Lr[l]=c,a()}else Me("vendor/eslint/eslint",(()=>{try{const t=((e,t,n)=>{const r=self.eslint,i={problem:"error",layout:1,suggestion:1},s=new r.Linter;if(s.defineRules(Gr.rules),t.extends&&t.extends.includes("eslint:recommended")){const e=t.rules=t.rules||{};s.getRules().forEach((function(t,n){if(!t||void 0!==e[n])return;const r=t.meta;if(!r)return;const s=r.docs
;if(!s||!s.recommended)return;let o;const a=[(r.type?i[r.type]:null)||1],l=r.schema;if(l){const e={};(Array.isArray(l)?l:[l]).forEach((t=>{if("object"!=t.type)return;const n=t.properties;n&&Object.entries(n).forEach((([t,n])=>{const r=n.default;void 0!==r&&!1!==r&&(e[t]=n.default,o=!0)}))})),o&&a.push(e)}e[n]=a}))}return s.verify(e,t,n)})(e,o,s);n.resolve(Dr(t||[]))}catch(e){n.reject(e.message)}}));else n.resolve([]);return r}};const Ms=()=>{const e=Re();Yt((()=>{const t={};let n={}
;const r=[],i={},s={};let o={},a={};const l={},c=(()=>{let e,t,n,r,i,s;if((e=F)&&(t=e.getItem("export_tm_settings"),n="true"===t,t=e.getItem("export_externals"),r="false"!==t,t=e.getItem("export_script_storage"),s="false"!==t,i=e.getItem("cloud_config")))try{i=JSON.parse(re(i))}catch(e){}return{script_storage:s,add_tm_settings:n,include_externals:r,cloud_config:i}})();let d;const A={},h=function(e,t){let n,r;if((n=A[e])&&(r=n[t]))return r.apply(this,[].slice.call(arguments,2))},p=e=>{
const t=e.msg||e;if(e.once){if(s[t])return!0;s[t]=!0}let n=confirm(t),r={};return n&&e.ok?r=e.ok:!n&&e.cancel&&(r=e.cancel),(e.ok||e.cancel)&&(n=!0),r.url&&sendMessage({method:"newTab",url:r.url},(()=>{})),n},f=(e,t)=>{try{const n=()=>{};t?sendMessage({method:"newTab",url:e},n):at(0,(r=>{lt(r.id,{method:"loadUrl",url:e,newtab:t},n)}))}catch(e){console.log("lU: "+e.message)}},m=(e,t,n,r,i)=>{r=r||{};const s=ys.createImage(r.default_icon||Qt.images.empty,void 0,n,t,r.title,i)
;if(s.inserted)return s;Array.isArray(e)||(e=e?[e]:[]);const o=async()=>{if(0==e.length){if(r.fill_with_question_mark&&s.parentNode){const e=ys.createIcon(Is.get("question_mark"),t+"_ico",n);s.parentNode.insertBefore(e,s),s.parentNode.removeChild(s)}return}const i=e.shift();let a,l;if(i.startsWith("data:")?a=i:l=await Gs(i),a)s.setAttribute("src",a);else if(l){if(l.tryObjectUrl)s.setAttribute("src",l.tryObjectUrl);else if(l.tryDataUri)s.setAttribute("src",l.tryDataUri);else if(l.tryBlob){
const e=URL.createObjectURL(l.tryBlob);s.setAttribute("src",e),s.onload=()=>URL.revokeObjectURL(e)}}else await o()};return o(),r.title&&(s.title=r.title),s};let g=!1;const _=e=>{if(g)return;const t=mt,n=It("<div>").hide(),r=e=>{e?(n.html("").append(It('<div class="contrib_iframe" style="font-size: 2.5em;"></div>').append(It('<div style="padding-top: 150px;">').text(e))),window.setTimeout(r,1e3)):(n.fadeOut(1e3),window.setTimeout(jt.hideDialog,1e3))
},i=It('<iframe src="https://www.tampermonkey.net/contrib.php?embedded=2'+(t?"&locale="+t:"")+"&src="+(e||"e")+ot.short_id+'" class="contrib_iframe"></iframe>'),s=[It('<button class="contrib_button">').html(Rt("Remind_me_later")).on("click",(()=>{H("later"),r(Rt("Ok"))})),It('<button class="contrib_button">').html(Rt("I_contributed_already")).on("click",(()=>{H("contributed"),r(Rt("Thank_you_very_much_"))
})),It('<button class="contrib_button">').html(Rt("I_dont_want_to_contribute")).on("click",(()=>{H("hide"),r(Rt("Ok"))}))],o=()=>{const e=jt.dialog(n[0]);!0===e?(n.fadeIn(1e3),H("dialog")):void 0===e&&window.setTimeout(o,500),i.unbind("load error")};i.bind("load",(()=>o())),i.bind("error",(()=>n.remove())),n.append(i,s).appendTo(document.body),g=!0,window.addEventListener("message",(e=>{let t;const i=e.data.clicked||e.data.type,s=e.data.amount,o=e.data.currency,a=e.data.redirect_url
;if(i)if(a&&f(a,!0),e.data.success){t=It(".contrib_iframe");const n=t.data("oheight");if(!n||n<0||n>1e3)return;t.animate({height:n},1e3),H("contributed",i,{id:e.data.id})}else e.data.clicked&&(H("clicked",i,{amount:s||"?",currency:o||"?"}),It(".contrib_button").remove(),n.append(It('<button class="contrib_button">').text(Rt("Ok")).on("click",(()=>{r()}))),e.data.enlarge&&(t=It(".contrib_iframe"),t.data("oheight",t.height()),t.animate({height:740},1e3)))}),!1)},v=e=>{const t=Ie(),n=new FileReader
;return n.onloadend=function(){t.resolve(this.result)},n.onerror=()=>{const e=n.error,r=e?e.message:"unknown error";t.reject(r)},n.onabort=()=>t.reject("aborted"),n.readAsBinaryString(e),t.promise()},k=()=>{if(F)return F.getItem("sort_key")},y=()=>{if(F)return F.getItem("sort_sequence")},w=e=>{if(F)return F.setItem("sort_key",e)},R=e=>{if(F)return F.setItem("sort_sequence",e)},C=e=>{const t=(e,n)=>e.tagName==n?e:e.parentNode?t(e.parentNode,n):null;let n="down"==y(),r=null,i=[],s=0
;const a=Date.now();Object.keys(o).forEach((e=>{const n=o[e];if(!n)return void console.warn("options: something went wrong!",e);const l=t(n.dom,"TR");if(l){const e=t(l,"TBODY");let o,c;r?e&&r!=e&&console.warn("options: different parents?!?!"):r=e,s++,(!(o=n.script.lastModified||n.script.lastUpdated)||(c=a-parseInt(o))&&isNaN(c))&&(c=0);const{code:d,resources:A,requires:u}=je(n.script),h=d+A+u;i.push({tr:l,sites:E.get(n.script),enabled:n.script.enabled,size:h,
position:n.script.position?n.script.position:1e3+s,name:Ct(n.script,"name").toLowerCase(),homepage:[n.script.origin?mn(n.script.origin.url).hostname:"z",Fe(n.script)?mn(Fe(n.script)).hostname:"z"].join("_"),updated:c,version:n.script.version||""}),l.inserted=!1,l.parentNode&&l.parentNode.removeChild(l)}else console.log("options: unable to sort script at pos "+n.pos)})),i=(e=>{let t;const r=e=>(t,n)=>t[e]-n[e],i=k();var s,o;return"pos"==i?t=r("position"):"enabled"==i?(s="enabled",o=n,t=(e,t)=>{
const n=(o^e[s]?"a":"b")+e.name,r=(o^t[s]?"a":"b")+t.name;return n<r?-1:n>r?1:0},n=null):t="ver"==i?(e=>(t,n)=>Xr(String(t[e]),String(n[e])))("version"):"size"==i?r("size"):"updated"==i?r("updated"):(e=>(t,n)=>t[e]<n[e]?-1:t[e]>n[e]?1:0)(i),e.sort(t),e})(i),n&&(i=i.reverse());for(let e=0;e<s;e++)r.appendChild(i[e].tr);It(".sorting").forEach((e=>{It(e)["pos"==k()&&"up"==y()?"fadeIn":"fadeOut"]()})),e&&e()},x=Xe({timeout:600,check_interval:300,retimeout_on_get:!0}),E=(()=>{let e={}
;const t=e=>e.toString().length<7?t("0"+e):e,n=e=>{if(e.includes||e.matches){const t={},n=[],r=[],i=e.includes.length?e.includes:e.matches;for(let s=0;s<i.length;s++){const o=i[s];if(!o){console.log("o: Warn: script '"+e.name+"' has invalid include (index: "+s+")");continue}const a=x.get(o),l=(void 0!==a?a:null)||ys.getInfoFromUrl(o);void 0===a&&x.set(o,l),l&&l.dom?t[l.dom]?r.push({include:o,info:l}):(t[l.dom]=!0,n.push({include:o,info:l})):n.push({include:o})}return n.concat(r)}};return{
init:function(t){e={},t.forEach((t=>{const r=n(t);r&&r.length&&r.forEach((t=>{t.info&&(e[t.info.dom]=(e[t.info.dom]||0)+1)}))}))},get:function(r){const i=n(r);if(!i||!i.length)return t(0);const s=i.map((t=>t.info?{score:1e3*e[t.info.dom]+t.info.dom.charCodeAt(0)||0,dom:t.info.dom}:{score:0,dom:""})).sort(((e,t)=>t.score-e.score))[0];return t(s.score)+s.dom},topIcons:function(t,r){let i;const s=[],o=n(t);if(!o||!o.length)return[];const a=o.map((t=>(t.score=t.info&&e[t.info.dom]||0,
t))).sort(((e,t)=>t.score-e.score));return z(a,(e=>{const n=e.info;if(0==r--){const e=fe("span","",t.uuid,"tbc");return e.textContent="...",s.push(e),!1}if(!n)return i=ys.createIcon(Is.get("question_mark"),"favicon",t.uuid,e.include,e.include),void s.push(i);if("*"==n.tld)return i=ys.createIcon(Is.get("web"),"web",t.uuid,e.include,e.include),s.push(i),!1;let o="com",a="";"*"!=n.tld&&"tld"!=n.tld&&(o=n.tld),n.subdom.length&&(a=n.subdom.join(".")+".")
;const l=(a+n.dom+"."+o).replace(/\*|^\./g,""),[c,d]=Le(l);i=m([c,d].filter((e=>e)),"favicon",t.uuid,{fill_with_question_mark:!0,title:e.include}),s.push(i)})),s}}})();let G=null;const Z=()=>{G&&(window.clearTimeout(G),G=null)},S=e=>{let t;const n=e.key||"general";(t=_t[n])&&It(t).remove(),_t[n]=ys.createGobalHint({onclose:()=>{sendMessage({method:"clearHint",key:n},(()=>null))},id:n,...e},It("body > div.status")[0])},B=(e,t,n,r)=>{void 0===r&&(r=3e3),S({key:"success",text:e,image:t,class:n,
delay:500,timeout:r,done:()=>{}})},I=e=>{B(e||Rt("Operation_completed_successfully"),"button_ok","notice")},T=(e,t)=>{void 0===t&&(t="button_ok"),B(e,t,"information",8e3)},U=(e,t)=>{void 0===t&&(t="error"),B(e,t,"warning",8e3)},M=e=>{G||(G=window.setTimeout((()=>{G=null,jt.wait(Rt("Please_wait___"))}),e||500))};let j=null,O=null;const N=(e,t)=>{null!=j?(window.clearTimeout(j),j=null,N(e||O.items,e?t:O.with_scripts)):(O={items:e,with_scripts:t},j=window.setTimeout((()=>{j=null,
O.with_scripts&&(o={},a={}),Be(O.items);let e=0,t=0,n=0;Object.values(o).forEach((({script:r})=>{e++,r.enabled&&t++,(r.blacklisted||r.foisted)&&n++})),Ji.emit("numberOfScripts",{count:e,enabled:t,blacklisted:n}),O=null}),50))},H=(e,t,n)=>{const r=Ie();return sendMessage({method:"begEvent",action:e,type:t,extra:n},(e=>{r.resolve(e)})),r.promise()},q=(e,t)=>{let n=Ie();try{const{json:r,code:i,url:s,data:o}=e;M();const a=st.connect("importEx");a.onMessage.addListener((e=>{if(!n)return
;const{items:t,reload:r,success:i,error:s,progress:o}=e;o?n.notify(o):(Z(),s?(n.reject(s),jt.hide()):(r?window.setTimeout((()=>{Ze()}),500):t?(i?I():U(Rt("Action_failed")),N(t,!0)):jt.hide(),n.resolve(e)),n=null,a.disconnect())})),a.onDisconnect.addListener((()=>{n&&n.reject("communication lost")})),a.postMessage({method:"importEx",json:r,data:o,code:i,url:s,reload:t.reload})}catch(e){console.log("sS: "+e.message),n.reject(e.message||"unknown error")}return n.promise()},W=(e,t,n,r)=>J({uuid:e
},t,n,r),J=(e,t,n,r)=>{const i=Ie();void 0===n.reload&&(n.reload=!0);try{n.auto_save||M(),sendMessage({...e,method:"saveScript",code:t,clean:n.clean,force:n.force,new_script:n.new_script,auto_save:n.auto_save,restore:n.restore,lastModTime:n.lastModTime,reload:n.reload},(e=>{n.auto_save||((r||I)(),Z()),(e=e||{}).items?N(e.items,!0):jt.hide(),!t&&n.reload&&jt.hide(),i.resolve(e)}))}catch(e){console.log("sS: "+e.message),i.reject({err:e.message})}return i.promise()},K=(e,t,r)=>{const i=Ie();try{
M(),sendMessage({method:"setOption",name:e,value:t},(e=>{Z(),I(),n=e.options||n,!r&&e.items?N(e.items,!1):jt.hide(),i.resolve(e)}))}catch(e){console.log("sO: "+e.message),i.reject({err:e.message})}return i.promise()},$=e=>{const t=Ie();try{M(),sendMessage({method:"purgeScripts",uuids:e},(e=>{Z(),e.items?(I(),N(e.items,!1)):jt.hide(),t.resolve(e)}))}catch(e){console.log("sO: "+e.message),t.reject({err:e.message})}return t.promise()},ee=(e,t,n)=>{const r=Ie();try{let i;M(),
"run_sync"===e&&(i=e=>jt.wait(e.text),Bs.on("syncProgressEvent",i)),sendMessage({method:"buttonPress",name:e,data:t},(t=>{"run_sync"===e&&Bs.off("syncProgressEvent",i),Z(),!n&&t.items?(I(),N(t.items,!1)):jt.hide(),r.resolve(t)}))}catch(e){console.log("sO: "+e.message),r.reject({err:e.message})}return r.promise()},ie=(e,t)=>Ie((({resolve:n})=>{sendMessage({method:"miscConfig",action:"set",realm:"cloud",key:e,config:t},(e=>{n(e)}))})).promise(),se=(e,t)=>{M(),sendMessage({method:"loadTree",
complete:e.complete,uuid:e.uuid,url:e.url,referrer:e.referrer,layout:e.layout,filter:e.filter},(e=>{Z(),e.items||(e.error?he(e.error):confirm(Rt("An_internal_error_occured_Do_you_want_to_visit_the_forum_"))&&(window.location.href="https://www.tampermonkey.net/bug")),t(e)}))},oe=()=>{const e=st.connect("syncInfo");e.onMessage.addListener((e=>{e&&Bs.emit("syncProgressEvent",e.info)})),e.onDisconnect.addListener((()=>{setTimeout(oe,500)})),e.postMessage({method:"syncInfo"})},ae=()=>{
Ie.onebyone(["options.scripts","options.trash"].map((e=>()=>{const t=Ie();return se({referrer:e},(e=>{t.resolve(e.items||[])})),t.promise()}))).then((e=>{const t=e.reduce(((e,t)=>e.concat(t)),[]).filter((e=>e));t&&t.length?N(t,!0):jt.hide()}))},le=(e,t,r)=>{null==r&&(r=!0);try{const i={method:"modifyScriptOptions",uuid:e,reload:r};for(const e in t)t.hasOwnProperty(e)&&(i[e]=t[e]);M(),sendMessage(i,(e=>{Z(),n=e.options||n,e.i18n&&xt(e.i18n),I(),e.items?N(e.items,!0):jt.hide()}))}catch(e){
console.log("mSo: "+e.message)}},ce=(e,t,n,r)=>{try{const i={method:"saveStorageKey",uuid:e,key:t,value:n,removed:r,id:"options"};M(),sendMessage(i,(()=>{Z(),I(),jt.hide()}))}catch(e){console.log("sSk: "+e.message)}},de=window.navigator.userAgent.includes("Mac")?"mac":"other";let ge;window.addEventListener("keydown",(e=>{let t,n,r=!1;if("keydown"==e.type&&!e.defaultPrevented){if(27==e.keyCode){if(t=d.getTabById("dashboard"),!t.isSelected())return
;if(e.target&&"text"==e.target.type)return void It(e.target).trigger("blur");A.multiselect.toggleRow(!1),r=!0}else("mac"!=de&&39==e.keyCode||"mac"==de&&40==e.keyCode)&&!e.ctrlKey&&e.altKey&&!e.shiftKey?(n=d.getNextTab(),n&&(n.select(),r=!0)):("mac"!=de&&37==e.keyCode||"mac"==de&&38==e.keyCode)&&!e.ctrlKey&&e.altKey&&!e.shiftKey&&(n=d.getPreviousTab(),n&&(n.select(),r=!0));return r?(e.stopPropagation(),e.preventDefault(),!1):void 0}}),!0),window.addEventListener("keydown",(e=>{let t,n,r=!1
;if("keydown"==e.type&&!e.defaultPrevented){if(112==e.keyCode)t=d.getTabById("help"),t&&t.select(),r=!0;else if(38!=e.keyCode&&40!=e.keyCode||!e.shiftKey){if(70==e.keyCode&&(e.ctrlKey||e.metaKey)){if(t=d.getTabById("dashboard"),!t.isSelected())return;A.multiselect.toggleRow(!0,!0),r=!0}else if(65==e.keyCode&&(e.ctrlKey||e.metaKey)){if(t=d.getTabById("dashboard"),!t.isSelected()||e.target&&"text"==e.target.type)return;A.multiselect.un_selectAll(!0),r=!0}}else{if(t=d.getTabById("dashboard"),
!t.isSelected())return;let i=At.length&&At[At.length-1];i&&(i=It(i))&&(()=>{r=!0;const t=i.closest("tr");if(!t.length)return;let s,o;if(38==e.keyCode?(n=It(t.prevAll("tr").filter((e=>It(e).is(":visible")))[0]),s=!0):(n=It(t.nextAll("tr").filter((e=>It(e).is(":visible")))[0]),s=!1),!n.length)return;const a=n.find('input[name="scriptselectors"]');if(At.includes(a[0])?(At.pop(),i.prop("checked",!1),i.removeClass("selected"),o=i):a.length&&(a.prop("checked",!0),a.addClass("selected"),At.push(a[0]),
void 0===At.direction&&(At.direction=s),o=a),o){const e=It(".scripttable").parent().get(0),{top:t,bottom:r}=e.getBoundingClientRect(),i=n,o=3*i.height(),a=e.scrollTop;if(!ys.isScrolledIntoView(i,e,{padding:s?{top:o}:{bottom:o}})){const n=s?i.offset().top-t-o-i.height():i.offset().top-t-(r-t)+o+2*i.height(),l=Math.floor(a+n);It(e).animate({scrollTop:l},0)}A.multiselect.single_click()}})()}return r?(e.stopPropagation(),e.preventDefault(),!1):void 0}}),!1),
document.addEventListener("dragover",(e=>e.preventDefault())),document.addEventListener("drop",(async e=>{try{const t=e.dataTransfer.files[0];if(!t)return;const n=await new ri({blob:t}).toTransferableData();ee("installFromUrl",{transferable:n}),e.preventDefault()}catch(e){}}),!0);const _e=(()=>{const e={},t=()=>{ge=ue(!0),Object.keys(e).forEach((t=>{const n=e[t];s.is(n.main,n.sub)&&n.fn()}))};let n;t(),window.onhashchange=function(){n||t.apply(this,arguments)};const i=e=>{n=!0,e(),n=!1},s={
set:function(e){const n=s.get();let r;r="object"==typeof e?[e.main,e.sub].filter((e=>e)):arguments,n.main===r[0]&&n.sub===r[1]||(!r[0]&&r[1]?s.setSub(r[1]):i((()=>{window.location.hash="nav="+[].join.call(r,"+"),t()})))},setSub:function(e){const n=s.get();n.sub!==e&&i((()=>{window.location.hash="nav="+(n.main?n.main:"")+"+"+e,t()}))},get:function(){const e=ge.nav?ge.nav.split("+"):[];return{main:e[0],sub:e[1]}},is:function(e,t){const n=s.get(),r=!e||n.main===e,i=!t||n.sub===t;return r&&i},
isSub:function(e){return s.is(null,e)},registerListener:function(t,n,r){void 0===r&&(r=n,n=null);const i=[t,n].join("+");return e[i]={main:t,sub:n,fn:r},s.is(t,n)&&r(),i},unregisterListener:function(t){delete e[t]},clear:function(){s.set("")}},o=s.get();return r.push((()=>{const e=d.getTabById(o.main);if(e)return e.select(!0);if(o.main){const e=d.getTabById("dashboard");e&&e.select(!0),r.push((()=>{window.setTimeout((()=>{s.set(o)}),0)}))}else window.onhashchange()})),s})();r.push((()=>{
n.statistics_enabled&&(Xn("opt",{version:st.manifest.version}),Yn(!0))}));const be=(()=>{let e={};const t={clear:()=>{z(e,(e=>u(e))),e={}},is:(n,r)=>{const i=void 0!==e[n];return r&&t.add(n),i},add:(n,r)=>{t.is(n)&&u(e[n]),e[n]=b((()=>{delete e[n]}),r||1e3)}};return t})(),ve=()=>{for(;r.length;)try{r.shift()()}catch(e){console.warn("doneListeners:",e)}},ke=(e,t)=>{const n=me("input",e.name,e.id,"Save");return n.inserted||(n.type="button",n.section=e,n.disabled=!0,n.value=Rt("Save"),
n.addEventListener("click",(function(){if(t&&t.warning&&!p(t.warning))return;const e=It(this.section).find("input, select, textarea").toArray(),n=[],r=[];for(let i=0;i<e.length;i++){let s=null;const o=e[i],a=o.key;if(r.includes(o))continue;let l=o.tagName.toLowerCase();if("input"==l&&(l=o.type),"textarea"==l)if(o.named){const e=document.getElementsByName(o.name);s=[],z(e,(e=>{s.push({name:e.named_name,value:e.value}),r.push(e)}))}else if(o.array){const e=o.value
;s="null"===e?null:o.value.split("\n").map((e=>e.trim())).filter((e=>e))}else if(o.json){let e;try{s=(e=o.value)&&(e=e.trim())?JSON.parse(e):null}catch(e){return void he(Rt("Unable_to_parse_0name0",o.name))}}else s=o.value;else if("checkbox"==l)s=o.checked;else if("select"==l){let e=0;o.selectedIndex>=0&&o.selectedIndex<o.options.length&&(e=o.selectedIndex),s=o[e]?o[e].value:o.options[0].value,o.array&&(s="null"===s?null:s.split(",").map((e=>e.trim())).filter((e=>e)))
}else"button"==l||(s=o.value);a&&n.push(K(a,s,t&&t.reload))}t&&t.reload&&(jt.wait(),Ie.when(n).done((()=>{Ze()})))}),!1)),n},ye=e?6:15,we=(i,s,c,d)=>{let u,h,m,g,_,b,v=[];const x=(e,t)=>{if(c)if(c.save_button)It(e).on("change",(()=>{c.save_button.disabled=!1,G()})),It(t).on("input propertychange",(()=>{c.save_button.disabled=!1,G()}));else{const e="Section needs a save button!";console.error(e,c)}},G=()=>{c.on_save_button_state_change&&c.on_save_button_state_change.forEach((e=>e()))}
;if(s.divider)return null;if(s.checkbox)c&&c.need_save?s.enabler&&(m=function(){const e=document.getElementsByName("enabled_by_"+this.key);z(e,(e=>{this.checked?e.removeAttribute("disabled"):e.setAttribute("disabled","disabled")}))},r.push((()=>m.bind(u.input)()))):m=function(){let e=!0;const t=this;t.warning&&(e=p(t.warning),e||(t.checked=!t.checked)),e&&K(this.key,t.checked,t.reload).always((()=>{t.reload&&Ze(500)}))},u=ys.createCheckbox(s.name,s,m),v.push(u.elem),
c&&c.save_button&&x(null,u.input);else if(s.button)m=function(){let e=!0;const t=this;t.warning&&(e=p(t.warning)),e&&ee(t.key,t.data,t.ignore).always((()=>{t.reload&&Ze(500)}))},u=ys.createButton(s.name,s,m),s.needsSave&&c&&c.save_button&&(c.on_save_button_state_change||(c.on_save_button_state_change=[]),c.on_save_button_state_change.push((()=>{u.disabled=!c.save_button.disabled}))),v.push(u);else if(s.status){u=ys.createTextarea(s.name,s),u.textarea.setAttribute("readonly",!0),
u.textarea.setAttribute("class","settingsta event_status"),l[s.event]&&Bs.off(s.event,l[s.event]);const e=e=>{u.textarea.value=(new Date).toLocaleString()+": "+e.text+"\n"+u.textarea.value};l[s.event]=e,Bs.on(s.event,e),v.push({element:u.elem,validation:u.label})}else if(s.named)u=ys.createNamedSettings(s.name,s),v.push({element:u.elem,validation:u.label}),x(null,u.textareas);else if(s.tags)u=ys.createTagEditor(s.name,s,s.value,{onChange:e=>{K(s.id,e,s.reload)}}),v.push({element:u.elem,
validation:u.label});else if(s.input)u=ys.createTextarea(s.name,s),v.push({element:u.elem,validation:u.label}),x(null,u.textarea);else if(s.permission){const e=s.value,t=!e.length,n=Ue(Rt("Host_permissions_denied_by_user_"),{id:"permissions",item:s},t?["<"+Rt("No_previously_denied_runtime_host_permissions_found")+">"]:e,(e=>ee("permissions",{origins:e||[],reset:null===e}).then((e=>e.granted),(()=>!1))));It(n.elem).addClass("cludes"),It(n.select,n.buttons).prop("disabled",!!t||null),
v.push(n.elem)}else if(s.text)u=ys.createInput(s.name,s),v.push(u.elem),x(u.input);else if(s.color)u=ys.createColorChooser(s.name,s),v.push(u.elem),x(u.input);else if(s.password)u=ys.createPassword(s.name,s),v.push(u.elem),x(u.input);else if(s.select){h=function(){let e=!0;const t=this;t.warning&&(e=p(t.warning),e||(t.value=t.oldvalue)),e&&K(t.key,ys.getValue(t),t.reload).always((()=>{t.reload&&Ze(500)}))};const e=function(){let e=!0
;this.selectedOptions.length&&this.selectedOptions[0].warning&&this.selectedOptions[0].value!==this.oldvalue&&(e=p(this.selectedOptions[0].warning),e||(this.value=this.previousValue||this.oldvalue)),this.previousValue=this.value};c&&c.need_save&&(h=s.enabler?function(){const e=document.getElementsByName("enabled_by_"+this.key),t=(this.selectedIndex<this.options.length?this.options[this.selectedIndex]:this.options[0]).getAttribute("enables"),n=t?JSON.parse(t):{};z(e,(e=>{
void 0===n[e.key]||n[e.key]?e.setAttribute("style",X):e.setAttribute("style",Y)}))}:null),u=ys.createDropDown(s.name,s,s.select,h,e),v.push(u.elem),c&&c.save_button&&x(u.select),i&&s.enabler&&(()=>{const e=h,t=u;r.push((()=>{e.apply(t.select,[])}))})()}else if(s.url){const e=me("a",s.name,s.id);e.href="#",e.url=s.url,e.newtab=s.newtab,e.inserted||(h=function(){f(this.url,this.newtab)},e.addEventListener("click",h)),e.textContent=s.name,v=Array(ye),v[3]=e}else if(s.main_menu_item){
if(_=me("div",s.name||s.image,s.id),s.image){const e=ys.createIcon(Is.get(s.image),"",s.uuid,s.id);_.appendChild(e)}else _.textContent=s.name||"";s.title&&(_.title=s.title);const o=me("div",s.name,s.id,"tab_content"),l=((t,i)=>{const s=(e,t,n,r,i)=>{let s;const o=fe("th","settingsth",e.name,e.id,t),a=fe("a","settingsth_a",e.name,e.id,t+"_a");a.setAttribute("name","settingsth_a"+e.name);const l=fe("a","settingsth_a_up",e.name,e.id,t+"_a_up");l.setAttribute("name","settingsth_a_up"+e.name)
;const c=fe("a","settingsth_a_down",e.name,e.id,t+"_a_down");c.setAttribute("name","settingsth_a_down"+e.name);const d=()=>{((t,n,r)=>{const i=document.getElementsByName("settingsth_a_up"+e.name),o=document.getElementsByName("settingsth_a_down"+e.name);for(s=0;s<i.length;s++)i[s].style.display="none";for(s=0;s<o.length;s++)o[s].style.display="none";"up"==y()?n.style.display="":r.style.display=""})(0,l,c)},A=()=>{d()},u=()=>{window.setTimeout((()=>{k()==r?R("down"==y()?"up":"down"):w(r),C(A)}),0)
};return o.inserted||(o.appendChild(a),o.appendChild(c),o.appendChild(l),a.addEventListener("click",u),l.addEventListener("click",u),c.addEventListener("click",u),a.textContent=n+" ",a.href="#",l.innerHTML="&#x25B4;",l.href="#",c.innerHTML="&#x25BE;",c.href="#"),i&&!k()?(w(r),R("up"),window.setTimeout(d,0)):r==k()&&window.setTimeout(d,0),o};let o,l,c,d,u,h;if(u=me("tbody",t.name,t.id,"body"),h=me("thead",t.name,t.id,"head"),"dashboard"==t.id){const a=gt(t),u=dt(t)
;o=me("table",t.name,t.id,"main"),o.inserted||o.setAttribute("class","scripttable multiselect");const p=fe("th","script_sel",t.name,t.id,"thead_sel");p.appendChild(a.selAllm);const f=ye-2,m=fe("th","left",t.name,t.id,"thead_multi_action");m.setAttribute("colspan",f),m.appendChild(a.actionBox),m.appendChild(u);const g=fe("th","right",t.name,t.id,"thead_multi_close");g.appendChild(a.close),c=fe("tr","multiselectrow",t.name,t.id,"filler_multi"),c.appendChild([p,m,g]),
d=fe("tr","multiselectscrolldummy",t.name,t.id,"scrolldummy");const _=fe("th","left",t.name,t.id,"scrolldummyth");_.setAttribute("colspan",f),d.appendChild([_]),l=fe("tr","scripttr multiselectreplaced",t.name,t.id,"filler");const b=fe("th","script_sel",t.name,t.id,"thead_sel");b.appendChild(a.selAll)
;const v=s(t,"thead_pos","#","pos"),w=s(t,"thead_en",Rt("Enabled"),"enabled"),R=fe("th","thead_icon",t.name,t.id,"thead_icon"),C=s(t,"thead_name",Rt("Name"),"name",!0),x=fe("th","settingsth",t.name,t.id,"thead_del");if(x.textContent=Rt("Actions"),e)z([b,v,w,R,C,x],(e=>{l.appendChild(e)}));else{const e=s(t,"thead_ver",Rt("Version"),"ver"),i=s(t,"thead_size",Rt("Size"),"size"),o=fe("th","settingsth small",t.name,t.id,"thead_type"),a=fe("span","script_type",t.name,t.id,"thead_type_span")
;a.textContent=Rt("Type"),o.appendChild(a);const c=fe("th","settingsth small",t.name,t.id,"thead_sync");c.textContent="";const d=s(t,"thead_sites",Rt("Sites"),"sites"),A=fe("th","settingsth",t.name,t.id,"thead_features");A.textContent=Rt("Features");const u=s(t,"thead_homepage",Rt("Homepage"),"homepage"),h=s(t,"thead_updated",Rt("Last_updated"),"updated"),p=fe("th","settingsth",t.name,t.id,"thead_sort"),f=fe("span","sorting",t.name,t.id,"thead_sort_span");f.textContent=Rt("Sort"),
"pos"==k()&&"up"==y()||f.setAttribute("style","display: none;"),p.appendChild(f);const m=()=>{n.sync_enabled&&(c.textContent=Rt("Imported"))};r.push(m),z([b,v,w,R,C,e,i,o,c,d,A,u,h,p,x],(e=>{l.appendChild(e)}))}h.appendChild([c,d,l]),l.inserted||(A.multiselect.checkScroll=()=>{const e=It(h),t=e.is(":visible")&&It(c).is(":visible")&&It(i).scrollTop()>0;e.toggleClass("multiscrolling",t)},It(i).on("scroll",A.multiselect.checkScroll))}else if("trash"==t.id){
o=fe("table","settingstable",t.name,t.id,"main");const e=fe("tr","","tr_trash"),n=fe("td","","td_trash"),r=fe("div","trash_actions","div_trash_actions");n.appendChild(r),e.appendChild(n),o.appendChild(e);const i=ys.createButton("restore_all",`${t.uuid}_delall`,Rt("Restore_all"),(()=>{if(confirm(Rt("Really_restore_all_userscripts_"))){const e=Object.keys(a);J({uuids:e},null,{reload:!0,restore:!0},void 0),e.length&&jt.wait()}
})),s=ys.createButton("delete_all",`${t.uuid}_delall`,Rt("Delete_all"),(()=>{if(confirm(Rt("Really_delete_all_userscripts_"))){const e=Object.keys(a);$(e),e.length&&jt.wait()}}));r.appendChild([i,s])}else o=fe("table","settingstable",t.name,t.id,"main");return o.appendChild(h),o.appendChild(u),{table:o,head:h,body:u}})(s,o);o.appendChild(l.table);let u=null;const h=d.appendTab(s.id,_,o,(e=>{e.then((()=>{u&&(t.global?u():r.push(u)),_e.set(s.id),document.title=s.name}))}))
;_e.registerListener(s.id,(()=>{h.select()})),s.hidden?h.hide():h.show(),s.referrer&&(u=()=>{u=null,M(50),se({referrer:s.referrer},(e=>{e.items&&e.items.forEach((e=>{we(i,e,c,d)})),ve(),Z(),jt.hide()}))}),s.items&&Re(l.body,s.items,null,d),!t.global&&s.selected_default&&r.push((()=>{d.getSelectedTab()||h.select()}))}else if(s.sub_menu_item)g=fe("div","section type_"+s.id,s.name,s.id,"section"),_=fe("div","section_head",s.name,s.id,"head"),b=fe("table","section_content",s.name,s.id,"content"),
_.textContent=s.name,g.appendChild(_),s.desc&&ys.setHelp(s.desc,_,_,s),g.appendChild(b),s.need_save&&(s.save_button=ke(b,s)),Re(b,s.items,s,d),s.save_button&&b.appendChild(s.save_button),v.push(g);else if(s.userscript)if(s.deleted){a[s.uuid]={dom:i,script:s};const e=fe("div","trash_list","deleted_scripts");e.appendChild(Qe(s,e,d)),e.inserted||fe("td","","td_trash").appendChild(e)}else if(v=He(s,i,d),o[s.uuid]={dom:i,script:s},i.setAttribute("class","scripttr"),
s.nnew)i.setAttribute("style","display: none;");else{const e="script_refresh";be.is(e)||(r.push((()=>{E.init(Object.values(o).map((e=>e.script))),A.multiselect.single_click(),C(),t.scripts=!0,be.clear()})),be.add(e))}else if(s.globalhint){const{info_url:e,buttons:t,key:n}=s.options;S(P(s.options,{onclick:e?()=>{f(e,!0)}:null,onbuttonclick:t?e=>{e.id?ee(e.id).always((()=>{sendMessage({method:"clearHint",key:n},(()=>null))})):e.url?f(e.url,!0):console.error("options: no button id or url")}:null}))
}else{const e=me("span","",s.uuid||s.id+s.name);e.textContent=s.name,v=Array(ye),v[3]=e}return v.forEach((e=>{if(e){if(void 0!==s.level&&(e.element||e).setAttribute("style",s.level>n.configMode?Y:""),s.hint){const t=me("span","",s.uuid||s.id+s.name,"hint");t.textContent=s.hint,e.appendChild(t)}s.validation&&Ce(s,e.validation||e.element||e),s.width&&e.setAttribute("class","width-172-"+s.width)}})),i&&(b=i.getAttribute("class"),_=" hide",!1===s.visible?b=(b||"")+_:b&&(b=b.replace(_,"")),
i.setAttribute("class",b)),v},Re=(e,t,n,r)=>{Object.keys(t).forEach((i=>{const s=t[i],o=e?fe("tr","settingstr",s.uuid||s.id+s.name,"pi"):null,a=we(o,s,n,r);a&&a.length&&(e&&e.appendChild(o),z(a,((e,t)=>{let n=e,r="";"Object"===D(e)&&(n=e.element,r=e.style||"");const i=fe("td",r+" settingstd","",s.uuid||s.id+s.name,t);e&&i.appendChild(n),o&&o.appendChild(i)})))}))},Ce=(e,t)=>{let n;if(e.validation){e.validation.url&&(n=function(){f(this.url,!0)});const r=ys.createAfterIcon(e,n)
;r&&(r.url=e.validation?e.validation.url:void 0,t.appendChild(r))}},xe=(e,t)=>{const n=Ie(),r=[];return((e,t)=>{const n=[];let r,i=Ie();const s=e=>{i&&(e&&i.reject(),r&&r.disconnect(),i=null)};try{M(),r=st.connect("exportToJson"),r.onMessage.addListener((e=>{if(i)if(e.partial&&n.push(e.partial),e.done){const e=n.join("");let t;try{t=JSON.parse(e)}catch(e){}Z(),jt.hide(),t?i.resolve(t):i.reject(),s()}else e.error&&s(!0)})),r.onDisconnect.addListener((()=>{s(!0)})),r.postMessage({
method:"exportToJson",ids:e,options:t})}catch(e){console.log("eJ: "+e.message),s(!0)}return i.promise()})(e,t).done((e=>{e.scripts.forEach((e=>{e.uuid&&e.userscript&&!e.system&&!e.nnew&&(e.code&&""!=e.code.trim()?r.push((e=>{const t=e=>{var t;return e.url?{meta:{name:null===(t=mn(e.url))||void 0===t?void 0:t.pathname.split("/").pop(),url:e.url,sri:e.sri,ts:e.ts,mimetype:e.mimetype,modified:e.modified},source:e.data.content}:void 0};return{source:e.code,meta:{name:e.name,uuid:e.uuid,
modified:e.lastModified,file_url:e.file_url&&e.file_url.trim()?e.file_url:void 0},settings:{enabled:e.enabled,position:e.position},resources:e.resources?e.resources.map(t).filter((e=>e)):[],requires:e.requires?e.requires.map(t).filter((e=>e)):[],storage:e.storage||{ts:0,data:{}},options:e.options}})(e)):console.log("options: Strange script: "+e.name))})),n.resolve({scripts:r,global_settings:e.global_settings})})).fail((()=>{n.reject()})),n.promise()};let Ee=()=>{{const e=Ie()
;return Me(["vendor/saveas/filesaver"],(()=>{Ee=Ie.Pledge,e.resolve()})),e.promise()}};const Ge=e=>({get:t=>(e=>Ie((({resolve:t})=>{sendMessage({method:"miscConfig",action:"get",realm:"cloud",key:e},(e=>{t(e.config||{})}))})).promise())(e).then((e=>{if(t){const n={};return t.forEach((t=>{n[t]=e[t]})),n}return e})),set:t=>(qi.emit("credentials",{type:e,credentials:t}),ie(e,t)),delete:t=>ie(e,{[t]:null}),clear:()=>ie(e,null)}),Se=e=>{const t={name:"utils",id:"utils"
},i=me("div",t.name,t.id,"tab_util_h"),s=i.textContent=Rt("Utilities"),o=me("div",t.name,t.id,"tab_util");e.appendTab(t.id,i,o,(e=>{e.then((()=>{_e.set(t.id),document.title=s}))})).show();const a=fe("div","tv_util",t.name,t.id,"tab_util_cont");let l=()=>{((e,t,n)=>{Mi=e=>{let t=Ie();const n=st.connect("tabWatch");return n.onMessage.addListener((e=>{let r;t&&((r=e.tab)?t.notify(r):(t.resolve(),t=null,n.disconnect()))})),n.onDisconnect.addListener((()=>{t&&t.resolve()})),n.postMessage({
method:"tabWatch",url:e.url,filter:e.filter,active:!1}),{promise:t.promise(),close:function(){t&&t.resolve(),n.disconnect()}}},Hi=n,Oi=t})(0,Ge,st.manifest.version),l=null},d={};const A=e=>{let t,n;return t="webdav"==e&&(n=c.cloud_config)?{url:n.url,basic_auth:ne(n.user+":"+n.pass)}:{},d[e]=d[e]||Qi[e](t)},u=()=>xe(null,{storage:c.script_storage,global_settings:c.add_tm_settings,externals:c.include_externals}).then((e=>(jt.wait(),
rr.zip.create(e.scripts,e.global_settings).progress(jt.wait).done((()=>I())).fail((()=>U(Rt("Action_failed"))))))),h=e=>{jt.wait();for(let t,n=0;t=e[n];n++)v(t).then((e=>q({data:e},{reload:!0}))).progress((e=>jt.wait(e))).fail((e=>{U(e||Rt("Unable_to_parse_this_"))})).always(jt.hide)},p=e=>{jt.wait();const n=Y.select.value,r=Y.select.selectedOptions[0].text;l&&l(),(e?Ie.Pledge(e):A(n).list().progress((e=>{e.total>0&&jt.wait(r+": "+Math.floor(e.loaded/e.total*100)+"%")
}))).then((e=>(e=e.filter((e=>e.name.match(/\.zip$/)))).length?e:Ie.Breach("empty"))).then((i=>{const s=Ie(),o=Q(),a=fe("table","nowrap file_select","table",o),l=me("thead","thead",o);a.appendChild(l);const c=me("th","thead_th",o);c.setAttribute("colspan","3"),l.appendChild(c);const d=me("span","thead_span",o);d.textContent=Rt("Please_select_a_file"),c.appendChild(d),i.sort(((e,t)=>e.modified-t.modified)).forEach(((t,i)=>{
const l=It('<input class="button" type="button" value="'+Rt("Import")+'">').on("click",(()=>{s.resolve(t),jt.hideDialog(),jt.wait()})),c=It('<input class="button" type="button" value="'+Rt("Delete")+'">').on("click",(()=>{It(u).hide(),A(n).delete(t).always((()=>{t.name="<deleted>"}))})),d=It('<input class="button" type="button" value="'+Rt("Save_to_disk")+'">').on("click",(()=>{jt.hideDialog(),jt.wait(),A(n).get(t).progress((e=>{e.total>0&&jt.wait(r+": "+Math.floor(e.loaded/e.total*100)+"%")
})).then((e=>{Ee().done((()=>{saveAs(e,V(t.name)),I()})).fail((()=>U(Rt("Action_failed"))))})).always((()=>{jt.hide(),p(e)}))})),u=me("tr","tr",i,o);a.appendChild(u),[t.name,Xi.formatBytes(t.size,2),new Date(t.modified).toLocaleString().replace((new Date).toLocaleDateString(),Rt("Today")),[l,c,d]].forEach(((e,t)=>{const n=me("td","td"+t,i,o);u.appendChild(n),n.appendChild(It("<span>")[Array.isArray(e)?"append":"text"](e).get(0),!0)}))}));const u=()=>{s.reject("cancel"),jt.hideDialog()
},h=It('<input class="button" type="button" value="'+Rt("Cancel")+'">').on("click",u),f=me("tfoot","tfoot",o);a.appendChild(f);const m=me("tr","tfoot_tr",o);f.appendChild(m);const g=me("td","td",t,o);return g.setAttribute("colspan","3"),m.appendChild(g),g.appendChild(h.get(0),!0),window.setTimeout((()=>h.trigger("focus")),100),jt.hide(),jt.dialog(a,u),s.promise()})).then((e=>A(n).get(e).progress((e=>{e.total>0&&jt.wait(r+": "+Math.floor(e.loaded/e.total*100)+"%")
})))).then((e=>h([e]))).fail((e=>{let t;"empty"===e?t=Rt("No_backups_found"):"cancel"==e||("auth_failed"==e?console.warn("cloud: Authentication failed"):t=Rt("Error")+": "+(e||Rt("Action_failed"))),t&&he(t),jt.hide()}))},f=ys.createButton(t.name,t.id+"_i_ta",Rt("Import"),(()=>{q({json:x.value},{reload:!0}).fail((e=>{U(e||Rt("Unable_to_parse_this_"))}))})),m=ys.createButton(t.name,t.id+"_i_cloud",Rt("Show_backups"),(()=>p())),g=ys.createButton(t.name,t.id+"_i_url",Rt("Install"),(()=>(jt.wait(),
q({url:de.value},{reload:!0}).progress((e=>jt.wait(e))).fail((e=>{U(e||Rt("Unable_to_parse_this_"))})).always(jt.hide)))),_=ys.createButton(t.name,t.id+"_e_ta",Rt("Export"),(()=>{jt.wait(),xe(null,{storage:c.script_storage,global_settings:c.add_tm_settings,externals:c.include_externals}).then((e=>rr.json.create(e.scripts,e.global_settings))).done((e=>{x.value=e,I()})).fail((()=>U(Rt("Action_failed")))).always(jt.hide)})),b=ys.createButton(t.name,t.id+"_e_file",Rt("Export"),(()=>{jt.wait(),
xe(null,{storage:c.script_storage,global_settings:c.add_tm_settings,externals:c.include_externals}).then((e=>rr.json.create(e.scripts,e.global_settings).then((e=>Ee().done((()=>{const t=new Blob([e],{type:"text/plain"});saveAs(t,V("tampermonkey-backup-"+Fs+"-"+(new Date).toISOString().replace(/[.:]/g,"-")+".txt")),I()})))))).fail((()=>U(Rt("Action_failed")))).always(jt.hide)})),k=ys.createButton(t.name,t.id+"_e_zip",Rt("Export"),(()=>u().then((e=>{Ee().done((()=>{
saveAs(e,V("tampermonkey-backup-"+Fs+"-"+(new Date).toISOString().replace(/[.:]/g,"-")+".zip"))}))})).always(jt.hide))),y=ys.createButton(t.name,t.id+"_e_zip",Rt("Export"),(()=>{const e=Ie(),t=Y.select.value,n=Y.select.selectedOptions[0].text;var r,i,s;return r=Rt("Name"),i="backup-"+Fs+"-"+(new Date).toISOString().replace(/[.:]/g,"-"),s=r=>{r?e.consume((e=>u().then((r=>(jt.wait(n+"..."),l&&l(),A(t).put(e+".zip",r).progress((e=>{e.total>0&&jt.wait(n+":"+Math.floor(e.loaded/e.total*100)+"%")
}))))).fail((e=>{he(Rt("Error")+": "+(e||Rt("Action_failed")))})).always(jt.hide))(r)):e.reject("aborted")},setTimeout((()=>{const e=prompt(r,i);s&&s(e)}),1),e.promise()})),w=ys.createCheckbox(Rt("Include_TM_settings"),{id:t.id+"_e_export_tm_settings",enabled:"true"},(function(){c.add_tm_settings=this.checked,F&&F.setItem("export_tm_settings",c.add_tm_settings)}));w.elem.setAttribute("style","padding-left: 2px"),w.input.checked=c.add_tm_settings
;const R=ys.createCheckbox(Rt("Include_script_storage"),{id:t.id+"_e_export_storage",enabled:"true"},(function(){c.script_storage=this.checked,F&&F.setItem("export_script_storage",c.script_storage)}));R.elem.setAttribute("style","padding-left: 2px; display: inline"),R.input.checked=c.script_storage;const C=ys.createCheckbox(Rt("Include_script_externals"),{id:t.id+"_e_export_externals",enabled:"true"},(function(){c.include_externals=this.checked,
F&&F.setItem("export_externals",c.include_externals)}));C.elem.setAttribute("style","padding-left: 2px"),C.input.checked=c.include_externals;var x=fe("textarea","importta",t.name,t.id,"ta");x.setAttribute("spellcheck","false");const E=fe("div","section",t.name,t.id,"ta"),G=fe("div","section_head",t.name,t.id,"head_ta"),Z=fe("div","section_content",t.name,t.id,"content_ta");G.textContent=Rt("General"),Z.appendChild(R.elem),Z.appendChild(w.elem),Z.appendChild(C.elem),E.appendChild(G),
E.appendChild(Z);const S=fe("div","section",t.name,t.id,"ta"),B=fe("div","section_head",t.name,t.id,"head_ta"),T=fe("div","section_content",t.name,t.id,"content_ta");B.textContent=Rt("TextArea"),T.appendChild(_),T.appendChild(f),T.appendChild(x),S.appendChild(B),S.appendChild(T);const M=fe("div","section",t.name,t.id,"sb"),j=fe("div","section_head",t.name,t.id,"head_sb"),L=fe("div","section_content",t.name,t.id,"content_sb");j.textContent=Rt("Zip"),M.appendChild(j),M.appendChild(L),
L.appendChild(k);const O=fe("div","section",t.name,t.id,"sb"),D=fe("div","section_head",t.name,t.id,"head_sb"),P=fe("div","section_content",t.name,t.id,"content_sb"),N=F,H=function(){It(ee).toggle("webdav"===this.value);const e=A(this.value);It(te).toggle(e&&!!e.revoke),N&&N.setItem("cloud_type",this.value)};let X;N&&(X=N.getItem("cloud_type"));var Y=ys.createDropDown(Rt("Type"),{id:"cloud_type",value:X||"drive"},[{name:Rt("Google_Drive"),value:"drive"},{name:Rt("Dropbox"),value:"dropbox"},{
name:Rt("OneDrive"),value:"onedrive"},{name:Rt("Yandex_Disk"),value:"yandex"},{name:Rt("WebDAV"),value:"webdav"}].filter((e=>e)),H);const W=e=>function(){let t;d={},(c.cloud_config=c.cloud_config||{})[e]=this.value,(t=F)&&t.setItem("cloud_config",ne(JSON.stringify(c.cloud_config)))},J=ys.createInput(Rt("URL"),{id:"select_cloud_url",uuid:t.uuid,value:(c.cloud_config?c.cloud_config.url:null)||""},W("url")),K=ys.createInput(Rt("Login"),{id:"select_cloud_username",uuid:t.uuid,
value:(c.cloud_config?c.cloud_config.user:null)||""},W("user")),$=ys.createInput(Rt("Password"),{id:"select_cloud_password",uuid:t.uuid,password:!0,value:(c.cloud_config?c.cloud_config.pass:null)||""},W("pass")),ee=[J.elem,K.elem,$.elem];r.push((()=>{H.apply(Y.select,[])})),D.textContent=Rt("Cloud"),O.appendChild(D),O.appendChild(P),P.appendChild([Y.elem].concat(ee)),P.appendChild(y),P.appendChild(m);const te=ys.createButton("revoke_token_button",t.uuid,Rt("Revoke_Access_Token"),(()=>{l&&l(),
It(te).prop("disabled",!0);const e=A(Y.select.value);jt.wait(),e.revoke().then((()=>Pe(15e3))).done((()=>I())).fail((()=>U(Rt("Action_failed")))).always((()=>{jt.hide(),It(te).prop("disabled",!1)}))}));P.appendChild(te);const re=fe("div","section",t.name,t.id,"fi"),ie=fe("div","section_head",t.name,t.id,"head_fi"),se=fe("div","section_content",t.name,t.id,"content_fi");ie.textContent=Rt("File"),re.appendChild(ie),re.appendChild(se),se.appendChild(b);const oe=ys.createFileSelect("",{name:"file",
id:t.id},h),ae=fe("div","section",t.name,t.id,"ifi"),le=fe("div","section_head",t.name,t.id,"head_ifi"),ce=fe("div","section_content",t.name,t.id,"content_ifi");ce.appendChild(oe.elem),le.textContent=Rt("Import_from_file"),ae.appendChild(le),ae.appendChild(ce);const de=fe("input","updateurl_input",t.name,t.id,"url");de.setAttribute("type","text"),de.setAttribute("spellcheck","false")
;const Ae=fe("div","section",t.name,t.id,"ur"),ue=fe("div","section_head",t.name,t.id,"head_ur"),pe=fe("div","section_content",t.name,t.id,"content_ur");ue.textContent=Rt("Import_from_URL"),Ae.appendChild(ue),Ae.appendChild(pe),pe.appendChild(de),pe.appendChild(g),a.appendChild(E),"undefined"!=typeof Blob&&a.appendChild(O),a.appendChild(ae),a.appendChild(M),"undefined"!=typeof Blob&&a.appendChild(re),a.appendChild(S),a.appendChild(Ae),z([S],(e=>{const t=" hide";let r=e.getAttribute("class")
;n.configMode<50?r+=t:r=r.replace(t,""),e.setAttribute("class",r)})),o.appendChild(a)},Be=e=>{Re(null,e,null,d),jt.hide(),ve()},Te=(e,t,n,r)=>{void 0===r&&(r={});const i=t.item,s=i.uuid+(r.orig||"")+t.id,o=(r.orig?"orig_":"use_")+t.id,a=e=>"select_"+Ae(e,i.uuid)+"_sel1",l=fe("div","cludes",e,s,"cb1"),c=me("span",e,s,"cb2");if(r.orig){const n=function(){if("checkbox"==this.type){const e={};e[this.key]=this.checked,le(this.uuid,e)}
},r="merge_"+t.id,s=!!(i.options&&i.options.override&&i.options.override[r]),o=ys.createCheckbox(e,{id:r,uuid:i.uuid,enabled:s},n);c.appendChild(o.elem)}else c.textContent=e;const d=fe("select","cludes",o,i.uuid,"sel1");d.innerHTML="",d.setAttribute("size","6"),d.setAttribute("multiple","true");for(let e=0;e<n.length;e++){const t=document.createElement("option");t.value=t.text=n[e],d.appendChild(t)}l.appendChild(c),i.desc&&ys.setHelp(i.desc,l,c,i),l.appendChild(d);const A=e=>{
const t=[],n=e&&e.options;for(let e=0,r=n.length;e<r;e++)n[e].selected&&t.push(n[e]);return t},u=()=>{const e=a("use_"+("excludes"==t.id?"includes":"excludes")),n=document.getElementById(e),r=A(d);let i=!1;const s="matches"==t.id;r.forEach((e=>{const t=s?"/"+on(e.value,!0)+"/":e.value;let r;e&&!n.querySelector('option[value="'+t+'"]')&&(r=e.cloneNode(!0),s&&(r.value=t,r.textContent=t),n.appendChild(r),i=!0)})),i&&g()},h=()=>{const e=prompt(Rt("Enter_the_new_rule"));if(e){
const t=document.createElement("option");t.value=t.text=e.trim(),d.appendChild(t),g()}},p=()=>{const e=d.options[d.selectedIndex];if(!e)return;const t=prompt(Rt("Enter_the_new_rule"),e.value);t&&(e.value=e.text=t.trim(),g())},f=()=>{const e=A(d);let t=!1;e.forEach((e=>{e&&(e.parentNode.removeChild(e),t=!0)})),t&&g()},m=e=>{const t=[];for(let n=0;n<e.options.length;n++)t.push(e.options[n].value);return t};var g=()=>{const e={includes:m(document.getElementById(a("use_includes"))),
matches:m(document.getElementById(a("use_matches"))),excludes:m(document.getElementById(a("use_excludes"))),connects:m(document.getElementById(a("use_connects"))),temp_connects:m(document.getElementById(a("use_temp_connects"))),blockers:m(document.getElementById(a("use_blockers")))};return le(i.uuid,e),!0};if(r.other_name){const t=me("button",e,s,"btn1",!0);t.textContent=Rt("Add_as_0clude0",r.other_name),t.addEventListener("click",u,!1),l.appendChild(t)}else if(!r.orig){
const t=me("button",e,s,"btn2",!0);t.textContent=Rt("Add")+"...",t.addEventListener("click",h,!1),l.appendChild(t);const n=me("button",e,s,"btn3",!0);n.textContent=Rt("Edit")+"...",n.addEventListener("click",p,!1),l.appendChild(n);const r=me("button",e,s,"btn4",!0);r.textContent=Rt("Remove"),r.addEventListener("click",f,!1),l.appendChild(r)}return{elem:l}},Ue=(e,t,n,r)=>{const i=t.item,s=i.uuid+t.id,o="permission_"+t.id,a=fe("div","permissions",e,s,"cb1"),l=me("span",e,s,"cb2");l.textContent=e
;const c=fe("select","permissions",o,i.uuid,"sel1");c.innerHTML="",c.setAttribute("size","6"),c.setAttribute("multiple","true");for(let e=0;e<n.length;e++){const t=document.createElement("option");t.value=t.text=n[e],c.appendChild(t)}a.appendChild(l),i.desc&&ys.setHelp(i.desc,a,l,i),a.appendChild(c);const d=(e,t)=>{const n=[],r=e&&e.options;for(let e=0,i=r.length;e<i;e++)void 0!==t&&r[e].selected!==t||n.push(r[e]);return n},A=e=>{const t=e.reduce(((e,t)=>{const n=t.value;return n&&(e[n]=!0),e
}),{}),n=Object.keys(t);n.length&&r(n)},u=me("button",e,s,"btn1",!0);u.textContent=Rt("Grant_selected"),u.addEventListener("click",(()=>{const e=d(c,!0);A(e)}),!1),a.appendChild(u);const h=me("button",e,s,"btn2",!0);h.textContent=Rt("Grant_all"),h.addEventListener("click",(()=>{const e=d(c);A(e)}),!1),a.appendChild(h);const p=me("button",e,s,"btn3",!0);return p.textContent=Rt("Reset_list"),p.addEventListener("click",(()=>{r(null)}),!1),a.appendChild(p),{elem:a,select:c,buttons:[u,h,p]}
},Fe=e=>e.homepage?e.homepage:e.namespace&&0==e.namespace.search(/https?:\/\//)?e.namespace:null,je=e=>({code:(e.code?e.code:e).length,requires:(e.requires||[]).reduce(((e,{data:t})=>e+(t&&t.content?t.content:t).length||0),0),resources:(e.resources||[]).reduce(((e,{data:t})=>e+(t&&t.content?t.content:t).length||0),0)}),Le=e=>{const t="http://"+e+"/",r="chrome://favicon/"+t;let i
;return i=0==t.indexOf("http://userscripts.org/")||0==t.indexOf("http://userscripts.com/")?Is.origin("uso"):"native"==n.favicon_service?t+"favicon.ico":"duckduckgo"==n.favicon_service?"https://icons.duckduckgo.com/ip2/"+encodeURIComponent(e)+".ico":"https://www.google.com/s2/favicons?sz=64&domain="+encodeURIComponent(e),[i,r]},Oe=(e,t)=>{const r=me("div",e.uuid,"script_setting_h"),i=me("div",e.uuid,"script_settings_c");r.textContent=Rt("Settings");const s=function(){const e={}
;if("checkbox"==this.type)e[this.key]=!!this.checked,le(this.uuid,e);else if("button"==this.type)e[this.key]=!this.oldvalue,le(this.uuid,e);else if("select-one"==this.type){let t=ys.getValue(this);this.array&&null!==t&&(t=t.split(",").map((e=>e.trim())).filter((e=>e))),e[this.key]=t,le(this.uuid,e)}else if("text"==this.type||"textarea"==this.type){const t=ys.getValue(this);e[this.key]=t,le(this.uuid,e)}},o=ys.createCheckbox(Rt("Enabled"),{id:"enabled",uuid:e.uuid,name:"upd",enabled:e.enabled
},s).elem,a=ys.createPosition(Rt("Position_")+": ",{id:"position",uuid:e.uuid,name:"pos",pos:e.position,posof:e.positionof},s),l=ys.createDropDown(Rt("Run_at"),{id:"run_at",uuid:e.uuid,name:"run-at",value:e.options.run_at},[{name:"document-start",value:"document-start"},{name:"document-body",value:"document-body"},{name:"document-end",value:"document-end"},{name:"document-idle",value:"document-idle"},{name:"context-menu",value:"context-menu"},{name:Rt("Default"),value:null
}],s),c=ys.createDropDown(Rt("No_frames"),{id:"noframes",uuid:e.uuid,name:"no_frames",value:e.options.noframes},[{name:Rt("Yes"),value:!0},{name:Rt("No"),value:!1},{name:Rt("Default"),value:null}],s),d=ys.createDropDown(Rt("Run_in"),{id:"run_in",uuid:e.uuid,name:"run_in",array:!0,value:e.options.run_in},[{name:Rt("Incognito_tabs"),value:["incognito-tabs"]},{name:Rt("Normal_tabs"),value:["normal-tabs"]},{name:Rt("All_tabs"),value:[]},{name:Rt("Default"),value:null
}],s),A=ys.createCheckbox(Rt("Check_for_Updates"),{id:"check_for_updates",uuid:e.uuid,name:"upd",enabled:e.options.check_for_updates});if(e.options.user_modified){const t=Rt("This_script_has_local_modifications_and_needs_to_be_updated_manually");ys.setHelp(t,A.elem,A.span,e,"critical")}const u=ys.createInput(Rt("Update_URL_"),{id:"file_url",uuid:e.uuid,name:"uu",value:e.file_url});u.input.setAttribute("class","updateurl_input"),u.elem.setAttribute("class","updateurl"),
e.is_external&&u.input.setAttribute("readonly","true");const p=t=>(e.options&&e.options.override?e.options.override[t]:null)||[],f=Te(Rt("Original_includes"),{id:"includes",item:e},p("orig_includes"),{orig:!0,other_name:Rt("User_excludes")}),m=Te(Rt("Original_matches"),{id:"matches",item:e},p("orig_matches"),{orig:!0,other_name:Rt("User_excludes")}),g=Te(Rt("Original_excludes"),{id:"excludes",item:e},p("orig_excludes"),{orig:!0,other_name:Rt("User_includes")
}),_=fe("div","clear",e.uuid,"clear"),b=Te(Rt("User_includes"),{id:"includes",item:e},p("use_includes")),v=Te(Rt("User_matches"),{id:"matches",item:e},p("use_matches")),k=Te(Rt("User_excludes"),{id:"excludes",item:e},p("use_excludes")),y=Te(Rt("Original_domain_whitelist"),{id:"connects",item:e},e.connects,{orig:!0}),w=Te(Rt("Temporary_domain_whitelist"),{id:"temp_connects",item:e},e.temp_connects),R=Te(Rt("User_domain_whitelist"),{id:"connects",item:e
},p("use_connects")),C=Te(Rt("User_domain_blacklist"),{id:"blockers",item:e},p("use_blockers")),x=[ys.createCheckbox(Rt("Apply_compatibility_options_to_required_script_too"),{id:"compatopts_for_requires",uuid:e.uuid,name:"",enabled:e.options.compatopts_for_requires},s),ys.createCheckbox(Rt("Fix_wrappedJSObject_property_access"),{id:"compat_wrappedjsobject",uuid:e.uuid,name:"",enabled:e.options.compat_wrappedjsobject
},s),ys.createCheckbox(Rt("Convert_CDATA_sections_into_a_chrome_compatible_format"),{id:"compat_metadata",uuid:e.uuid,name:"",enabled:e.options.compat_metadata},s),ys.createCheckbox(Rt("Replace_for_each_statements"),{id:"compat_foreach",uuid:e.uuid,name:"",enabled:e.options.compat_foreach},s),ys.createDropDown(Rt("Add_GM_functions_to_this_or_window"),{id:"compat_powerful_this",uuid:e.uuid,name:"",value:e.options.compat_powerful_this,
desc:Rt("Enabling_this_makes_it_easy_for_scripts_to_leak_it_s_granted_powers_to_the_page_Therefore__off__is_the_safest_option_",Rt("Off"))},[{name:Rt("Auto"),value:null},{name:Rt("On"),value:!0},{name:Rt("Off"),value:!1}],s)],E=fe("div","section",e.uuid,"ta_opt"),G=fe("div","section_head",e.uuid,"head_ta_opt"),Z=fe("div","section_content",e.uuid,"content_ta_opt");G.textContent=Rt("General"),E.appendChild(G),E.appendChild(Z)
;const S=fe("div","section",e.uuid,"ta_upd"),B=fe("div","section_head",e.uuid,"head_ta_upd"),I=fe("div","section_content",e.uuid,"content_ta_upd");B.textContent=Rt("Updates"),S.appendChild(B),S.appendChild(I);const T=fe("div","section",e.uuid,"ta_cludes"),U=fe("div","section_head",e.uuid,"head_ta_cludes"),F=fe("div","section_content",e.uuid,"content_ta_cludes");U.textContent=Rt("Includes_Excludes"),T.appendChild(U),T.appendChild(F)
;const M=fe("div","section",e.uuid,"ta_security"),j=fe("div","section_head",e.uuid,"head_ta_security"),L=fe("div","section_content",e.uuid,"content_ta_security");j.textContent=Rt("XHR_Security"),M.appendChild(j),M.appendChild(L);const O=fe("div","section",e.uuid,"ta_compat"),D=fe("div","section_head",e.uuid,"head_ta_compat"),z=fe("div","section_content",e.uuid,"content_ta_compat");D.textContent=Rt("GM_compat_options_"),O.appendChild(D),O.appendChild(z),Z.appendChild(o),Z.appendChild(a),
Z.appendChild(l.elem),Z.appendChild(c.elem),Z.appendChild(d.elem);const P=ys.createButton("save_update_button",e.uuid,Rt("Save"),(()=>{s.apply(A.input,[]),e.is_external||s.apply(u.input,[])})),N=ys.createButton("start_update_button",e.uuid,Rt("Check_for_userscripts_updates"),(()=>h(e.uuid,"scriptUpdate")));I.appendChild([A.elem,u.elem,P,N]),F.appendChild([f.elem,m.elem,g.elem,_,b.elem,v.elem,k.elem]);const V=[y.elem,w.elem,R.elem,C.elem];It(V).addClass("domain"),L.appendChild(V)
;for(let e=0;e<x.length;e++)z.appendChild(x[e].elem);const Q={name:"",uuid:e.uuid,id:"comment",value:e.options.comment},H=ys.createTextarea(null,Q);H.elem.setAttribute("class","script_setting_wrapper");const q=me("div",e.uuid,"save"),X=ys.createButton("save_button",e.uuid,Rt("Save"),(()=>{s.apply(H.textarea,[])}));q.appendChild(X);const Y=fe("div","section",e.uuid,"ta_comment"),W=fe("div","section_head",e.uuid,"head_ta_comment"),J=fe("div","section_content",e.uuid,"content_ta_comment");let $
;if(W.textContent=Rt("Comment"),Y.appendChild(W),Y.appendChild(J),J.appendChild(H.elem),J.appendChild(q),n.tags_enabled){$=fe("div","section scriptsettings",e.uuid,"ta_tags");const t={name:"",uuid:e.uuid,id:"tags"};let r={};const i=n.tags||{};e.options.tags.forEach((e=>{const t=i[e];r[e]={...t||{},disabled:t?void 0:Rt("This_tag_is_not_part_of_the_system_tag_list_"),add:!t}}));const s=ys.createTagEditor(Rt("Tags"),t,r,{tagsToAdd:Object.keys(i).filter((e=>!r[e])),onTagAdd:t=>{const i=n.tags||{}
;i[t]||(i[t]={},K("tags",i),le(e.uuid,{tags:Object.keys(r)}))},onChange:t=>{le(e.uuid,{tags:Object.keys(t)})}}),o=fe("div","section_head",e.uuid,"head_ta_tags"),a=fe("div","section_content",e.uuid,"content_ta_tags");o.textContent=Rt("Script_Tags"),$.appendChild(o),$.appendChild(a),a.appendChild(s.elem)}const ee=fe("div","section",e.uuid,"ta_det"),te=fe("div","section_head",e.uuid,"head_ta_det"),ne=fe("div","section_content",e.uuid,"content_ta_det");te.textContent=Rt("Details"),
ee.appendChild(te),ee.appendChild(ne),te.textContent=Rt("Details");const re=fe("table","script_details",e.uuid,"script_details");[{label:Rt("Size"),value:Xi.formatBytes((e.code?e.code:e).length,2)},{label:Rt("Last_updated"),value:new Date(e.lastModified||e.lastUpdated).toLocaleString()},{label:Rt("UUID"),value:e.uuid}].forEach((t=>{const n=fe("tr","external_desc",e.uuid,t.label,"tr"),r=fe("td","external_desc",e.uuid,t.label,"td1"),i=fe("td","",e.uuid,t.label,"td2");r.textContent=t.label,
i.textContent=t.value,n.appendChild(r),n.appendChild(i),re.appendChild(n)})),ne.appendChild(re),i.appendChild([ee,E,S,...n.tags_enabled?[$]:[],T,M,O,Y]);const ie=t.appendTab("settings",r,i,(e=>{e.then((()=>{_e.setSub("settings")}))}));return _e.registerListener(e.uuid,"settings",(()=>{ie.select()})),{}},De=(e,t,n,r)=>{const i=fe("table","externals",e.uuid,"outer_req2html",t,!0);let s=0;const o=[{label:Rt("URL"),prop:"display_url"},{label:Rt("Size"),prop:"data",fn:function(e){let t="?"
;return e&&(void 0!==e.length?t=e.length:void 0!==e.content&&(t=e.content.length)),Xi.formatBytes(t,2)}},{label:Rt("MIME_Type"),prop:"mimetype"},{label:Rt("Subresource_Integrity"),prop:"sri",fn:function(e){return e?e.type+"="+e.value:Rt("_not_set_")}},{label:Rt("Last_updated"),prop:"ts",fn:function(e){return e?new Date(e).toString():null}}];if(o.push({label:Rt("User_modified"),prop:"modified",klass:"validation",
title:Rt("This_external_resource_will_not_auto_update__Please_delete_it_in_order_to_enable_updates_again_"),icon:Is.get("info"),fn:function(e){return e?Rt("Yes"):null}}),e[t].forEach((a=>{const l=s+Ae(a.url)+t;o.forEach((t=>{let n;const r=fe("tr","",e.uuid,t.prop,"tr"+l),s=fe("td","external_label",e.uuid,t.prop,"td1"+l),o=fe("td","external_desc",e.uuid,t.prop,"td2"+l);if(s.textContent=t.label,o.textContent=n=(t.fn?t.fn:e=>e)(a[t.prop]),null!==n){if(t.klass&&It(o).addClass(t.klass),
t.title&&o.setAttribute("title",t.title),t.icon){const n=fe("span","validation",e.uuid,"validation","span"+l);n.appendChild(ys.createIcon(t.icon+" red","warning",e.uuid,l+"no_require_update_warning")),o.appendChild(n)}r.appendChild(s),r.appendChild(o),i.appendChild(r)}}));const c=fe("tr","external_desc_buttons",e.uuid,a.url,"tr"+l),d=fe("td","",e.uuid,"buttons","td"+l);if(c.appendChild(d),i.appendChild(c),a.ts){if("requires"==t||a.viewable){
const t=me("span",e.uuid,"edit_external"+l),i=ys.createImageTextButton(e.uuid,"edit_external"+l,a.editable?Rt("Edit"):Rt("View"),"edit",(()=>{ze(n,e.uuid,a,r)}));t.appendChild(i),d.appendChild(t)}const i=me("span",e.uuid,"update_external"+l),s=ys.createImageTextButton(e.uuid,"update_external"+l,Rt("Update"),"update",(()=>{((e,t)=>{try{M(),sendMessage({method:"buttonPress",name:"externals_update",scriptuid:e,safe_url:t},(e=>{Z(),I(),e.items?N(e.items,!1):jt.hide()}))}catch(e){
console.log("dEx: "+e.message)}})(e.uuid,a.url),s.parentNode&&s.parentNode.removeChild(s)}));i.appendChild(s),d.appendChild(i);const o=me("span",e.uuid,"delete_external"+l),c=ys.createImageTextButton(e.uuid,"delete_external"+l,Rt("Delete"),"delete",(()=>{((e,t)=>{try{M(),sendMessage({method:"buttonPress",name:"externals_delete",scriptuid:e,safe_url:t},(e=>{Z(),I(),e.items?N(e.items,!1):jt.hide()}))}catch(e){console.log("dEx: "+e.message)}})(e.uuid,a.url),c.parentNode&&c.parentNode.removeChild(c)
}));o.appendChild(c),d.appendChild(o)}s++})),!s){const t=fe("tr","script_desc",e.uuid,s,"tr"),n=fe("td","script_desc",e.uuid,s,"td1");n.textContent=Rt("No_entry_found"),t.appendChild(n),i.appendChild(t)}return i},ze=(e,t,n,r)=>{const s="externals-"+t+"-"+n.url;A[s]={};const o=Ve({uuid:s,script_uuid:t,readonly:!n.editable,mimetype:n.mimetype,name:Rt("Externals")+" - "+n.url,file_url:n.url,referrer:"options.scripts.userscripts"},e,(()=>{r(o.getEditor())}),(()=>{delete i["tab"+s],
delete i["editor"+s],delete A[s]}));o.show()},Ne=(e,t,r)=>{const s=fe("tr","editor_container p100100",e.uuid,"container"),a=r.mimetype||"text/javascript",l=!a||/(application|text)\/(?:x-)?(?:java|ecma)script/.test(a);if(!e.nnew&&h(e.uuid,"lastI"))return i["editor"+e.uuid]||{};e.nnew&&(e.code=(e=>{const t=ge.url?re(ge.url):null;return e.replace("<$URL$>",t||"http://*/*").replace("<$ICON$>",t?Le(mn(t).domain)[0]:Qt.images.empty).replace("<$DATE$>",(new Date).toISOString().split("T")[0])
})(n.script_templates[0].value));const c=me("div",e.uuid,"script_editor_h"),d=c.inserted,u=me("div",e.uuid,"script_editor_c"),p=fe("tr","editormenubar",e.uuid,"container_menu"),f=fe("tr","editorbuttonbar",e.uuid,"container_button_menu"),m=fe("table","editor_container_o p100100 noborder",e.uuid,"container_o");m.appendChild(f),m.appendChild(p),m.appendChild(s),u.appendChild(m);const g=()=>{let t=!1
;return s.editor&&(n.editor_enabled?t|=s.editor.changed&&s.editor.mirror.historySize().undo:t=s.editor.value!=e.code),t},_=()=>{Ts.commands.trimTrailingSpacesIfEnabled(s.editor.mirror),h(e.uuid,"saveEm")},b=(e,t,n)=>{r.do_close&&r.do_close(t,n)},v=t=>{if((t||confirm(Rt("Really_reset_all_changes_")))&&s.editor){const t=e.code||"";n.editor_enabled?(s.editor.mirror.setValue(t),k(!1)):s.editor.value=t}},k=e=>{s.editor.changed!=e&&(r.set_tab_class&&r.set_tab_class("modified",e),
H.toggleClass("modified",e)),s.editor.changed=e},y=()=>{window.setTimeout((()=>{let e;n.editor_enabled&&(e=s.editor)&&(jt.wait(),window.setTimeout((()=>{(()=>{const t=e.mirror.performLint(!0);return t&&t.then?t:Ie.Pledge(t)})().then((()=>{const t=e.mirror.state.lint?e.mirror.state.lint.marked:null;if(t&&t.length){1===t.length?U(Rt("One_error_or_hint_was_found_")):U(Rt("0count0_errors_or_hints_were_found_",t.length));for(let n,r=0;n=t[r];r++){let t,r,i
;if(void 0!==n.line&&void 0!==n.column?(r=n.line,i=n.column):n.lines&&n.lines.length&&(t=n.lines[0],r=t.lineNo()+1,i=t.markedSpans&&t.markedSpans.length?t.markedSpans[0].from+1:1),r&&i){e.mirror.setCursor(r-1,i-1),e.mirror.focus();break}}}else T(Rt("No_syntax_errors_were_found_"));jt.hide()}))}),100))}),0)},w=fe("td","",e.uuid,"editor_buttonmenu_td");f.appendChild(w);const R=fe("ul","editormenu",e.uuid,"editormenu"),C=fe("td","",e.uuid,"editormenu_td");let x,E;C.appendChild(R);const G=(e,t)=>{
let n,r;e&&(n=It(e),r=!!t&&n.hasClass("visible")),It("ul.editormenu .submenu").removeClass("visible"),e?(n.toggleClass("visible",!r),E=r?null:e):E=null,e&&r||(x=e=>{let t;x&&(t=It(e.target))&&!t.closest(".editormenu").length&&(G(),document.body.removeEventListener("click",x),x=null)},document.body.addEventListener("click",x))},S=(t,n)=>{const r=fe("li","entry",e.uuid,t,"editormenuentry",!0),i=me("label",e.uuid,t,"editormenulabel_id");i.textContent=n
;const s=fe("table","submenu noborder",e.uuid,"editormenucontent"+t);return r.appendChild([i,s]),r.addEventListener("click",(e=>{It(e.target).closest(".entry").hasClass("list")||G(s,!0)})),r.addEventListener("mousemove",(function(){E&&E!=this&&G(s)})),{elem:r,content:s}},B=e=>()=>{s.editor.mirror.focus(),s.editor.mirror.execCommand(e)},F={file:{text:Rt("File"),entries:[].concat(e.readonly?[]:[{text:Rt("Save"),command:"save",image:Is.get("filesave"),cb:_},{text:Rt("Save_to_disk"),
command:"save_to_disk",image:Is.get("save_to_disk"),cb:()=>{h(e.uuid,"saveToDisk")}},{type:"sep"},{command:"cancel",text:Rt("Editor_reset"),image:Is.get("editor_cancel"),cb:v}]).concat(e.nnew||e.is_external?[]:[{text:Rt("Check_for_Updates"),command:"update",image:Is.get("update"),disabled:(!e.options.check_for_updates||!e.file_url||"none"==e.file_url)&&Rt("Update_check_is_disabled"),cb:()=>{g()&&!confirm(Rt("Really_reset_all_changes_"))||(v(!0),h(e.uuid,"scriptUpdate"))}},{text:Rt("Delete"),
command:"remove",image:Is.get("delete"),cb:()=>{b(0,!0,!1),A[e.uuid].deleteScript()}}]).concat([{type:"sep"},{text:Rt("Close"),command:"close",image:Is.get("exit"),cb:b}])},edit:e.readonly||!n.editor_enabled?void 0:{text:Rt("Edit"),entries:[{text:Rt("Undo"),command:"undo"},{text:Rt("Redo"),command:"redo"},{type:"sep"},{text:Rt("Select_All"),command:"selectAll"},{type:"sep"},{text:Rt("Toggle_Comment"),command:"toggleComment"},{text:Rt("Toggle_Comment_Indented"),command:"toggleCommentIndented"},{
text:Rt("Toggle_Block_Comment"),command:"toggleBlockComment",modes:["js"]},{type:"sep"},{id:"edit_lines",text:Rt("Lines_Menu"),modes:["js"],entries:[{text:Rt("Indent"),command:"intelligentTab"},{text:Rt("Indent_Less"),command:"indentLess"},{text:Rt("Indent_More"),command:"indentMore"},{text:Rt("Auto_Indent_all"),command:"reindentall"},{type:"sep"},{text:Rt("Move_Line_Up"),command:"swapLineUp"},{text:Rt("Move_Line_Down"),command:"swapLineDown"},{text:Rt("Duplicate_Lines"),command:"duplicateLine"
},{text:Rt("Delete_Line"),command:"deleteLine"},{text:Rt("Join_Lines"),command:"joinLines"},{type:"sep"},{text:Rt("Insert_Line_Before"),command:"insertLineBefore"},{text:Rt("Insert_Line_After"),command:"insertLineAfter"}]},{id:"edit_text",text:Rt("Text"),entries:[{text:Rt("Upper_Case"),command:"upcaseAtCursor"},{text:Rt("Lower_Case"),command:"downcaseAtCursor"},{type:"sep"},{text:Rt("Delete_Line_Left"),command:"delLineLeft"},{text:Rt("Delete_Line_Right"),command:"delLineRight"},{
text:Rt("Delete_to_Previous_Word_Boundary"),command:"delGroupBefore"},{text:Rt("Delete_to_Next_Word_Boundary"),command:"delGroupAfter"},{text:Rt("Delete_Line"),command:"deleteLine"},{type:"sep"},{text:Rt("Transpose"),command:"transposeChars"}]},{id:"edit_sort",text:Rt("Sort"),entries:[{text:Rt("Lines"),command:"sortLines"},{text:Rt("Line_Case_Insensitive"),command:"sortLinesInsensitive"}]},{id:"edit_folding",text:Rt("Folding"),entries:[{text:Rt("Fold"),command:"fold"},{text:Rt("Unfold"),
command:"unfold"},{text:Rt("Fold_All"),command:"foldAll"},{text:Rt("Unfold_All"),command:"unfoldAll"}]},{id:"edit_sublime_mark",text:Rt("Sublime_Mark"),entries:[{text:Rt("Set_Sublime_Mark"),command:"setSublimeMark"},{text:Rt("Select_to_Sublime_Mark"),command:"selectToSublimeMark"},{text:Rt("Swap_with_Sublime_Mark"),command:"swapWithSublimeMark"},{text:Rt("Delete_to_Sublime_Mark"),command:"deleteToSublimeMark"},{text:Rt("Yank_Sublime_Mark"),command:"sublimeYank"}]}]},
selection:e.readonly||!n.editor_enabled?void 0:{text:Rt("Selection"),entries:[{text:Rt("Incremental_Find"),command:"selectMatchingPartsOfCurrentSelection"},{text:Rt("Split_into_Lines"),command:"splitSelection"},{type:"sep"},{text:Rt("Incremental_Find"),command:"selectMatchingPartsOfCurrentSelection"},{type:"sep"},{text:Rt("Select_Line"),command:"selectLine"},{text:Rt("Select_Scope"),command:"selectScope"},{text:Rt("Select_Bookmarks"),command:"selectBookmarks"},{
text:Rt("Select_between_Brackets"),command:"selectBetweenBrackets"},{text:Rt("Select_Next_Occurrence"),command:"selectNextOccurrence"},{text:Rt("Select_All_Occurrences"),command:"selectAllOccurrences"}]},find:n.editor_enabled?{text:Rt("Find"),entries:[{text:Rt("Find"),command:"find"},{text:Rt("Replace"),command:"replace"},{text:Rt("Replace_All"),command:"replaceAll"},{type:"sep"},{text:Rt("Find_Next"),command:"findNext"},{text:Rt("Find_Previous"),command:"findPrev"},{text:Rt("Find_Under"),
command:"findUnder"},{text:Rt("Find_All_Under"),command:"findAllUnder"},{text:Rt("Find_Under_Previous"),command:"findUnderPrevious"},{type:"sep"},{text:Rt("Incremental_Find"),command:"selectMatchingPartsOfCurrentSelection"}]}:void 0,goto:n.editor_enabled?{text:Rt("GoTo"),entries:[{text:Rt("Jump_to_line"),command:"jump"},{text:Rt("Document_Start"),command:"goDocStart"},{text:Rt("Document_End"),command:"goDocEnd"},{text:Rt("Group_Left"),command:"goGroupLeft"},{text:Rt("Group_Right"),
command:"goGroupRight"},{text:Rt("Closing_Bracket"),command:"goToBracket"},{text:Rt("Line_Up"),command:"goLineUp"},{text:Rt("Line_Down"),command:"goLineDown"},{type:"sep"},{text:Rt("Center_Cursor"),command:"showInCenter"},{type:"sep"},{id:"bookmarks",text:Rt("Bookmarks"),entries:[{text:Rt("Toggle"),command:"toggleBookmark"},{text:Rt("Clear_All"),command:"clearBookmarks"},{type:"sep"},{text:Rt("Next_Bookmark"),command:"nextBookmark"},{text:Rt("Prev_Bookmark"),command:"prevBookmark"}]}]}:void 0,
dev:e.readonly||!n.editor_enabled?void 0:{text:Rt("Developer"),entries:[{command:"macro",text:Rt("Insert_constructor"),modes:["js"]}].concat(!e.system&&n.editor_enabled&&l?[{type:"sep"},{text:Rt("Run_syntax_check"),command:"lint_script",image:Is.get("check")},{text:Rt("Auto_Indent_all"),command:"reindentall"}]:[]).concat(e.nnew||e.is_external?[]:[{type:"sep"},{text:Rt("Full_reset"),command:"reset",image:Is.get("script_cancel"),cb:()=>{
confirm(Rt("Really_factory_reset_this_script_"))&&A[e.uuid].fullReset((e=>{e.cleaned&&b(0,!0,!1)}))}}]).concat(e.nnew?[{type:"sep"},{id:"templates",text:Rt("Templates"),entries:n.script_templates.map((e=>({text:e.name,command:e.name,image:Is.get("dot"),cb:function(){if(g()&&!confirm(Rt("Really_reset_all_changes_")))return;const t=e.value||"";s.editor&&(n.editor_enabled?(s.editor.mirror.setValue(t),k(!1)):s.editor.value=t)}})))}]:[])}},j=(e,t)=>{const n=t[0];let r,i
;return(r=e[n])?j(r,t.slice(1)):(e.entries&&e.entries.some((e=>e.command==n?(i=e,!0):!(!e.entries||!(i=j(e,t)))||void 0)),i)};["file.save_to_disk","file.save","file.cancel","dev.reset","find.findNext","find.findPrev","edit.replace","goto.jump","edit.reindentall","dev.lint_script","file.close"].forEach((t=>{const n=j(F,t.split("."))
;n&&(n.image?w.appendChild(ys.createImageButton(e.uuid,n.id||n.command,n.text,n.image,n.cb||B(n.command))):w.appendChild(ys.createButton(n.id||n.command,e.uuid,n.text,n.cb||B(n.command))))}));const L=(e,t)=>{const r=t||Ts.keyMap[n.editor_keyMap];let i,s;return Object.keys(r).every((t=>r[t]!=e||(i=t,!1))),i||(r.fallthrough&&(s=Ts.keyMap[r.fallthrough])&&s!==r?L(e,s):void 0)},O=(t,n)=>n.map(((n,r)=>{if(n.modes&&!n.modes.includes(l?"js":"other"));else{if("sep"==n.type)return(t=>{
const n=fe("tr","entry sep",e.uuid,"editorsubmenusep_tr"+t),r=me("td",e.uuid,"editorsubmenusep_td"+t),i=me("hr",e.uuid,"editorsubmenusep"+t);return r.setAttribute("colspan",4),n.appendChild(r),r.appendChild(i),n})(t+r);if(n.command)return((t,n,r,i,s,o,a,l)=>{const c=o||Ts.commands.hasOwnProperty(n),d=fe("tr","entry "+(!a&&c?"":"disabled"),e.uuid,t,"editorsubmenuentry_tr",!0);let A;A=fe("i",i?"far fa-"+i:"far",e.uuid,t,"editorsubmenuentry_i",!0);const u=me("td",e.uuid,"editorsubmenuentry_td_i"+t)
;u.appendChild(A);const h=fe("td","label",e.uuid,"editorsubmenuentry_td_l"+t);h.textContent=r;const p=fe("td","shortcut",e.uuid,"editorsubmenuentry_td_m"+t);return p.setAttribute("colspan",2),s&&(p.textContent=s),d.appendChild([u,h,p]),a?"string"==typeof a&&d.setAttribute("title",a):c&&d.addEventListener("click",l),d})(t+"_"+n.command,n.command,n.text,n.image,L(n.command),!!n.cb,n.disabled,n.cb||B(n.command));{const r=t+n.id,i=((t,n,r)=>{
const i=fe("tr","entry list",e.uuid,t,"editorsubmenulistentry",!0);let s;s=fe("i",r?"far fa-"+r:"far",e.uuid,t,"editorsubmenulistentry_i",!0);const o=me("td",e.uuid,"editorsubmenulistentry_td_i"+t);o.appendChild(s);const a=fe("td","label",e.uuid,"editorsubmenulistentry_td_l"+t);a.textContent=n;const l=fe("td",e.uuid,"editorsubmenulistentry_td_m"+t),c=me("td",e.uuid,"editorsubmenulistentry_td_s"+t);i.appendChild([o,a,l,c])
;const d=fe("span","submenulist",e.uuid,"editorsubmenulistcontent"+t),A=fe("span","submenumore",e.uuid,"editorsubmenulistmore"+t),u=fe("i","more far fa-caret-right",e.uuid,t,"editorsubmenulistentrymore_i",!0);return A.appendChild([u,d]),c.appendChild([u,A]),{elem:i,content:d}})(r,n.text,n.image),s=O(r,n.entries);if(s.length>0)return i.content.appendChild(s),i.elem}}})).filter((e=>e));Object.keys(F).forEach((e=>{const t=F[e];if(!t)return;const n=S(e,t.text),r=O(e,t.entries)
;r.length>0&&(R.appendChild(n.elem),n.content.appendChild(r))}));const D=fe("textarea","editorta",e.uuid,"editor");D.setAttribute("wrap","off"),D.setAttribute("spellcheck","false");const z=fe("td","editor_outer",e.uuid,"edit"),Q=fe("div","editor_100 editor_border",e.uuid,"edit");z.appendChild(Q),p.appendChild(C),s.inserted||(Q.appendChild(D),s.appendChild(z)),A[e.uuid].saveToDisk=()=>{if(!s.editor)return;const t=n.editor_enabled?s.editor.mirror.getValue():s.editor.value
;let r=o[e.uuid]?o[e.uuid].script:e;return jt.wait(),Ee().done((()=>{const e=new Blob([t],{type:"text/plain"});saveAs(e,V(r.name+(r.version?`-${r.version}`:"")+(r.is_external?"":".user.js")))})).always((()=>{jt.hide()}))},e.system||(e.is_external?A[e.uuid].saveEm=t=>{if(!s.editor)return;const r=n.editor_enabled?s.editor.mirror.getValue():s.editor.value;((e,t,n)=>{const r=Ie();try{n.auto_save||M(),sendMessage({method:"saveExternal",uuid:e,code:t,mimetype:n.mimetype,url:n.url,auto_save:n.auto_save
},(e=>{n.auto_save||(I(),Z()),(e=e||{}).items?N(e.items,!0):jt.hide(),r.resolve(e)}))}catch(e){console.log("sS: "+e.message),r.reject({err:e.message})}return r.promise()})(e.script_uuid,r,{url:e.file_url,mimetype:e.mimetype,auto_save:t&&t.auto_save}).done((e=>{e.success?k(!1):e.aborted||t&&t.auto_save||(e.messages&&e.messages.errors&&e.messages.errors.length?he(e.messages.errors.join("\n")):he(Rt("Unable_to_parse_this_")))}))}:A[e.uuid].saveEm=t=>{if(!s.editor)return;let r=!0
;n.showFixedSrc&&(r=confirm(Rt("Do_you_really_want_to_store_fixed_code_",Rt("Show_fixed_source"))));const i=n.editor_enabled?s.editor.mirror.getValue():s.editor.value;return r&&W(e.uuid,i,{clean:!1,new_script:e.nnew,auto_save:t&&t.auto_save,reload:!0,lastModTime:A[e.uuid].saveEm_lastModTime}).done((n=>{n.installed?e.nnew?b(0,!0,!1):(k(!1),
n.lastModified&&(e.lastModTime=n.lastModified)):n.aborted||t&&t.auto_save||(n.messages&&n.messages.errors&&n.messages.errors.length?he(n.messages.errors.join("\n")):he(Rt("Unable_to_parse_this_")))})),r});const H=t.insertTab(null,r.navid,c,u,(e=>{e.then((()=>{_e.setSub(r.navid),r.on_tab_select&&r.on_tab_select()}))}),r.on_tab_close);if(H.setHeading(r.tab_name||Rt("Editor"),50),_e.registerListener(e.uuid,r.navid,(()=>{H.select(),n.editor_enabled&&s.editor&&(s.editor.refresh(),
s.editor.mirror.focus())})),d)return i["editor"+e.uuid];const q=(e,t,n,r)=>{e=r.getValue();const i=r.getHelper(Ts.Pos(0,0),"lint")(e,n,r);if(i&&i.then)return i.then(t);t(i)},X=e=>{K("editor_theme",e,!0).done((()=>{T("Theme switched to "+e)}))},Y=()=>{n.editor_autoSave&&g()&&h(e.uuid,"saveEm",{auto_save:!0})},J={getTab:function(){return H},getEditor:function(){if(s.editor)return n.editor_enabled?s.editor.mirror:{save:_}},onShow:()=>{(()=>{const t=Ie()
;return e.referrer&&void 0===e.code?se(P(r.tree_opts||{},{referrer:e.referrer+"."+r.treeid,uuid:e.uuid}),(n=>{n.items?(e.code=n.items[0],t.resolve()):t.reject(),jt.hide()})):window.setTimeout(t.resolve,100),t.promise()})().done((()=>{const t=u.getElementsByTagName("textarea");if(A[e.uuid].lastI=()=>e,t.length&&!s.editor){const i=t[0],o=()=>{s.editor&&k(!!s.editor.mirror.historySize().undo)};if(n.editor_enabled){const t=i.parentNode;t.removeChild(i)
;const r=e.code||"",c=r.indexOf("\r\n")>=0?"\r\n":null;s.editor=new Us(t,{mode:a,readOnly:e.readonly,theme:n.editor_theme,fontSize:n.editor_fontSize,themeOptions:{all:Qt.getEditorThemes().map((e=>e.value)),onChange:X},lineSeparator:c,value:r,indentUnit:Number(n.editor_indentUnit),tabSize:Number(n.editor_tabSize),indentWithTabs:"tabs"==n.editor_indentWithTabs,smartIndent:"classic"!=n.editor_tabMode,indentByTab:"indent"==n.editor_tabMode,electricChars:"true"==n.editor_electricChars.toString(),
lineNumbers:!0,lineWrapping:n.editor_lineWrapping,extraKeys:{Enter:"newlineAndIndentContinueComment"},keyMap:n.editor_keyMap,bookmarkGutter:!0,gutters:["gutter","CodeMirror-linenumbers","CodeMirror-foldgutter","CodeMirror-lint-markers","CodeMirror-bookmarks"],matchBrackets:!0,foldGutter:!0,styleActiveLine:!0,specifyMoreJsTokens:l,styleSelectedText:!0,autoTrimTrailingSpace:"true"==n.editor_trimTrailingSpacesFromModifiedLines.toString(),
highlightSelectionMatches:"off"!=n.editor_highlightSelectionMatches?{showToken:/\w/,annotateScrollbar:!0,cursorOnly:"cursor"==n.editor_highlightSelectionMatches}:void 0,hintOptions:l?{keywords:Mr}:void 0,lint:l?{lintOnChange:n.editor_autoLint,autoLintMaxLen:n.editor_autoLintMaxLen,async:!0,getAnnotations:q,hintConfig:n.editor_linter_config||Ur,userscript:e.userscript,external:e.is_external}:void 0,showTrailingSpace:n.editor_highlightTrailingWhitespace},Rt,{save:_,close:b,lint_script:y},{
change:o,blur:Y})}else s.editor=i,i.value=e.code;r.on_editor_load&&r.on_editor_load()}Ji.emit("scriptEditorOpened",{uuid:e.uuid})})).fail((()=>{b(0,!1,!0)}))},onClose:function(t){const n=()=>{s.editor=null,delete A[e.uuid].lastI};if(Ji.emit("scriptEditorClosed",{uuid:e.uuid}),!t&&g()){const e=confirm(Rt("There_are_unsaved_changed_"));return e&&n(),!e}return n(),!1}};return i["editor"+e.uuid]=J,J},Ve=(e,t,n,r)=>{let i;e.is_external=!0;const s=()=>{i&&!o.onClose()&&(r(),i&&i.remove(),i=null)
},o=Ne(e,t,{tab_name:e.name,navid:e.uuid,treeid:"external",readonly:e.readonly,mimetype:e.mimetype,tree_opts:{url:e.file_url,uuid:e.script_uuid},do_close:function(){window.setTimeout(s,100)},on_tab_close:s,on_tab_select:n,on_editor_load:n});return i=o.getTab(),o.show=()=>{o.onShow(),i.select()},o},Qe=e=>{A[e.uuid]||(A[e.uuid]={});const t=(t,n,r,i)=>{const s=fe("dt"," "+(i||""),e.name,e.uuid,"dt_mapping"+n),o=fe("dd"," "+(i||""),e.name,e.uuid,"dd_mapping"+n);s.textContent=n,
"string"==typeof r?(o.textContent=r,o.setAttribute("title",r)):o.appendChild(r),t.appendChild([s,o])},n=fe("dl","trash_script_details",e.name,e.uuid,"script_details_dl"),r=fe("span","name",e.uuid,"sname"),i=fe("span","nameNname16",e.uuid,"sname_name"),s=Ct(e,"name");if(i.textContent=s,e.icon){const t=m(e.icon,e.uuid,"sname_img");It(t).addClass("nameNicon16"),r.appendChild(t)}r.appendChild(i),t(n,Rt("Name"),r),t(n,Rt("Version"),e.version||""),
t(n,Rt("Deleted_on"),new Date(e.deleted).toISOString().split("T")[0]);const o=ys.createButton("restore",`${e.uuid}_restore`,Rt("Restore"),(()=>{W(e.uuid,null,{reload:!0,restore:!0}),n.parentNode.removeChild(n)})),a=A[e.uuid].purgeScript=(t,r)=>{1==(r||confirm(Rt("Really_delete_0name0__",e.name)))&&($([e.uuid]),n.parentNode.removeChild(n))},l=ys.createButton("delete",`${e.uuid}_delete`,Rt("Delete"),a),c=fe("span","",e.uuid,"actions");return c.appendChild([o,l]),t(n,Rt("Actions"),c,"actions"),[n]
},He=(t,s,a)=>{let l,c;A[t.uuid]||(A[t.uuid]={}),A[t.uuid].getName=()=>Ct(t,"name");const d=t.icon,u=[];u.push("clickable"),(t.blacklisted||t.foisted)&&u.push("crossedout");const p=fe("span",u.join(" "),t.uuid,"sname",null,n.tags_enabled),f=d?m(t.icon,t.uuid,"sname_img"):fe("span","nameNicon16 icon16 nameNOicon16",t.uuid,"sname_img");d&&It(f).addClass("nameNicon16");const g=fe("span","nameNname16 "+(t.enabled?"":"greyed"),t.uuid,"sname_name"),_=Fe(t),b=Ct(t,"name");g.textContent=b
;const v=me("span",t.uuid,"spos");v.textContent=t.position||"";const k=me("span",t.uuid,"sversion");k.textContent=t.version?t.version:"";const y=me("span",t.uuid,"ssize");if(!t.nnew){const{code:e,resources:n,requires:r}=je(t);y.textContent=Xi.formatBytes(e+r+n,0),y.title=[Rt("Size")+": "+Xi.formatBytes(e,2),r?Rt("Requires")+": "+Xi.formatBytes(r,2):void 0,n?Rt("Resources")+": "+Xi.formatBytes(n,2):void 0].filter((e=>e)).join("\n")}const w=[],R=(e,t,n)=>{const r=fe("span",n||"",e.uuid,"wrap")
;return t&&("Array"===D(t)?z(t,(e=>{r.appendChild(e)})):r.appendChild(t)),r},C=(e,n)=>{void 0===n&&(n=!e),c&&c.onClose&&c.onClose(e)||(t.uuid&&_e.is(t.uuid)&&_e.clear(),l&&(l.remove(),l=null),delete i["tab"+t.uuid],delete i["editor"+t.uuid],p.parentNode&&p.parentNode.removeChild(p),n&&window.setTimeout((()=>{const e=Object.keys(o);for(let n,r=0;n=e[r];r++){const e=o[n].script;if(e.uuid==t.uuid){Re(s,[e],null,a);break}}ve()}),0))},x=e=>{c&&c.onSelect&&c.onSelect(e)},E=()=>{let e=null
;t.nnew?(e=fe("div","head_icon",t.uuid,"details_h"),e.appendChild(ys.createIcon(Is.get(t.image),"",t.uuid,"new_script_head"))):e=me("div",t.uuid,"details_h"),t.title&&(e.title=t.title);const r=me("div",t.uuid,"details_c");if(l=a.insertTab(null,t.uuid,e,r,x,t.nnew?null:C),!t.nnew){let e;e=t.version?" "+t.version:"";const n=Rt("Edit")+" - "+Ct(t,"name")+e;l.setHeading(n,50),l.isSelected()&&l.select(!0)}c=((e,t,r,s,o)=>{
const a=me("div",t.uuid,"script_tab_head"),l=a.inserted,c=fe("table","noborder p100100 heading",t.uuid,"h_table"),d=fe("tr","",t.uuid,"h_tr1"),A=fe("td","nameNicon64",t.uuid,"h_td1"),u=fe("td","",t.uuid,"h_td2");c.appendChild(d),d.appendChild(A),d.appendChild(u),u.setAttribute("style","width: 99%");const p=t.icon64||t.icon;A.appendChild(m(p?[p]:[],t.uuid,"heading_icon",{default_icon:Qt.images.origin("unknown")}));const f=fe("div","nameNname64",t.uuid,"heading_name");f.textContent=Ct(t,"name"),
u.appendChild(f);const g=fe("div","author",t.uuid,"author");t.author?g.textContent="by "+t.author:t.copyright&&(g.innerHTML="&copy; ",g.textContent+=t.copyright),u.appendChild(g);const _=fe("table","noborder p100100",t.uuid,"table"),b=fe("tr","script_tab_head",t.uuid,"tr1"),v=fe("tr","details",t.uuid,"tr2"),k=fe("td","",t.uuid,"td1"),y=fe("td","",t.uuid,"td2");a.appendChild(c),k.appendChild(a),b.appendChild(k),v.appendChild(y),_.appendChild(b),_.appendChild(v),s.appendChild(_)
;const w=Rs.create("_details"+t.uuid,y,{tv:"tv tv_alt",tv_table:"tv_table tv_table_alt",tr_tabs:"tr_tabs tr_tabs_alt",tr_content:"tr_content tr_content_alt",td_content:"td_content td_content_alt",td_tabs:"td_tabs td_tabs_alt",tv_tabs_align:"tv_tabs_align tv_tabs_align_alt",tv_tabs_fill:"tv_tabs_fill tv_tabs_fill_alt",tv_tabs_table:"tv_tabs_table tv_tabs_table_alt",tv_contents:"tv_contents tv_contents_alt",tv_tab_selected:"tv_tab tv_selected tv_tab_alt tv_selected_alt",
tv_tab_close:"tv_tab_close",tv_tab:"tv_tab tv_tab_alt",tv_content:"tv_content tv_content_alt"},!0),R=((t,n,r,i)=>Ne(t,n,{do_close:i,set_tab_class:(t,n)=>{e.toggleClass(t,n)},navid:"editor",treeid:"source"}))(t,w,0,o),C=t.nnew||t.system?{}:Oe(t,w),x=t.nnew||t.system||!t.storage_key_count||n.configMode<80?{}:((e,t)=>{const n=me("div","",e.uuid,"script_storage_h");n.textContent=Rt("Storage")
;const r=me("div","",e.uuid,"script_storages_c"),i=fe("div","section",e.uuid,"ta_storage"),s=fe("div","section_head",e.uuid,"head_ta_storage"),o=fe("div","section_content",e.uuid,"content_ta_storage");let a;s.textContent=Rt("Storage"),i.appendChild(s),i.appendChild(o);const l=fe("textarea","storageta",e.uuid,"storage");l.setAttribute("wrap","off"),l.setAttribute("spellcheck","false"),l.addEventListener("change",(()=>a=!0));const c=ys.createButton("storage_save_button",e.uuid,Rt("Save"),(()=>{
let t=null;try{t=JSON.parse(l.value)}catch(e){return void he(Rt("Unable_to_parse_this_"))}((t,n)=>{const r=Object.keys(n),i=Object.keys(t);r.concat(i).filter(((e,t,n)=>n.lastIndexOf(e)===t)).forEach((s=>{var o,a;r.includes(s)?i.includes(s)?n[s]!==t[s]&&(o=s,t[s],a=n[s],ce(e.uuid,o,Ss(a))):((t,n)=>{ce(e.uuid,t,Ss(n))})(s,n[s]):(t=>{ce(e.uuid,t,void 0,!0)})(s,t[s])}))})(e.storage,t)})),d=ys.createButton("storage_reload_button",e.uuid,Rt("Reload"),(()=>{
a&&!confirm(Rt("Really_reset_all_changes_"))||(jt.wait(),u(!0,!0).always((()=>{window.setTimeout(jt.hide,500),I()})))})),A=ys.createButton("storage_reset_button",e.uuid,Rt("Editor_reset"),(()=>{a&&!confirm(Rt("Really_reset_all_changes_"))||(jt.wait(),u(!0).always((()=>{window.setTimeout(jt.hide,500),I()})))})),u=(t,n)=>(l.setAttribute("disabled","disabled"),(()=>{if(e.referrer&&(n||void 0===e.storage)){const t=Ie();return se({referrer:e.referrer+".storage",uuid:e.uuid},(e=>{if(e.items){
const n={},r=e.items[0];Object.keys(r).forEach((e=>{n[e]=((e,t)=>{if(!e)return t;const n=e[0];switch(e=e.substring(1),n){case"b":return"true"==e;case"n":return Number(e);case"o":try{return JSON.parse(e)}catch(e){console.error("Storage: getValue ERROR: "+e.message)}return t;case"x":try{return te(re(e))}catch(t){return e}default:return e}})(r[e])})),t.resolve(n)}else t.reject();jt.hide()})),t.promise()}return Ie.Pledge(e.storage)})().done((n=>{!t&&l.value||(l.value=(e=>{
const t=`__undefined__${Q()}`;return JSON.stringify(e,((e,n)=>void 0===n?t:n),4).replace(new RegExp(`"${t}"`,"g"),"undefined")})(n),a=!1),e.storage=n,l.removeAttribute("disabled")})).fail((()=>{l.setAttribute("disabled",!0)})));o.appendChild(l),o.appendChild(c),o.appendChild(A),o.appendChild(d),r.appendChild(i);const h=t.appendTab("storage",n,r,(t=>{t.then((()=>{_e.setSub("storage"),Ji.emit("storageEditorOpened",{uuid:e.uuid})}))}),(()=>{Ji.emit("storageEditorClosed",{uuid:e.uuid})}))
;return _e.registerListener(e.uuid,"storage",(()=>{u(),h.select()})),{}})(t,w),E=t.nnew||t.system||!t.requires.length&&!t.resources.length?{}:((e,t)=>{let n;const r=e=>{n=e},i=me("div","",e.uuid,"script_external_h");i.textContent=Rt("Externals");const s=me("div","",e.uuid,"script_externals_c"),o=fe("div","section",e.uuid,"ta_requires"),a=fe("div","section_head",e.uuid,"head_ta_requires"),l=fe("div","section_content",e.uuid,"content_ta_requires");a.textContent=Rt("Requires"),o.appendChild(a),
o.appendChild(l),l.appendChild(De(e,"requires",t,r));const c=fe("div","section",e.uuid,"ta_resources"),d=fe("div","section_head",e.uuid,"head_ta_resources"),A=fe("div","section_content",e.uuid,"content_ta_resources");d.textContent=Rt("Resources"),c.appendChild(d),c.appendChild(A),A.appendChild(De(e,"resources",t,r)),s.appendChild(o),s.appendChild(c);const u=t.appendTab("externals",i,s,(e=>{e.then((()=>{_e.setSub("externals")}))}));return _e.registerListener(e.uuid,"externals",(()=>{u.select()
})),{getEditor:function(){return n}}})(t,w);if(l)return i["tab"+t.uuid];const G=()=>{let e;return(e=w.getSelectedTab())&&e.isCloseable()?E.getEditor():R.getEditor()},Z=t=>{let r=!1;if("keydown"==t.type&&e.isSelected()&&!t.defaultPrevented){if(27==t.keyCode){if(!n.editor_enabled||"vim"!=n.editor_keyMap){let e;(e=w.getSelectedTab())&&e.isCloseable()?e.close():window.setTimeout(o,0),r=!0}}else if(n.editor_enabled){const e={save:!0,find:!0,findNext:!0,findPrev:!0,replace:!0,replaceAll:!0
},n=G(),i=Ts.keyName(t);n&&!n.hasFocus()&&"handled"==Ts.lookupKey(i,n.getOption("keyMap"),(t=>{if(e[t])return n.execCommand(t),!0}))&&(r=!0)}else if(83==t.keyCode&&(t.ctrlKey||t.metaKey)){const e=G();e&&(e.save(),r=!0)}return r?(t.stopPropagation(),t.preventDefault(),!1):void 0}},S={onShow:()=>{z([C,R,x,E],(e=>{e.onShow&&e.onShow()})),window.addEventListener("keydown",Z,!1)},onClose:e=>{let t;return z([C,R,x,E],(n=>{if(n.onClose&&n.onClose(e))return t=!0,!1})),
t||window.removeEventListener("keydown",Z,!1),t},onSelect:e=>{e.then((()=>{if(t.uuid){const e=_e.get().sub,n=w.getTabById(e);_e.set(t.uuid,(n?e:null)||"editor"),document.title=h(t.uuid,"getName")}})),z([C,R,E],(t=>{t.onSelect&&t.onSelect(e)}))}};return i["tab"+t.uuid]=S,S})(l,t,0,r,C)},G=A[t.uuid].scriptClick=(e,t)=>{l||E(),c&&c.onShow&&c.onShow(),l.show(),e&&1==e.button||t||l.select(),g.setAttribute("open","true")};"true"==g.getAttribute("open")&&G(null,!0)
;const Z=fe("span","script_update",t.uuid,"last_updated_c",!0),S=me("span","",t.uuid,"last_updated",!0);Z.appendChild(S);let B="?";if(t.nnew||t.system)B="";else{if(A[t.uuid].scriptUpdate=()=>{const e=S.textContent;S.textContent="",S.appendChild(ys.createIcon(Is.get("download"),"down",t.uuid,"spinner")),((e,t)=>{try{sendMessage({method:"buttonPress",name:"run_script_updates",scriptuid:e},(e=>{I(),t&&t(e.updatable)}))}catch(e){console.log("rSu: "+e.message)}})(t.uuid,(n=>{S.textContent=e,
n?(It(S).addClass("green"),S.title=Rt("There_is_an_update_for_0name0_avaiable_",t.name),C(!0),ae()):(It(S).addClass("red"),S.title=Rt("No_update_found__sry_"))}))},t.options.check_for_updates&&t.file_url&&"none"!=t.file_url){S.addEventListener("click",(()=>{h(t.uuid,"scriptUpdate")})),S.setAttribute("class","clickable"),S.title=Rt("Check_for_Updates");const e=t.options.user_modified;if(e){const n=ys.createIcon(Is.get("critical"),"edited",t.uuid)
;n.title=Rt("The_script_was_modified_locally_on_0date0__Updating_it_will_overwrite_your_changes_",new Date(e).toLocaleString()),Z.appendChild(n)}}else S.setAttribute("class","greyed italic"),S.title=Rt("Update_check_is_disabled");if(t.lastModified||t.lastUpdated)try{B=((e,t)=>{const n=e.getTime(),r=t.getTime(),i=Math.abs(n-r),s=Math.round(i/6e4),o=Math.round(i/36e5),a=Math.round(i/864e5);return s<=60?s+" min":o<=48?o+" h":a<=28?a+" d":t.toLocaleDateString()
})(new Date,new Date(t.lastModified||t.lastUpdated))}catch(e){console.log("o: error calculating time "+e.message)}}S.textContent=B;let T,U=me("div",t.uuid,"imported"),F="";r.push((()=>{
n.sync_enabled&&(F=t.nnew||t.system?"":t.sync&&t.sync.imported?2==t.sync.imported?'<img src="'+Is.brand("chrome")+'" class="icon16" title="Google Sync"/>':4==t.sync.imported?'<img src="'+Is.brand("gdrive")+'" class="icon16" title="Google Drive"/>':5==t.sync.imported?'<img src="'+Is.brand("dropbox")+'" class="icon16" title="Dropbox"/>':6==t.sync.imported?'<img src="'+Is.brand("webdav")+'" class="icon16" title="WebDAV"/>':7==t.sync.imported?'<img src="'+Is.brand("yandex")+'" class="icon16" title="Yandex"/>':8==t.sync.imported?'<img src="'+Is.brand("onedrive")+'" class="icon16" title="OneDrive"/>':'<i class="icon16 far fa-'+Is.get("question_mark")+'" />':"",
U.innerHTML=F,U=null)}));const M=me("span",t.uuid,"hp");if(t.origin){T=me("a",t.uuid,"hp_origin"),T.setAttribute("href",t.origin.url),T.setAttribute("target","_blank");const e=ys.createImage(Is.origin(t.origin.token),"",t.uuid,t.origin.token);T.appendChild(e),M.appendChild(T)}if(_&&(!t.origin||t.origin.url!==_)){T=me("a",t.uuid,"hp_script"),T.setAttribute("href",_),T.setAttribute("target","_blank");const e=ys.createIcon(Is.get("home"),"",t.uuid,"homepage","");T.appendChild(e),M.appendChild(T)}
A[t.uuid].saveEm_lastModTime=t.lastModified,A[t.uuid].fullReset=e=>{W(t.uuid,null,{clean:!0,reload:!0}).done(e||(()=>{})),s.parentNode.removeChild(s)},A[t.uuid].reportAnIssue=e=>{((e,t)=>{try{sendMessage({method:"reportAnIssue",uuid:e,to:t},(()=>{}))}catch(e){console.log("rRi: "+e.message)}})(t.uuid,e)},A[t.uuid].deleteScript=(e,r)=>{const i="off"!=n.trash_mode,o={reload:!r};(i||confirm(Rt("Really_delete_0name0__",t.name)))&&(W(t.uuid,null,o,(()=>{
const e=Rt(i?"The_script_was_successfully_moved_to_the_trash_":"The_script_was_successfully_deleted_");I(e)})),s.parentNode.removeChild(s))};const j=[];if(!t.nnew&&!t.system&&t.origin&&t.origin.abuse_url){const e=ys.createIcon(Is.get("flag"),"",t.uuid,"issue",Rt("Report_an_issue_to_the_script_hoster_"),(()=>{h(t.uuid,"reportAnIssue","hoster")}));j.push(e)}if(!t.nnew&&!t.system&&(t.origin||t.supportURL)){const e=ys.createIcon(Is.get("bug"),"",t.uuid,"bug",Rt("Report_a_bug"),(()=>{
h(t.uuid,"reportAnIssue","author")}));j.push(e)}if(!t.nnew&&!t.system){if(t.remote_url){const e=ys.createIcon(Is.get("cloud"),"",t.uuid,"cloud_edit",Rt("Edit"),(()=>{sendMessage({method:"newTab",url:t.remote_url},(()=>{}))}));j.push(e)}const e=ys.createIcon(Is.get("edit"),"",t.uuid,"edit",Rt("Edit"),(()=>{h(t.uuid,"scriptClick")}));j.push(e)}if(!t.nnew&&!t.system){const e=ys.createIcon(Is.get("delete"),"",t.uuid,"delete",Rt("Delete"),(()=>{h(t.uuid,"deleteScript")}));j.push(e)}
e?p.inserted||g.addEventListener("click",(()=>{It(s).toggleClass("show_details")})):p.inserted||(p.addEventListener("click",G),p.addEventListener("auxclick",G)),p.appendChild(g);let L=[t.name];t.description&&L.push(Ct(t,"description")),(t.blacklisted||t.foisted)&&(L=t.blacklisted?t.warnings&&t.warnings.length?[t.blacklisted,...t.warnings]:[t.blacklisted]:[t.foisted]),p.title=L.join("\n\n").replace(/\"/g,'"');let O=[]
;if(!t.nnew&&t.options.tags&&n.tags_enabled&&(O=t.options.tags.map?t.options.tags.map((e=>{const r=n.tags[e];if(r)return ys.createTag(e,t,{color:r?r.color:void 0,class:["clickable"].concat(r?[]:["unknown"]).join(" "),onClick:t=>{A.multiselect.setFilter("tag",e),t.stopPropagation(),t.preventDefault()}})})).filter((e=>e)):[],O.length&&p.appendChild(O)),w.push(t.nnew||t.system?null:{element:ht(t),style:"script_sel"}),w.push(v),w.push((()=>{let e=null
;e=t.blacklisted||t.foisted?"enabler_warning":t.enabled?t.contexter?"enabler_enabled enabler_later":"enabler_enabled":"enabler_disabled";const n=t.blacklisted||t.foisted||(t.enabled?Rt("Enabled"):Rt("Disabled")),r=ys.createEnabler(e,t.uuid,"enabled",{append:"enabled",disabled:!!t.blacklisted,title:n},(()=>{h(t.uuid,"switchEnabled")}));return A[t.uuid].switchEnabled=(e,n,r)=>{void 0===n&&(n=!t.enabled),le(t.uuid,{enabled:n,whitewash:!!t.foisted},r)},A[t.uuid].toggleTag=(e,n,r,i)=>{
const s=t.options.tags;r?s.includes(n)||(s.push(n),le(t.uuid,{tags:s},i)):s.includes(n)&&(s.splice(s.indexOf(n),1),le(t.uuid,{tags:s},i))},r})()),w.push({element:f,style:"script_icon"}),w.push({element:p,style:"script_name"}),e){const e=(e,n,r,i)=>{const s=fe("dt","script_info "+(i||""),t.name,t.uuid,"dt_mapping"+n),o=fe("dd","script_info "+(i||""),t.name,t.uuid,"dd_mapping"+n);s.textContent=n,"string"==typeof r?o.textContent=r:o.appendChild(r),e.appendChild([s,o])
},n=fe("dl","script_details",t.name,t.uuid,"script_details_dl");let r;p.appendChild(n),e(n,Rt("Version"),k),e(n,Rt("Type"),Ye(t),"script_type"),e(n,Rt("Sites"),$e(t)),(r=We(t))&&e(n,Rt("Features"),r),_&&e(n,Rt("Homepage"),M),B&&e(n,Rt("Last_updated"),Z),w.push(R(t,j,"actions"))}else{w.push({element:k,style:"script_version"}),w.push(y),w.push(Ye(t)),w.push(U),w.push($e(t)),w.push(We(t)),w.push(M),w.push(Z),w.push(Ke(t)),w.push(R(t,j,"actions"));for(let e=w.length;e<ye;e++)w.push(null)}
return _e.registerListener(t.uuid,(()=>{G()})),t.nnew&&r.push((()=>{G(null,!0)})),w},Ye=e=>{let t;const n=fe("span","script_type","",e.uuid,"pos_type",!0);return e.nnew||e.userscript&&(t=ys.createImage(Is.origin("uso"),"",e.uuid,"user_script",Rt("This_is_a_userscript")),n.appendChild(t)),n},We=e=>{let t,n,r,i=null;const s=[];if(e.nnew)return null
;e.includes.length||e.matches.length||(t=ys.createIcon(Is.get("error"),"includeless",e.uuid,"crit",Rt("This_script_does_not_provide_any__include_information_")),s.push(t)),e.system&&(t=ys.createIcon(Is.get("lock"),"",e.uuid,"lock",Rt("This_is_a_system_script")),s.push(t)),e.warnings&&e.warnings.forEach(((n,r)=>{t=ys.createIcon(Is.get("critical"),"warnings",e.uuid,"warning_"+r,n),It(t).addClass("flashing"),s.push(t)})),e.requires.length&&(r=!e.requires.filter((e=>e.modified)).length,
t=ys.createIcon(Is.get("script_download")+(r?"":" red"),"",e.uuid,"requires",e.requires.filter((e=>e&&e.url)).map((e=>e.url)).join("\n"),(()=>{_e.set(e.uuid,"externals")})),s.push(t)),e.resources.length&&(r=!e.resources.filter((e=>e.modified)).length,t=ys.createIcon(Is.get("resources")+(r?"":" red"),"",e.uuid,"resources",e.resources.filter((e=>e&&e.url)).map((e=>e.url)).join("\n"),(()=>{_e.set(e.uuid,"externals")})),s.push(t));let o=!1;const a={includes:!0,matches:!0};for(n in a)if(e[n]){
for(let r=0;r<e[n].length;r++)if(e[n][r]&&(0==e[n][r].search(/\/\^?http(s|\.\*?|s\?|\[[^\]]*s[^\]]*\]|\([^\)]*s[^\)]*\))+/)||0==e[n][r].search(/http[s\*]{1,1}|\*/))){t=ys.createIcon(Is.get("encrypted"),"",e.uuid,"encrypt",Rt("This_script_has_access_to_https_pages")),s.push(t),o=!0;break}if(o)break}const l={};e.grant.forEach((e=>{l[e]=!0})),(l.GM_xmlhttpRequest||l["GM.xmlHttpRequest"])&&(t=ys.createIcon(Is.get("web"),"",e.uuid,"web",Rt("This_script_has_full_web_access")),s.push(t)),
(l.GM_setValue||l["GM.setValue"])&&(t=ys.createIcon(Is.get("db"),"",e.uuid,"db",Rt("This_script_stores_data")),s.push(t)),(l.none||0===e.grant.length)&&(t=ys.createIcon(Is.get("permissionless"),"",e.uuid,"none",Rt("This_script_does_not_require_any_special_powers_")),s.push(t));const c=e.options&&(e.options.run_in||e.options.override&&e.options.override.orig_run_in)
;for(n in c&&c.length&&(c.includes("incognito-tabs")?c.includes("normal-tabs")||(t=ys.createIcon(Is.get("incognito")+" green","",e.uuid,"run_in",Rt("Does_not_run_in_normal_tabs")),s.push(t)):(t=ys.createIcon(Is.get("incognito")+" blue","",e.uuid,"run_in",Rt("Does_not_run_in_incognito_tabs")),s.push(t))),e.options)if(-1!=n.indexOf("compat_")&&e.options[n]){t=ys.createIcon(Is.get("critical"),"compat",e.uuid,"crit",Rt("One_or_more_compatibility_options_are_set")),s.push(t);break}
for(n in e.antifeatures){const r=e.antifeatures[n];let i;i=Rt("ads"==n?"Antifeature_ads":"miner"==n?"Antifeature_miner":"tracking"==n?"Antifeature_tracking":"Antifeature_other");const o=r[Et(Object.keys(r))]||r.default||r.en||Rt("Antifeature_no_details");t=ys.createIcon(Is.get("about"),"compat",e.uuid,"crit",Rt("Antifeature__0name0__0description0",i,o)),s.push(t)}return s.length&&(i=me("span","",e.uuid,"pos_features",!0),i.appendChild(s,!0)),i},Je=(()=>{let e,t=null,n=null,r=null,i=0,s=0,o=0
;const a=n=>{const i=r.x+n.pageX,s=r.y+n.pageY;t.style.top=s+e.scrollTop+"px",t.style.left=i+"px"};let l,c;const d=r=>{if(t&&!l){let t;if("scroll"==r.type&&c)return void(t=c);t=c=r;let d,u,h,p,f,m=null;a(t);const g=e.scrollTop,_=5,{top:b,bottom:v}=e.getBoundingClientRect();for(;m!=o&&(m=o,u=n.previousSibling,h=n.nextSibling,p=n.parentNode,f=A(n),!(t.pageY<b||t.pageY>v));)t.pageY>f.y+i+s&&h?(p.removeChild(h),p.insertBefore(h,n),o++,d=!1):t.pageY<f.y&&o>1&&(p.removeChild(u),
h?p.insertBefore(u,h):p.appendChild(u),o--,d=!0);if(void 0!==d){const t=It(d?u:h);if(t.length&&!ys.isScrolledIntoView(t,e,{padding:d?{top:_}:{bottom:_}})){const n=d?t.offset().top-b-_-t.height():t.offset().top-b-(v-b)+_+2*t.height();l=!0;const r=Math.floor(g+n);e.scrollTop=r,It(e).animate({scrollTop:r},0).then((()=>{l=!1}))}}return t.stopPropagation&&(t.stopPropagation(),t.preventDefault()),!1}},A=e=>{const t=e.getBoundingClientRect();return{x:Math.floor(t.left),y:Math.floor(t.top)}},u=e=>{
t.style.position="static";const i={};return i[t.key]=o,le(t.uuid,i),t=n=r=null,document.removeEventListener("mousemove",d),document.removeEventListener("mouseup",u),e.stopPropagation(),e.preventDefault(),!1};return{start:function(l,c){return((o,l)=>{e||(e=It(".scripttable").parent().get(0));const c=o.parentNode.parentNode,d=c.parentNode;t=o,n=d,i=n.offsetHeight,s=n.offsetHeight-c.clientHeight,r=A(e),r.x=-r.x-t.offsetWidth/2,r.y=-r.y-n.offsetHeight/2+2*s,t.style.position="absolute",a(l)
})(this,l),o=c,document.addEventListener("mousemove",d),document.addEventListener("mouseup",u),l.stopPropagation(),l.preventDefault(),!1}}})(),Ke=e=>{const t=fe("span","sorting","",e.uuid,"pos_images",!0);if(e.nnew)return t;if("pos"==k()&&"up"==y()||t.setAttribute("style","display: none;"),e.position>1||e.position<e.positionof){const n=fe("span","clickable movable","position",e.uuid,!0);n.innerHTML="&#9776;",n.title=Rt("Click_here_to_move_this_script"),n.uuid=e.uuid,n.key="position",
n.addEventListener("mousedown",(function(t){Je.start.apply(this,[t,e.position])})),t.appendChild(n)}return t},$e=e=>{let t=me("span","",e.uuid,"site_images"),n=null;return t.inserted&&(n=t,n.setAttribute("id",n.id+"foo"),t=me("span","",e.uuid,"site_images")),E.topIcons(e,7).forEach((e=>{t.appendChild(e,!0)})),n&&n.parentNode.removeChild(n),t};let et,tt,nt,rt,it=!1,ct=[];const dt=e=>{const t=fe("div","search_filter",e.name,e.id,"filter");if(t.inserted)return t;const i=It(t);let s,a,l,c=null
;const d=(e,t,n)=>{s&&!e||(c&&window.clearTimeout(c),p.show(),it=!0!==n,c=window.setTimeout((()=>{f=null,a=g,l=u.value;const e=()=>a!=g||l!=u.value,t=document.getElementsByName("scriptselectors"),n=[];for(let e=0;e<t.length;e++)n.push(t[e]);s=!0,(["matching_url","auto"].includes(a)&&l.match(m.matching_url.match)?(_=m.matching_url.update_interval||_,(e=>{const t=Ie();return sendMessage({method:"determineScriptsToRun",url:e},(e=>{t.resolve(e.scripts||[])})),t.promise()
})(l)):Ie.Pledge()).then((t=>{const r=new RegExp(L(l),"i"),s=new RegExp(L(l)),c=qe({threads:32}),d=[],A=n.map((n=>()=>{let c,A,u,h;return(A=o[n.s_id])&&(u=A.script)&&(h=A.dom)&&!e()?(d.push((()=>{It(h).toggle(!!c),n.is_hidden=!c})),(()=>{if(!it)return Ie.Pledge();if(!i.is(":visible"))return Ie.Pledge();if("auto"==a)return Ie.or(Object.keys(m).map((e=>()=>{const n=m[e];return n.check?n.check(u,n.ignore_case?r:s,l,t):Ie.Breach()})));{const e=m[a];if(e.check)return e.check(u,e.ignore_case?r:s,l,t)}
return Ie.Breach()})().done((()=>{c=!0})).fail((()=>{c=!1}))):Ie.Breach()}));return Ie.sidebyside(A.map((e=>c.add(e)))).always((()=>{d.forEach((e=>e())),ct.forEach((e=>e()))}))})).always((()=>{s=!1,e()?d(!1,1):p.fadeOut()}))}),t||_))},u=fe("input","filter_text","sms","all",null);u.setAttribute("spellcheck","false");let h="";u.inserted||u.addEventListener("keyup",(()=>{h!=u.value&&(h=u.value,d())}));const p=It(ys.createIcon(Is.get("download"),"wait_for_filter",e.uuid));let f
;p.attr("style","vertical-align: middle;"),p.hide(),A.filter={onShow:function(e){d(!0),e&&It(u).trigger("focus")},onHide:function(){d(!0,1,!0)}};const m={auto:{name:Rt("Auto"),update_interval:500},name:{name:"@name",sync:function(e,t){return-1!=e.name.search(t)},ignore_case:!0},namespace:{name:"@namespace",sync:function(e,t){return e.namespace&&-1!=e.namespace.search(t)},ignore_case:!0},author:{name:"@author",sync:function(e,t){return e.author&&-1!=e.author.search(t)},ignore_case:!0},grant:{
name:"@grant",default:"GM_",sync:function(e,t){return e.grant&&e.grant.filter((e=>-1!=e.search(t))).length>0},ignore_case:!0},includes:{name:"@include/@match",sync:function(e,t){return e.includes&&e.includes.filter((e=>-1!=e.search(t))).length>0||e.matches&&e.matches.filter((e=>-1!=e.search(t))).length>0},ignore_case:!0},code:{name:Rt("Source_Code"),async:function(e,t){const n=Ie();if(!f){const e=Ie();f=e.promise(),r={code:t.toString()},i=t=>{e.resolve(t)},se({
referrer:"options.scripts.userscripts.matches",uuid:null,filter:r},(e=>{jt.hide(),i(e.items)}))}var r,i;return f.done((t=>{window.setTimeout((()=>{(t.includes(e.uuid)?n.resolve:n.reject)()}),0)})),n.promise()},ignore_case:!0},tag:{name:Rt("Tags"),sync:function(e,t,n){return e.options&&e.options.tags.filter((e=>""===n||""==e.replace(t,""))).length>0},enabled:n.tags_enabled,ignore_case:!0},comment:{name:Rt("Comment"),sync:function(e,t){
return e.options&&e.options.comment&&-1!=e.options.comment.search(t)},ignore_case:!0},url:{name:Rt("Update_URL_"),default:"https://",sync:function(e,t){return e.file_url&&-1!=e.file_url.search(t)},ignore_case:!0},matching_url:{name:Rt("Matching_URL"),update_interval:1500,default:"http://",match:new RegExp("^https?://.*"),sync:function(e,t,n,r){return r&&r.includes(e.uuid)}}};Object.keys(m).forEach((e=>{const t=m[e];"enabled"in t&&!t.enabled||(t.check=t.sync||t.async?function(){
return t.sync?t.sync.apply(this,arguments)?Ie.Pledge():Ie.Breach():t.async?t.async.apply(this,arguments):void 0}:null)}));let g="auto",_=m.auto.update_interval;const b=ys.createDropDown(Rt("Filter_by"),{value:0,uuid:"sms-filter",name:"select"},Object.keys(m).map((e=>{const t=m[e];if(!("enabled"in t)||t.enabled)return{name:t.name,value:e}})).filter((e=>e)),(function(){let e,t;const n=this.value;n!=g&&(g=n,e=m[g],_=(e?e.update_interval:null)||m.auto.update_interval,t=(e?e.default:null)||"",
u.value=u.value||t,d())}));return b.elem.setAttribute("class","label"),t.appendChild(b.elem),t.appendChild(u),t.appendChild(p[0]),et=b.select,tt=u,nt=void 0,ge.filter?r.push((()=>{A.multiselect.toggleRow(!0,!0),u.value=ge.filter})):ge.tag&&r.push((()=>{A.multiselect.toggleRow(!0,!0),et.value="tag",tt.value=ge.tag,It(et).trigger("change"),It(tt).trigger("keyup")})),t};let At=[];const ut=e=>{At=[],e&&At.push(e)},ht=e=>{const t=me("input","",e.uuid,"sel");return t.type="checkbox",t.s_id=e.uuid,
t.name="scriptselectors",t.inserted||t.addEventListener("click",(e=>{ut(t),e.shiftKey?A.multiselect.shift_click(e):e.ctrlKey||e.metaKey?A.multiselect.un_selectAll():A.multiselect.single_click(e)})),t};let pt,ft;const gt=e=>{const t=fe("input","multiselectcb","sms","all",null),r=fe("div","filter multiselectcb clickable","sms2","all",null);t.inserted||(t.type="checkbox",t.addEventListener("click",A.multiselect.un_selectAll),r.addEventListener("click",(()=>{A.multiselect.toggleRow()})));let i=[{
name:Rt("__Please_choose__"),value:0},{name:Rt("Toggle_Enable"),value:1,select:[{name:Rt("Enable"),value:"on"},{name:Rt("Disable"),value:"off"},{name:Rt("Toggle"),value:"toggle"}]}];n.tags_enabled&&(i.push((()=>{const e={name:Rt("Add_Tag"),value:2,select:Object.keys(n.tags).map((e=>({name:e,value:e}))),init:e=>{e?(pt=e.value,e.addEventListener("change",(()=>{pt=e.value}))):console.warn("no select")}};return e})()),i.push((()=>{const e={name:Rt("Remove_Tag"),value:3,select:[],init:e=>{
if(!e)return void console.warn("no select");let t=()=>{e.childNodes.forEach((e=>e.remove()));const t=[...document.getElementsByName("scriptselectors")].map((e=>e.is_hidden?null:o[e.s_id])).filter((e=>e&&e.script.options&&e.script.options.tags)).map((e=>e.script.options.tags)).flat(),r=(i=t,[...new Set(i)]).filter((e=>n.tags[e])).sort();var i;const s=It(e).find("option").map((e=>{if(r.includes(e.value))return e.value==ft&&(e.selected=!0),e.value;e.remove()}));r.forEach(((t,n)=>{
if(s.includes(t))return;const r=me("option","remove_tag",n,t);r.value=t,r.textContent=t,t==ft&&(r.selected=!0);for(let n=0;n<e.childNodes.length;n++)if(e.childNodes[n].value>t)return void e.insertBefore(r,e.childNodes[n]);e.appendChild(r)})),0==r.length?e.setAttribute("disabled","true"):(void 0===ft&&(ft=r[0]),e.removeAttribute("disabled"))};t(),ft=e.value,e.addEventListener("change",(()=>{ft=e.value})),ct.push(t)},clean:()=>{ct=ct.filter((e=>void 0!==e))}};return e})())),i.push({
name:Rt("Export"),value:4}),i=i.concat([{name:Rt("Trigger_Update"),value:5},{name:Rt("Factory_Reset"),value:8},{name:Rt("Delete"),value:6}]);const s={value:(rt?rt.value:void 0)||0,uuid:"sms-select",name:"select"},a=ys.createDropDown(Rt("Apply_this_action_to_the_selected_scripts"),s,i,(function(){0==this.value?d.setAttribute("disabled","true"):d.removeAttribute("disabled"),rt&&rt.clean&&rt.clean();const e=i.find((e=>e.value==this.value));rt=e;const{select:t,input:n}=rt||{};let r;if(nt=void 0,
l.childNodes.forEach((e=>e.remove())),t){if(0==t.length){const e=ys.createDropDown(null,{name:Rt("__Please_choose__"),value:""},t);r=e.select,r.setAttribute("disabled","true"),nt=void 0}else{const e=ys.createDropDown(null,t[0],t);r=e.select,nt=t[0].value}r.inserted||r.addEventListener("change",(()=>{nt!=r.value&&(nt=r.value)}))}else if(void 0!==n){const e=fe("input","actionargvalue","actionarg","value");e.setAttribute("spellcheck","false"),nt=e.value=n,r=e,
r.inserted||r.addEventListener("keyup",(()=>{nt!=r.value&&(nt=r.value)}))}rt&&rt.init&&rt.init(r),r&&l.appendChild(r,!0)})),l=fe("span","actionarg",e.name,e.id,"action_arg"),d=ys.createButton("MultiSelectButton","start_button",Rt("Start"),(()=>{if(!rt||0==rt.value)return void console.log("option: ?!?!");let e,t=null;if(6==rt.value?t=Rt("Really_delete_the_selected_items_"):8==rt.value&&(t=Rt("Really_factory_reset_the_selected_items_")),t&&!confirm(t))return
;const n=document.getElementsByName("scriptselectors"),r=[];for(e=0;e<n.length;e++)r.push(n[e]);const i={};let s,a=!1,l=100;for(e=0;e<r.length;e++)if(r[e].checked&&!r[e].is_hidden)if(1==rt.value){let t;s="switchEnabled";const n=o[r[e].s_id]?o[r[e].s_id].script:null;"toggle"==nt?n&&(t=!n.enabled||!!n.foisted):"off"==nt&&n&&n.foisted||(t="on"==nt),void 0!==t&&(h(r[e].s_id,s,null,t,!1),a=!0)}else if(2==rt.value){if(s="toggleTag",!pt)return void console.log("option: no tag",pt)
;h(r[e].s_id,s,null,pt,!0),a=!0}else if(3==rt.value){if(s="toggleTag",!ft)return void console.log("option: no tag",ft);h(r[e].s_id,s,null,ft,!1),a=!0}else 4==rt.value?i[r[e].s_id]=!0:5==rt.value?(s="scriptUpdate",h(r[e].s_id,s)):6==rt.value?(s="deleteScript",h(r[e].s_id,s,null,!0),a=!0,l=1500):8==rt.value&&(s="fullReset",h(r[e].s_id,s),a=!0,l=1500);4==rt.value&&(jt.wait(),xe(i,{storage:c.script_storage,externals:c.include_externals}).then((e=>(jt.wait(),
rr.zip.create(e.scripts).progress(jt.wait)))).then((e=>Ee().done((()=>{saveAs(e,"tampermonkey_scripts.zip"),I()})))).fail((()=>U(Rt("Action_failed")))).always((()=>{jt.hide()}))),a&&(jt.wait(Rt("Please_wait___")),window.setTimeout((()=>{ae()}),l))}));d.setAttribute("class","action_button"),rt||d.setAttribute("disabled","true"),a.elem.appendChild(l),a.elem.appendChild(d);const u=fe("div","actions",e.name,e.id,"actions");u.appendChild(a.elem)
;const p=ys.createButton("MultiSelectButton","close_button",Rt("Close"),(()=>{A.multiselect.toggleRow(!1)}));return It(p).addClass("close_button"),{selAllm:t,selAll:r,actionBox:u,close:p}};(()=>{let e,t=0;A.multiselect={};let n=!1;A.multiselect.toggleRow=(t,r)=>{const i=It(".multiselect");void 0===t&&(t=!n),t?(n=!0,i.addClass("multiselectvisible"),A.filter.onShow(r)):(e=void 0,n=!1,i.removeClass("multiselectvisible"),A.filter.onHide(),A.multiselect.un_selectAll(!1))},
A.multiselect.shift_click=t=>{const n=document.getElementsByName("scriptselectors");let r=[];if(e){let i;for(let s of n)if(s==e||s==t.target){if(r.push(s),i)break;i=s}else i&&r.push(s)}if(r.length)for(let e of n)e.checked=r.includes(e);else e=t.target.checked?t.target:void 0;A.multiselect.single_click()},A.multiselect.single_click=n=>{let r=0;const i=document.getElementsByName("scriptselectors");let s=!0,o=!1,a=!1,l=0;for(let e=0;e<i.length;e++){a=!0,s=s&&i[e].checked,o=o||i[e].checked
;const t=It(i[e]).closest("tr");i[e].checked?(l++,t.addClass("selected")):t.removeClass("selected")}a&&s&&(r+=2),r!=t&&(t=r,It(".multiselectcb").prop("checked",0!=r?"checked":"")),l&&A.multiselect.toggleRow(!0),A.multiselect.checkScroll&&A.multiselect.checkScroll(),n&&(e=n.target.checked?n.target:void 0)},A.multiselect.un_selectAll=n=>{++t>3&&(t=0),1==t&&t++;let r=!1,i=0;const s=document.getElementsByName("scriptselectors");ut(),!0===n&&(t=2);for(let e=0;e<s.length;e++){
const o=It(s[e]).closest("tr");!1===n?(s[e].checked=!1,o.removeClass("selected")):(0==e&&3==t&&(t=0),r=!0,s[e].checked=3==t||2==t,s[e].checked?(i++,o.addClass("selected"),At.push(s[e])):o.removeClass("selected")),i&&A.multiselect.toggleRow(!0)}void 0===n?t>1&&!r&&(t=0):n||(t=0),It(".multiselectcb").prop("checked",0!=t?"checked":""),e=void 0},A.multiselect.setFilter=(e,t)=>{A.multiselect.toggleRow(!0),et.value=e,tt.value=t,It(et).trigger("change"),It(tt).trigger("keyup")}})();const _t={}
;st.onMessage.addListener(((e,t,r)=>{if("updateOptions"==e.method)n=e.options||n,N(e.items,!1),r({});else if("confirm"==e.method){const t=e=>{r({confirm:e})};pe(e.msg,t)}else if("showMsg"==e.method)he(e.msg),r({});else{if("status"!=e.method)return!1;{const t=e.options;["options","all"].includes(e.realm)&&S(t),r({})}}return!0})),oe(),(()=>{const e=fe("div","content_wrapper","options","main"),i=fe("div","options status","options","status");It(document.body).append(e,i).addClass("main")
;const s=fe("div","head_container","opt","head_container"),o=fe("div","tv_container_fit","opt","tv_container"),a=me("a","head_link","heads","head_link");a.href="https://www.tampermonkey.net",a.target="_blank";const l=fe("div","float","heads","head1"),c=fe("img","banner","heads");c.src=Is.brand("tampermonkey");const A=fe("div","float head","heads","head2"),u=fe("div","header_title","heads"),h=fe("div","version","version","version");h.textContent=`v${st.manifest.version}`
;const p=me("div","search","box",""),f=fe("div","footer","footer");u.innerHTML="Tampermonkey<sup>®</sup>";const m=fe("div","social","social");m.textContent=" by Jan Biniok",m.appendChild(ys.createSocialButtons()),u.appendChild(m),l.appendChild(c),A.appendChild(u),A.appendChild(h),a.appendChild(l),a.appendChild(A),s.appendChild(a),s.appendChild(p),e.appendChild(s),e.appendChild(o),window.tab_view=d=Rs.create("_main",o),d.setFooter(f),r.unshift((()=>{Se(d),(e=>{let t,r
;const i="help",s="help",o=me("div",i,s,"tab_help_h"),a=o.textContent=Rt("Help"),l=me("div",i,s,"tab_help");e.appendTab(s,o,l,(e=>{e.then((()=>{if(_e.set(s),document.title=a,!r){jt.wait();const e=fe("div","section",i,s,"ta"),o=fe("div","section_head",i,s,"head_ta"),a=fe("div","section_content",i,s,"content_ta");o.textContent=Rt("Editor"),e.appendChild([o,a]);const c=fe("dl","dl-horizontal shortcuts",i,s,"dl");a.appendChild(c);const d=fe("dt","keymapping",i,s,"dt_mapping")
;if(t=fe("dd","keymapping",i,s,"dd_mapping"),d.textContent=Rt("Key_Mapping"),t.textContent=n.editor_keyMap,c.appendChild([d,t]),"vim"==n.editor_keyMap)t=fe("dd","keymapping",i,s,"dd_unsup"),t.textContent=Rt("Please_check_the_0editor0_documentation_for_more_details_","VIM"),c.appendChild(t);else if("emacs"==n.editor_keyMap)t=fe("dd","keymapping",i,s,"dd_unsup"),t.textContent=Rt("Please_check_the_0editor0_documentation_for_more_details_","Emacs"),c.appendChild(t);else{const e=[];r={};const t=e=>{
const t=e.split(/-+/),n=t.pop(),r=[];let i="";return-1!=(i=["up","down","left","right"].indexOf(n.toLowerCase()))&&r.push("#cursor"),n.toLowerCase().match(/f[0-9]{1,2}/)&&r.push("#function"),r.length?r.join("-")+t.join("-")+i+n:("0000"+(100-Math.min(n.length,4))).slice(-4)+n+t.join("-")};[{Esc:"backOrClose",..."mac"!=de?{"Alt-Left":"previousTab","Alt-Right":"nextTab"}:{"Alt-Up":"previousTab","Alt-Down":"nextTab"}
},Ts.keyMap[n.editor_keyMap],"mac"!=de?Ts.keyMap.pcDefault:Ts.keyMap.macDefault,Ts.keyMap.default].forEach((n=>{Object.keys(n).forEach((i=>{r[i]||"fallthrough"==i||(e.push({name:i,fn:n[i].replace?n[i].replace(/([A-Z])/g," $1").replace(/ [A-Z]/g,(e=>e.toLowerCase())):n[i],sort:t(i)}),r[i]=!0)}))})),e.sort(((e,t)=>e.sort<t.sort?-1:e.sort>t.sort?1:0)),e.forEach((e=>{const t=me("dt",i,s,"dt_"+e.sort),n=me("dd",i,s,"dd_"+e.sort);t.textContent=e.name,n.textContent=e.fn,c.appendChild([t,n])}))}
l.appendChild(e),jt.hide()}}))}))})(d)})),r.push((()=>{void 0!==ge.contribute&&window.setTimeout((()=>{_("f")}),100),t.global=!0})),window.onbeforeunload=()=>{let e;return d.getAllTabs().forEach((t=>{e=e||t.modified()})),e?Rt("There_are_unsaved_changed_"):void 0}})(),se({referrer:"options"},(e=>{if(e.options&&e.options.layout_user_css&&"reset"!==ge.layout){const t=document.createElement("style");t.innerHTML=e.options.layout_user_css,
(document.head||document.body||document.documentElement||document).appendChild(t)}var t,i;n=e.options||n,e.xhr&&(t=e.xhr,ci=t,()=>Promise.resolve(!0),void 0!==(i=async e=>{const t=st.connect("xhrLocking");return await new Promise((n=>{t.onMessage.addListener((()=>{n()})),t.onDisconnect.addListener((()=>{console.warn("xhrLocking: port disconnected unexpectedly"),n()})),t.postMessage({method:"xhrLocking",url:e})})),{done:()=>{t.disconnect()}}})&&(di=i)),ns.init(),e.items?(N(e.items,!1),
e.begging&&r.push((()=>{window.setTimeout((()=>{_(e.begging)}),100)}))):jt.hide()}))}))};window.requestFileSystem||(window.requestFileSystem=window.webkitRequestFileSystem),window.BlobBuilder||(window.BlobBuilder=window.WebKitBlobBuilder);const js=Xi.images,Ls=window.MirrorFrame,Os=Xi.images;let Ds,zs=3;if(Re())try{window.matchMedia("(orientation: portrait)").matches&&(zs=1)}catch(e){}const Ps=()=>{try{Ds=parseInt(location.hash.substr(1))||void 0}catch{}Yt((()=>{let e={};const t=(r,d)=>{
const m=[];if(d.sub_menu_item){if(d.tabId){Ds=d.tabId;try{location.hash=Ds}catch{}}if(d.items.length){const e=fe("table","actiontable at_"+d.id,"actiontable-"+d.id);if(d.more_menu){const t=fe("tr","",d.name,d.id,"mm_outer_tr");e.appendChild(t);const r=fe("td","",d.name,d.id,"mm_outer_td1"),i=fe("td","",d.name,d.id,"mm_outer_td2");i.setAttribute("colspan",2),t.appendChild([r,i]);const s=fe("table","moremenu","mmtable-"+d.id);i.appendChild(s)
;const o=fe("tr","moremenu_toggle",d.name,d.id,"tw_mm_tr1");let a=[o];const l=fe("div","clickable",d.name,d.id,"mmname");l.textContent=d.name;const c=fe("td","",d.name,d.id,"mm_td2");c.setAttribute("colspan",2),o.appendChild(c),c.appendChild(l)
;const A=fe("div","",d.name,d.id,"mmenablercol"),u=fe("i","ifdisabled clickable far fa-"+Os.get("enabler"),d.name,d.id,"mmenabler"),h=fe("i","ifenabled clickable far fa-"+Os.get("enabler_enabled"),d.name,d.id,"mmenabler_enabled"),p=fe("td","moremenuenabler",d.name,d.id,"tw_td3"),f=It(p);if(!p.inserted){let e;It("body").on("click",(()=>{e?e=!1:(It(s).removeClass("show_moremenu"),f.removeClass("enabled"))}),!1),t.addEventListener("click",(()=>{f.toggleClass("enabled"),
It(s).toggleClass("show_moremenu"),e=!0}),!0)}A.appendChild([u,h]),o.appendChild(p),p.appendChild(A),s.appendChild(a),n(s,d.items)}else n(e,d.items);m.push(e)}}else{let n=null;if(d.image?n=ys.createIcon(Os.get(d.image),d.name,d.id,null,""):d.enabler&&(n=ys.createIcon(Os.get(e.enabled?"button_ok":"cancel"),d.name,d.uuid,null,"")),n&&m.push(n),d.url||d.urls){const e=me("span",d.name,d.id,"urls"),t=d.urls||[d];for(let n=0;n<t.length;n++){const s=t[n],o=document.createElement("span");let a
;s.social?(a=ys.createSocialButtons(),It(o).addClass("social").append(a)):s.button?(a=fe("i","bw clickable far fa-"+Os.get(s.image),d.name,d.id,"mobile_availibility",!0),a.addEventListener("click",(e=>{c(s.id,s.data,s.reload),e.stopPropagation()})),a.setAttribute("title",s.title),It(a).addClass("bw"),It(o).addClass("mobileplatforms").append(a)):(o.textContent=s.name,a=d.urls?o:r,a.url=s.url,a.url_alt=s.url_alt,a.newtab=s.newtab);const l=function(e){
return this.url_alt&&e&&(0!=e.button||e.ctrlKey||e.metaKey)?i(this.url_alt,this.newtab||s.social):i(this.url||this.href,this.newtab||s.social),e.stopPropagation(),e.preventDefault(),!1};if(It(a).addClass("clickable").on("click auxclick",l),e.appendChild(o),d.always_visible&&It(r).addClass("always_visible"),n<t.length-1){const t=document.createElement("span");t.textContent=" | ",e.appendChild(t)}}m.push(e)}else if(d.globalhint){const{info_url:e,buttons:t,key:n}=d.options;a({onclick:e?()=>{i(e,!0)
}:null,onbuttonclick:t?e=>{e.id?c(e.id).always((()=>{sendMessage({method:"clearHint",key:n},(()=>null))})):e.url?i(e.url,!0):console.warn("No button id or url found")}:null,...d.options})}else if(d.button){const e=function(){let e=!0;this.warning&&(e=o(this.warning)),e&&c(this.key,this.data,this.reload)},t=fe("span",d.display||"",d.name,d.id,"bu",!0);t.textContent=d.name,r.key=d.id,r.warning=d.warning,r.reload=d.reload,r.data=d.data,r.addEventListener("click",e),It(r).addClass("clickable"),
m.push(t)}else if(d.userscript){const t=fe("table","",d.name,d.uuid,"tw"),n=fe("tr","script",d.name,d.uuid,"tw_tr1");let o=[n];const a=fe("div","clickable"+(d.active_count?"":" not_executed")+(d.deleted?" was_deleted":""),d.name,d.uuid,"ai");if(d.uuid){const t=[];let o=null;o=d.blacklisted||d.foisted?"enabler_warning":d.enabled?d.contexter?"enabler_enabled enabler_later":"enabler_enabled":"enabler_disabled";const c=d.blacklisted||d.foisted||(d.enabled?Rt("Enabled"):Rt("Disabled")),f=function(e){
if(e&&(0!=e.button||e.ctrlKey||e.metaKey))return i(st.getURL("options.html")+"#nav="+this.key,!0),e.stopPropagation(),e.preventDefault(),!1;d.foisted?p(d.uuid,"whitewash",!0):p(d.uuid,"enabled",!d.enabled)},m=ys.createEnabler(o,d.uuid,"enabled",{append:"enabled",disabled:d.blacklisted||d.deleted,title:c},f),g=fe("td","",d.name,d.uuid,"tw_td1");n.appendChild(g),g.appendChild(m);const _=s(d.icon64||d.icon,d.uuid,"icon",{default_icon:Qt.images.origin("unknown")});a.appendChild(_)
;const b=fe("div","script_name",d.name,d.uuid,"name");if(b.textContent=Ct(d,"name"),a.appendChild(b),a.uuid=d.uuid,a.key=d.uuid,!d.deleted&&!d.blacklisted){It(a).on("click auxclick",f);const e=Rt("Edit"),n=ys.createIcon(Os.get("edit"),"",d.uuid,"edit_script",e),r=fe("span","clickable",d.name,d.uuid,"edit_script");r.setAttribute("title",e),r.textContent=e,t.push({always_visible:!1,id:"edit_script",img:n,text:r,oc:function(){i(st.getURL("options.html")+"#nav="+d.uuid,!0)}})}
if(d.blacklisted||d.foisted){let e;e=d.blacklisted?d.warnings&&d.warnings.length?`${d.blacklisted} - ${d.warnings.join("\n")}`:d.blacklisted:d.foisted,r.setAttribute("title",e),It(b).addClass("crossedout")}else d.active_count?b.title=Rt("This_script_was_executed_0count0_times",d.all_time_active_count):d.all_time_active_count?b.title=Rt("This_script_was_executed_0count0_times_but_is_not_active_anymore",d.all_time_active_count):b.title=Rt("This_script_was_not_executed_yet")
;const v=fe("td","",d.name,d.uuid,"tw_td2");let k;if(v.appendChild(a),!d.nnew&&!d.system&&d.abuse){const e=ys.createIcon(Os.get("flag"),"",d.uuid,"issue",Rt("Report_an_issue_to_the_script_hoster_")),n=fe("span","clickable",d.name,d.uuid,"action_issue_expl");n.textContent=Rt("Report_an_issue_to_the_script_hoster_").split(/[\.\(]+/)[0],t.push({always_visible:!1,id:"action_issue_expl",img:e,text:n,oc:function(){l(d.uuid,"hoster")}})}if(!d.nnew&&!d.system&&d.support){
const e=ys.createIcon(Os.get("bug"),"",d.uuid,"bug",Rt("Report_a_bug")),n=fe("span","clickable",d.name,d.uuid,"action_issue_expl");n.textContent=Rt("Report_a_bug"),t.push({always_visible:!1,id:"action_issue_expl",img:e,text:n,oc:function(){l(d.uuid,"author")}})}const y={};if(d.active_urls&&d.active_urls.forEach((e=>{const n=mn(e).hostname;if(y[n])return;y[n]=!0;const r="/"+on("*://*."+n+"/*",!0)+"/";if(d.options.override&&d.options.override.use_excludes.includes(r))return
;const i=Rt("Exclude_0domain0",n),s=ys.createIcon(Os.get("no"),"",d.uuid,"domain"+e,i),o=fe("span","clickable",d.name,d.uuid,"action_domain");o.setAttribute("title",i),o.textContent=i,t.push({always_visible:!1,id:"action_domain",img:s,text:o,oc:function(){p(d.uuid,"add_excludes",[r])}})})),!d.deleted&&!d.blacklisted){const e=Rt("Delete"),n=ys.createIcon(Os.get("delete"),"",d.uuid,"delete_script",e),r=fe("span","clickable",d.name,d.uuid,"delete_script");r.setAttribute("title",e),r.textContent=e,
t.push({always_visible:!1,id:"delete_script",img:n,text:r,oc:function(){h(d.uuid)}})}if(d.menu_cmds){let e;try{e=new RegExp("^"+L(d.name)+"[ -:+/]*")}catch(e){console.log(e)}d.menu_cmds.forEach((n=>{const i=fe("span","clickable",d.name,d.uuid,"menucmd_"+d.id);i.setAttribute("title",n.title||d.name);const s=(e?n.name.replace(e,""):"")||n.name;i.textContent=s;const o=e=>{u(n.id,e,(()=>{xe.CLOSE_ALLOWED&&n.autoClose&&window.close()}))};if(n.accessKey){const e=n.accessKey[0];if(A(e,o,r)){
const t=new RegExp(L(e),"i");let r=i.textContent.search(t);const s=[];-1==r&&(i.textContent+=" ("+e.toUpperCase()+")",r=i.textContent.search(t)),s.push({text:i.textContent.substr(0,r)}),s.push({text:i.textContent.substr(r,1),class:"underlined"}),s.push({text:i.textContent.substr(r+1)}),i.textContent="",s.forEach((e=>{const t=fe("span",e.class||"",n.id,e);t.textContent=e.text,i.appendChild(t)}))}else console.warn("Registering keyboard shortcut for '"+n.name+"' failed")}t.push({always_visible:!0,
id:d.id,img:ys.createIcon(Os.get(n.image),s,n.id,null,""),text:i,oc:o,aoc:o})}))}if(d.deleted||!t.length)n.appendChild(v);else{const r=fe("td","",d.name,d.uuid,"mma_outer_td2");r.setAttribute("colspan",2);const s=fe("table","moremenu","mmatable-"+d.uuid);r.appendChild(s),n.appendChild(r);const o=fe("tr","moremenu_toggle",d.name,d.uuid,"mma_n_tr");if(o.appendChild(v),s.appendChild(o),e.tags_enabled){const t=Object.keys(e.tags),n=d.options.tags.filter((e=>t.includes(e)));if(n.length){
const e=fe("tr","",d.uuid,"tw_tagdn_tr1"),t=fe("td","clickable",d.uuid,"tw_tagdn",2,!0);t.setAttribute("colspan","2");const r=fe("span","tags",d.uuid,"tw_tag");e.appendChild(t),s.appendChild(e);const o=ys.createIcon(Os.get("tags"),Rt("Tags"),"tags",null,"");t.appendChild([o,r]),r.appendChild(n.map((e=>{const t=()=>i(st.getURL("options.html")+"#nav=dashboard&tag="+encodeURIComponent(e),!0);return ys.createTag(e,{...d,id:"s_tag_elem"+e},{class:"clickable",onClick:t,onAuxClick:t})})))}}
t.forEach((e=>{const t=d.uuid+e.id,n=fe("tr",e.always_visible?" always_visible":"",d.name,d.uuid,"tw_a_tr1"),r=fe("td","clickable",t,"tw_tdn",2,!0);r.setAttribute("colspan","2"),r.addEventListener("click",e.oc),e.aoc&&r.addEventListener("auxclick",e.aoc),r.appendChild([e.img,e.text]),n.appendChild(r),s.appendChild(n)})),k=fe("div","",d.name,d.uuid,"moremenuenabler")
;const a=fe("i","ifdisabled clickable far fa-"+Os.get("enabler"),d.name,d.uuid,"moremenuenabler"),l=fe("i","ifenabled clickable far fa-"+Os.get("enabler_enabled"),d.name,d.uuid,"moremenuenabler_enabled"),c=fe("td","moremenuenabler",d.name,d.uuid,"tw_td3"),A=It(c);let u;It("body").on("click",(()=>{u?u=!1:(It(s).removeClass("show_moremenu"),A.removeClass("enabled"))}),!1),k.addEventListener("click",(()=>{A.toggleClass("enabled"),It(s).toggleClass("show_moremenu"),u=!0}),!0),k.appendChild([a,l]),
o.appendChild(c),c.appendChild(k)}}t.appendChild(o),m.push(t)}else if(d.tag){const e=fe("div","clickable",d.tag,"div","ai",!0),t=function(e){if(e&&(0!=e.button||e.ctrlKey||e.metaKey)?i(st.getURL("options.html")+"#nav=dashboard&tag="+encodeURIComponent(d.tag),!0):c("toggle_tagged_scripts",{tag:d.tag,toggle:!r},!1,!1),e)return e.stopPropagation(),e.preventDefault(),!1
},n=d.data.x==d.data.of,r=d.data.x>0,s=n?"enabler_enabled":r?"enabler_middle":"enabler_disabled",o=Rt(n?"Disable_all_scripts_of_this_tag":r?"Disable_all_remaining_scripts_of_this_tag":"Enable_all_scripts_of_this_tag"),a=ys.createEnabler(s,d.tag,"enabled",{title:o,onf:!0,append:"enabled"},t),l=ys.createTag(d.tag,{...d,id:"tagelem"+d.tag},{class:"clickable",onClick:t,onAuxClick:t});e.appendChild(l),m.push(a,e)}else if(d.referrer){const e=fe("span",d.class||"",d.referrer,d.id,"ref",!0)
;e.textContent=d.name,It(r).addClass("pleasewait"),m.push(e),f(P(d.data,{referrer:d.referrer}),(i=>{It(r).removeClass("pleasewait");const[s,o]=t(r,i.items[0]);let a,l;o?(a=s,l=o):l=s,a&&n.parentNode.replaceChild(a,n),e.parentNode.replaceChild(l,e)}))}else{const e=fe("span",d.class||"",d.name,d.id,"ai");e.textContent=d.name,m.push(e)}}return m},n=(e,n,r)=>{Object.keys(n).forEach((i=>{const s=n[i];if(!e){if(!r[s.pos])return void console.warn("Warn(cAm): unknown pos "+s.pos)
;s.items&&s.items.length&&It(r[s.pos]).show()}const o=e||r[s.pos],a=o?me("tr",s.name,s.uuid||s.id,"outer"):null,l=t(a,s);if(l&&l.length){o.appendChild(a);for(let e,t=0;e=l[t];t++){const n=t==l.length-1?3-t:0,r=me("td","actiontd",s.name,s.uuid||s.id,t);n>0&&r.setAttribute("colspan",n),e&&r.appendChild(e),a.appendChild(r)}}}))},r=t=>{let r;if(r=document.getElementById("action"))r.innerHTML="";else{r=me("div"),r.setAttribute("id","action"),r.setAttribute("class","action")
;const e=fe("div","action status","status","status");e.setAttribute("id","status"),It(document.body).append(e,r)}const i=fe("table","actionlayout","actionlayout");r.appendChild(i);const s=fe("tr","actionpostr","hor"),o=fe("td","actionpostd","hor_west");let a;s.appendChild(o),i.appendChild(s);const l=fe("table","actionregion noborder ar_top","top"),c=fe("table","actionregion noborder ar_right","right");let d
;const A=fe("table","actionregion noborder ar_left","left"),u=fe("table","actionregion noborder ar_bottom","bottom");if(Math.min(e.action_menu_columns,zs)>2){d=fe("table","actionregion noborder ar_center","center");const e=fe("td","actionpostd","hor_center");a=fe("td","actionpostd","hor_east"),e.appendChild(d),s.appendChild(e),s.appendChild(a)}else Math.min(e.action_menu_columns,zs)>1?(d=c,a=fe("td","actionpostd","hor_east"),s.appendChild(a)):(d=A,a=o);It([d,c]).hide(),o.appendChild(l),
a.appendChild(c),o.appendChild(A),o.appendChild(u),n(null,t,{top:l,left:A,center:d,right:c,bottom:u})},i=(e,t)=>{try{const n=()=>{t&&xe.CLOSE_ALLOWED&&window.close()};t?sendMessage({method:"newTab",url:e},n):at(0,(r=>{lt(r.id,{method:"loadUrl",url:e,newtab:t},n)}))}catch(e){console.warn("lU:",e)}},s=(e,t,n,r)=>{r=r||{};const i=ys.createImage(r.default_icon||Qt.images.empty,void 0,n,t);if(i.inserted)return i;Array.isArray(e)||(e=e?[e]:[]);const s=async()=>{if(0==e.length)return;const t=e.shift()
;let n,r;if(t.startsWith("data:")?n=t:r=await Gs(t),n)i.setAttribute("src",n);else if(r){if(r.tryObjectUrl)i.setAttribute("src",r.tryObjectUrl);else if(r.tryDataUri)i.setAttribute("src",r.tryDataUri);else if(r.tryBlob){const e=URL.createObjectURL(r.tryBlob);i.setAttribute("src",e),i.onload=()=>URL.revokeObjectURL(e)}}else await s()};return s(),i},o=e=>{let t=confirm(e.msg),n={};return t&&e.ok?n=e.ok:!t&&e.cancel&&(n=e.cancel),(e.ok||e.cancel)&&(t=!0),n.url&&sendMessage({method:"newTab",url:n.url
},(()=>null)),t},a=e=>{let t;const n=e.key||"general";(t=m[n])&&It(t).remove(),m[n]=ys.createGobalHint(P(e,{instant:!0,onclose:()=>{sendMessage({method:"clearHint",key:n},(()=>null))},id:n,...e}),document.getElementById("status"))},l=(e,t,n)=>{try{sendMessage({method:"reportAnIssue",uuid:e,to:t},(()=>{n&&n()}))}catch(e){console.warn("raI:",e)}},c=(t,n,i,s)=>{const o=Ie();void 0===s&&(s=!0);try{sendMessage({method:"buttonPress",name:t,data:n},(t=>{
i?Ze():s&&xe.CLOSE_ALLOWED?window.close():t&&t.items&&(t.options&&(e={...e,...t.options}),r(t.items)),o.resolve()}))}catch(e){console.warn("rSU:",e)}return o.promise()};let d;const A=(e,t,n)=>{if(d)return e=e.toLowerCase(),d[e]?(console.log("MenuCmdKeyListener: ...failed!"),!1):(d[e]={key:e,cb:t,elem:n},!0)},u=(e,t,n)=>{try{const{keyCode:r,key:i,code:s,button:o,shiftKey:a,altKey:l,ctrlKey:c,metaKey:d}=t;sendMessage({method:"execMenuCmd",id:e,event:{keyCode:r,key:i,code:s,button:o,shiftKey:a,
altKey:l,ctrlKey:c,metaKey:d}},(()=>{n&&n()}))}catch(t){console.warn("Error(eMC):",t)}},h=t=>{try{sendMessage({method:"saveScript",uuid:t,code:null},(t=>{document.getElementById("action").innerHTML="",t&&t.items&&(t.options&&(e={...e,...t.options}),r(t.items))}))}catch(e){console.warn("Error(sS): "+e.message)}},p=(t,n,i)=>{try{const s={method:"modifyScriptOptions",uuid:t};n&&""!=n&&(s[n]=i),sendMessage(s,(t=>{document.getElementById("action").innerHTML="",t&&t.items&&(t.options&&(e={...e,
...t.options}),r(t.items))}))}catch(e){console.warn("Error(mSo): "+e.message)}},f=(t,n)=>{const r=Date.now(),i=t.referrer,s=t.min_delay,o=t.layout;sendMessage({method:"loadTree",referrer:i,layout:o,url:t.url,available_columns:zs,uuid:t.uuid,tabId:t.tabId},(t=>{t.options&&(e={...e,...t.options},e.statistics_enabled&&(Xn("act",{version:st.manifest.version}),Yn(!0)));const i=Date.now()-r,o=()=>{n(t)};!s||i>=s?o():window.setTimeout(o,s-i)}))},m={};st.onMessage.addListener(((e,t,n)=>{
if("update"==e.method)g(),n({});else{if("status"!=e.method)return console.log('onMessage: Unknown method "'+e.method+'"'),!1;{const t=e.options;["actions","all"].includes(e.realm)&&a(t),n({})}}return!0}));const g=window.main=()=>{f({referrer:"actions",min_delay:xe.MIN_DELAY,layout:!0,tabId:Ds},(e=>{if(e.options&&e.options.layout_user_css){const t=document.createElement("style");t.innerHTML=e.options.layout_user_css,(document.head||document.body||document.documentElement||document).appendChild(t)
}d||(console.log("MenuCmdKeyListeners initialized"),document.body.addEventListener("keydown",(e=>{e.altKey||e.ctrlKey||e.shiftKey||Object.keys(d).forEach((t=>{const n=d[t];n&&e.key==n.key&&(console.log("MenuCmdKeyListener: ... found",e.keyCode,String.fromCharCode(e.keyCode)),n.cb.apply(n.elem||window,[e]))}))}),!1)),d={},r(e.items)}))};g()}))};(async()=>{const e=async()=>{mt||wt||(mt=await bt(gt))},t=vt(wt?At.getUILanguage():navigator.language);if(t){const e=[t],n=t.split(/_/)
;n[0]!==t&&e.push(n[0]),e.forEach((e=>{gt.unshift(e)}))}await e(),wt&&(await new Promise((e=>{At.getAcceptLanguages((t=>{t.forEach((e=>{gt.push(vt(e))})),e()}))})),await e())})(),(async()=>{let e;for(;!e;)await new Promise((t=>{if(e=Be.connect("keepalive"),e){e.onDisconnect.addListener((()=>{e=void 0,t()}));try{e.postMessage({method:"keepAlive",url:location.href,messageId:Math.random().toString(36).substr(2,9)})}catch(n){return e=void 0,void t()}}else t()})),await Pe(1e3)})(),
0===window.location.pathname.indexOf("/action")?(()=>{window.sendMessage=(e,t)=>{e.origin="action";const r=e=>{n(),t(e)};st.sendMessage(e,(e=>{if(e&&e.i18n)return xt(e.i18n).always((()=>r(e)));r(e)}))};let e=null,t=null;const n=()=>{e&&(window.clearTimeout(e),e=null),t&&(t.remove(),t=null)};e=window.setTimeout((()=>{t=It('<div id="initialWait" class="lds-css ng-scope"><div class="lds-dual-ring"><div></div><div></div></div></div>'),It("body").append(t)}),200),Kt({suc:()=>{Ps()},fail:()=>{n(),
confirm(Rt("An_internal_error_occured_Do_you_want_to_visit_the_forum_"))&&window.open("https://www.tampermonkey.net/bug")}})})():0===window.location.pathname.indexOf("/options")?(window.sendMessage=(e,t)=>{e.origin="options",st.sendMessage(e,(e=>{if(e&&e.i18n)return xt(e.i18n).always((()=>{t(e)}));t(e)}))},jt.wait(Rt("Please_wait___")),Kt({suc:()=>{jt.hide(),Ms()},fail:()=>{
window.confirm(Rt("An_internal_error_occured_Do_you_want_to_visit_the_forum_"))&&(window.location.href="https://www.tampermonkey.net/bug")}}),document.title="..."):0===window.location.pathname.indexOf("/ask")?(window.sendMessage=(e,t)=>{e.origin="extension",st.sendMessage(e,(e=>{if(e&&e.i18n)return xt(e.i18n).always((()=>{t(e)}));t(e)}))},Kt({suc:()=>{jt.wait(Rt("Please_wait___")),(()=>{const e=Re();Yt((()=>{let t=null,n={},r="???",i=null;const s=(e,t,r)=>{r=r||{};const i=Ie();try{const s={
aid:e,method:t,message:r.data};sendMessage({method:"askCom",data:s},(e=>{const t=e.message||{};r.bg||jt.hide(),n=e.options||n,t.error?(t.please_close&&window.setTimeout(window.close,100),i.reject(t)):i.resolve(t)})),r.bg||jt.wait(Rt("Please_wait___"))}catch(e){console.warn("sS: "+e.message),i.reject()}return i.promise()},o=()=>s(t.aid,"ping",{bg:!0}),a=()=>s(t.aid,"abort"),l=()=>{let e;const t=fe("div","content_wrapper","ask","main"),n=fe("div","ask status","ask","status")
;It(document.body).append(t,n).addClass("main");const r=fe("div","head_container","ask","head_container"),i=fe("div","tv_container_fit","ask","tv_container"),s=me("a","head_link","ask","head_link");s.href="https://www.tampermonkey.net",s.target="_blank";const o=fe("div","float","ask","head1"),a=fe("img","banner","ask");a.src=js.brand("tampermonkey");const l=fe("div","float head","ask","head2"),d=fe("div","header_title","heading"),A=fe("div","version","version","version")
;A.textContent=`v${st.manifest.version}`;const u=me("div","search","box","");d.innerHTML="Tampermonkey<sup>&reg;</sup>",o.appendChild(a),l.appendChild(d),l.appendChild(A),s.appendChild(o),s.appendChild(l),r.appendChild(s),r.appendChild(u),t.appendChild(n),t.appendChild(r),t.appendChild(i);const h=Rs.create("_main",i);return e=c(h),jt.hide(),e},c=e=>{const t="main",n="main",i=me("div",t,n,"tab_content_h");i.textContent=r;const s=me("div",t,n,"tab_content")
;return e.appendTab(Ae(t,n),i,s).select(),s},d=e=>{const t=e.script,r=e.oldscript,i=fe("div","viewer_bottom_tab","bottom",""),s={tv:"tv tv_alt",tv_table:"tv_table tv_table_alt",tr_tabs:"tr_tabs tr_tabs_alt",tr_content:"tr_content tr_content_alt",td_content:"td_content td_content_alt",td_tabs:"td_tabs td_tabs_alt",tv_tabs_align:"tv_tabs_align tv_tabs_align_alt",tv_tabs_fill:"tv_tabs_fill tv_tabs_fill_alt",tv_tabs_table:"tv_tabs_table tv_tabs_table_alt",tv_contents:"tv_contents tv_contents_alt",
tv_tab_selected:"tv_tab tv_selected tv_tab_alt tv_selected_alt",tv_tab_close:"",tv_tab:"tv_tab tv_tab_alt",tv_content:"tv_content tv_content_alt"};if(n.editor_enabled){const e=Rs.create("_source"+t.uuid,i,s);let o;const a=(e,t)=>{const n=fe("div","tv_content tv_content_alt",e.uuid,t+"container_o"),r=fe("table","editor_container_o editor_400p_container_o p100100 noborder",e.uuid,t+"container_o"),i=fe("tr","editor_container p100100",e.uuid,t+"container");n.appendChild(r),r.appendChild(i)
;const s=fe("td","editor_outer editor_400p_outer",e.uuid,t+"edit"),o=fe("div","editor_100 editor_border",e.uuid,t+"edit");return i.appendChild(s),s.appendChild(o),{c:n,e:o}};let l=()=>{const e=Ie();return Me(["vendor/jsdiff/diff"],(()=>{l=Ie.Pledge,e.resolve()})),e.promise()};(r&&r.textContent!=t.textContent?l().then((()=>{const s=me("div",t.uuid,"diff_h");s.textContent=Rt("Changes");const l=a(t,"diff");i.diff=new Ls(l.e,{theme:"diff",fontSize:n.editor_fontSize,value:Rt("Please_wait___"),
noButtons:!0,mode:"diff",readOnly:!0},Rt),o=e.appendTab("diff",s,l.c,(()=>{window.setTimeout((()=>{i.diff.refresh(),i.diff.mirror.focus()}),1)})),window.setTimeout((()=>{let e;try{e=window.JsDiff.createTwoFilesPatch(Rt("Current_Version"),Rt("New_Version"),r.textContent,t.textContent,void 0,void 0,{timeout:4e3})}catch(e){console.warn(e)}e||(e=Rt("The_diff_for_this_script_is_too_large_to_render")),i.diff.mirror.setValue(e)}),500)})):Ie.Pledge()).then((()=>{const r=me("div",t.uuid,"source_h")
;r.textContent=Rt("Source_Code");const s=a(t,"source");i.editor=new Ls(s.e,{theme:n.editor_theme,fontSize:n.editor_fontSize,tabSize:Number(n.editor_tabSize),styleSelectedText:!0,highlightSelectionMatches:"off"!=n.editor_highlightSelectionMatches?{showToken:/\w/,annotateScrollbar:!0,cursorOnly:"cursor"==n.editor_highlightSelectionMatches}:void 0,value:t.textContent,noButtons:!0,matchBrackets:!0,readOnly:!0},Rt);const l=e.appendTab("source",r,s.c,(()=>{window.setTimeout((()=>{i.editor.refresh(),
i.editor.mirror.focus()}),1)}));o=o||l})).then((()=>{o.select()}))}else{const e=fe("div","editor_400p_outer","editor",t.name),n=fe("div","editor_400p editor_border","editor",t.name);i.appendChild(e),e.appendChild(n);const r=fe("textarea","editorta","editor",t.name);r.setAttribute("wrap","off"),n.appendChild(r),r.value=t.textContent}return i},A=(()=>{const e={};return window.addEventListener("keydown",(t=>{let n=!1;if("keydown"==t.type)return e[t.keyCode]&&(n=e[t.keyCode](t)),
n?(t.stopPropagation(),t.preventDefault(),!1):void 0}),!0),{registerListener:function(t,n){e[t]=n}}})(),u=(e,t,n)=>{n.filter((e=>e.label)).forEach((n=>{const r=n.icon?ys.createImageTextButton(n.id,n.id,n.label,n.icon,n.fn):ys.createButton(n.label,n.id,n.label,n.fn),i=It(r);if(i.addClass(t),n.id&&i.attr("data-btn-id",n.id),n.class&&i.addClass(n.class),e.appendChild(r),n.focus&&window.setTimeout((()=>{i.trigger("focus")}),300),n.keyDown){
const e=n.keyDown.keyCode?n.keyDown.keyCode:n.keyDown,t=n.keyDown.cb?n.keyDown.cb:n.fn;A.registerListener(e,t)}}))},h=e=>{const n=e.script,r=fe("div","viewer_last","install"),i=fe("div","viewer_content","install_content"),o=fe("div","ask_action_buttons","install_buttons"),a=[];return a.push({label:e.messages.action,fn:function(){s(t.aid,"install")},focus:!0}),we()<21&&a.push({label:e.messages.flags.install?Rt("Process_with_Chrome"):null,fn:function(){g(n.fileURL),It(r).hide()}}),a.push({
label:Rt("Cancel"),fn:_,keyDown:27}),e.messages.flags.update&&!e.messages.flags.user_modified&&a.push({label:Rt("Disable_Updates"),class:"disable_update",fn:function(){s(t.aid,"abort",{data:{disable_update:!0}})}}),u(o,"install",a),i.appendChild(o),r.appendChild(i),r},p=()=>{const e=fe("div","viewer_last","ok"),t=fe("div","viewer_content","ok_content"),n=fe("div","ask_action_buttons","ok_buttons");return u(n,"import",[{label:Rt("Ok"),fn:_,focus:!0}]),t.appendChild(n),e.appendChild(t),e
},f=(e,t)=>{const n=me("input",e+"_",t,"",!0);return n.setAttribute(`data-${e}-id`,t),n.checked=!0,n.type="checkbox",n.title=Rt("Press_ctrl_to_toggle_all_checkboxes"),n.addEventListener("click",(r=>{(r.ctrlKey||r.metaKey)&&It(`input[type=checkbox][data-${e}-id]:not([data-${e}="${t}"])`).prop("checked",n.checked)})),n},m=(t,n)=>{const r=t.preparat,i=t.content,s=r.script||{},o=s.uuid||s.id||s.name;r.short_info||(r.short_info=[])
;const a=fe("div","viewer_upper",o),l=fe("div","viewer_info "+(n?"viewer_info_wide":"viewer_info_multiple"),"general",o),c=fe("div","viewer_content","general_content",o),d=me("h3","install","heading",o);if(t.checkbox&&d.appendChild(f("import",t.key)),s.icon||s.icon64){const e=me("img","version","heading",o);e.src=s.icon||s.icon64,d.appendChild(e)}const A=me("span","name","heading",o);if(A.textContent=r.heading||Ct(s,"name")||"",d.appendChild(A),s.version){
const e=fe("span","view_version","heading",o);e.textContent="v"==s.version[0]?"":"v",e.textContent+=s.version,d.appendChild(e)}l.appendChild(d),t.externals&&r.short_info.unshift({prop:"externals",dom:f("externals",t.key),label:Rt("Externals")}),t.storage&&r.short_info.unshift({prop:"storage",dom:f("storage",t.key),label:Rt("Storage")}),n&&r.short_info.unshift({prop:"heading",value:r.messages.heading,label:Rt("Action")});const u=fe("table","script_desc",o);r.short_info.forEach((e=>{
const t=(e.i18n?Ct(s,e.prop):s[e.prop])||e.value,r=e.dom;if(!r&&!t&&n)return;const i=fe("tr","script_desc",e.prop,o),a=fe("td","script_desc",e.prop,o+"dt"),l=fe("td","script_desc",e.prop,o+"dd");a.textContent=e.label?e.label:"",r?l.appendChild(r):l.textContent=t||Rt("_not_set_"),i.appendChild(a),i.appendChild(l),u.appendChild(i)})),c.appendChild(u);const h=fe("div","viewer_info viewer_info_multiple","info",o);let p;if(n)p=c;else{p=fe("div","viewer_content","info_content",o)
;const e=me("h4","action","heading",o);document.title=e.textContent=r.messages.heading,p.appendChild(e)}let m=0;["errors","warnings","info"].forEach((t=>{const n=me("table",t,o+m);(r.messages[t]||[]).forEach((r=>{m++;const i=me("tr",t,o+m),s=me("td",t,o+"dt"+m),a=me("td",t,o+"dd"+m);if("info"==t)if(r.label&&r.value)s.textContent=r.label,a.textContent=r.value;else{let t='<i class="far fa-'+js.get("about")+'"></i>&nbsp;';e||(s.innerHTML=t,t=""),
a.innerHTML=t+ys.safeTagsReplace(r).replace(/\n/g,"<br />")}else if("warnings"==t){let t='<i class="far fa-'+js.get("critical")+'"></i>&nbsp;';e||(s.innerHTML=t,t=""),a.innerHTML=t+ys.safeTagsReplace(r).replace(/\n/g,"<br />")}else if("errors"==t){let t='<i class="far fa-'+js.get("error")+'"></i>&nbsp;';e||(s.innerHTML=t,t=""),a.innerHTML=t+ys.safeTagsReplace(r).replace(/\n/g,"<br />")}i.appendChild(s),i.appendChild(a),n.appendChild(i)})),p.appendChild(n)}));const g=(e,t,n,r)=>{
const i=me("table",e,o);let a=0;const l={};if(t.forEach((t=>{if(a>r)return;const s=t;if(l[s])return;l[s]=!0;const c=fe("tr",e+"desc",s,o+a),d=fe("td",e+"desc",s,o+a+"dt"),A=fe("td",e+"desc",s,o+a+"dd");d.innerHTML=0==a?ys.safeTagsReplace(n.label):"&nbsp;",A.innerHTML=a==r?'<span title="'+ys.safeTagsReplace(n.warning)+'">...!</span>':ys.safeTagsReplace(s),c.appendChild(d),c.appendChild(A),i.appendChild(c),a++})),s.options){const t=s.options.override&&s.options.override["use_"+e];if(t&&t.length){
const t=fe("tr",e+"desc","ovverride",o+a),r=fe("td",e+"desc","ovverride",o+a+"dt"),s=fe("td",e+"desc","ovverride",o+a+"dd");r.innerHTML=0==a?ys.safeTagsReplace(n.label):"&nbsp;",s.innerHTML=ys.safeTagsReplace(" ("+Rt("overwritten_by_user")+")"),t.appendChild(r),t.appendChild(s),i.appendChild(t)}}p.appendChild(i)};g("includes",(s.includes||[]).concat(s.matches||[]),{label:Rt("Include_s__"),warning:Rt("Attention_Can_not_display_all_includes_")},5),g("excludes",s.excludes||[],{
label:Rt("Exclude_s__"),warning:Rt("Attention_Can_not_display_all_excludes_")},3),l.appendChild(c),h.appendChild(p),a.appendChild(l),a.appendChild(h);const _=fe("div","section",o,"install_src");_.appendChild(a),t.install&&_.appendChild(t.install(r)),t.editor&&_.appendChild(t.editor(r)),i.appendChild(_)},g=e=>{a(),window.setTimeout((()=>{window.location=e+"#bypass=true"}),10)};var _=()=>{a(),window.setTimeout((()=>{window.close()}),3e3)};window.addEventListener("unload",(()=>{s(t.aid,"unload"),
i&&(window.clearInterval(i),i=null)}),{once:!0});const b=()=>{window.location.search||window.location.hash?(t=ue(),t.aid?((t.aid,s(t.aid,"preparat")).done((a=>{if(n.statistics_enabled&&(Xn("ask",{version:st.manifest.version}),Yn(!0)),n.layout_user_css){const e=document.createElement("style");e.innerHTML=n.layout_user_css,(document.head||document.body||document.documentElement||document).appendChild(e)}r=Rt("Install");let c=null;a.preparat&&("install"==a.type?c=()=>{m({content:l(),
preparat:a.preparat,install:h,editor:d})}:"install_error"==a.type?c=()=>{m({content:l(),preparat:a.preparat,install:p},!0)}:"import"==a.type?c=()=>{((n,r)=>{if(document.title=Rt("Import"),n.appendChild((e=>{const n=fe("div","viewer_last","import"),r=fe("div","viewer_content","import_content"),i=fe("div","ask_action_buttons import_buttons","import_buttons");u(i,"import",[{label:Rt("Import"),fn:function(){
const n=Object.keys(e.scripts).filter((e=>!!It('input[type="checkbox"][data-import-id="'+e+'"]').value()));var r,i,o,a;r=Object.keys(e.scripts).filter((e=>!!It('input[type="checkbox"][data-storage-id="'+e+'"]').value())),i=Object.keys(e.scripts).filter((e=>!!It('input[type="checkbox"][data-externals-id="'+e+'"]').value())),o=n,a=e.global_settings&&!!It('input[type="checkbox"][data-settings-id="global_settings"]').value(),s(t.aid,"import",{data:{storage_ids:r,externals_ids:i,import_ids:o,
global_settings:a}})},focus:!0},{label:Rt("Cancel"),fn:_,keyDown:27}]),r.appendChild(i),n.appendChild(r);const o=fe("div","section","btn");return o.appendChild(n),o})(r)),r.global_settings){const t=fe("div","viewer_upper","");(t=>{const n=t.key,r=t.content,i=fe("div","viewer_upper",n),s=fe("div","viewer_info viewer_info_wide","general",n),o=fe("div","viewer_content","general_content",n),a=me("h3","install","heading",n);t.checkbox&&a.appendChild(f(t.checkbox,t.key))
;const l=me("img","version","heading",n);l.src=js.brand("tampermonkey"),a.appendChild(l);const c=me("span","name","heading",n);c.textContent=Rt("Global_Settings"),a.appendChild(c),s.appendChild(a);const d=fe("table","script_desc",n);let A=fe("tr","settings_desc","action",n),u=fe("td","settings_desc","action",n+"dt"),h=fe("td","settings_desc","action",n+"dd");u.textContent=Rt("Action"),h.textContent=Rt("Global_settings_import"),A.appendChild(u),A.appendChild(h),d.appendChild(A),
A=fe("tr","settings_desc","warning",n),u=fe("td","settings_desc","warning",n+"dt"),h=fe("td","settings_desc","warning",n+"dd");let p='<i class="far fa-'+js.get("critical")+'"></i>&nbsp;';e||(u.innerHTML=p,p=""),h.innerHTML=p+ys.safeTagsReplace(Rt("This_will_overwrite_your_global_settings_")),A.appendChild(u),A.appendChild(h),d.appendChild(A),o.appendChild(d),s.appendChild(o),i.appendChild(s);const m=fe("div","section","settings_src");m.appendChild(i),r.appendChild(m)})({content:t,
checkbox:"settings",key:"global_settings"}),n.appendChild(t)}const i=r.storage_ids||[],o=r.externals_ids||[];r.scripts&&Object.keys(r.scripts).forEach((e=>{const t=r.scripts[e],s=fe("div","viewer_upper",e);m({content:s,preparat:t,checkbox:"import",storage:i.includes(e),externals:o.includes(e),key:e},!0),n.appendChild(s)}))})(l(),a.preparat)}:"permission"==a.type?c=()=>{((e,n)=>{
const r=fe("div","viewer_last","ok"),i=fe("div","viewer_content","ok_content"),o=fe("div","ask_action_buttons","ok_buttons");u(o,"permission",[{label:Rt("Ok"),fn:()=>{const e={permissions:n.permissions,origins:n.origins};dt.request(e,(n=>{let r;ot.lastError&&(r=ot.lastError.message,console.warn("notify: error on getting permission",e,"reason:",r)),((e,n,r)=>{s(t.aid,"permission",{data:{granted:e,permissions:n.permissions,origins:n.origins,error:r}})})(n,e,r)}))},focus:!0},{label:Rt("Cancel"),
fn:_,keyDown:27}]),i.appendChild(o),r.appendChild(i);const a="permission",l=fe("div","viewer_upper",a),c=fe("div","viewer_info viewer_info_wide","general",a),d=fe("div","viewer_content","general_content",a),A=me("h3","install","heading",a),h=me("span","install","heading_span",a),p=fe("span","message","heading",a);document.title=h.textContent=n.title,p.innerHTML=ys.safeTagsReplace(n.message).replace(/\n/g,"<br>"),A.appendChild(h),d.appendChild(p),c.appendChild(A),c.appendChild(d),l.appendChild(c)
;const f=fe("div","section","perm_src",a);f.appendChild(l),f.appendChild(r),e.appendChild(f)})(l(),a.preparat)}:"connect"==a.type&&(c=()=>{((e,n)=>{const r=Date.now();let i,o;n.timeout&&(i=window.setTimeout((()=>{_(),l()}),n.timeout));const a=()=>It("input[data-btn-id]")[0],l=()=>{let e;o&&window.clearInterval(o),i&&window.clearTimeout(i),o=i=null,(e=a())&&e.parentNode.removeChild(e)
},c=fe("div","viewer_last","ok"),d=fe("div","viewer_content","ok_content"),A=fe("div","ask_action_buttons","ok_buttons"),h=fe("div","ask_action_buttons","ok_buttons"),p=fe("div","ask_action_buttons","ok_buttons"),f="connect";u(A,"connect",[{label:Rt("Allow_once"),icon:"button_ok",fn:function(){return s(t.aid,"connect",{data:{ok:!0,allow:!0,once:!0}})},focus:!0},{label:Rt("Temporarily_allow"),icon:"clock",fn:function(){return s(t.aid,"connect",{data:{ok:!0,allow:!0,temporary:!0}})}},{
label:n.hostname!=n.domain?Rt("Always_allow"):Rt("Always_allow_domain"),icon:"yes_domain",fn:function(){return s(t.aid,"connect",{data:{ok:!0,allow:!0}})}},n.domain&&n.hostname!=n.domain?{label:Rt("Always_allow_domain"),icon:"yes_domain",fn:function(){return s(t.aid,"connect",{data:{ok:!0,allow:!0,whole_domain:!0}})}}:null,n.all_domains?{label:Rt("Always_allow_all_domains"),icon:"critical",fn:function(){if(l(),
window.confirm(Rt("This_gives_this_script_the_permission_to_retrieve_and_send_data_from_and_to_every_webpage__This_is_potentially_unsafe__Are_you_sure_you_want_to_continue_")))return s(t.aid,"connect",{data:{ok:!0,allow:!0,all_domains:!0}})}}:null].filter((e=>e))),u(h,"connect",[{label:Rt("Forbid_once"),icon:"cancel",fn:function(){return s(t.aid,"connect",{data:{ok:!0,deny:!0,once:!0}})},keyDown:27},{label:n.hostname!=n.domain?Rt("Always_forbid"):Rt("Always_forbid_domain"),icon:"no_domain",
fn:function(){return s(t.aid,"connect",{data:{ok:!0,deny:!0}})}},n.domain&&n.hostname!=n.domain?{label:Rt("Always_forbid_domain"),icon:"no",fn:function(){return s(t.aid,"connect",{data:{ok:!0,deny:!0,whole_domain:!0}})}}:null,{label:Rt("Dont_ask_again"),icon:"no",fn:function(){return s(t.aid,"connect",{data:{ok:!0,deny:!0,all_domains:!0}})}}].filter((e=>e))),u(p,"connect_misc",[n.tabid?{label:Rt("Focus_tab"),icon:"windowlist",fn:function(){((e,t)=>{try{sendMessage({method:"buttonPress",
name:"focus_tab",...t},(()=>{}))}catch(e){console.log("button: "+e.message)}})(0,{tabid:n.tabid})}}:null,(()=>{if(n.timeout)return o=window.setInterval((()=>{let e;(e=a())&&It(e).attr("value",Rt("Skip_timeout__0seconds0_seconds_",Math.round((n.timeout+r-Date.now())/1e3)))}),1e3),{label:Rt("Skip_timeout__0seconds0_seconds_",Math.round(n.timeout/1e3)),id:"skip_timeout_button",fn:l}})()].filter((e=>e)))
;const m=fe("div","viewer_upper",f),g=fe("div","viewer_info viewer_info_wide","general",f),b=fe("div","viewer_content","general_content",f),v=me("h3","install","heading",f),k=me("span","install","heading_span",f),y=fe("span","message","heading",f);if(n.script.icon){const e=me("img","version","heading",f);e.src=n.script.icon,k.appendChild(e)}document.title=k.textContent=Rt("A_userscript_wants_to_access_a_cross_origin_resource_")
;const w=fe("div","ask_action_buttons message","help",f),R=me("div","help",f)
;let C=Rt("A_request_to_a_cross_origin_resource_is_nothing_unusual__You_just_have_to_check_whether_this_script_has_a_good_reason_to_access_this_domain__For_example_there_are_only_a_very_few_reasons_for_a_userscript_to_contact_your_bank__Please_note_that_userscript_authors_can_avoid_this_dialog_by_adding_@connect_tags_to_their_scripts__You_can_change_your_opinion_at_any_time_at_the_scripts_settings_tab_",n.connect_url,n.settings_url)
;C=ys.safeTagsReplace(C).replace(/\[url=([^\]]+)\](.*)\[\/url\]/g,'<a target="_blank" href="$1">$2 &#x2B00;</a>').replace(/\n/g,"<br>"),R.innerHTML=C+"<br><br>",w.appendChild(R),v.appendChild(k),d.appendChild([p,w,A,h]),c.appendChild(d);const x=fe("table","script_desc",f);[{prop:"name",label:Rt("Name")},{prop:"src_url",label:Rt("Tab_URL")},n.domain?{prop:"hostname",label:Rt("Destination_domain")}:null,{prop:"url",label:Rt("Destination_URL")}].forEach((e=>{if(!e)return
;const t=n[e.prop]||n.script[e.prop]||e.value,r=fe("tr","script_desc",e.prop,f),i=fe("td","script_desc",e.prop,f+"dt"),s=fe("td","script_desc",e.prop,f+"dd");i.textContent=e.label?e.label:"",s.textContent=t||Rt("_not_set_"),r.appendChild(i),r.appendChild(s),x.appendChild(r)})),y.appendChild(x),b.appendChild(y),g.appendChild(v),g.appendChild(b),m.appendChild(g);const E=fe("div","section","connect_src",f);E.appendChild(m),E.appendChild(c),e.appendChild(E)})(l(),a.preparat)}),
i=window.setInterval(o,3e3),c&&window.setTimeout((()=>{var e,t;c(),(e=a.preparat)&&(t=e.hints)&&t.length&&t.forEach((e=>{e.globalhint&&ys.createGobalHint(e.options,It("body > div.status")[0])}))}),1))})).fail((()=>{_()})),jt.wait(Rt("Please_wait___"))):_()):window.onhashchange=()=>{b()}};st.onMessage.addListener(((e,t,r)=>{if(n=e.options||n,"confirm"==e.method){const t=e=>{r({confirm:e})};pe(e.msg,t)}else{if("showMsg"!=e.method)return!1;he(e.msg),r({})}return!0})),b()}))})()},fail:()=>{
window.confirm(Rt("An_internal_error_occured_Do_you_want_to_visit_the_forum_"))&&(window.location.href="https://www.tampermonkey.net/bug")}}),document.title="..."):0===window.location.pathname.indexOf("/userscript")&&(()=>{const e=ue(!0);e.id&&(window.location.href=st.getURL("options.html")+"#nav="+e.id+"+editor")})()})()})();