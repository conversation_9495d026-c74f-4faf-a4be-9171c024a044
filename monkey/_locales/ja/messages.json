{"0count0_changes_exported": {"message": "$count$ 件の変更点をエクスポートしました", "placeholders": {"count": {"content": "$1"}}}, "0count0_changes_imported": {"message": "$count$ 件の変更点をインポートしました", "placeholders": {"count": {"content": "$1"}}}, "0count0_errors_or_hints_were_found_": {"message": "$count$ 個のエラーまたはヒントが見つかりました。", "placeholders": {"count": {"content": "$1"}}}, "0name0_0version0_is_available__Please_re_start_your_browser_to_update_": {"message": "$name$ $version$ があります。\n更新するにはブラウザーを再起動してください!", "placeholders": {"name": {"content": "$1"}, "version": {"content": "$2"}}}, "0name0_Your_service_bot": {"message": "$name$ - サービス ボット", "placeholders": {"name": {"content": "$1"}}}, "1": {"message": "1"}, "10": {"message": "10"}, "11": {"message": "11"}, "15_Seconds": {"message": "15 秒"}, "1_Hour": {"message": "1 時間"}, "1_Minute": {"message": "1 分"}, "2": {"message": "2"}, "3": {"message": "3"}, "30_Seconds": {"message": "30 秒"}, "4": {"message": "4"}, "5": {"message": "5"}, "5_Minutes": {"message": "5 分"}, "6": {"message": "6"}, "7": {"message": "7"}, "8": {"message": "8"}, "9": {"message": "9"}, "A_recheck_of_the_GreaseMonkey_FF_compatibility_options_may_be_required_in_order_to_run_this_script_": {"message": "GreaseMonkey/FF 互換性オプションを再確認してください。このスクリプトの実行に必要な可能性があります。"}, "A_reload_is_required": {"message": "再読み込みが必要です: \n保存していない変更はすべて失われます!"}, "A_request_to_a_cross_origin_resource_is_nothing_unusual__You_just_have_to_check_whether_this_script_has_a_good_reason_to_access_this_domain__For_example_there_are_only_a_very_few_reasons_for_a_userscript_to_contact_your_bank__Please_note_that_userscript_authors_can_avoid_this_dialog_by_adding_@connect_tags_to_their_scripts__You_can_change_your_opinion_at_any_time_at_the_scripts_settings_tab_": {"message": "オリジン間リソースへのリクエストは珍しいことではありません。\nこのスクリプトにこのドメインにアクセスする正当な理由があるかどうかを確認する必要があります。\n例えば、UserScript がご利用の銀行に問い合わせる理由はごくわずかです。\n\nUserScript の作者は、スクリプトに [url=$connect$]@connect タグ[/url]を追加することで、\nこのダイアログを回避できることにご注意ください。\n\nあなたがどう判断した場合でも、スクリプトの[url=$settings$]設定タブ[/url]でいつでも変更できます。", "placeholders": {"connect": {"content": "$1"}, "settings": {"content": "$2"}}}, "A_userscript_wants_to_access_a_cross_origin_resource_": {"message": "UserScript がオリジン間リソースへのアクセスを要求しています。"}, "Aborted_by_user": {"message": "ユーザーにより中止されました"}, "Action": {"message": "操作"}, "Action_Menu": {"message": "操作メニュー"}, "Action_failed": {"message": "操作が失敗しました"}, "Actions": {"message": "操作"}, "Add": {"message": "追加"}, "Add_GM_functions_to_this_or_window": {"message": "this または window に GM 関数を追加"}, "Add_TM_to_CSP": {"message": "既存のコンテンツ セキュリティ ポリシー (CSP) を修正する"}, "Add_Tag": {"message": "タグを追加"}, "Add_Tag_to_System": {"message": "システムのリストにタグを追加"}, "Add_Tampermonkey_to_the_site_s_content_csp": {"message": "Tampermonkey を HTML の CSP に追加"}, "Add_as_0clude0": {"message": "$clude$ として追加", "placeholders": {"clude": {"content": "$1"}}}, "Add_new_script___": {"message": "新規スクリプトを追加..."}, "Add_to_icon_badge_text": {"message": "アイコン バッジ テキストに追加"}, "Advanced": {"message": "上級者"}, "All": {"message": "すべて"}, "All_but_HttpOnly": {"message": "HttpOnly 以外のすべて"}, "All_local_files": {"message": "すべてのローカルファイル"}, "All_modifications_are_only_kept_until_this_incognito_session_is_closed_": {"message": "すべての変更点は、このシークレット モードが閉じられた際に失われます!"}, "All_script_settings_will_be_reset_": {"message": "スクリプト設定はすべて失われます!"}, "All_tabs": {"message": "すべてのタブ"}, "Allow_Tampermonkey_to_collect_anonymous_statistics": {"message": "Tampermonkey が自己ホスト型の Matomo を介して匿名の統計を収集できるようにします。この統計は、作者が Tampermonkey を改良するのに役立ち、どの部分の開発に集中すべきかの判断材料としても役立ちます。協力したくない場合は無効にしてください。"}, "Allow_communication_with_cooperate_pages": {"message": "協力ページとの通信を許可する"}, "Allow_headers_to_be_modified_by_scripts": {"message": "スクリプトによる HTTP ヘッダーの変更を許可する"}, "Allow_once": {"message": "一度だけ許可"}, "Allow_scripts_to_run_scripts_in": {"message": "スクリプトを実行する既定のタブの種類"}, "Alltime_running_instances": {"message": "常時実行中のインスタンス"}, "Always": {"message": "常に"}, "Always_allow": {"message": "常に許可"}, "Always_allow_all_domains": {"message": "常にすべてのドメインを許可"}, "Always_allow_domain": {"message": "常にドメインを許可"}, "Always_ask": {"message": "常に確認"}, "Always_forbid": {"message": "常に禁止"}, "Always_forbid_domain": {"message": "常にドメインを禁止"}, "An_error_occured_during_import_": {"message": "インポート中にエラーが発生しました。"}, "An_internal_error_occured_Do_you_want_to_visit_the_forum_": {"message": "内部エラーが発生しました。この問題点が継続する場合は、OK を押してこの問題点をフォーラムで報告してください。\n\nTampermonkey フォーラムに移動しますか?"}, "Anonymous_statistics": {"message": "匿名の統計"}, "Antifeature__0name0__0description0": {"message": "$name$ を含みます: $description$", "placeholders": {"description": {"content": "$2"}, "name": {"content": "$1"}}}, "Antifeature_ads": {"message": "広告"}, "Antifeature_miner": {"message": "暗号通貨の採掘"}, "Antifeature_no_details": {"message": "説明なし"}, "Antifeature_other": {"message": "アンチ機能"}, "Antifeature_tracking": {"message": "追跡"}, "Appearance": {"message": "外観"}, "Apply_compatibility_options_to_required_script_too": {"message": "@require のスクリプトにも互換性オプションを適用する"}, "Apply_this_action_to_the_selected_scripts": {"message": "選択したスクリプトすべてにこの操作を適用"}, "Are_you_sure_that_you_don_t_want_to_be_notified_of_updates_": {"message": "Tampermonkey がブラウザーによって更新される際、同時に再起動もされます。残念ですが、これにより実行中のスクリプトが壊れる場合があります!\n\n本当に更新を通知しないように設定しますか?"}, "Are_you_unsure_about_what__sandbox_value_to_use_": {"description": "A question asked by the service bot", "message": "@sandbox のどの値を使用すべきか不明ですか?"}, "Ask_if_unknown": {"message": "不明な場合は確認"}, "At_least_one_new_connect_statement_was_added_": {"message": "少なくとも1つの新しい@connect文が追加されました。"}, "At_least_one_of_the_include_match_or_exclude_statements_was_changed_": {"message": "@include、@matchまたは@exclude文の少なくとも1つが変更されました。"}, "At_least_one_part_of_this_page_is_forbidden_by_a_setting_": {"message": "このページは、「禁止ページ」設定の一覧に載っている要素を 1 個以上含んでいます!"}, "Attention_Can_not_display_all_excludes_": {"message": "注意: exclude リストを短縮しました。\n手作業で確認してください!"}, "Attention_Can_not_display_all_includes_": {"message": "注意: include リストを短縮しました。\n手作業で確認してください!"}, "Author": {"message": "作者"}, "Auto": {"message": "自動"}, "Auto_Indent_all": {"message": "すべて自動インデント"}, "Auto_reload_on_script_enabled": {"message": "ページを自動で再読み込みする"}, "Auto_reload_on_script_enabled_desc": {"message": "スクリプトの ON/OFF を切り替えた際に、影響するページを自動で再読み込みします"}, "Auto_syntax_check_max_length": {"message": "自動構文チェックの最大サイズ"}, "Auto_syntax_check_on_typing": {"message": "文字入力時の自動構文チェック"}, "Automatically_added_user_includes_for_compatibility_reasons_": {"message": "互換性の理由から、一部のユーザー include が自動的に追加されました!"}, "Beginner": {"message": "初心者"}, "BlackCheck": {"message": "ブラックリスト確認"}, "Blacklist": {"message": "ブラックリスト"}, "Blacklist_0domain0": {"message": "$domain$ で実行しない", "placeholders": {"domain": {"content": "$1"}}}, "Blacklist_Severity": {"message": "セキュリティ ブロックに基づくブロック"}, "Blacklisted_Pages": {"message": "ページのブラックリスト"}, "Bookmarks": {"message": "ブックマーク"}, "Both": {"message": "両方"}, "Browser_API": {"message": "ブラウザー API"}, "Browser_API_Downloads": {"message": "ブラウザー API ダウンロード"}, "Browser_Sync": {"message": "ブラウザーの同期"}, "CONFLICT__This_script_was_modified_0t0_seconds_ago_": {"message": "競合:\nこのスクリプトは別のタブで$t$秒前に変更されました!", "placeholders": {"t": {"content": "$1"}}}, "Cancel": {"message": "キャンセル"}, "Casual": {"message": "寛容"}, "Center_Cursor": {"message": "カーソル行を中央に"}, "Changelog": {"message": "更新履歴"}, "Changes": {"message": "変更"}, "Changes_the_number_of_visible_config_options": {"message": "設定の表示項目数が変わります"}, "Check_disabled_scripts": {"message": "無効化されているスクリプトを更新する"}, "Check_for_Updates": {"message": "更新を確認する"}, "Check_for_userscripts_updates": {"message": "UserScript の更新を確認"}, "Check_interval": {"message": "確認の間隔"}, "Check_only_scripts_up_to_this_size_automatically_": {"message": "このサイズ以下のスクリプトのみを自動的にチェックします。"}, "Classic": {"message": "クラシック"}, "Clean_after_session": {"message": "セッション後に空にする"}, "Clear_All": {"message": "すべて消去"}, "Click_here_to_allow_TM_to_access_the_following_hosts_0hostlist0": {"message": "Tampermonkey による以下のホストへのアクセスを許可するため OK を押してください:\n\n$hostlist$", "placeholders": {"hostlist": {"content": "$1"}}}, "Click_here_to_allow_TM_to_start_downloads": {"message": "Tampermonkey がダウンロードを開始するには OK を押してください。"}, "Click_here_to_install_it_": {"message": "インストールするにはここをクリックしてください。"}, "Click_here_to_move_this_script": {"message": "このスクリプトを移動させるにはここをクリックしてください"}, "Click_here_to_see_the_recent_changes": {"message": "最近の変更点を見るにはここをクリックしてください"}, "Close": {"message": "閉じる"}, "Closing_Bracket": {"message": "閉じ中括弧"}, "Cloud": {"message": "クラウド"}, "Columns": {"message": "列数"}, "Comment": {"message": "コメント"}, "Config_Mode": {"message": "設定のモード"}, "Configures_which_sandbox_values_are_valid": {"message": "@sandbox の値としてどれを有効にするかを設定します"}, "Content_Script": {"message": "コンテンツ スクリプト"}, "Content_Script_API": {"message": "コンテンツ スクリプト API"}, "Context_Menu": {"message": "コンテキスト メニュー"}, "Controls_how_deleted_scripts_are_handled__0enabled0_moves_scripts_to_a_virtual_trash__0disabled0_permanently_deletes_scripts__0cleanAfterSession0_automatically_deletes_all_on_session_end_": {"message": "削除されたスクリプトの扱いを制御します。「%enabled%」はスクリプトを仮想ごみ箱に移動して、復元可能な状態で保持します。「%disabled%」は確認後にスクリプトを完全に削除します。「%cleanAfterSession%」はブラウザーのセッション終了後にごみ箱を自動的に空にします。", "placeholders": {"cleanAfterSession": {"content": "$3"}, "disabled": {"content": "$2"}, "enabled": {"content": "$1"}}}, "Convert_CDATA_sections_into_a_chrome_compatible_format": {"message": "CDATA セクションをブラウザー互換形式に変換する"}, "Copy": {"message": "コピー"}, "Cross_Origin_Request_Permission": {"message": "オリジン間リクエスト権限"}, "Current_Version": {"message": "現在のバージョン"}, "Cursor": {"message": "カーソル"}, "Custom_CSS": {"message": "カスタム CSS"}, "Custom_Linter_Config": {"message": "構文解析 (linter) のカスタム設定"}, "Cut": {"message": "切り取り"}, "DOM_mode_is_unsecure__Scripts_can_install_new_scripts_": {"message": "「DOM」サンドボックス モードはセキュアではありません。実行中の UserScript には拡張機能のほぼ完全な権限があり、UserScript を変更/新規インストールすることもさえできます。"}, "Dashboard": {"message": "ダッシュボード"}, "Debug": {"message": "デバッグ"}, "Debug_scripts": {"message": "スクリプトをデバッグする"}, "Decoding": {"message": "デコード中..."}, "Default": {"message": "標準"}, "Default_Dark": {"message": "標準 - ダーク"}, "Default_Darker": {"message": "標準 - もっとダーク"}, "Default_Light": {"message": "標準 - ライト"}, "Delete": {"message": "削除"}, "Delete_Line": {"message": "行を削除"}, "Delete_Line_Left": {"message": "行頭まで削除"}, "Delete_Line_Right": {"message": "行末まで削除"}, "Delete_all": {"message": "すべて削除"}, "Delete_to_Next_Word_Boundary": {"message": "次の単語境界まで削除"}, "Delete_to_Previous_Word_Boundary": {"message": "前の単語境界まで削除"}, "Delete_to_Sublime_Mark": {"message": "Sublime マークから削除"}, "Deleted_on": {"message": "削除日"}, "Description": {"message": "説明"}, "Destination_URL": {"message": "宛先URL"}, "Destination_domain": {"message": "宛先ドメイン"}, "Details": {"message": "詳細"}, "Developer": {"message": "開発用"}, "Developer_Mode": {"message": "開発用モード"}, "Disable": {"message": "無効にする"}, "Disable_Updates": {"message": "更新を無効にする"}, "Disable_all_remaining_scripts_of_this_tag": {"message": "クリックしてこのタグの残りのすべてのスクリプトを無効化"}, "Disable_all_scripts_of_this_tag": {"message": "クリックしてこのタグのすべてのスクリプトを無効化"}, "Disabled": {"message": "無効"}, "Do_you_need_help_finding_Tampermonkey_s_console_output_": {"description": "A question asked by the service bot", "message": "Tampermonkey のコンソール出力を見つける際にお手伝いが必要ですか?"}, "Do_you_need_help_installing_new_scripts_to_Tampermonkey_": {"description": "A question asked by the service bot", "message": "Tampermonkey に新しいスクリプトをインストールする際にお手伝いが必要ですか?"}, "Do_you_need_help_syncing_all_your_installed_scripts_to_another_browser_": {"description": "A question asked by the service bot", "message": "インストール済みスクリプトを別のブラウザーにすべて同期させる際にお手伝いが必要ですか?"}, "Do_you_need_help_viewing_and_editing_values_stored_by_a_userscript_": {"description": "A question asked by the service bot", "message": "UserScript によって保管された値を表示/編集する際にお手伝いが必要ですか?"}, "Do_you_need_help_working_with_Tampermonkey_": {"description": "A question asked by the service bot", "message": "Tampermonkey の使い方についてお手伝いが必要ですか?"}, "Do_you_really_want_to_store_fixed_code_": {"message": "オプション「$option$」が有効です!\n\n修正されたソースを本当に保存しますか?", "placeholders": {"option": {"content": "$1"}}}, "Do_you_want_to_know_how_to_allow_Tampermonkey_access_to_local_file_URIs_": {"description": "A question asked by the service bot", "message": "Tampermonkey にローカル ファイルの URI へのアクセスを許可する方法を知りたいですか?"}, "Do_you_want_to_use_an_external_editor_to_edit_your_scripts__nWould_you_like_to_know_how_to_set_this_up_": {"description": "A question asked by the service bot", "message": "外部エディターでスクリプトを編集したいですか? これを設定する方法を知りたいですか?"}, "Document_End": {"message": "文書の末尾"}, "Document_Start": {"message": "文書の先頭"}, "Does_not_run_in_incognito_tabs": {"message": "シークレット モードのタブで実行しない"}, "Does_not_run_in_normal_tabs": {"message": "通常のタブで実行しない"}, "Dont_ask_again": {"message": "二度と表示しない"}, "Dont_ask_me_for_simple_script_updates": {"message": "単純なスクリプトの更新について確認を求めない"}, "Downgrade": {"message": "ダウングレード"}, "Download_Mode": {"message": "ダウンロード方法"}, "Downloaded_from_0url0": {"message": "ダウンロード元: $url$", "placeholders": {"url": {"content": "$1"}}}, "Downloads": {"message": "ダウンロード"}, "Dropbox": {"message": "Dropbox"}, "DuckDuckGo": {"message": "DuckDuckGo"}, "Duplicate_Lines": {"message": "行を複製"}, "Edit": {"message": "編集"}, "Editor": {"message": "エディター"}, "Editor_reset": {"message": "変更内容を破棄"}, "Emacs": {"message": "Emacs"}, "Enable": {"message": "有効にする"}, "Enable_Editor": {"message": "高機能なエディターを有効にする"}, "Enable_Script_Sync": {"message": "UserScript 同期を有効にする"}, "Enable_Tags": {"message": "タグを有効化"}, "Enable_all_scripts_of_this_tag": {"message": "クリックしてこのタグのすべてのスクリプトを有効化"}, "Enable_autoSave": {"message": "エディターがフォーカスを失った際に内容を保存する"}, "Enable_context_menu": {"message": "コンテキスト メニューを有効にする"}, "Enable_easySave": {"message": "保存する際に確認ダイアログを表示しない"}, "Enable_this_option_to_automatically_check_the_code_on_typing_": {"message": "入力時に自動でコードをチェックするには、このオプションを有効にします。"}, "Enabled": {"message": "有効"}, "Enabling_this_makes_it_easy_for_scripts_to_leak_it_s_granted_powers_to_the_page_Therefore__off__is_the_safest_option_": {"message": "これを有効にすると、UserScript に付与された力をページに漏洩することが非常に簡単になります。そのため、「$off$」が最も安全なオプションですが、互換性の問題を引き起こす場合があります。", "placeholders": {"off": {"content": "$1"}}}, "Enforce": {"message": "強制的に検証"}, "Enter_the_new_rule": {"message": "新規ルールを入力"}, "Error": {"message": "エラー"}, "Every_12_Hour": {"message": "12 時間ごと"}, "Every_6_Hours": {"message": "6 時間ごと"}, "Every_Day": {"message": "毎日"}, "Every_Hour": {"message": "毎時間"}, "Every_Month": {"message": "毎月"}, "Every_Week": {"message": "毎週"}, "Exclude_0domain0": {"message": "$domain$ を除外", "placeholders": {"domain": {"content": "$1"}}}, "Exclude_s__": {"message": "exclude"}, "Experimental": {"message": "実験的"}, "Export": {"message": "エクスポート"}, "Export_script_0name0_0uuid0": {"message": "スクリプト「$name$」($uuid$) をエクスポート", "placeholders": {"name": {"content": "$1"}, "uuid": {"content": "$2"}}}, "Export_script_meta_data_of_0name0": {"message": "スクリプト「$name$」($uuid$) のメタ データをエクスポート", "placeholders": {"name": {"content": "$1"}, "uuid": {"content": "$2"}}}, "Export_script_source_of_0name0": {"message": "スクリプト「$name$」($uuid$) のソースをエクスポート", "placeholders": {"name": {"content": "$1"}, "uuid": {"content": "$2"}}}, "Externals": {"message": "外部"}, "Factory_Reset": {"message": "完全に初期化"}, "Fast": {"message": "高速"}, "Favicon_Service": {"message": "Favicon サービス"}, "Features": {"message": "機能"}, "File": {"message": "ファイル"}, "Filter_by": {"message": "絞り込み"}, "Find": {"message": "検索"}, "Find_All_Under": {"message": "カーソルの文字列検索 全"}, "Find_Next": {"message": "次を検索"}, "Find_Previous": {"message": "前を検索"}, "Find_Under": {"message": "カーソルの文字列検索 次"}, "Find_Under_Previous": {"message": "カーソルの文字列検索 前"}, "Fix_wrappedJSObject_property_access": {"message": "wrappedJSObject プロパティのアクセスを修正する"}, "Focus_tab": {"message": "ソース タブにフォーカスを移動"}, "Fold": {"message": "折りたたむ"}, "Fold_All": {"message": "すべて折りたたむ"}, "Folding": {"message": "折りたたみ"}, "Font_Size": {"message": "フォントサイズ"}, "Forbid_once": {"message": "一度だけ禁止"}, "Force_DOM": {"message": "DOM を強制"}, "Force_JavaScript": {"message": "JavaScript を強制"}, "Force_Raw": {"message": "Raw を強制"}, "Found_0count0_available_scripts": {"message": "$count$ 件のスクリプトを利用できます", "placeholders": {"count": {"content": "$1"}}}, "Full_reset": {"message": "初期化"}, "GM_compat_options_": {"message": "GM/FF 互換性オプション"}, "General": {"message": "全般"}, "Get_new_scripts___": {"message": "新規スクリプトを探索..."}, "Get_some_scripts___": {"message": "スクリプトを取得..."}, "Global_Settings": {"message": "グローバル設定"}, "Global_settings_import": {"message": "グローバル設定をインポート"}, "GoTo": {"message": "移動"}, "Google": {"message": "Google"}, "Google_Drive": {"message": "Google Drive"}, "Grant_all": {"message": "すべて許可"}, "Grant_selected": {"message": "選択したものを許可"}, "Group_Left": {"message": "前のまとまり"}, "Group_Right": {"message": "次のまとまり"}, "Help": {"message": "ヘルプ"}, "Hide_disabled_scripts": {"message": "無効なスクリプトを非表示にする"}, "Hide_notification_after": {"message": "通知を隠すまでの時間"}, "Highlight_selection_matches": {"message": "選択箇所と一致する文字列を強調表示する"}, "Highlight_trailing_whitespace": {"message": "末尾の空白を強調表示する"}, "Homepage": {"message": "ホームページ"}, "Host_permissions_denied_by_user_": {"message": "ユーザーにより拒否されたホストの権限:"}, "I_contributed_already": {"message": "私は既に貢献しました"}, "I_dont_want_to_contribute": {"message": "貢献したくありません"}, "Icon_badge_color": {"message": "アイコン バッジの色"}, "Icon_badge_info": {"message": "アイコン バッジの情報"}, "Icon_badge_text_color": {"message": "アイコン バッジの文字色"}, "Import": {"message": "インポート"}, "Import_from_URL": {"message": "URL からインポート"}, "Import_from_file": {"message": "ファイルからインポート"}, "Import_remote_script_0uuid0": {"message": "リモート スクリプト ($uuid$) をインポート", "placeholders": {"uuid": {"content": "$1"}}}, "Imported": {"message": "インポート済み"}, "In_order_to_search_for_userscripts__0on_action_menu0__and__0icon_badge_number0__automatically_transfer_the_tab_s_url_to_the_search_website__0on_click0_opens_the_search_page_on_click_Please_click_at_this_icon_to_open_the_privacy_policy_": {"message": "モード「$icon_badge_number$」では、タブの URL が検索 Web サイトに自動的に転送され、\n検索値として使用されます。\n\nモード「$on_action_menu$」では、URL を使用した検索は、\n操作メニューが開かれた際にのみ実行されます。\n\n「$on_click$」は、操作メニューの項目をクリックした際にのみ検索サイトを開きます。\n\n検索サイトのプライバシー ポリシーを開くには、ここをクリックしてください。", "placeholders": {"icon_badge_number": {"content": "$1"}, "on_action_menu": {"content": "$2"}, "on_click": {"content": "$3"}}}, "Include_TM_settings": {"message": "Tampermonkey の設定を含める"}, "Include_s__": {"message": "include"}, "Include_script_externals": {"message": "外部のスクリプト リソースを含める"}, "Include_script_storage": {"message": "スクリプトの保存データを含める"}, "Includes_Excludes": {"message": "include/exclude"}, "Incognito_tabs": {"message": "シークレット モードのタブ"}, "Incremental_Find": {"message": "インクリメンタル検索"}, "Indent": {"message": "インデント"}, "Indent_Less": {"message": "インデントを減らす"}, "Indent_More": {"message": "インデントを増やす"}, "Indent_with": {"message": "インデント"}, "Indentation_Width": {"message": "インデント幅"}, "Info": {"message": "情報"}, "Inject_Mode": {"message": "注入方法"}, "Insert_Line_After": {"message": "下に行を挿入"}, "Insert_Line_Before": {"message": "上に行を挿入"}, "Insert_constructor": {"message": "コンストラクタを挿入"}, "Install": {"message": "インストール"}, "Install_this_script": {"message": "このスクリプトをインストール"}, "Installed_Version_": {"message": "インストールされたバージョン"}, "Installed_userscripts": {"message": "インストール済み UserScript"}, "Instant": {"message": "即時"}, "Invalid_UserScript__Sry_": {"message": "UserScript が無効です。すみません!"}, "Invalid_UserScript_name__Sry_": {"message": "UserScript 名が無効です。すみません!"}, "JavaScript_and_DOM": {"message": "JavaScript+DOM"}, "Join_Lines": {"message": "行を連結"}, "Jump_to_line": {"message": "指定行にジャンプ"}, "Just_another_service_provided_by_your_friendly_script_updater_": {"message": "使いやすいスクリプト アップデーターが提供する他のサービス"}, "Key_Mapping": {"message": "キー割り当て"}, "Language": {"message": "言語"}, "Last_updated": {"message": "最終更新"}, "Layout": {"message": "レイアウト"}, "Learn_more": {"message": "詳細"}, "Limited_runtime_host_permissions_might_break_some_Tampermonkey_features_": {"message": "実行時ホスト権限を制限すると、スクリプト更新、GM_xmlhttpRequest などのような Tampermonkey の一部の機能が動作しないおそれがあります!"}, "Line_Case_Insensitive": {"message": "行 (大文字小文字区別なし)"}, "Line_Down": {"message": "次の行"}, "Line_Up": {"message": "前の行"}, "Line_break": {"message": "右端で折り返す"}, "Lines": {"message": "行"}, "Lines_Menu": {"message": "行"}, "Loading": {"message": "読み込み中..."}, "LogLevel": {"message": "ログ レベル"}, "Login": {"message": "ログイン"}, "Lookup_remote_script_0uuid0": {"message": "リモート スクリプト ($uuid$) を検索", "placeholders": {"uuid": {"content": "$1"}}}, "Lookup_remote_script_list": {"message": "リモート スクリプト一覧を検索"}, "Lower_Case": {"message": "小文字に変換"}, "MIME_Type": {"message": "MIME タイプ"}, "Malicious_scripts_can_violate_your_privacy_": {"message": "悪意があるスクリプトは、あなたのプライバシーを侵害する場合があり、あなたに成りすます場合があります!\n信頼できる入手元のスクリプトのみをインストールしてください。"}, "Manual_Script_Blacklist": {"message": "UserScript と @require のブラックリストの手動指定"}, "Matching_URL": {"message": "一致させたい URL"}, "Modify": {"message": "変更"}, "Modifying_a_script_will_disable_automatic_script_updates_": {"message": "スクリプトを変更すると、スクリプトの自動更新が無効になります!"}, "Move_Line_Down": {"message": "行を下に移動"}, "Move_Line_Up": {"message": "行を上に移動"}, "Name": {"message": "名前"}, "Native": {"message": "ネイティブ"}, "Never": {"message": "しない"}, "New_Tag": {"message": "新しいタグ"}, "New_Version": {"message": "新しいバージョン"}, "New_script_template_": {"message": "新規 UserScript のテンプレート"}, "New_userscript": {"message": "<新規 UserScript>"}, "Next_Bookmark": {"message": "次のブックマーク"}, "No": {"message": "いいえ"}, "No_available_scripts": {"message": "利用できるスクリプトはありません"}, "No_backups_found": {"message": "バックアップが見つかりません"}, "No_entry_found": {"message": "エントリがありません"}, "No_frames": {"message": "最上位フレーム (top) のみで実行する"}, "No_previously_denied_runtime_host_permissions_found": {"message": "以前に拒否された実行時ホスト権限は見つかりませんでした"}, "No_script_is_installed": {"message": "インストール済のスクリプトはありません"}, "No_script_is_running": {"message": "実行中のスクリプトなし"}, "No_syntax_errors_were_found_": {"message": "構文エラーは見つかりませんでした。"}, "No_update_found__sry_": {"message": "更新はありません。すみません!"}, "Normal": {"message": "通常"}, "Normal_tabs": {"message": "通常のタブ"}, "Note": {"message": "注釈"}, "Novice": {"message": "新参者"}, "Off": {"message": "OFF"}, "Ok": {"message": "OK"}, "On": {"message": "ON"}, "On_Action_Menu": {"message": "操作メニューを開いたとき"}, "On_Click": {"message": "クリックしたとき"}, "OneDrive": {"message": "OneDrive"}, "One_error_or_hint_was_found_": {"message": "1つのエラーまたはヒントが見つかりました。"}, "One_of_your_scripts_is_blacklisted__Would_you_like_to_know_why_": {"description": "A question asked by the service bot", "message": "あなたのスクリプトの 1 つがブラックリストに登録されています。その理由を知りたいですか?"}, "One_or_more_compatibility_options_are_set": {"message": "互換性オプションが 1 つ以上設定されています。これらを再確認する必要がある可能性があります。"}, "Only_Manual": {"message": "手動のみ"}, "Only_files_with_these_extensions_can_be_saved_to_the_harddisk_Be_careful_to_not_allow_file_extensions_that_represent_executables_at_your_operating_system_": {"message": "これらの拡張子を持つファイルのみをハードディスクに保存できます。\nあなたのオペレーティング システムの実行可能ファイルの拡張子を指定しないようにご注意ください!"}, "Open_changelog": {"message": "更新履歴を開く"}, "Operation_completed_successfully": {"message": "操作が正常に完了しました"}, "Original_domain_whitelist": {"message": "元のドメイン ホワイトリスト"}, "Original_excludes": {"message": "元の exclude"}, "Original_includes": {"message": "元の include"}, "Original_matches": {"message": "元の match"}, "Overwrite": {"message": "上書き"}, "Page_Filter_Mode": {"message": "ページ フィルターのモード"}, "Password": {"message": "パスワード"}, "Paste": {"message": "貼り付け"}, "Permanent": {"message": "永続的"}, "Please_check_the_0editor0_documentation_for_more_details_": {"message": "詳細については、$editor$のドキュメントを確認してください。", "placeholders": {"editor": {"content": "$1"}}}, "Please_consider_a_contribution": {"message": "寄付をお願いいたします"}, "Please_enable_anonymous_statistics_and_help_to_improve_this_extension_": {"message": "匿名統計を有効にして、この拡張機能の最適化にご協力ください。技術的なデータおよび拡張機能との相互作用のデータのみが収集されます。データにの詳細はこちらをクリックしてください。"}, "Please_enable_developer_mode_to_allow_userscript_injection_": {"message": "UserScript の注入を許可するには、開発用モードを有効にしてください。詳細はこちらをクリックしてください。"}, "Please_select_a_file": {"message": "ファイルを選択してください"}, "Please_wait___": {"message": "お待ちください..."}, "Position_": {"message": "実行順"}, "Press_ctrl_to_toggle_all_checkboxes": {"message": "チェックボックスをすべて反転するには Ctrl/Cmd を押してください"}, "Prev_Bookmark": {"message": "前のブックマーク"}, "Process_with_Chrome": {"message": "ブラウザーで処理"}, "Raw_and_JavaScript": {"message": "Raw と JavaScript"}, "Really_delete_0name0__": {"message": "「$name$」を本当に削除しますか?", "placeholders": {"name": {"content": "$1"}}}, "Really_delete_all_userscripts_": {"message": "すべての UserScript を本当に完全に削除しますか?"}, "Really_delete_the_selected_items_": {"message": "選択項目を本当に削除しますか?"}, "Really_factory_reset_the_selected_items_": {"message": "選択項目を本当に初期化しますか?"}, "Really_factory_reset_this_script_": {"message": "このスクリプトを本当に初期化しますか?"}, "Really_reset_all_changes_": {"message": "すべての変更を本当に破棄しますか?"}, "Really_restore_all_userscripts_": {"message": "すべての UserScript を本当に復元しますか?"}, "Recent_Sync_Log": {"message": "最近の同期ログ"}, "Redo": {"message": "やり直し"}, "Reindent_on_typing": {"message": "タイピング中に再インデントする"}, "Reinstall": {"message": "再インストール"}, "Reload": {"message": "再読み込み"}, "Remind_me_later": {"message": "後で知らせる"}, "Remove": {"message": "削除"}, "Remove_Tag": {"message": "タグを削除"}, "Remove__possibly_unsecure_": {"message": "完全に削除 (おそらくセキュアではありません)"}, "Remove_local_script_0name0_0uuid0": {"message": "ローカル スクリプト「$name$」($uuid$) を削除", "placeholders": {"name": {"content": "$1"}, "uuid": {"content": "$2"}}}, "Remove_remote_script_0name0_0uuid0": {"message": "リモート スクリプト「$name$」($uuid$) を削除", "placeholders": {"name": {"content": "$1"}, "uuid": {"content": "$2"}}}, "Replace": {"message": "置換"}, "Replace_": {"message": "置換しますか?"}, "Replace_All": {"message": "すべて置換"}, "Replace_all_with": {"message": "すべて置換"}, "Replace_for_each_statements": {"message": "「for each」文を置換する"}, "Replace_with": {"message": "次で置換"}, "Report_a_bug": {"message": "バグを報告"}, "Report_an_issue_to_the_script_hoster_": {"message": "不正行為を報告。\n(ユーザー アカウントが必要な場合があります)"}, "Requested_Host_Permissions": {"message": "要求されたホストの権限"}, "Requires": {"message": "require 指定"}, "Reset_Section": {"message": "初期化"}, "Reset_list": {"message": "リストをリセット"}, "Resources": {"message": "リソース"}, "Restart_Tampermonkey": {"message": "Tampermonkey を再起動"}, "Restore": {"message": "復元"}, "Restore_all": {"message": "すべて復元"}, "Revoke_Access_Token": {"message": "アクセス トークンを失効させる"}, "Run_at": {"message": "実行のタイミング"}, "Run_in": {"message": "実行"}, "Run_syntax_check": {"message": "構文チェックを実行"}, "Running_scripts": {"message": "実行中のスクリプトのインスタンス数"}, "Runtime_Host_Permissions": {"message": "実行時ホスト権限"}, "Sandbox_Mode": {"message": "サンドボックス モード"}, "Save": {"message": "保存"}, "Save_to_disk": {"message": "ディスクに保存"}, "Scan_the_QR_code_to_use_Tampermonkey_on_your_phone_or_tablet_": {"message": "QR コードをスキャンして、スマートフォンやタブレットで Tampermonkey を使用"}, "Script_0name0_is_slowing_down_some_page_loads_": {"message": "UserScript '$name$' がページの読み込みを低速化させています", "placeholders": {"name": {"content": "$1"}}}, "Script_Blacklist_Source": {"message": "UserScript ブラックリストの情報源"}, "Script_Include_Mode": {"message": "UserScript @include モード"}, "Script_Sync": {"message": "UserScript 同期"}, "Script_Tags": {"message": "スクリプトタグ"}, "Script_URL_detection": {"message": "UserScript URL の検出"}, "Script_Update": {"message": "UserScript の更新"}, "Script_authors_can_secure_external_resources_by_adding_a_SRI_hash_to_the_URL_": {"message": "スクリプト作成者は、ソース URL に SRI ハッシュを追加することで、外部リソースを保護できます。"}, "Script_cookies_access": {"message": "スクリプトによるクッキーへのアクセスを許可する"}, "Script_local_files_access": {"message": "スクリプトによるローカル ファイルへのアクセスを許可する"}, "Script_menu_commands": {"message": "UserScript メニュー コマンド"}, "Script_name_0name0": {"message": "UserScript 名: $name$", "placeholders": {"name": {"content": "$1"}}}, "Script_order": {"message": "UserScript の並び順"}, "Scripts_activated_by_context_menu": {"message": "@run-at コンテキスト メニューを使用する UserScript"}, "Search": {"message": "検索"}, "Search_for": {"message": "検索文字列"}, "Search_for_userscripts_for_this_tab": {"message": "このページ向けの UserScript を検索"}, "Searching_for_userscripts_for_this_tab": {"message": "UserScript を検索中..."}, "Security": {"message": "セキュリティ"}, "Select_All": {"message": "すべて選択"}, "Select_All_Occurrences": {"message": "出現箇所をすべて選択"}, "Select_Bookmarks": {"message": "ブックマークを選択"}, "Select_Line": {"message": "行を選択"}, "Select_Next_Occurrence": {"message": "次の出現箇所を選択"}, "Select_Scope": {"message": "スコープを選択"}, "Select_between_Brackets": {"message": "中括弧内の内側を選択"}, "Select_to_Sublime_Mark": {"message": "Sublime マークから選択"}, "Selection": {"message": "選択"}, "Server_And_Manual": {"message": "リモート + 手動"}, "Set_Sublime_Mark": {"message": "Sublime マークを設定"}, "Settings": {"message": "設定"}, "Show_backups": {"message": "バックアップを表示"}, "Show_fixed_source": {"message": "修正されたソースを表示する"}, "Show_notification": {"message": "通知を表示する"}, "Sites": {"message": "サイト"}, "Size": {"message": "サイズ"}, "Skip_timeout__0seconds0_seconds_": {"message": "タイムアウトをスキップ ($seconds$秒)", "placeholders": {"seconds": {"content": "$1"}}}, "Smart": {"message": "スマート"}, "Some_scripts_might_be_blocked_by_the_javascript_settings_for_this_page_or_a_script_blocker_": {"message": "このページまたはスクリプト ブロッカーの JavaScript 設定でブロックされたスクリプトがある可能性があります!"}, "Sort": {"message": "並べ替え"}, "Source": {"message": "入手元"}, "Source_Code": {"message": "ソースコード"}, "Spaces": {"message": "空白"}, "Split_into_Lines": {"message": "複数行に分割"}, "Start": {"message": "実行"}, "Stop": {"message": "停止"}, "Storage": {"message": "保存データ"}, "Store_data_in_incognito_mode": {"message": "シークレット モードでもデータを保管する"}, "Strict": {"message": "厳格"}, "Sublime": {"message": "Sublime Text"}, "Sublime_Mark": {"message": "Sublime マーク"}, "Subresource_Integrity": {"message": "サブリソースの整合性"}, "Swap_with_Sublime_Mark": {"message": "Sublime マークを入れ替え"}, "Sync_Now": {"message": "今すぐ実行"}, "Sync_Reset": {"message": "消去"}, "Sync_Type": {"message": "種類"}, "Sync_failed": {"message": "同期が失敗しました"}, "Sync_finished": {"message": "同期が完了しました"}, "Sync_is_running": {"message": "同期が実行中です"}, "Synchronize_your_scripts_across_browsers_and_operation_systems": {"message": "ブラウザーとオペレーティング システムの間でスクリプトを同期する"}, "System_Tags": {"message": "システムタグ"}, "TabMode": {"message": "タブの入力方式"}, "Tab_Size": {"message": "タブ サイズ"}, "Tab_URL": {"message": "タブの URL"}, "Tabs": {"message": "タブ"}, "Tag_Already_Exists": {"message": "このタグはすでに存在します"}, "Tags": {"message": "タグ"}, "Tam": {"description": "Name of the service bot", "message": "Tam"}, "Tampermonkey_and_script_version": {"message": "Tampermonkey + スクリプトのバージョン"}, "Tampermonkey_has_no_access_to_this_page": {"message": "Tampermonkey はこのページへのアクセス権なし"}, "Tampermonkey_has_no_file_access_permission_": {"message": "Tampermonkey にローカル ファイルへのアクセス権なし"}, "Tampermonkey_is_available_on_mobile_platforms": {"message": "Tampermonkey はモバイル プラットフォームで利用可能です"}, "Tampermonkey_might_not_be_able_to_provide_access_to_the_unsafe_context_when_this_is_disabled": {"message": "無効にすると、安全ではない場合 (unsafeWindow、ページの関数や変数) へのアクセスを Tampermonkey が提供できなくなります。"}, "Tampermonkey_needs_to_be_restarted_to_make_this_change_apply_Do_you_want_to_continue_": {"message": "この変更を適用するには、Tampermonkeyを再起動する必要があります。\n\n続行しますか?"}, "Tampermonkey_version": {"message": "Tampermonkeyのバージョンのみ"}, "Tampermonkey_won_t_inject_into_other_tab_types_anymore_": {"message": "Tampermonkey は他のタブ タイプにはもう注入されません!"}, "Templates": {"message": "テンプレート"}, "Temporarily_allow": {"message": "一時的に許可"}, "Temporary": {"message": "一時的"}, "Temporary_domain_whitelist": {"message": "一時的なドメイン ホワイトリスト"}, "Text": {"message": "テキスト"}, "TextArea": {"message": "テキストエリア"}, "Thank_you_very_much_": {"message": "ありがとうございました!"}, "The_Browser_API_mode_requires_a_special_permission_": {"message": "ブラウザー API モードには特別な許可が必要です。"}, "The_diff_for_this_script_is_too_large_to_render": {"message": "このスクリプトの差分 (diff) は大きすぎて描画できません"}, "The_downgraded_script_might_have_problems_to_read_its_stored_data_": {"message": "ダウングレードされたスクリプトが格納されたデータを読み取ることで問題点が発生する場合があります!"}, "The_origin_of_this_script_cant_be_determined_": {"message": "警告: このスクリプトの出所を特定できません。\n悪意がある第三者が非公開データや一部の基本的なブラウザー設定を盗むためにインストールしたか、またはオペレーティング システムまたはハードウェアが変更されました!\n\n有効にするには、開いて確認してからスクリプトを保存してください。"}, "The_script_was_modified_locally_on_0date0__Updating_it_will_overwrite_your_changes_": {"message": "スクリプトはローカルで $date$ に変更されました。更新するとあなたの変更内容は上書きされます!", "placeholders": {"date": {"content": "$1"}}}, "The_script_was_successfully_deleted_": {"message": "スクリプトを削除しました。"}, "The_script_was_successfully_moved_to_the_trash_": {"message": "スクリプトをごみ箱に移動しました。"}, "The_update_url_has_changed_from_0oldurl0_to__0newurl0": {"message": "更新 URL が変更されました。変更前:\n    「$oldurl$」\n    変更後:\n    「$newurl$」\n", "placeholders": {"newurl": {"content": "$2"}, "oldurl": {"content": "$1"}}}, "Theme": {"message": "テーマ"}, "There_are_no_active_scripts__Do_you_want_to_search_for_userscripts_": {"description": "A question asked by the service bot", "message": "アクティブなスクリプトはありません。UserScript を検索しますか?"}, "There_are_unsaved_changed_": {"message": "保存されていない変更があります。\n本当にエディターを閉じますか?"}, "There_is_an_update_for_0name0_avaiable_": {"message": "「$name$」の更新を利用できます。 :)", "placeholders": {"name": {"content": "$1"}}}, "This_external_resource_will_not_auto_update__Please_delete_it_in_order_to_enable_updates_again_": {"message": "この外部リソースは自動更新されません! 再度更新を有効にするには削除してください。"}, "This_gives_this_script_the_permission_to_retrieve_and_send_data_from_and_to_every_webpage__This_is_potentially_unsafe__Are_you_sure_you_want_to_continue_": {"message": "この操作によりこのスクリプトにあらゆるウェブページにデータを送受信する権限を与えることになります。これは潜在的に危険です!\n\n本当に続行しますか?"}, "This_is_a_possibly_foisted_script_and_a_modification_will_enable_it_": {"message": "このスクリプトの出所を特定できません。\n第三者が非公開データまたは一部の基本的なブラウザー設定を盗むためにインストールしたか、またはオペレーティング システムまたはハードウェアが変更されました。\nこの変更により、スクリプトが有効になります!"}, "This_is_a_system_script": {"message": "これはシステム スクリプトです。"}, "This_is_a_userscript": {"message": "これは JavaScript で書かれた UserScript です"}, "This_option_allows_the_Tampermonkey_homepage_and_some_script_hosting_pages_to_determine_the_Tampermonkey_version_and_whether_a_script_is_installed_": {"message": "このオプションを使用すると、Tampermonkeyのホームページといくつかのスクリプト配布ページで、Tampermonkeyのバージョンと基本的なスクリプトの情報を取得できます (インストール済み、バージョン、有効/無効)。"}, "This_page_is_blacklisted_at_the_security_settings": {"message": "セキュリティ設定でブラックリストに入っています"}, "This_script_does_not_provide_any__include_information_": {"message": "このスクリプトは @include 情報を何も提供しません。"}, "This_script_does_not_require_any_special_powers_": {"message": "このスクリプトは特殊な権限を必要としません。"}, "This_script_has_access_to_https_pages": {"message": "このスクリプトは https ページにアクセスします。"}, "This_script_has_full_web_access": {"message": "このスクリプトはインターネットに完全なアクセスをします。"}, "This_script_has_local_modifications_and_needs_to_be_updated_manually": {"message": "このスクリプトにはローカルでの変更点があり、手動で更新する必要があります。"}, "This_script_is_blacklisted_": {"message": "このスクリプトはブラックリストに入っています!"}, "This_script_stores_data": {"message": "このスクリプトは Tampermonkey にデータを保存します。"}, "This_script_was_deleted": {"message": "このスクリプトは削除されました"}, "This_script_was_deleted_by_the_hoster_": {"message": "このスクリプトはホストによって削除されました"}, "This_script_was_executed_0count0_times": {"message": "このスクリプトは$count$回実行されました", "placeholders": {"count": {"content": "$1"}}}, "This_script_was_executed_0count0_times_but_is_not_active_anymore": {"message": "このスクリプトは$count$回実行されましたが、もうアクティブではありません", "placeholders": {"count": {"content": "$1"}}}, "This_script_was_not_executed_yet": {"message": "このスクリプトはまだ実行されていません"}, "This_tag_is_not_part_of_the_system_tag_list_": {"message": "このタグはシステムタグリストに含まれていません"}, "This_will_overwrite_your_global_settings_": {"message": "この操作により、グローバル設定が上書きされます!"}, "This_will_remove_all_remote_data_from_sync_Ok_": {"message": "本当に同期先からすべてのデータを削除しますか?"}, "This_will_remove_all_scripts_and_reset_all_settings_Ok_": {"message": "本当にすべてのスクリプトを削除しすべての設定も初期化しますか?"}, "This_will_restart_Tampermonkey_Ok_": {"message": "本当に Tampermonkey を再起動しますか?"}, "Today": {"message": "今日"}, "Toggle": {"message": "切り替え"}, "Toggle_Block_Comment": {"message": "ブロック コメントを切り替え"}, "Toggle_Comment": {"message": "コメントを切り替え"}, "Toggle_Comment_Indented": {"message": "コメントを切り替え (インデントあり)"}, "Toggle_Enable": {"message": "有効/無効を反転"}, "Trace": {"message": "トレース"}, "Transpose": {"message": "1文字入れ替え"}, "Trash_Mode": {"message": "ごみ箱の動作"}, "Trash_bin": {"message": "ごみ箱"}, "Treat_like__match": {"message": "@match と同様に扱う"}, "Trigger_Update": {"message": "更新を確認"}, "Trim_trailing_whitespace_from_modified_lines": {"message": "変更した行から末尾の空白を削除する"}, "Try_to_install_as_script": {"message": "スクリプトとしてのインストールを試行"}, "Type": {"message": "種類"}, "URL": {"message": "URL"}, "UUID": {"message": "UUID"}, "Unable_to_load_script_from_url_0url0": {"message": "URL からスクリプトを読み込めません: \n $url$", "placeholders": {"url": {"content": "$1"}}}, "Unable_to_parse_0name0": {"message": "$name$ を構文解析できません", "placeholders": {"name": {"content": "$1"}}}, "Unable_to_parse_this_": {"message": "構文解析できません! :("}, "Undo": {"message": "元に戻す"}, "Unfold": {"message": "展開"}, "Unfold_All": {"message": "すべて展開"}, "Unique_running_scripts": {"message": "実行中のユニーク スクリプト数"}, "Unknown_method_0name0": {"message": "不明なメソッド $name$", "placeholders": {"name": {"content": "$1"}}}, "Unsafe": {"message": "安全ではない"}, "Update": {"message": "更新"}, "Update_Notification": {"message": "更新の通知の表示"}, "Update_URL_": {"message": "更新 URL"}, "Update_check_is_disabled": {"message": "この UserScript は更新の確認が無効または利用できません"}, "Update_interval": {"message": "更新間隔"}, "Update_local_script_0name0_0uuid0": {"message": "ローカル スクリプト「$name$」($uuid$) を更新", "placeholders": {"name": {"content": "$1"}, "uuid": {"content": "$2"}}}, "Updated_to__0version0": {"message": "$version$ に更新しました", "placeholders": {"version": {"content": "$1"}}}, "Updates": {"message": "更新"}, "Upper_Case": {"message": "大文字に変換"}, "UserScripts_API": {"message": "UserScripts API"}, "UserScripts_API_Dynamic": {"message": "UserScripts API Dynamic"}, "User_domain_blacklist": {"message": "ユーザーによるドメイン ブラックリスト"}, "User_domain_whitelist": {"message": "ユーザーによるドメイン ホワイトリスト"}, "User_excludes": {"message": "ユーザーによる exclude"}, "User_includes": {"message": "ユーザーによる include"}, "User_matches": {"message": "ユーザーによる match"}, "User_modified": {"message": "変更あり"}, "Userscript_Search": {"message": "UserScript 検索"}, "Userscript_search_integration_mode": {"message": "UserScript 検索統合"}, "Userscripts": {"message": "UserScript"}, "Using__include_is_potentially_unsafe_and_may_be_obsolete_soon___0off0_disables__include_completely__0match0__is_safe__but_may_not_compatible_the_script_developers_intention__0unsafe0__mostly_keeps_the_legacy_behavior_0default0__means__0used_default0": {"message": "@include の使用は潜在的に安全ではなく、2023年初頭のマニフェスト v3 では廃止される可能性があります。この設定は、@include が解釈される方法を構成できるようにします。「$off$」は @include を完全に無効にし、「$match$」は安全ですが、スクリプト開発者の意図と互換性がない場合があります。「$unsafe$」はレガシーな動作をほとんど維持し、「$default$」は今のところ「$used_default$」を意味します。", "placeholders": {"default": {"content": "$4"}, "match": {"content": "$2"}, "off": {"content": "$1"}, "unsafe": {"content": "$3"}, "used_default": {"content": "$5"}}}, "Utilities": {"message": "ユーティリティ"}, "VSCode": {"message": "VSCode"}, "Validate_if_given": {"message": "指定された場合に検証"}, "Validate_if_supported": {"message": "可能であれば検証"}, "Verbose": {"message": "冗長"}, "Version": {"message": "バージョン"}, "View": {"message": "閲覧"}, "Vim": {"message": "Vim"}, "Waiting_for_sync_to_finish": {"message": "同期の完了を待機中"}, "Warning": {"message": "警告"}, "Warning_unsafe_site_warnings_might_appear_": {"message": "警告: スクリプトに潜在的に危険なURLが含まれていると、安全でないサイトの警告が表示されることがあります。"}, "WebDAV": {"message": "WebDAV"}, "Whitelist": {"message": "ホワイトリスト"}, "Whitelisted_File_Extensions": {"message": "ファイル拡張子のホワイトリスト"}, "Whitelisted_Pages": {"message": "ページのホワイトリスト"}, "Windows": {"message": "Windows"}, "Would_you_like_to_know_how_to_overwrite_or_extend_a_script_s_includes_and_or_excludes_": {"description": "A question asked by the service bot", "message": "スクリプトの include や exclude を上書き/拡張する方法を知りたいですか?"}, "Would_you_like_to_learn_how_to_export_and_import_your_scripts_": {"description": "A question asked by the service bot", "message": "スクリプトをエクスポート/インポートする方法を学びたいですか?"}, "XHR_Security": {"message": "XHR セキュリティ"}, "Yandex_Disk": {"message": "Yandex.Disk"}, "Yank_Sublime_Mark": {"message": "Sublime マーク ヤンク"}, "Yes": {"message": "はい"}, "You_are_about_to_downgrade_a_UserScript": {"message": "注意! UserScript のダウングレード"}, "You_are_about_to_install_a_UserScript_": {"message": "UserScript のインストール"}, "You_are_about_to_modify_a_UserScript_": {"message": "UserScript の修正"}, "You_are_about_to_reinstall_a_UserScript_": {"message": "UserScript の再インストール"}, "You_are_about_to_update_a_UserScript_": {"message": "UserScript の更新"}, "You_can_add_your_custom_CSS_rules_here_": {"message": "Tampermonkey UI 用の 独自 CSS ルールをここに追加できます。表示が崩れた場合は、オプション ページのURLに \"?layout=reset\" を追加することで、デフォルトのレイアウトに戻せます。"}, "You_can_add_your_custom_linter_config_here_": {"message": "ここに構文解析のカスタム設定を追加できます。"}, "Your_language_is_not_supported__Click_here_to_get_intructions_how_to_translate_TM_": {"message": "あなたの言語には未対応ですか?\nTampermonkey を翻訳する手順についてはこちらをクリックしてください。"}, "Your_whitelist_seems_to_include_executable_files_This_means_your_userscripts_may_download_malware_or_spyware_to_your_harddisk_": {"message": "ホワイトリストが実行可能ファイルを含んでいるようです!\nこれは、UserScript がマルウェアやスパイウェアをダウンロードできてしまうことを意味します!!"}, "Zip": {"message": "ZIP"}, "__Please_choose__": {"message": "-- 選択してください --"}, "_not_set_": {"message": "<未設定>"}, "connect_mode": {"message": "@connect 文を確認"}, "extDescription": {"message": "ユーザースクリプトでウェブを書き換える"}, "extName": {"message": "Tam<PERSON>mon<PERSON>"}, "extNameBeta": {"message": "Tampermonkey ベータ版"}, "extNameLegacy": {"message": "Tampermonkey Legacy"}, "extShortName": {"message": "Tam<PERSON>mon<PERSON>"}, "extShortNameBeta": {"message": "TM ベータ"}, "extShortNameLegacy": {"message": "TM Legacy"}, "fatal_error": {"message": "致命的なエラー"}, "overwritten_by_user": {"message": "ユーザー設定により上書きされました"}, "require_and_resource": {"message": "外部 (@require と @resource)"}, "severity_1": {"message": "1 (セキュア度最大)"}, "severity_10": {"message": "10 (セキュア度最小)"}, "severity_2": {"message": "2"}, "severity_3": {"message": "3"}, "severity_4": {"message": "4"}, "severity_5": {"message": "5"}, "severity_6": {"message": "6"}, "severity_7": {"message": "7"}, "severity_8": {"message": "8"}, "severity_9": {"message": "9"}, "some_secs": {"message": "数"}, "strict_mode": {"message": "厳格モード"}, "top_level_await": {"message": "top-level await"}}