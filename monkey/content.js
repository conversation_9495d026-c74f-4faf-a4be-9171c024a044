((twod, k) => { if(window[k]) return; window[k] = true;(()=>{"use strict";function e(e,t,n,o,s,r,i){try{var c=r(e,i),a=$t(c,"value")}catch(e){return void n(e)}$t(c,"done")?t(a):nn(new on((e=>e(a))),o,s)}function t(t){const n=function(){var n=this,o=arguments;return new on((function(s,r){function i(t){e(a,s,r,i,c,sn,t)}function c(t){e(a,s,r,i,c,rn,t)}var a=Pt(t,n,o);i(void 0)}))};return Ct(n,"apply",(function(e,t){return Pt(n,e,t)})),n}function n(e,t){let n,o=!0;return"boolean"==typeof e?(o=e,n=t):n=e,jn((e=>{const t=pn(ht.document)
;"interactive"==t||"complete"==t?(n&&n(),e()):Pn.addEventListener("DOMContentLoaded",(()=>{n&&n(),e()}),jt({capture:o,once:!0}))}))}const o=["chrome"],s=e=>{const t=Object.call,n=t.bind(t),o=Object.assign,s=Object.getOwnPropertyDescriptor,r=Object.getPrototypeOf,i=e=>o({__proto__:null},e),c=r(function*(){}());return i({sourceWindow:e,cSO:i,F_c:n,F_a:Object.apply,F_b:Object.bind,F_tS:Function.toString,A_fE:[].forEach,A_so:[].some,A_sh:[].shift,A_j:[].join,A_iO:[].indexOf,A_iA:Array.isArray,O_a:o,
O_k:Object.keys,O_v:Object.values,O_dP:Object.defineProperties,O_dPy:Object.defineProperty,O_hOP:Object.hasOwnProperty,O_gOPN:Object.getOwnPropertyNames,O_gOPD:s,O_gOPDs:Object.getOwnPropertyDescriptors,O_gPO:r,O_tS:{}.toString,J_p:JSON.parse,J_s:JSON.stringify,M_f:Math.floor,M_r:Math.random,M_m:Math.max,M_mi:Math.min,N_tS:(0).toString,N_MSI:Number.MAX_SAFE_INTEGER,P_t:Promise.prototype.then,P_c:Promise.prototype.catch,P_co:Promise.prototype.constructor,G_n:c.next,G_t:c.throw,
R_rABS:FileReader.prototype.readAsBinaryString,R_rAT:FileReader.prototype.readAsText,R_r:s(FileReader.prototype,"result").get,R_enq:e.ReadableStreamDefaultController?e.ReadableStreamDefaultController.prototype.enqueue:null,R_cl:e.ReadableStreamDefaultController?e.ReadableStreamDefaultController.prototype.close:null,S_fCC:String.fromCharCode,S_sl:"".slice,S_su:"".substr,S_sp_nr:"".split,S_iO:"".indexOf,S_tr:"".trim,S_r_nr:"".replace,S_rA_nr:"".replaceAll,S_cCA:"".charCodeAt,S_tLC:"".toLowerCase,
S_tUC:"".toUpperCase,Y_tST:Symbol.toStringTag,Y_unsc:Symbol.unscopables,USP_tS:URLSearchParams.prototype.toString,D_pFS:DOMParser.prototype.parseFromString,U_cOU:URL.createObjectURL,U_rOU:URL.revokeObjectURL,X_o:XMLHttpRequest.prototype.open,X_pSD:XMLHttpRequest.prototype.DONE,X_pSH:XMLHttpRequest.prototype.HEADERS_RECEIVED,X_pSL:XMLHttpRequest.prototype.LOADING,X_pSO:XMLHttpRequest.prototype.OPENED,X_pSU:XMLHttpRequest.prototype.UNSENT,X_s:XMLHttpRequest.prototype.send,D_n:Date.now,
I_tS:e=>""+e,E_r:Element.prototype.remove,E_s:Element.prototype.setAttribute,E_rA:Element.prototype.removeAttribute,D_cS:s(Document.prototype,"currentScript").get,D_gRS:s(Document.prototype,"readyState").get,D_cE:Document.prototype.createElementNS,D_gEBT:Document.prototype.getElementsByTagName,M_rT:s(MouseEvent.prototype,"relatedTarget").get,C_d:s(CustomEvent.prototype,"detail").get,P_p:s(PageTransitionEvent.prototype,"persisted").get,C_pA:CustomEvent.prototype.AT_TARGET,
M_d:MutationObserver.prototype.disconnect,W_aEL:addEventListener,W_rEL:removeEventListener,parseInt,parseFloat,CustomEvent,CompositionEvent,KeyboardEvent,MouseEvent,MutationObserver,console:Object.assign({},console),Error,Uint8Array,Blob,ReadableStream,Number,String,Proxy,Window,FileReader,DOMParser,XMLHttpRequest,Function,RegExp,Promise,encodeURIComponent,decodeURIComponent,encodeURI,decodeURI,escape,unescape,atob,btoa,setTimeout,clearTimeout,setInterval,clearInterval,postMessage,dispatchEvent,
alert,prompt,confirm,close,getElementById:e.Document.prototype.getElementById,createEvent:e.Document.prototype.createEvent,createElement:e.Document.prototype.createElement})},r="vault"in twod;if(r&&void 0===twod.vault)throw"Invalid vault"
;const i=twod.vault=twod.vault||s(twod.unsafeWindow),{cSO:c,F_c:a,F_a:d,F_b:l,F_tS:u,A_fE:p,A_so:g,A_sh:m,A_j:f,A_iO:h,A_iA:v,O_a:_,O_k:b,O_v:w,O_dP:y,O_dPy:M,O_hOP:E,O_gOPN:L,O_gOPD:I,O_gOPDs:S,O_gPO:O,O_tS:x,J_p:R,J_s:D,M_f:P,M_r:j,M_m:C,M_mi:$,N_tS:A,N_MSI:T,P_t:U,P_c:k,P_co:B,G_n:F,G_t:X,R_rABS:N,R_rAT:q,R_r:W,R_enq:H,R_cl:G,S_fCC:J,S_sl:K,S_su:z,S_iO:Y,S_sp_nr:V,S_tr:Z,S_rA_nr:Q,S_cCA:ee,S_tLC:te,S_tUC:ne,Y_tST:oe,Y_unsc:se,D_pFS:re,D_cS:ie,D_gRS:ce,D_cE:ae,D_gEBT:de,E_r:le,E_s:ue,E_rA:pe,C_pA:ge,M_d:me,C_d:fe,P_p:he,M_rT:ve,U_cOU:_e,U_rOU:be,USP_tS:we,X_o:ye,X_s:Me,X_pSD:Ee,X_pSH:Le,X_pSL:Ie,X_pSO:Se,X_pSU:Oe,D_n:xe,I_tS:Re,W_aEL:De,W_rEL:Pe,parseInt:je,parseFloat:Ce,console:$e,encodeURIComponent:Ae,decodeURIComponent:Te,encodeURI:Ue,decodeURI:ke,escape:Be,unescape:Fe,atob:Xe,btoa:Ne,postMessage:qe,dispatchEvent:We,alert:He,prompt:Ge,confirm:Je,close:Ke,getElementById:ze,createEvent:Ye,createElement:Ve,CustomEvent:Ze,CompositionEvent:Qe,KeyboardEvent:et,MouseEvent:tt,MutationObserver:nt,Uint8Array:ot,FileReader:st,DOMParser:rt,XMLHttpRequest:it,Function:ct,RegExp:at,Blob:dt,ReadableStream:lt,Number:ut,String:pt,Proxy:gt,Window:mt}=i,ft=twod,{unsafeWindow:ht,unsafeThis:vt}=ft,_t=a,bt=d,wt=b,yt=w,Mt=_,Et=M,Lt=L,It=I,St=O,Ot=v,xt=j,Rt=C,Dt=$,Pt=(e,t,n)=>_t(bt,e,t,n),jt=c,Ct=(e,t,n)=>(Et(e,t,jt({
value:n,configurable:!0,enumerable:!0,writable:!0})),e),$t=(e,t)=>{const n=It(e,t);return n?jt(n).value:void 0},At=(e,t)=>{const n=(e,t,o)=>{const s=It(e,t),r=s?jt(s):void 0;let i;return r?r.enumerable?r.value:void 0:--o>=0&&(i=St(e))?n(i,t,o):void 0};return n(e,t,5)},Tt=e=>{const t=(t,...n)=>Pt(e,t,n);return Ct(t,"wrappedJSObject",e),t},Ut=()=>e=>Tt(e),kt=Tt(l),Bt=R,Ft=Ut()(g),Xt=Ut()(m),Nt=(e,t)=>{const n=[];return Jt(e,(e=>{t(e)&&Ht(n,e)})),n},qt=(e,t,n)=>{const o=e.length;let s=t||0
;if(s>=o)return[];s<0&&(s=Rt(0,o+s));let r=void 0===n?o:n;r<0&&(r=Rt(0,o+r)),r>o&&(r=o);const i=jt({});for(let t=s;t<r;t++)i[t]=$t(e,t);return yt(i)},Wt=Ut()(f),Ht=(e,t)=>{let n=e.length||0;return Ct(e,n,t),n++,e.length=n},Gt=Ut()(h),Jt=Ut()(p),Kt=Ut()(E),zt=Ut()(V),Yt=Ut()(K),Vt=Tt(x),Zt=St({}),Qt=e=>{const t=jt(e),n=Lt(t);for(let e=0;e<n.length;e++){const o=n[e],s=t[o];null!==s&&"object"==typeof s&&St(s)===Zt&&(t[o]=Qt(s))}return t},en=Ut()(u),tn=Ut()(A),nn=Ut()(U),on=(Ut()(k),
kt(B,St((async()=>{})()))),sn=Ut()(F),rn=Ut()(X),cn=(Ut()(N),Ut()(q),Ut()(W),H&&Ut()(H),G&&Ut()(G),J),an=Ut()(z),dn=Ut()(Y),ln=(Ut()(Z),Ut()(Q||function(e,t){return Wt(zt(this,e),t)}),Ut()(ee)),un=Ut()(te),pn=(Ut()(ne),Tt(re),Tt(ie),Tt(ce)),gn=Tt(ae),mn=Tt(de),fn=(Tt(le),Tt(ue),Tt(pe),Tt(ve)),hn=Tt(fe),vn=Tt(he),_n=me?Ut()(me):me,bn=be,wn=(Tt(we),Ut()(ye),Ut()(Me),xe),yn=Ze,Mn=tt,En=ot,Ln=nt,In=dt,Sn=at,On=function(e,t){return $t(e,t)},xn=jt({addEventListener:!1,Array:!0,Blob:!0,close:!1,
CustomEvent:!0,Date:!0,DOMParser:!0,Error:!0,Event:!0,FileReader:!0,KeyboardEvent:!0,location:!0,Math:!0,MouseEvent:!0,Number:!0,Object:!0,ReadableStream:!0,removeEventListener:!1,Uint8Array:!0,XMLHttpRequest:!0}),Rn=((()=>{const e=jt({getElementById:ze,createEvent:Ye,createElement:Ve,dispatchEvent:We,addEventListener,removeEventListener}),t=jt({});Jt(wt(e),(n=>{try{const o=e[n];t[n]=function(...e){return Pt(o,ht.document,e)}}catch(e){t[n]=((e,t)=>{
if(Dn.error(`Tampermonkey sandbox preparation ${t?"("+t+") ":""}failed. This usually is caused by a third-party extension.`,e),t)return()=>{}})(e,`document.${n}`)}}))})(),jt({top:!0,location:!0}));twod.bridges=twod.bridges||jt({});const Dn=twod.console=twod.console||jt({}),Pn=jt({addEventListener:kt(De,ht),removeEventListener:kt(Pe,ht)});Jt(wt(Rn),(async e=>{if(!Pn[e])try{const t=ht[e];if(null==t)return;Pn[e]=t}catch(e){}})),Jt(wt(xn),(async e=>{if(!Pn[e])try{let t=$t(ht,e)
;if(void 0===t&&(vt===ht||void 0===(t=$t(vt,e))))return;const n=xn[e];Pn[e]=!1===n&&"function"==typeof t?kt(t,vt):t}catch(e){}}));const jn=e=>{let t,n=[],o=!1;e((e=>{if(!o){if(n.length){const t=n;n=[],Jt(t,(t=>t(e)))}else t=e;o=!0}}));const s=jt({then:e=>(o?e(t):Ht(n,e),s)});return s},Cn=()=>tn(wn()+19831206*xt()+1,36),$n=function(){var e=t((function*(e){yield null,e()}));return function(t){return e.apply(this,arguments)}}(),An=(()=>{const{console:e,bridges:t}=twod,n=jt({});let o
;const s=(t,o,s,i)=>{let c,a=[],d=[],l=[],u=[];const p=()=>{d=[],l=[],u=[],h(),y=null,delete n[o]},g=e=>{t.send("port.message",jt({response_id:o,value:e}))},m=e=>{i&&"messageId"in e&&Ht(a,e),g(e)},f=(e,t=!0)=>{c=e,t&&g(e)},h=()=>{c=void 0},v=jt({addListener:e=>{Ht(d,e)}}),_=jt({addListener:e=>{Ht(l,e)}}),b=jt({addListener:e=>{Ht(u,e)}}),w=()=>{p(),t.send("port.message",jt({response_id:o,disconnect:!0}))};let y=jt(s?{postMessage:m,connectMessage:f,stopReconnecting:h,onMessage:v,onDisconnect:_,
onReconnect:b,disconnect:w}:{postMessage:m,onMessage:v,onDisconnect:_,disconnect:w});return n[o]=jt({message:t=>{if(i&&(e=>"ack"in e)(t)){const{messageId:n}=t;(t=>{if(!t)return void e.warn("PortMessaging: no message id in ack message");if(c&&c.messageId===t)return;let n=-1;Ft(a,((e,o)=>e.messageId===t&&(n=o,!0))),-1!==n?(Jt(qt(a,0,n),(t=>e.warn(`PortMessaging: message ${t.messageId} was not ack'ed!`,t))),a=qt(a,n+1)):e.warn(`PortMessaging: no one is waiting for ${t}`)})(n)}d&&Jt(d,(e=>e(t)))},
disconnect:()=>{if(c&&s)return r(s,o),f(c),i&&Jt(a,(e=>g(e))),void(u.length&&Jt(u,(e=>e())));l.length&&Jt(l,(e=>e())),p()}}),y},r=(e,n)=>{t.first.send("port.message",jt({response_id:n,connect:!0,destination:e}))};return jt({message:(t,r)=>{let i;if(t.connect){if(!t.destination||!t.response_id)throw"invalid message";o&&o(t.destination,s(r,t.response_id))}else{if(!t.response_id)throw"invalid message";if(!(i=n[t.response_id]))return void e.warn("ports: unknown id",t.response_id,t)
;t.disconnect?i.disconnect():i.message(t.value)}},connect:function(e,n,o){const i=Cn();return r(e,i),s(t.first,i,n?e:void 0,o)},onConnect:jt({addListener:e=>{o=e}})})})(),{setInterval:Tn,setTimeout:Un,clearInterval:kn,clearTimeout:Bn,console:Fn,cloneInto:Xn,exportFunction:Nn,createObjectIn:qn}=ht,Wn=Object.assign({},Fn),Hn=Tn.bind(ht),Gn=Un.bind(ht),Jn=kn.bind(ht),Kn=(Bn.bind(ht),
vt==ht.top),{arrayBuffer:zn,blob:Yn}=ht.Response.prototype,{arrayBuffer:Vn}=ht.Blob.prototype,{then:Zn}=ht.Promise.prototype,{fetch:Qn,location:eo,document:to,Response:no}=ht,oo=Tt(zn),so=Tt(Yn),ro=Tt(Vn),io=Xn,co=jt({encode:e=>Fe(Ae(e)),decode:e=>Te(Be(e))}),ao=(jt({encode:e=>{let t="";for(let n=0;n<e.length;n++)t+=cn(255&ln(e,n));return Ne(t)},decode:e=>Xe(e)}),(e,t)=>{const n=(e,t)=>e<<t|e>>>32-t,o=(e,t)=>{
const n=2147483648&e,o=2147483648&t,s=1073741824&e,r=1073741824&t,i=(1073741823&e)+(1073741823&t);return s&r?2147483648^i^n^o:s|r?1073741824&i?3221225472^i^n^o:1073741824^i^n^o:i^n^o},s=(e,t,s,r,i,c,a)=>(e=o(e,o(o(((e,t,n)=>e&t|~e&n)(t,s,r),i),a)),o(n(e,c),t)),r=(e,t,s,r,i,c,a)=>(e=o(e,o(o(((e,t,n)=>e&n|t&~n)(t,s,r),i),a)),o(n(e,c),t)),i=(e,t,s,r,i,c,a)=>(e=o(e,o(o(((e,t,n)=>e^t^n)(t,s,r),i),a)),o(n(e,c),t)),c=(e,t,s,r,i,c,a)=>(e=o(e,o(o(((e,t,n)=>t^(e|~n))(t,s,r),i),a)),o(n(e,c),t)),a=e=>{
let t,n,o="",s="";for(n=0;n<=3;n++)t=e>>>8*n&255,s="0"+tn(t,16),o+=an(s,s.length-2,2);return o};let d,l,u,p,g,m,f,h;t&&"utf-8"==un(t)&&(e=co.encode(e));const v=(e=>{let t;const n=e.length,o=n+8,s=16*((o-o%64)/64+1),r=[];for(let e=0;e<=s-1;e++)Ht(r,0);let i=0,c=0;for(;c<n;)t=(c-c%4)/4,i=c%4*8,r[t]=r[t]|ln(e,c)<<i,c++;return t=(c-c%4)/4,i=c%4*8,r[t]=r[t]|128<<i,r[s-2]=n<<3,r[s-1]=n>>>29,r})(e);g=1732584193,m=4023233417,f=2562383102,h=271733878;for(let e=0;e<v.length;e+=16)d=g,l=m,u=f,p=h,
g=s(g,m,f,h,v[e+0],7,3614090360),h=s(h,g,m,f,v[e+1],12,3905402710),f=s(f,h,g,m,v[e+2],17,606105819),m=s(m,f,h,g,v[e+3],22,3250441966),g=s(g,m,f,h,v[e+4],7,4118548399),h=s(h,g,m,f,v[e+5],12,1200080426),f=s(f,h,g,m,v[e+6],17,2821735955),m=s(m,f,h,g,v[e+7],22,4249261313),g=s(g,m,f,h,v[e+8],7,1770035416),h=s(h,g,m,f,v[e+9],12,2336552879),f=s(f,h,g,m,v[e+10],17,4294925233),m=s(m,f,h,g,v[e+11],22,2304563134),g=s(g,m,f,h,v[e+12],7,1804603682),h=s(h,g,m,f,v[e+13],12,4254626195),
f=s(f,h,g,m,v[e+14],17,2792965006),m=s(m,f,h,g,v[e+15],22,1236535329),g=r(g,m,f,h,v[e+1],5,4129170786),h=r(h,g,m,f,v[e+6],9,3225465664),f=r(f,h,g,m,v[e+11],14,643717713),m=r(m,f,h,g,v[e+0],20,3921069994),g=r(g,m,f,h,v[e+5],5,3593408605),h=r(h,g,m,f,v[e+10],9,38016083),f=r(f,h,g,m,v[e+15],14,3634488961),m=r(m,f,h,g,v[e+4],20,3889429448),g=r(g,m,f,h,v[e+9],5,568446438),h=r(h,g,m,f,v[e+14],9,3275163606),f=r(f,h,g,m,v[e+3],14,4107603335),m=r(m,f,h,g,v[e+8],20,1163531501),
g=r(g,m,f,h,v[e+13],5,2850285829),h=r(h,g,m,f,v[e+2],9,4243563512),f=r(f,h,g,m,v[e+7],14,1735328473),m=r(m,f,h,g,v[e+12],20,2368359562),g=i(g,m,f,h,v[e+5],4,4294588738),h=i(h,g,m,f,v[e+8],11,2272392833),f=i(f,h,g,m,v[e+11],16,1839030562),m=i(m,f,h,g,v[e+14],23,4259657740),g=i(g,m,f,h,v[e+1],4,2763975236),h=i(h,g,m,f,v[e+4],11,1272893353),f=i(f,h,g,m,v[e+7],16,4139469664),m=i(m,f,h,g,v[e+10],23,3200236656),g=i(g,m,f,h,v[e+13],4,681279174),h=i(h,g,m,f,v[e+0],11,3936430074),
f=i(f,h,g,m,v[e+3],16,3572445317),m=i(m,f,h,g,v[e+6],23,76029189),g=i(g,m,f,h,v[e+9],4,3654602809),h=i(h,g,m,f,v[e+12],11,3873151461),f=i(f,h,g,m,v[e+15],16,530742520),m=i(m,f,h,g,v[e+2],23,3299628645),g=c(g,m,f,h,v[e+0],6,4096336452),h=c(h,g,m,f,v[e+7],10,1126891415),f=c(f,h,g,m,v[e+14],15,2878612391),m=c(m,f,h,g,v[e+5],21,4237533241),g=c(g,m,f,h,v[e+12],6,1700485571),h=c(h,g,m,f,v[e+3],10,2399980690),f=c(f,h,g,m,v[e+10],15,4293915773),m=c(m,f,h,g,v[e+1],21,2240044497),
g=c(g,m,f,h,v[e+8],6,1873313359),h=c(h,g,m,f,v[e+15],10,4264355552),f=c(f,h,g,m,v[e+6],15,2734768916),m=c(m,f,h,g,v[e+13],21,1309151649),g=c(g,m,f,h,v[e+4],6,4149444226),h=c(h,g,m,f,v[e+11],10,3174756917),f=c(f,h,g,m,v[e+2],15,718787259),m=c(m,f,h,g,v[e+9],21,3951481745),g=o(g,d),m=o(m,l),f=o(f,u),h=o(h,p);const _=a(g)+a(m)+a(f)+a(h);return un(_)}),lo=(e,t)=>{const n=gn(ht.document,"http://www.w3.org/1999/xhtml",e);return"string"==typeof t?n.append(t):t&&Jt(wt(t),(e=>{
"textContent"==e?n.textContent=t[e]:n.setAttribute(e,t[e])})),n},uo=e=>{const t=ht.document;if(t.body||"text/xml"==t.contentType)e();else{let t=!1;const o=new Ln((()=>{t=!0,uo(e),(_n||(e=>e.disconnect()))(o)}));o.observe(ht.document,jt({childList:!0})),n((()=>{t||((_n||(e=>e.disconnect()))(o),e())}))}},po=e=>{n((()=>{Gn((()=>e()),1)}))};let go,mo;const fo=eo.host,ho=e=>{"n:"==an(e,0,2)?go=an(e,2):e&&(mo=an(e,2))},vo=()=>go||(mo?(go=ao(`${fo}#${mo}`),
go):void 0),_o=e=>void 0!==$t(e,"objUrl"),bo=e=>void 0!==$t(e,"blob"),wo=e=>void 0!==$t(e,"dataUri"),yo=e=>void 0!==e.binary,Mo=globalThis,{chrome:Eo,browser:Lo}=Mo;let Io,So,Oo,xo;Oo=()=>{if(void 0!==Io)return Io;try{const e=navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./);e&&(Io=parseInt(e[2]))}catch(e){}return Io},xo=()=>{if(void 0!==So)return So;try{So=-1!=navigator.userAgent.search(/Android|Mobile/)}catch(e){}return So},Oo();const Ro=["chrome-extension:"];[].concat(["chrome"])
;const Do=(()=>{const e={getInternalPathRegexp:function(e,t){const n=new RegExp("(\\"+["/",".","+","?","|","(",")","[","]","{","}","\\"].join("|\\")+")","g"),o=Ro[0]+"//"+Do.id+"/";return new RegExp(o.replace(n,"\\$1")+"([a-zA-Z"+(e?"\\/":"")+"]*)"+(t||"").replace(n,"\\$1"))},getInternalPageRegexp:function(){return Do.getInternalPathRegexp(!1,".html")},getPlatformInfo:e=>Eo.runtime.getPlatformInfo(e),getBrowserInfo:e=>{e({name:"Chrome",version:`${Oo()}`,vendor:"unknown"})}}
;return Object.defineProperty(e,"lastError",{get:()=>Eo.runtime.lastError,enumerable:!0}),Object.defineProperty(e,"id",{get:()=>Eo.runtime.id,enumerable:!0}),Object.defineProperty(e,"short_id",{get:()=>e.id.replace(/[^0-9a-zA-Z]/g,"").substr(0,4),enumerable:!0}),e})(),Po=(()=>{let e;return Object.defineProperties({},{inIncognitoContext:{get:()=>(void 0===e&&(e=Eo.extension.inIncognitoContext),e),set:t=>{e=t},enumerable:!0},...Object.getOwnPropertyDescriptors({getURL:function(e){
return Eo.runtime.getURL(e)},sendMessage:(e,t)=>Eo.runtime.sendMessage(e,t),onMessage:{addListener:e=>Eo.runtime.onMessage.addListener(e)},connect:e=>Eo.runtime.connect({name:e}),urls:{prepareForReport:function(e){return e}}})})})();t((function*(e,t){yield Eo.offscreen.createDocument({url:"offscreen.html",...e}),t&&t()})),t((function*(e){yield Eo.offscreen.closeDocument(),e&&e()}));const jo=Eo&&Eo.userScripts&&Eo.userScripts.onBeforeScript?{supported:!0,onBeforeScript:{
addListener:e=>Eo.userScripts.onBeforeScript.addListener(e)}}:{supported:!1},Co=({sendPrefix:e,listenPrefix:n,send:o,onMessage:s})=>{if(void 0===o||void 0===s)throw"invalid args";let r,i,c=1;const a=jt({}),d=jt({}),l=e=>{e&&(r=e)},u=e=>{const t=++c;return d[c]=e,t};s(((t,s)=>t==`${n}_${r}`?(t=>{const{m:n,r:s,a:c,n:l}=t;if(a[n]&&(Jt(a[n],(e=>e(c))),delete a[n]),"message.response"==n){if(null==s)throw"Invalid Message";((e,t)=>{let n;e&&(n=d[e])&&(n(t),delete d[e])})(s,c)}else if(i){const t=s?t=>{
o(`${e}_${r}`,jt({m:"message.response",a:t,r:s}))}:()=>{};i(jt({method:n,args:c,node:l}),t)}})(s):null));const p=jt({init:(g=t((function*(e){r?l():l(e)})),function(e){return g.apply(this,arguments)}),refresh:()=>null,switchId:e=>{r&&p.cleanup(),l(e)},send:(t,n,s,i)=>jn((c=>{let a,d;"function"!=typeof s&&null!==s?(a=s,d=i):d=s,o(`${e}_${r}`,jt({m:t,a:n,r:d?u(d):null,n:a})),c()})),sendToId:(t,n,s)=>{o(`${e}_${t}`,jt({m:n,a:s,r:null}))},once:(e,t)=>{a[e]||(a[e]=[]),Ht(a[e],t)},
setMessageListener:e=>{i=e},cleanup:()=>null});var g;return p},$o=jo,Ao=jo.supported&&!ht.pagejs;let To;const Uo=(e,t)=>{try{To(e),t&&t()}catch(e){t&&t(e.message||e)}},ko="u"+Cn(),{bridges:Bo}=twod;let Fo=[];const Xo=e=>{$o.onBeforeScript.addListener((o=>{const s=(e,n)=>{Jt(t,(t=>t(e,n)))},r=e=>Ht(n,jt({listener:e,clone:o.export}));Et(o.global,"pagejs",jt({set:o.export((t=>{delete o.global.pagejs;const n=o.metadata,{modes:i,nonce:c}=n;if(c&&ho(c),Jt(Fo,(e=>e(t,n))),Fo=[],i.js){const n=jt({
unsafeWindow:o.global,unsafeThis:o.global.window,pageWindow:void 0,contextId:ko,fSend:s,fOnMessage:r,cloneInto:void 0,exportFunction:void 0});t(o.export(n)),Bo.js.sendToId(ko,"commid",jt({id:e}))}})),configurable:!0})),To=o.global.eval}));const t=[],n=[],o=(e,t)=>{Jt(n,(({listener:n,clone:o})=>n(e,o(t))))},s=e=>Ht(t,e);return jt({createBridge:()=>{const t=Co(jt({sendPrefix:"2S",listenPrefix:"2U",cloneInto:io,send:o,onMessage:s}));return t.init(e),t}})};let No=[],qo=[];const Wo=e=>{
const t=Po.connect(e);if(Do.lastError)return;let n=[];const o=jt({postMessage:e=>t.postMessage(e),onMessage:jt({addListener:e=>t.onMessage.addListener(e)}),onDisconnect:jt({addListener:e=>{Ht(n,e)}}),disconnect:()=>(s(!1),t.disconnect())}),s=e=>{e&&Jt(n,(e=>e())),n=[],No=Nt(No,(e=>e!==r)),qo=Nt(qo,(e=>e!==i))};t.onDisconnect.addListener((()=>s(!0)));const r=()=>{t.disconnect(),Do.lastError,s(!0)};Ht(No,r);const i=()=>{t.disconnect(),Do.lastError};return Ht(qo,i),o};let Ho
;Pn.addEventListener("pagehide",(e=>{vn(e)&&Jt(qo,(e=>e()))})),Pn.addEventListener("pageshow",(e=>{vn(e)&&Jt(No,(e=>e()))}));const{console:Go}=twod,Jo=e=>(e.details&&e.details.url,jn(function(){var n=t((function*(t){t(e)}));return function(e){return n.apply(this,arguments)}}())),Ko=jt({setInterval:e=>{let t;e.onMessage.addListener((n=>{"setInterval"==n.method&&(t=Hn(e.postMessage,n.t))})),e.onDisconnect.addListener((()=>{t&&Jn(t),t=null}))},registerMenuCommand:e=>{
const t=Wo("registerMenuCommand");t?(t.onMessage.addListener((t=>{if("ack"in t)e.postMessage(t);else{const{method:n,event:o}=t;e.postMessage(jt({method:n,event:o}))}})),t.onDisconnect.addListener((()=>{e.disconnect()})),e.onMessage.addListener((e=>{const{messageId:n,name:o,uuid:s,accessKey:r,autoClose:i,title:c,id:a}=e,d=Wt([a,Ho,s],"#");t.postMessage(jt({messageId:n,method:"registerMenuCommand",name:o,title:c,uuid:s,menuId:d,accessKey:r,autoClose:i}))})),e.onDisconnect.addListener((()=>{
t.disconnect()}))):Gn((()=>e.disconnect()),1)},notification:e=>{const t=Wo("notification");t?(t.onMessage.addListener((t=>{e.postMessage(t)})),t.onDisconnect.addListener((()=>{e.disconnect()})),e.onMessage.addListener((e=>{t.postMessage(e)})),e.onDisconnect.addListener((()=>{t.disconnect()}))):Gn((()=>e.disconnect()),1)},openInTab:e=>{const t=Wo("openInTab");t?(t.onMessage.addListener((t=>{e.postMessage(t)})),t.onDisconnect.addListener((()=>{e.disconnect()})),e.onMessage.addListener((e=>{
const{messageId:n}=e;if("method"in e){const{method:o}=e;if("openInTab"==o)if("url"in e){let o=e.url;const{active:s,loadInBackground:r,insert:i,incognito:c,setParent:a}="boolean"==typeof e.options||void 0===e.options?jt({loadInBackground:e.options}):e.options,d=void 0===s?void 0!==r&&!r:s,l=void 0===i||i;o&&"//"==an(o,0,2)&&(o=eo.protocol+o),t.postMessage(jt({messageId:n,method:"openInTab",details:jt({url:o,options:jt({active:!!d,insert:!!l,incognito:!!c,setParent:!c&&!!a})}),uuid:e.uuid}))
}else t.postMessage(e);else Go.warn(`content: unknown method ${o}`)}else"name"in e?t.postMessage(jt({messageId:n,name:e.name})):"focus"in e?t.postMessage(jt({messageId:n,focus:!0})):"close"in e&&t.postMessage(jt({messageId:n,close:!0}))})),e.onDisconnect.addListener((()=>{t.disconnect()}))):Gn((()=>e.disconnect()),1)},download:e=>{let t;const n=Wo("download");n?(n.onMessage.addListener((t=>{e.postMessage(t)})),n.onDisconnect.addListener((()=>{e.disconnect(),t=!0})),e.onMessage.addListener((e=>{
"cancel"in e?(t=!0,n.postMessage(jt({cancel:!0,id:Ho,uuid:e.uuid}))):"details"in e?Jo(e).then((e=>{if(t)return;let{url:o,...s}=e.details;n.postMessage(jt({messageId:e.messageId,method:"download",details:jt({...s,from:jt({url:o})}),id:Ho,uuid:e.uuid,location:eo.href}))})):n.postMessage(jt({messageId:e.messageId,method:"download",uuid:e.uuid,id:e.id}))})),e.onDisconnect.addListener((()=>{n.disconnect()}))):Gn((()=>e.disconnect()),1)},webRequest:e=>{const t=Wo("webRequest")
;t?(t.onMessage.addListener((t=>{e.postMessage(t)})),t.onDisconnect.addListener((()=>{e.disconnect()})),e.onMessage.addListener((e=>{const{messageId:n,rules:o,uuid:s}=e;t.postMessage(jt({messageId:n,method:"webRequest",rules:o,uuid:s}))})),e.onDisconnect.addListener((()=>{t.disconnect()}))):Gn((()=>e.disconnect()),1)},xhr:e=>{let n,o;const s=[],r=function(){var e=t((function*(e){if(e&&Ht(s,e),o)yield o,r();else{const e=Xt(s);e&&e()}}));return function(t){return e.apply(this,arguments)}
}(),i=Wo("xhr");i?(i.onMessage.addListener((n=>{"ack"in n?e.postMessage(n):r((()=>{const{onpartial:s,data:r,...i}=n;let c;if(s&&r){const{tfd:n}=r;if(n){const r=(e=>e&&(bo(e)||_o(e)||wo(e)||yo(e))?(e=>{let t,n,o,s,r;if(_o(e))t=e.objUrl.url;else if(bo(e))n=e.blob;else if(wo(e))o=e.dataUri;else{if(!yo(e))throw"incompatible TransferableData";s=e.binary,r=e.type}return jt({toBlob:()=>jn((e=>{if(n)e(n);else if(t)try{nn(Qn(t),(t=>{nn(so(t),(t=>{e(t)}))}))}catch(t){e(void 0)}else if(o)e((e=>{let t
;const n=zt(e,","),o=On(n,0),s=On(n,1);t=-1!=dn(o,"base64")?Xe(s):Fe(s);const r=On(zt(o,":"),1),i=On(zt(r,";"),0);return new In([t],jt({type:i}))})(o));else{if(!s)throw"incompatible Transferable";{const t=(e=>{const t=new En(e.length);for(let n=0;n<e.length;n++)t[n]=ln(e,n);return t.buffer})(s);e(new In([t],r?jt({type:r}):void 0))}}})),dispose:()=>{t&&bn(t),t=n=o=s=void 0}})})(e):void 0)(n);if(!r)return;return void(o=jn(function(){var n=t((function*(t){const n=()=>{o=null,t(),r&&r.dispose()}
;try{const t=yield r.toBlob();if(!t)return void n();const o=t.type;let a;if(a="arrayBuffer"in t?yield ro(t):yield oo(new no(t)),!a)return void n();c=jt({nada:jt({buffer:a,type:o})}),e.postMessage(jt({...i,onpartial:s,data:c})),n()}catch(e){Go.warn(e),n()}}));return function(e){return n.apply(this,arguments)}}()))}c=r}else c=r;e.postMessage(jt({...i,onpartial:s,data:c}))}))})),i.onDisconnect.addListener((()=>{r(function(){var o=t((function*(){e.disconnect(),n=!0}));return function(){
return o.apply(this,arguments)}}())})),e.onMessage.addListener((e=>{Jo(e).then((e=>{n||(e.location=eo.href,i.postMessage(e))}))})),e.onDisconnect.addListener((()=>{i.disconnect()}))):Gn((()=>e.disconnect()),1)},onurlchange:e=>{const t=Wo("onurlchange");if(!t)return void Gn((()=>e.disconnect()),1);let n=()=>{e.postMessage(jt({url:eo.href}))};const o=()=>{n&&(Pn.removeEventListener("hashchange",n),n=null)};Pn.addEventListener("hashchange",n),t.onMessage.addListener((t=>{e.postMessage(t)})),
t.onDisconnect.addListener((()=>{e.disconnect(),o()})),e.onMessage.addListener((e=>{t.postMessage(e)})),e.onDisconnect.addListener((()=>{t.disconnect(),o()}))},values:e=>{const t=Wo("values");t?(t.onMessage.addListener((t=>{e.postMessage(t)})),t.onDisconnect.addListener((()=>{e.disconnect()})),e.onMessage.addListener((e=>{t.postMessage(e)})),e.onDisconnect.addListener((()=>{t.disconnect()}))):Gn((()=>e.disconnect()),1)},backgroundControl:e=>{const t=Wo("backgroundControl")
;t?(t.onMessage.addListener((t=>{e.postMessage(t)})),t.onDisconnect.addListener((()=>{e.disconnect()})),e.onMessage.addListener((e=>{t.postMessage(e)})),e.onDisconnect.addListener((()=>{t.disconnect()}))):Gn((()=>e.disconnect()),1)},keepAlive:e=>{const t=Wo("keepAlive");t?(t.onMessage.addListener((t=>{e.postMessage(t)})),t.onDisconnect.addListener((()=>{e.disconnect()})),e.onMessage.addListener((e=>{t.postMessage(e),Do.lastError})),e.onDisconnect.addListener((()=>{t.disconnect()
}))):Gn((()=>e.disconnect()),1)}}),zo=jt({setTimeout:({args:e,cb:t})=>{Gn(t,e.t)},setClipboard:({args:e,cb:t})=>{const{content:n,info:o,uuid:s}=e;let r,i;"object"==typeof o?(o.type&&(r=o.type),o.mimetype&&(i=o.mimetype)):"string"==typeof o&&(r=o);const c=i||("html"==r?"text/html":"text/plain");ht.document.addEventListener("copy",(e=>{e.stopImmediatePropagation(),e.preventDefault(),e.clipboardData&&e.clipboardData.setData(c,n)}),jt({capture:!0,once:!0})),ht.document.execCommand("copy"),t()},
closeTab:({args:{uuid:e},cb:t})=>{Po.sendMessage(jt({method:"closeTab",uuid:e,id:Ho}),(e=>{e&&e.error&&Go.warn(e.error),t(e)}))},focusTab:({args:{uuid:e},cb:t})=>{Po.sendMessage(jt({method:"focusTab",uuid:e,id:Ho}),(e=>{e&&e.error&&Go.warn(e.error),t(e)}))},addElement:(Yo=t((function*({args:e,node:t,cb:o}){try{const s=lo(e.tag,jt({...e.properties||jt({}),...e.id?jt({id:e.id}):jt({})}));let r,i;if("script"===e.tag&&(r=vo())&&s.setAttribute("nonce",r),t)i=t;else{const e=ht.document
;if(i=e.head||e.body||e.documentElement,!i){const e=ht.document;i=yield jn((t=>n((()=>t(e.head||e.body)))))}}i.appendChild(s),r&&s.removeAttribute("nonce"),o()}catch(e){Go.warn("content: error adding script",e)}})),function(){return Yo.apply(this,arguments)}),tabs:({args:e,cb:t})=>{e.method="tabs",Po.sendMessage(e,(e=>e&&t(e.data)))},cookie:({args:e,cb:t})=>{e.method="cookie",Po.sendMessage(e,(e=>e&&t(e.data)))}});var Yo;const Vo=jt({init:()=>{Ho=twod.contextId},
processMessage:({method:e,args:t,node:n},o)=>{let s;if(s=zo[e])return s(jt({args:t,node:n,cb:o}));o()},processConnect:(e,t)=>{let n;if(n=Ko[e])return n(t)}}),Zo=["GM_info","GM.info"],Qo=["unsafeWindow",...Zo],es=e=>e.replace(new RegExp("[\"']","g"),"\\$1");let ts=!1;const ns=(e,n,o,s,r)=>{const{console:i}=twod;ts=ts||e.logLevel>=60;const{version:c,injectMode:a,inIncognitoContext:d,isFirstPartyIsolation:l,downloadMode:u,relaxedCsp:p,userAgent:g,container:m,logLevel:f}=e,h=jt({version:c,
injectMode:a,inIncognitoContext:d,isFirstPartyIsolation:l,downloadMode:u,relaxedCsp:p,container:m});Jt(n,function(){var n=t((function*(t){const{script:n,storage:c}=t,{name:a,uuid:d,options:{run_at:l}}=n,{requires:u,...p}=n,m=u.length,v=!n.options.unwrap,_=jt({...h,userAgent:g,logLevel:f,sandboxMode:r});let b,w;const y=((e,t)=>{
const{measure_scripts:n,top_level_await:o,enforce_strict_mode:s}=e,{source_url:r,script:i,code:c}=t,{name:a,grant:d}=i,{requires:l}=i,u=!i.options.unwrap,p=l.map((e=>e.textContent||"")).join("\n");if(u){let e="";const t=["define","module","exports"],l=t.map((()=>"undefined")),u=-1!==i.grant.indexOf("none");let g;return g=u?Zo:d.concat(Qo),g.forEach((e=>{const n=e.split(".")[0];"window"!==n&&-1===t.indexOf(n)&&(t.push(n),l.push(`p.${n}`))})),
[`with (${u?"this.s":"this"}) {`,"(async (u, { p, r, s }) => {","try {",n?`console.time("${e=`SCRIPT RUN TIME[${es(a)}]`}");\n`:"","r(u, s, [",l.join(","),"]);",n?`console.timeEnd("${e}");\n`:"","} catch (e) {","if (e.message && e.stack) {","console.error(\"ERROR: Execution of script '",es(a),"' failed! \" + e.message);","console.log(e.stack);","} else {","console.error(e);","}","}","})","(",(o?"async ":"")+"function(",t.join(","),") {",s?'"use strict";\n':"\n",p,c,"\n",`}, ${u?"this":"seed"})`,"}","\n",r?`//# sourceURL=${r}\n`:""].join("")
}return[p,c,"\n",r?`//# sourceURL=${r}\n`:""].join("")})(e,t);return v?(b=()=>{ts&&i.debug(`env: inject "${a}" now`);const e=`__f__${Cn()}`,t=jt({storage:c,script:p}),n=`window["${e}"] = function(){${y}}`;let r=!0;s.once(`scriptack-${e}`,(()=>{ts&&!r&&i.log(`env: execution of "${a}" was delayed`),o(n,(e=>{e&&i.error(`Uncaught SyntaxError: ${e}`)}))})),s.send("script",jt({id:e,unwrap:!1,bundle:t,flags:_})),r=!1},w="context-menu"==l||"document-start"==l?void 0:()=>{
ts&&i.debug(`env: run "${a}" now  (${m} requires)`),s.send("run",jt({uuid:d}))}):w=()=>{ts&&i.debug(`env: inject @unwrap "${a}" now`),o(y,(e=>{e&&i.error(`Uncaught SyntaxError: ${e}`)}))},os(a,l,b,w,ts)}));return function(e){return n.apply(this,arguments)}}())},os=(e,t,o,s,r)=>{const{console:i}=twod;let c;"document-start"==t?(r&&i.debug(`env: run "${e}" ASAP -> document-start`),c=e=>e()):"document-body"==t?(r&&i.debug(`env: schedule "${e}" for document-body`),
c=uo):"context-menu"==t?r&&i.debug(`env: run "${e}" ASAP -> context-menu`):"document-end"==t?(r&&i.debug(`env: schedule "${e}" for document-end`),c=e=>n(!1,e)):(r&&i.debug(`env: schedule "${e}" for document-idle`),c=po),o&&o(),s&&c&&c((()=>{s()}))},ss=(e,t)=>{let n,o;try{o=(0,eval)(e)}catch(e){n=e.message||e}return t&&t(n),o},rs=({sendPrefix:e,listenPrefix:n,cloneInto:o})=>{const s=e=>o?o(e,ht.document):e,i=jt({});let c,a,d=1;const l=jt({});let u=!1,p=[];const g=()=>{
b=ht.document.documentElement,u=!1;const e=p;p=[],Jt(e,(e=>{u||y()?Ht(p,e):e()}))};let m;const f=e=>{const t=++d;return l[d]=e,t},h=(e,t)=>{const{m:n,a:o,r,n:i}=t,{m:c,c:a}=((e,t,n)=>{let o,r;return n?(r=new Mn(e,jt({relatedTarget:n})),o=new yn(e,jt({detail:s(t)}))):o=new yn(e,jt({detail:s(t)})),jt({m:r,c:o})})(e,jt({m:n,a:o,r}),i);c&&Pt(We,ht,[c]),Pt(We,ht,[a])},v=t=>{const n=(e=>{if("MouseEvent"===(e=>{const t=zt(Vt(e)," ");return Yt(Wt(qt(t,1)," "),0,-1)})(e)){const t=fn(e)
;if(!t)throw"Invalid MouseEvent";return void(m=t)}const t=Qt(hn(e));return m&&(t.n=m,m=void 0),t})(t);if(!n)return;const{m:o,r:s,a:r,n:d}=n;if(i[o]&&(Jt(i[o],(e=>e(r))),delete i[o]),"unlock"==o)h(`${e}_${a}`,jt({m:"unlocked",a:void 0,r:null})),g();else if("unlocked"==o)g();else if("message.response"==o){if(null==s)throw"Invalid Message";((e,t)=>{let n;e&&(n=l[e])&&(n(t),delete l[e])})(s,r)}else if(c){const t=s?t=>{h(`${e}_${a}`,jt({m:"message.response",a:t,r:s}))}:()=>{};c(jt({method:o,args:r,
node:d}),t)}},_=e=>{e&&(a=e),a&&Pn.addEventListener(`${n}_${a}`,v,!0)};let b,w;const y=()=>{if((()=>{const e=ht.document.documentElement;return b||(b=e),b!==e})()){if(w){const e=w;w=void 0,e(ht.document)}return!0}},M=e=>(b=ht.document.documentElement,jn(function(){var n=t((function*(t){if(w=t,r&&!e&&(yield null,y()))return;const n=new Ln((()=>{y()&&n.disconnect()}));n.observe(ht.document,jt({childList:!0}))}));return function(e){return n.apply(this,arguments)}}())),E=jt({
init:(L=t((function*(t,n){a?_():_(t),M(n).then((()=>{u=!0,E.refresh(),h(`${e}_${a}`,jt({m:"unlock",a:void 0,r:null}))}))})),function(e,t){return L.apply(this,arguments)}),refresh:()=>{const e=a;e&&(E.cleanup(),E.init(e,!0))},switchId:e=>{a&&E.cleanup(),_(e)},send:(t,n,o,s)=>jn((r=>{let i,c;"function"!=typeof o&&null!==o?(i=o,c=s):c=o,y();const d=()=>{h(`${e}_${a}`,jt({m:t,a:n,r:c?f(c):null,n:i})),r()};u?Ht(p,d):d()})),sendToId:(t,n,o)=>{h(`${e}_${t}`,jt({m:n,a:o,r:null}))},
setMessageListener:e=>{c=e},once:(e,t)=>{i[e]||(i[e]=[]),Ht(i[e],t)},cleanup:()=>{a&&(Pn.removeEventListener(`${n}_${a}`,v,!0),a=void 0)}});var L;return E},is=function(e,t,n,o,s){let r;const i=vo(),c=lo("script");i&&c.setAttribute("nonce",i);try{c.textContent=t,e.appendChild(c)}catch(e){r=e.message||e}if(i&&c.removeAttribute("nonce"),!s){const e=c.parentNode;e&&e.removeChild(c)}return o&&o(r),!r},cs=(e,t)=>{try{is(to.head||to.body||to.documentElement||to,e,0,t)}catch(e){t&&t(e.message||e)}}
;let as,ds;const ls=e=>{const t=Wo("tabInfo");t?(t.onMessage.addListener((e=>{if("ack"in e);else{const{info:t}=e;t?as=Qt(t):Wn.warn("tabInfo: unexpected message",e)}})),t.onDisconnect.addListener((()=>{us()})),e(t)):Gn((()=>ls(e)),100)},us=()=>{ls((e=>{as=ds||as,ds=void 0,e.postMessage(jt({method:"tabInfo",messageId:Cn(),info:as}))}))},ps=e=>{e=e||Pn.location.href;const t=Wo("tabInfo");t?(t.onMessage.addListener((e=>{"ack"in e||Wn.warn("tabInfo.frame: unexpected message",e)})),
t.onDisconnect.addListener((()=>{ps(e)})),t.postMessage(jt({method:"tabInfo",messageId:Cn(),url:e}))):Gn((()=>ps(e)),100)};let gs=!1;const ms=location.pathname+location.search;Do.short_id,window.btoa(ms.length+ms).substr(0,255).replace(/[#=/]/g,"_");let fs=!1;const hs=(e,t,o)=>{const{contextId:s,bridges:r,console:i}=twod;let c;jn((n=>{const o=e=>{let t=1;const o=()=>{gs&&i.log('content: send "prepare" message'),Po.sendMessage({method:"prepare",id:s,topframe:Kn,url:window.location.href},(s=>{
if(!fs){if(!s)return gs&&i.log("content: _early_ execution, connection to bg failed -> retry!"),window.setTimeout(o,t),void(t=Dt(2*t,3e3));fs=!0,s.nonce&&ho(s.nonce),wt(s.contexters).length||wt(s.scripts).length||s.external_connect?(e&&e(s),n({info:s,type:"complete"==pn(ht.document)?"late":"normal"})):n({info:s})}}))};!function(e){const t=()=>"prerender"!==document.webkitVisibilityState,n=()=>{t()&&(document.removeEventListener("webkitvisibilitychange",n,!1),e())}
;t()?e():document.addEventListener("webkitvisibilitychange",n,!1)}(o)},r=()=>{let r;if(gs&&i.log("content: Started ("+s+", "+window.location.origin+window.location.pathname+")",vt.tm_info),r=vt.tm_info){delete vt.tm_info,r.nonce&&ho(r.nonce);const{contexters:o,scripts:i}=r;if(wt(o).length||wt(i).length){if(r.contexters.raw||r.scripts.raw)if(e.raw)void 0===c&&(c=e.raw());else if(!t)throw"raw inject missing";n({info:r,type:"sync"})}else n({info:r});{let e;if("userscripts-dynamic"===r.contentMode){
const t=e={};Jt(wt(i),(e=>{const n=i[e];n&&Jt(n,(({script:{uuid:e,name:n}})=>t[e]=n))}))}Po.sendMessage({method:"prepare",id:s,topframe:Kn,url:window.location.href,scripts:e,cleanup:!0},(()=>null))}}else t?o((({scripts:t,contexters:n})=>{e.raw&&void 0===c&&(t.raw||n.raw)&&(c=e.raw())})):(e.raw&&void 0===c&&(c=e.raw()),o())};t?window.setTimeout((()=>r()),1):r()})).then((({info:a,type:d})=>{const{external_connect:l}=a;if(!t&&(e=>{if(e){const t=location.href.split("#")[0]
;return e.some((({source:e,flags:n})=>{const o=new Sn(e,n);if(""===t.replace(o,""))return!0}))}return!1})(l)&&(void 0===c&&e.raw&&(c=e.raw()),c||i.warn("content: external connectability detected, but unable to initialize raw mode!"),$n((()=>r.first.send("external.connect")))),d){gs=gs||a.logLevel>=60;const{scripts:c}=a;if(c.raw){let n;t&&!e.raw&&(n=!0),n&&(e.js?(c.js=(c.js||[]).concat(c.raw||[]),delete c.raw):e.dom&&(c.dom=(c.dom||[]).concat(c.raw||[]),delete c.raw))}if(c.js){
if(!e.js)throw"js inject missing";e.js()}if(c.dom){if(!e.dom)throw"dom inject missing";e.dom()}n(!1,(()=>{gs&&i.log("content: DOMContentLoaded"),Jt(wt(r),(e=>r[e].send("DOMContentLoaded")))})),u=()=>{gs&&i.log("content: load"),Jt(wt(r),(e=>r[e].send("load")))},"complete"==pn(ht.document)?u():Pn.addEventListener("load",(()=>u()),jt({capture:!0,once:!0}));const l=(c.dom||[]).length+(c.js||[]).length+(c.raw||[]).length
;gs&&i.log("content: "+(d||"normal")+" start event processing for "+s+" ("+l+" to run)"),vs(a,"sync"===d),!Kn&&l&&ps(location.href),o()}else o();var u}))},vs=e=>{const{bridges:t}=twod,{scripts:n,...s}=e;n.js&&ns(s,n.js,Uo,t.js,"js"),n.dom&&(Jt(o,(e=>vt[e]=void 0)),ns(s,n.dom,ss,t.dom,"dom")),n.raw&&ns(s,n.raw,cs,t.raw,"raw")},_s=function(){var e=t((function*(){const{contextId:e}=twod,{location:t,document:n}=ht;n.addEventListener("mouseenter",(()=>{Po.sendMessage(jt({method:"contextmenu",
url:t.href,id:e}),(()=>{}))}),!1)}));return function(){return e.apply(this,arguments)}}();let bs;t((function*(){const{unsafeWindow:e,bridges:t}=twod,{location:n,document:o}=e,r=o instanceof XMLDocument;if(!jt({"http:":!0,"https:":!0,"file:":!0})[e.location.protocol])return;Kn&&(us(),Pn.addEventListener("pagehide",(e=>{vn(e)&&(ds=as)})));const i=twod.contextId?twod.contextId:twod.contextId=Cn();let c;const a=jt({});Mt(twod.console,Wn),jn((n=>{if(Ao){const{createBridge:e}=Xo(i);o=(o,s)=>{bs=c=o
;const{modes:i}=s;i.js&&(t.js=e(),a.js=()=>!1,r&&(i.raw=!1)),n(s)},Ht(Fo,o)}else jn((t=>{bs=c=e.pagejs,delete e.pagejs,bs?t():(()=>{let e=n=>{delete ht.pagejs,e=void 0,(e=>{if(!e)throw"Error: pagejs missing. Please see http://tmnk.net/faq#Q208 for more information.";bs=c=e,t()})(n)};Et(ht,"pagejs",jt({set:e,configurable:!0})),Gn((()=>{e&&e()}),1)})()})).then((()=>{n(jt({modes:jt({js:!1,raw:!0,dom:!0})}))}));var o})).then((({modes:{dom:o,raw:d},answer:l})=>{var u
;if(l&&(u=l).scripts&&u.contexters&&(vt.tm_info=l),o){const e=Ao?ss(`() => ${c};`)():c,{createBridge:n,inject:o}=((e,t)=>{const n=(e,t)=>{Jt(s,(n=>n(e,t)))},o=e=>Ht(r,jt({listener:e,clone:e=>e})),s=[],r=[],i=(e,t)=>{Jt(r,(({listener:n,clone:o})=>n(e,o(t))))},c=e=>Ht(s,e),a="c"+Cn();let d;return jt({createBridge:()=>(d=Co(jt({sendPrefix:"2S",listenPrefix:"2U",send:i,onMessage:c})),d.init(e),d),inject:()=>(t(jt({unsafeWindow:ht,unsafeThis:vt,pageWindow:void 0,contextId:a,fSend:n,fOnMessage:o})),
d.sendToId(a,"commid",jt({id:e})),!0)})})(i,e);a.dom=o,t.dom=n()}if(d){const{createBridge:e,inject:n}=((e,t)=>{let n;return jt({createBridge:()=>(n=rs(jt({sendPrefix:"2P",listenPrefix:"2C",cloneInto:io})),n),inject:()=>{n.init(e);let o=!1;const r=Cn(),i="("+en(((e,t)=>{const n=window,o=t(n),s=n.parent,{O_dPy:r,cSO:i}=o;r(s,e,i({value:o,enumerable:!1,writable:!1,configurable:!0}))}))+')("'+r+'", '+en(s)+")";return n.once("ack",(()=>{o=!0})),(o=>{((e,t)=>{
const n=On(mn(ht.document,"*"),0)||ht.document,o=lo("div"),s=o.attachShadow(jt({mode:"closed"}));s.appendChild(lo("style",":host { display: none }"));const r=lo("iframe",jt({sandbox:"allow-scripts allow-same-origin",style:"display: none",src:"javascript:void 0"}));let i=()=>{if(null===i)return;i=null;let n=!1;try{const t=r.contentDocument,o=r.contentWindow;t&&o&&(n=is(On(t.getElementsByTagName("*"),0),e))}catch(e){}t(o,n),r.remove(),o.remove()};r.addEventListener("load",i,jt({once:!0,capture:!0
})),s.appendChild(r),n.appendChild(o),i&&i()})(o,((o,s)=>{is(o,`(${t})({ unsafeWindow: typeof globalThis === "undefined" ? window : globalThis, unsafeThis: window, vault: ${s?`window["${r}"]`:"null"}, contextId: "${r}", __proto__: null });delete window["${r}"]\n`,0,(()=>{n.sendToId(r,"commid",jt({id:e}))}),!0)}))})(i),o}})})(i,c);a.raw=n,t.raw=e()}t.first=t.js||t.raw||t.dom,Po.onMessage.addListener(((o,s,r)=>{
"executeScript"==o.method?(o.url&&0!==dn(n.href,o.url)||void 0!==o.topframe&&o.topframe!=Kn||vs(o.info,!1),r(jt({}))):Kn&&("loadUrl"==o.method?(e.location=o.url,r(jt({}))):"reload"==o.method?(n.reload(),r(jt({}))):"setForeignAttr"==o.method?(t.first.send(o.method,o),r(jt({}))):Wn.log("content: unknown method "+(e=>{const t=(e,n)=>{let o;if(null===e)o="null";else{const i=typeof e;if("object"===i){if(n){if(-1!=Gt(n,e))throw"Converting circular structure to JSON";Ct(n,n.length,e)}else n=[e]
;if(Ot(e)){let i="";for(let o=0;o<e.length;o++){let c;c=Kt(e,o)?Kt(s=e,r=o)?s[r]:void 0:At(e,o);const a=t(c,n);i+=`${o?",":""}${void 0===a?"null":a}`}o=`[${i}]`}else{let s="";Jt(wt(e),(o=>{const r=t(e[o],n);void 0!==r&&(s+=`${s?",":""}${D(o)}: ${r}`)})),o=`{${s}}`}n.length-=1}else{if("bigint"===i)throw"Do not know how to serialize a BigInt";o=D(e)}}var s,r;return o};return t(e)})(o)))})),An.onConnect.addListener(((e,t)=>{Vo.processConnect(e,t)})),Vo.init(),Jt(wt(t),(e=>{if("first"==e)return
;const n=t[e];n.setMessageListener(((t,o)=>{const{method:s,args:r}=t;if("port.message"==s)An.message(r,n);else if("csp"==s){let t;t="raw"==e?cs:"dom"==e?ss:Uo,t(r.src,(e=>{e&&Wn.error(`Uncaught Error: ${e}`)}))}else if("external.message"==s)Po.sendMessage(jt({method:"externalMessage",request:r}),(e=>{o(e)}));else if("console"==s){const e=r,t=On(e,0),n=On(e,1),o=Wn[t]||Wn.log;o&&Pt(o,Wn,((e,...t)=>{let n=e.length;const o=jt(e);for(let e=0;e<t.length;e++){const s=t[e],r=Ot(s)?s:[s]
;for(let e=0;e<r.length;e++)o[n+e]=$t(r,e);n+=r.length}return yt(o)})(["injected:"],(e=>{const t=[];return Jt(e,(e=>{Ht(t,(e=>"string"==typeof e?Bt(e):e)(e))})),t})(n)))}else Vo.processMessage(t,o)}))})),hs(a,r,(()=>bs=void 0)),_s()}))}))()})();})({ __proto__: null, unsafeWindow: typeof globalThis === "undefined" ? window : globalThis, unsafeThis: window, }, "tm_z9ouw3")