class BrowserUI {
  constructor() {
    this.tabs = new Map()
    this.activeTabId = null
    this.rightTabs = new Map()
    this.activeRightTabId = null
    this.isResizing = false
    this.rightPanelWidth = 400
    this.leftPanelWidth = 70
    this.settings = {}
    this.workspaces = new Map()
    this.activeWorkspaceId = 'default'
    this.mouseGesture = {
      isActive: false,
      startX: 0,
      startY: 0,
      path: []
    }
    this.initializeElements()
    this.setupEventListeners()
    this.loadSettings()
    this.loadInitialTabs()
    this.loadWorkspaces()
  }

  async loadSettings() {
    this.settings = await window.electronAPI.getSettings()
    this.leftPanelWidth = this.settings.leftPanelWidth || 70
    this.updateLeftPanelWidth()
  }

  async saveSettings() {
    await window.electronAPI.setSettings(this.settings)
  }

  initializeElements() {
    this.backBtn = document.getElementById('back-btn')
    this.forwardBtn = document.getElementById('forward-btn')
    this.reloadBtn = document.getElementById('reload-btn')
    this.homeBtn = document.getElementById('home-btn')
    this.addressBar = document.getElementById('address-bar')
    this.newTabBtn = document.getElementById('new-tab-btn')
    this.tabBar = document.getElementById('tab-bar')
    this.statusText = document.getElementById('status-text')
    this.urlPreview = document.getElementById('url-preview')
    
    // 左侧面板元素
    this.leftPanel = document.getElementById('left-panel')
    this.workspaceList = document.getElementById('workspace-list')
    this.newWorkspaceBtn = document.getElementById('new-workspace-btn')
    
    // 右侧面板元素
    this.toggleRightPanelBtn = document.getElementById('toggle-right-panel-btn')
    this.rightPanel = document.getElementById('right-panel')
    this.rightBackBtn = document.getElementById('right-back-btn')
    this.rightForwardBtn = document.getElementById('right-forward-btn')
    this.rightReloadBtn = document.getElementById('right-reload-btn')
    this.rightHomeBtn = document.getElementById('right-home-btn')
    this.rightAddressBar = document.getElementById('right-address-bar')
    this.rightNewTabBtn = document.getElementById('right-new-tab-btn')
    this.rightTabBar = document.getElementById('right-tab-bar')
    
    // 设置按钮
    this.settingsBtn = document.getElementById('settings-btn')
  }

  setupEventListeners() {
    // 主面板导航按钮
    this.backBtn.addEventListener('click', () => window.electronAPI.goBack())
    this.forwardBtn.addEventListener('click', () => window.electronAPI.goForward())
    this.reloadBtn.addEventListener('click', () => window.electronAPI.reload())
    this.homeBtn.addEventListener('click', () => window.electronAPI.goHome())
    
    // 主面板地址栏
    this.addressBar.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.navigate(this.addressBar.value)
      }
    })
    
    // 主面板新标签页
    this.newTabBtn.addEventListener('click', () => this.createNewTab())
    
    // 左侧面板
    this.newWorkspaceBtn.addEventListener('click', () => this.createNewWorkspace())
    
    // 右侧面板控制
    this.toggleRightPanelBtn.addEventListener('click', () => {
      this.toggleRightPanel()
    })
    
    // 右侧面板导航按钮
    this.rightBackBtn.addEventListener('click', () => window.electronAPI.goRightBack())
    this.rightForwardBtn.addEventListener('click', () => window.electronAPI.goRightForward())
    this.rightReloadBtn.addEventListener('click', () => window.electronAPI.reloadRight())
    this.rightHomeBtn.addEventListener('click', () => window.electronAPI.goRightHome())
    
    // 右侧面板地址栏
    this.rightAddressBar.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.navigateRight(this.rightAddressBar.value)
      }
    })
    
    // 右侧面板新标签页
    this.rightNewTabBtn.addEventListener('click', () => this.createNewRightTab())
    
    // 设置按钮
    this.settingsBtn.addEventListener('click', () => window.electronAPI.openSettings())
    
    // 右侧面板拖拽调整大小
    this.setupRightPanelResize()
    
    // 鼠标手势
    this.setupMouseGestures()
    
    // Electron事件监听
    window.electronAPI.onNavigationUpdated((event, data) => {
      this.updateNavigationState(data)
    })
    
    window.electronAPI.onRightNavigationUpdated((event, data) => {
      this.updateRightNavigationState(data)
    })
    
    window.electronAPI.onTabUpdated((event, tabId, tabData) => {
      this.updateTab(tabId, tabData)
    })
    
    window.electronAPI.onRightTabUpdated((event, tabId, tabData) => {
      this.updateRightTab(tabId, tabData)
    })
    
    window.electronAPI.onTabSwitched((event, tabId, tabData) => {
      this.switchToTab(tabId, tabData)
    })
    
    window.electronAPI.onRightTabSwitched((event, tabId, tabData) => {
      this.switchToRightTab(tabId, tabData)
    })
    
    window.electronAPI.onTabClosed((event, tabId) => {
      this.removeTab(tabId)
    })
    
    window.electronAPI.onRightTabClosed((event, tabId) => {
      this.removeRightTab(tabId)
    })

    window.electronAPI.onTabCreated((event, tabId, tabData) => {
      if (tabData.isRightPanel) {
        this.addRightTabToUI(tabId, tabData)
        this.switchToRightTab(tabId, tabData)
      } else {
        this.addTabToUI(tabId, tabData)
        this.switchToTab(tabId, tabData)
      }
    })
    
    window.electronAPI.onRightPanelToggled((event, visible) => {
      this.rightPanel.style.display = visible ? 'block' : 'none'
    })

    window.electronAPI.onRightPanelResized((event, width) => {
      this.rightPanelWidth = width
      this.rightPanel.style.width = width + 'px'
    })

    window.electronAPI.onLeftPanelResized((event, width) => {
      this.leftPanelWidth = width
      this.updateLeftPanelWidth()
    })

    window.electronAPI.onWorkspaceCreated((event, workspaceId, workspace) => {
      this.addWorkspaceToUI(workspaceId, workspace)
    })

    window.electronAPI.onWorkspaceSwitched((event, workspaceId, workspace) => {
      this.switchToWorkspace(workspaceId, workspace)
    })

    window.electronAPI.onWorkspaceDeleted((event, workspaceId) => {
      this.removeWorkspaceFromUI(workspaceId)
    })

    window.electronAPI.onWorkspaceUpdated((event, workspaceId, workspace) => {
      this.updateWorkspaceInUI(workspaceId, workspace)
    })



    // ESC键关闭设置
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const modal = document.querySelector('.settings-modal')
        if (modal) {
          modal.remove()
        }
      }
    })
  }

  setupRightPanelResize() {
    const resizeHandle = document.createElement('div')
    resizeHandle.className = 'resize-handle'
    this.rightPanel.appendChild(resizeHandle)

    resizeHandle.addEventListener('mousedown', (e) => {
      this.isResizing = true
      document.addEventListener('mousemove', this.handleResize.bind(this))
      document.addEventListener('mouseup', this.stopResize.bind(this))
      e.preventDefault()
    })
  }

  handleResize(e) {
    if (!this.isResizing) return
    
    const windowWidth = window.innerWidth
    const newWidth = windowWidth - e.clientX
    const maxWidth = Math.floor(windowWidth * 0.5)
    const minWidth = 300
    
    const clampedWidth = Math.max(minWidth, Math.min(newWidth, maxWidth))
    window.electronAPI.resizeRightPanel(clampedWidth)
  }

  stopResize() {
    this.isResizing = false
    document.removeEventListener('mousemove', this.handleResize)
    document.removeEventListener('mouseup', this.stopResize)
  }

  async toggleRightPanel() {
    console.log('Toggling right panel...')
    await window.electronAPI.toggleRightPanel()
  }

  navigate(url) {
    if (!url) return
    
    if (!url.includes('.') && !url.startsWith('http')) {
      const searchEngine = this.settings.searchEngine || 'google'
      url = this.getSearchUrl(url, searchEngine)
    } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    
    window.electronAPI.navigate(url)
    this.statusText.textContent = '正在加载...'
  }

  navigateRight(url) {
    if (!url) return
    
    if (!url.includes('.') && !url.startsWith('http')) {
      const searchEngine = this.settings.searchEngine || 'baidu'
      url = this.getSearchUrl(url, searchEngine)
    } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    
    window.electronAPI.navigateRight(url)
  }

  getSearchUrl(query, searchEngine = 'google') {
    const engines = {
      google: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
      bing: `https://www.bing.com/search?q=${encodeURIComponent(query)}`,
      baidu: `https://www.baidu.com/s?wd=${encodeURIComponent(query)}`
    }
    
    // 检查自定义搜索引擎
    if (this.settings.customSearchEngines) {
      const customEngine = this.settings.customSearchEngines.find(e => e.id === searchEngine)
      if (customEngine) {
        return customEngine.url.replace('%s', encodeURIComponent(query))
      }
    }
    
    return engines[searchEngine] || engines.google
  }

  async createNewTab(url = '') {
    const tabId = await window.electronAPI.createTab(url)
  }

  async createNewRightTab(url = '') {
    const tabId = await window.electronAPI.createRightTab(url)
  }

  addTabToUI(tabId, tabData) {
    if (this.tabs.has(tabId)) return

    const tab = document.createElement('div')
    tab.className = 'tab'
    tab.dataset.tabId = tabId
    
    const title = document.createElement('span')
    title.className = 'tab-title'
    title.textContent = tabData.title || 'New Tab'
    
    const closeBtn = document.createElement('button')
    closeBtn.className = 'tab-close'
    closeBtn.textContent = '×'
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation()
      this.closeTab(tabId)
    })
    
    tab.appendChild(title)
    tab.appendChild(closeBtn)
    
    tab.addEventListener('click', () => {
      window.electronAPI.switchTab(tabId)
    })
    
    // 插入到新建标签按钮之前
    this.tabBar.insertBefore(tab, this.newTabBtn)
    this.tabs.set(tabId, { element: tab, data: tabData })
  }

  addRightTabToUI(tabId, tabData) {
    if (this.rightTabs.has(tabId)) return

    const tab = document.createElement('div')
    tab.className = 'tab'
    tab.dataset.tabId = tabId
    
    const title = document.createElement('span')
    title.className = 'tab-title'
    title.textContent = tabData.title || 'New Tab'
    
    const closeBtn = document.createElement('button')
    closeBtn.className = 'tab-close'
    closeBtn.textContent = '×'
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation()
      this.closeRightTab(tabId)
    })
    
    tab.appendChild(title)
    tab.appendChild(closeBtn)
    
    tab.addEventListener('click', () => {
      window.electronAPI.switchRightTab(tabId)
    })
    
    this.rightTabBar.appendChild(tab)
    this.rightTabs.set(tabId, { element: tab, data: tabData })
  }

  updateTab(tabId, tabData) {
    const tab = this.tabs.get(tabId)
    if (!tab) return
    
    tab.data = { ...tab.data, ...tabData }
    const titleElement = tab.element.querySelector('.tab-title')
    titleElement.textContent = tabData.title || 'Loading...'
    
    if (tabId === this.activeTabId) {
      this.addressBar.value = tabData.url || ''
      this.statusText.textContent = '就绪'
    }
  }

  updateRightTab(tabId, tabData) {
    const tab = this.rightTabs.get(tabId)
    if (!tab) return
    
    tab.data = { ...tab.data, ...tabData }
    const titleElement = tab.element.querySelector('.tab-title')
    titleElement.textContent = tabData.title || 'Loading...'
    
    if (tabId === this.activeRightTabId) {
      this.rightAddressBar.value = tabData.url || ''
    }
  }

  switchToTab(tabId, tabData) {
    if (this.activeTabId) {
      const prevTab = this.tabs.get(this.activeTabId)
      if (prevTab) {
        prevTab.element.classList.remove('active')
      }
    }
    
    this.activeTabId = tabId
    this.updateActiveTab()
    this.addressBar.value = tabData.url || ''
  }

  switchToRightTab(tabId, tabData) {
    if (this.activeRightTabId) {
      const prevTab = this.rightTabs.get(this.activeRightTabId)
      if (prevTab) {
        prevTab.element.classList.remove('active')
      }
    }
    
    this.activeRightTabId = tabId
    this.updateActiveRightTab()
    this.rightAddressBar.value = tabData.url || ''
  }

  updateActiveTab() {
    const tab = this.tabs.get(this.activeTabId)
    if (tab) {
      tab.element.classList.add('active')
      tab.element.scrollIntoView({ behavior: 'smooth', inline: 'nearest' })
    }
  }

  updateActiveRightTab() {
    const tab = this.rightTabs.get(this.activeRightTabId)
    if (tab) {
      tab.element.classList.add('active')
      tab.element.scrollIntoView({ behavior: 'smooth', inline: 'nearest' })
    }
  }

  removeTab(tabId) {
    const tab = this.tabs.get(tabId)
    if (tab) {
      tab.element.remove()
      this.tabs.delete(tabId)
    }
  }

  removeRightTab(tabId) {
    const tab = this.rightTabs.get(tabId)
    if (tab) {
      tab.element.remove()
      this.rightTabs.delete(tabId)
    }
  }

  closeTab(tabId) {
    window.electronAPI.closeTab(tabId)
  }

  closeRightTab(tabId) {
    window.electronAPI.closeRightTab(tabId)
  }

  updateNavigationState(data) {
    this.backBtn.disabled = !data.canGoBack
    this.forwardBtn.disabled = !data.canGoForward
    
    if (data.url) {
      this.addressBar.value = data.url
    }
  }

  updateRightNavigationState(data) {
    this.rightBackBtn.disabled = !data.canGoBack
    this.rightForwardBtn.disabled = !data.canGoForward
    
    if (data.url) {
      this.rightAddressBar.value = data.url
    }
  }

  showSettings() {
    const modal = document.createElement('div')
    modal.className = 'settings-modal'
    modal.innerHTML = `
      <div class="settings-content">
        <h3>设置</h3>
        <div class="setting-item">
          <label>主页地址:</label>
          <input type="text" id="homepage-input" value="${this.settings.homepage || 'https://www.baidu.com'}" placeholder="https://www.google.com">
        </div>
        <div class="setting-item">
          <label>搜索引擎:</label>
          <select id="search-engine-select">
            <option value="google" ${this.settings.searchEngine === 'google' ? 'selected' : ''}>Google</option>
            <option value="bing" ${this.settings.searchEngine === 'bing' ? 'selected' : ''}>Bing</option>
            <option value="baidu" ${this.settings.searchEngine === 'baidu' ? 'selected' : ''}>百度</option>
          </select>
        </div>
        <div class="setting-buttons">
          <button id="save-settings">保存</button>
          <button id="cancel-settings">取消</button>
        </div>
      </div>
    `
    
    document.body.appendChild(modal)
    
    document.getElementById('save-settings').addEventListener('click', async () => {
      const homepage = document.getElementById('homepage-input').value
      const searchEngine = document.getElementById('search-engine-select').value
      
      this.settings.homepage = homepage
      this.settings.searchEngine = searchEngine
      
      await this.saveSettings()
      modal.remove()
    })
    
    document.getElementById('cancel-settings').addEventListener('click', () => {
      modal.remove()
    })
  }

  async loadInitialTabs() {
    const tabs = await window.electronAPI.getAllTabs()
    for (const [id, tab] of Object.entries(tabs)) {
      this.addTabToUI(parseInt(id), tab)
      if (tab.active) {
        this.activeTabId = parseInt(id)
        this.updateActiveTab()
      }
    }
  }

  setupMouseGestures() {
    let isRightMouseDown = false
    let gestureStartX = 0
    let gestureStartY = 0
    let gesturePath = []
    let gestureTriggered = false

    document.addEventListener('mousedown', (e) => {
      if (e.button === 2) { // 右键
        isRightMouseDown = true
        gestureStartX = e.clientX
        gestureStartY = e.clientY
        gesturePath = []
        gestureTriggered = false
      }
    })

    document.addEventListener('mousemove', (e) => {
      if (isRightMouseDown) {
        const deltaX = e.clientX - gestureStartX
        const deltaY = e.clientY - gestureStartY

        if (Math.abs(deltaX) > 50 || Math.abs(deltaY) > 50) {
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            if (deltaX > 0) {
              gesturePath.push('right')
            } else {
              gesturePath.push('left')
            }
          } else {
            if (deltaY > 0) {
              gesturePath.push('down')
            } else {
              gesturePath.push('up')
            }
          }
          gestureStartX = e.clientX
          gestureStartY = e.clientY
          gestureTriggered = true
        }
      }
    })

    document.addEventListener('mouseup', (e) => {
      if (e.button === 2 && isRightMouseDown) {
        isRightMouseDown = false

        if (gestureTriggered) {
          this.executeGesture(gesturePath)
          e.preventDefault()
        }
      }
    })
  }

  executeGesture(path) {
    const pathString = path.join('-')
    
    switch (pathString) {
      case 'left':
        window.electronAPI.goBack()
        break
      case 'right':
        window.electronAPI.goForward()
        break
      case 'down-right':
        if (this.activeTabId) {
          window.electronAPI.closeTab(this.activeTabId)
        }
        break
    }
  }

  updateLeftPanelWidth() {
    this.leftPanel.style.width = this.leftPanelWidth + 'px'
    document.documentElement.style.setProperty('--left-panel-width', this.leftPanelWidth + 'px')
  }

  async createNewWorkspace() {
    try {
      const name = await window.electronAPI.promptWorkspaceName()
      if (name) {
        await window.electronAPI.createWorkspace(name)
      }
    } catch (error) {
      console.error('Failed to create workspace:', error)
    }
  }

  addWorkspaceToUI(workspaceId, workspace) {
    const workspaceItem = document.createElement('div')
    workspaceItem.className = 'workspace-item'
    workspaceItem.dataset.workspaceId = workspaceId
    workspaceItem.textContent = workspace.name

    workspaceItem.addEventListener('click', () => {
      window.electronAPI.switchWorkspace(workspaceId)
    })

    // 添加右键菜单
    workspaceItem.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      this.showWorkspaceContextMenu(e, workspaceId, workspace)
    })

    this.workspaceList.appendChild(workspaceItem)
    this.workspaces.set(workspaceId, { element: workspaceItem, data: workspace })
  }

  switchToWorkspace(workspaceId, workspace) {
    // 更新活跃工作区
    if (this.activeWorkspaceId) {
      const prevWorkspace = this.workspaces.get(this.activeWorkspaceId)
      if (prevWorkspace) {
        prevWorkspace.element.classList.remove('active')
      }
    }

    this.activeWorkspaceId = workspaceId
    const currentWorkspace = this.workspaces.get(workspaceId)
    if (currentWorkspace) {
      currentWorkspace.element.classList.add('active')
    }

    // 清空当前标签页显示
    this.tabBar.innerHTML = ''
    this.tabBar.appendChild(this.newTabBtn)
    this.tabs.clear()
    this.activeTabId = null

    // 重新加载工作区的标签页
    this.loadWorkspaceTabs()
  }

  async loadWorkspaceTabs() {
    // 重新加载当前工作区的标签页
    const tabs = await window.electronAPI.getAllTabs()
    for (const [id, tab] of Object.entries(tabs)) {
      this.addTabToUI(parseInt(id), tab)
      if (tab.active) {
        this.activeTabId = parseInt(id)
        this.updateActiveTab()
        this.addressBar.value = tab.url || ''
      }
    }
  }

  async loadWorkspaces() {
    const workspaces = await window.electronAPI.getAllWorkspaces()
    for (const workspace of workspaces) {
      this.addWorkspaceToUI(workspace.id, workspace)
    }

    // 设置默认工作区为活跃状态
    const defaultWorkspace = this.workspaces.get('default')
    if (defaultWorkspace) {
      defaultWorkspace.element.classList.add('active')
    }
  }

  showWorkspaceContextMenu(e, workspaceId, workspace) {
    const menu = document.createElement('div')
    menu.className = 'context-menu'
    menu.style.cssText = `
      position: fixed;
      left: ${e.clientX}px;
      top: ${e.clientY}px;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 10000;
      min-width: 150px;
    `

    const menuItems = []

    // 基础菜单项
    menuItems.push(
      { text: '修改名称', action: () => this.editWorkspaceName(workspaceId, workspace) },
      { text: '设置主页', action: () => this.editWorkspaceHomepage(workspaceId, workspace) }
    )

    // 如果不是默认工作区，可以删除
    if (workspaceId !== 'default') {
      menuItems.push(
        { text: '---', action: null }, // 分隔线
        { text: '删除工作区', action: () => this.deleteWorkspace(workspaceId) }
      )
    }

    menuItems.forEach(item => {
      if (item.text === '---') {
        // 分隔线
        const separator = document.createElement('div')
        separator.style.cssText = `
          height: 1px;
          background: #ddd;
          margin: 4px 0;
        `
        menu.appendChild(separator)
      } else {
        const menuItem = document.createElement('div')
        menuItem.textContent = item.text
        menuItem.style.cssText = `
          padding: 8px 12px;
          cursor: pointer;
          border-bottom: 1px solid #eee;
        `
        menuItem.addEventListener('click', () => {
          if (item.action) {
            item.action()
          }
          menu.remove()
        })
        menuItem.addEventListener('mouseenter', () => {
          menuItem.style.background = '#f0f0f0'
        })
        menuItem.addEventListener('mouseleave', () => {
          menuItem.style.background = 'white'
        })
        menu.appendChild(menuItem)
      }
    })

    document.body.appendChild(menu)

    // 点击其他地方关闭菜单
    const closeMenu = (e) => {
      if (!menu.contains(e.target)) {
        menu.remove()
        document.removeEventListener('click', closeMenu)
      }
    }
    setTimeout(() => document.addEventListener('click', closeMenu), 0)
  }

  async editWorkspaceName(workspaceId, workspace) {
    const newName = prompt('请输入新的工作区名称:', workspace.name)
    if (newName && newName.trim() && newName.trim() !== workspace.name) {
      await window.electronAPI.updateWorkspace(workspaceId, { name: newName.trim() })
    }
  }

  async editWorkspaceHomepage(workspaceId, workspace) {
    const newHomepage = prompt('请输入工作区主页地址:', workspace.homepage || 'https://www.google.com')
    if (newHomepage && newHomepage.trim() && newHomepage.trim() !== workspace.homepage) {
      await window.electronAPI.updateWorkspace(workspaceId, { homepage: newHomepage.trim() })
    }
  }

  async deleteWorkspace(workspaceId) {
    if (confirm('确定要删除这个工作区吗？删除后无法恢复。')) {
      await window.electronAPI.deleteWorkspace(workspaceId)
    }
  }

  removeWorkspaceFromUI(workspaceId) {
    const workspace = this.workspaces.get(workspaceId)
    if (workspace) {
      workspace.element.remove()
      this.workspaces.delete(workspaceId)
    }
  }

  updateWorkspaceInUI(workspaceId, workspace) {
    const workspaceUI = this.workspaces.get(workspaceId)
    if (workspaceUI) {
      workspaceUI.element.textContent = workspace.name
      workspaceUI.data = workspace
    }
  }


}

// 初始化浏览器UI
document.addEventListener('DOMContentLoaded', () => {
  new BrowserUI()
})
