class BrowserUI {
  constructor() {
    this.tabs = new Map()
    this.activeTabId = null
    this.rightTabs = new Map()
    this.activeRightTabId = null
    this.isResizing = false
    this.rightPanelWidth = 600
    this.leftPanelWidth = 70
    this.settings = {}
    this.workspaces = new Map()
    this.activeWorkspaceId = 'default'
    this.mouseGesture = {
      isActive: false,
      startX: 0,
      startY: 0,
      path: []
    }
    this.initializeElements()
    this.setupEventListeners()
    this.loadSettings()
    this.loadInitialTabs()
    this.loadWorkspaces()
  }

  async loadSettings() {
    this.settings = await window.electronAPI.getSettings()
    this.leftPanelWidth = this.settings.leftPanelWidth || 70
    this.updateLeftPanelWidth()
    this.applyTabWidthSetting()
  }

  applyTabWidthSetting() {
    this.updateTabWidths()
  }

  updateTabWidths() {
    const tabWidth = this.settings.tabWidth || 150
    const tabCount = this.tabs.size
    const newTabBtnWidth = 40 // 新建标签按钮的宽度
    const tabBarWidth = this.tabBar.clientWidth
    const availableWidth = tabBarWidth - newTabBtnWidth - 20 // 减去一些边距

    let finalTabWidth = tabWidth

    // 如果标签总宽度超过可用宽度，压缩标签
    if (tabCount * tabWidth > availableWidth && tabCount > 0) {
      finalTabWidth = Math.max(80, Math.floor(availableWidth / tabCount)) // 最小宽度80px
    }

    // 移除之前的样式
    const existingStyle = document.getElementById('tab-width-style')
    if (existingStyle) {
      existingStyle.remove()
    }

    // 添加新的样式
    const style = document.createElement('style')
    style.id = 'tab-width-style'
    style.textContent = `
      .tab {
        min-width: ${finalTabWidth}px !important;
        max-width: ${finalTabWidth}px !important;
        width: ${finalTabWidth}px !important;
        flex-shrink: 0;
      }
      .tab-title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    `
    document.head.appendChild(style)
  }

  async saveSettings() {
    await window.electronAPI.setSettings(this.settings)
  }

  initializeElements() {
    this.backBtn = document.getElementById('back-btn')
    this.forwardBtn = document.getElementById('forward-btn')
    this.reloadBtn = document.getElementById('reload-btn')
    this.homeBtn = document.getElementById('home-btn')
    this.addressBar = document.getElementById('address-bar')
    this.newTabBtn = document.getElementById('new-tab-btn')
    this.tabBar = document.getElementById('tab-bar')
    this.statusText = document.getElementById('status-text')
    this.urlPreview = document.getElementById('url-preview')
    
    // 左侧面板元素
    this.leftPanel = document.getElementById('left-panel')
    this.workspaceList = document.getElementById('workspace-list')
    this.newWorkspaceBtn = document.getElementById('new-workspace-btn')
    
    // 右侧面板元素
    this.toggleRightPanelBtn = document.getElementById('toggle-right-panel-btn')
    this.rightPanel = document.getElementById('right-panel')
    this.rightBackBtn = document.getElementById('right-back-btn')
    this.rightForwardBtn = document.getElementById('right-forward-btn')
    this.rightReloadBtn = document.getElementById('right-reload-btn')
    this.rightHomeBtn = document.getElementById('right-home-btn')
    this.rightAddressBar = document.getElementById('right-address-bar')
    this.rightNewTabBtn = document.getElementById('right-new-tab-btn')
    this.rightTabBar = document.getElementById('right-tab-bar')
    
    // 设置按钮
    this.settingsBtn = document.getElementById('settings-btn')
  }

  setupEventListeners() {
    // 主面板导航按钮
    this.backBtn.addEventListener('click', () => window.electronAPI.goBack())
    this.forwardBtn.addEventListener('click', () => window.electronAPI.goForward())
    this.reloadBtn.addEventListener('click', () => window.electronAPI.reload())
    this.homeBtn.addEventListener('click', () => window.electronAPI.goHome())
    
    // 主面板地址栏
    this.addressBar.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.navigate(this.addressBar.value)
      }
    })
    
    // 主面板新标签页
    this.newTabBtn.addEventListener('click', () => this.createNewTab())
    
    // 左侧面板
    this.newWorkspaceBtn.addEventListener('click', () => this.createNewWorkspace())
    
    // 右侧面板控制
    this.toggleRightPanelBtn.addEventListener('click', () => {
      this.toggleRightPanel()
    })
    
    // 右侧面板导航按钮
    this.rightBackBtn.addEventListener('click', () => window.electronAPI.goRightBack())
    this.rightForwardBtn.addEventListener('click', () => window.electronAPI.goRightForward())
    this.rightReloadBtn.addEventListener('click', () => window.electronAPI.reloadRight())
    this.rightHomeBtn.addEventListener('click', () => window.electronAPI.goRightHome())
    
    // 右侧面板地址栏
    this.rightAddressBar.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.navigateRight(this.rightAddressBar.value)
      }
    })
    
    // 右侧面板新标签页
    this.rightNewTabBtn.addEventListener('click', () => this.createNewRightTab())
    
    // 设置按钮
    this.settingsBtn.addEventListener('click', () => window.electronAPI.openSettings())

    // 用户脚本按钮
    document.getElementById('userscripts-btn').addEventListener('click', () => {
      window.electronAPI.openUserScripts()
    })

    // 笔记按钮
    document.getElementById('notes-btn').addEventListener('click', () => {
      window.electronAPI.openNotes()
    })

    // 密码管理器按钮
    document.getElementById('password-manager-btn').addEventListener('click', () => {
      window.electronAPI.openPasswordManager()
    })

    // 下载管理器按钮
    document.getElementById('download-manager-btn').addEventListener('click', () => {
      window.electronAPI.openDownloadManager()
    })


    
    // 右侧面板拖拽调整大小
    this.setupRightPanelResize()
    
    // 鼠标手势
    this.setupMouseGestures()
    
    // Electron事件监听
    window.electronAPI.onNavigationUpdated((event, data) => {
      this.updateNavigationState(data)
    })
    
    window.electronAPI.onRightNavigationUpdated((event, data) => {
      this.updateRightNavigationState(data)
    })
    
    window.electronAPI.onTabUpdated((event, tabId, tabData) => {
      this.updateTab(tabId, tabData)
    })
    
    window.electronAPI.onRightTabUpdated((event, tabId, tabData) => {
      this.updateRightTab(tabId, tabData)
    })
    
    window.electronAPI.onTabSwitched((event, tabId, tabData) => {
      this.switchToTab(tabId, tabData)
    })
    
    window.electronAPI.onRightTabSwitched((event, tabId, tabData) => {
      this.switchToRightTab(tabId, tabData)
    })
    
    window.electronAPI.onTabClosed((event, tabId) => {
      this.removeTab(tabId)
    })
    
    window.electronAPI.onRightTabClosed((event, tabId) => {
      this.removeRightTab(tabId)
    })

    window.electronAPI.onTabCreated((event, tabId, tabData) => {
      if (tabData.isRightPanel) {
        this.addRightTabToUI(tabId, tabData)
        this.switchToRightTab(tabId, tabData)
      } else {
        this.addTabToUI(tabId, tabData)
        this.switchToTab(tabId, tabData)
      }
    })
    
    window.electronAPI.onRightPanelToggled((event, visible) => {
      this.rightPanel.style.display = visible ? 'block' : 'none'
    })

    window.electronAPI.onRightPanelResized((event, width) => {
      this.rightPanelWidth = width
      this.rightPanel.style.width = width + 'px'
    })

    window.electronAPI.onLeftPanelResized((event, width) => {
      this.leftPanelWidth = width
      this.updateLeftPanelWidth()
    })

    // 启动时检测并同步左侧栏宽度
    this.syncLeftPanelWidth()

    window.electronAPI.onWorkspaceCreated((event, workspaceId, workspace) => {
      this.addWorkspaceToUI(workspaceId, workspace)
    })

    window.electronAPI.onWorkspaceSeparatorCreated((event, separatorId, separator) => {
      this.addSeparatorToUI(separatorId, separator)
    })

    window.electronAPI.onWorkspaceSwitched((event, workspaceId, workspace) => {
      this.switchToWorkspace(workspaceId, workspace)
    })

    window.electronAPI.onWorkspaceDeleted((event, workspaceId) => {
      this.removeWorkspaceFromUI(workspaceId)
    })

    window.electronAPI.onWorkspaceUpdated((event, workspaceId, workspace) => {
      this.updateWorkspaceInUI(workspaceId, workspace)
    })

    // 标签休眠事件监听
    window.electronAPI.onTabSlept((event, tabId) => {
      this.markTabAsSleeping(tabId, false)
    })

    window.electronAPI.onTabWokeUp((event, tabId) => {
      this.markTabAsAwake(tabId, false)
    })

    window.electronAPI.onRightTabSlept((event, tabId) => {
      this.markTabAsSleeping(tabId, true)
    })

    window.electronAPI.onRightTabWokeUp((event, tabId) => {
      this.markTabAsAwake(tabId, true)
    })

    // 标签移动事件监听
    window.electronAPI.onTabMoved((event, tabId) => {
      this.removeTabFromUI(tabId)
    })

    // 窗口大小改变时更新标签宽度
    window.addEventListener('resize', () => {
      this.updateTabWidths()
    })



    // ESC键关闭设置
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const modal = document.querySelector('.settings-modal')
        if (modal) {
          modal.remove()
        }
      }
    })
  }

  setupRightPanelResize() {
    const resizeHandle = document.createElement('div')
    resizeHandle.className = 'resize-handle'
    this.rightPanel.appendChild(resizeHandle)

    resizeHandle.addEventListener('mousedown', (e) => {
      this.isResizing = true
      document.addEventListener('mousemove', this.handleResize.bind(this))
      document.addEventListener('mouseup', this.stopResize.bind(this))
      e.preventDefault()
    })
  }

  handleResize(e) {
    if (!this.isResizing) return
    
    const windowWidth = window.innerWidth
    const newWidth = windowWidth - e.clientX
    const maxWidth = Math.floor(windowWidth * 0.5)
    const minWidth = 300
    
    const clampedWidth = Math.max(minWidth, Math.min(newWidth, maxWidth))
    window.electronAPI.resizeRightPanel(clampedWidth)
  }

  stopResize() {
    this.isResizing = false
    document.removeEventListener('mousemove', this.handleResize)
    document.removeEventListener('mouseup', this.stopResize)
  }

  async toggleRightPanel() {
    console.log('Toggling right panel...')
    await window.electronAPI.toggleRightPanel()
  }

  navigate(url) {
    if (!url) return

    // 处理本地文件路径
    if (this.isLocalFilePath(url)) {
      url = this.normalizeLocalFileUrl(url)
    } else if (!url.includes('.') && !url.startsWith('http') && !url.startsWith('file://')) {
      const searchEngine = this.settings.searchEngine || 'google'
      url = this.getSearchUrl(url, searchEngine)
    } else if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('file://')) {
      url = 'https://' + url
    }

    window.electronAPI.navigate(url)
    this.statusText.textContent = '正在加载...'
  }

  navigateRight(url) {
    if (!url) return

    // 处理本地文件路径
    if (this.isLocalFilePath(url)) {
      url = this.normalizeLocalFileUrl(url)
    } else if (!url.includes('.') && !url.startsWith('http') && !url.startsWith('file://')) {
      const searchEngine = this.settings.searchEngine || 'baidu'
      url = this.getSearchUrl(url, searchEngine)
    } else if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('file://')) {
      url = 'https://' + url
    }

    window.electronAPI.navigateRight(url)
  }

  getSearchUrl(query, searchEngine = 'google') {
    const engines = {
      google: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
      bing: `https://www.bing.com/search?q=${encodeURIComponent(query)}`,
      baidu: `https://www.baidu.com/s?wd=${encodeURIComponent(query)}`
    }
    
    // 检查自定义搜索引擎
    if (this.settings.customSearchEngines) {
      const customEngine = this.settings.customSearchEngines.find(e => e.id === searchEngine)
      if (customEngine) {
        return customEngine.url.replace('%s', encodeURIComponent(query))
      }
    }
    
    return engines[searchEngine] || engines.google
  }

  // 检查是否为本地文件路径
  isLocalFilePath(url) {
    // 检查是否为file://协议
    if (url.startsWith('file://')) {
      return true
    }

    // 检查是否为绝对路径（Windows: C:\ 或 D:\, macOS/Linux: /）
    if (url.match(/^[A-Za-z]:\\/) || url.startsWith('/')) {
      return true
    }

    // 检查是否包含中文路径特征
    if (url.includes('%') && (url.includes('/') || url.includes('\\'))) {
      try {
        const decoded = decodeURIComponent(url)
        if (decoded.startsWith('/') || decoded.match(/^[A-Za-z]:\\/)) {
          return true
        }
      } catch (e) {
        // 解码失败，可能不是有效的URL编码
      }
    }

    return false
  }

  // 规范化本地文件URL
  normalizeLocalFileUrl(url) {
    // 如果已经是file://协议，直接返回
    if (url.startsWith('file://')) {
      return url
    }

    // 处理Windows路径
    if (url.match(/^[A-Za-z]:\\/)) {
      return 'file:///' + url.replace(/\\/g, '/')
    }

    // 处理Unix路径
    if (url.startsWith('/')) {
      return 'file://' + url
    }

    // 处理URL编码的路径
    if (url.includes('%')) {
      try {
        const decoded = decodeURIComponent(url)
        if (decoded.startsWith('/')) {
          return 'file://' + url // 保持原始编码
        } else if (decoded.match(/^[A-Za-z]:\\/)) {
          return 'file:///' + url.replace(/\\/g, '/')
        }
      } catch (e) {
        console.warn('Failed to decode URL:', url)
      }
    }

    return url
  }

  async createNewTab(url = '') {
    const tabId = await window.electronAPI.createTab(url)
  }

  async createNewRightTab(url = '') {
    const tabId = await window.electronAPI.createRightTab(url)
  }

  addTabToUI(tabId, tabData) {
    if (this.tabs.has(tabId)) return

    const tab = document.createElement('div')
    tab.className = 'tab'
    tab.dataset.tabId = tabId
    tab.draggable = true
    
    const title = document.createElement('span')
    title.className = 'tab-title'
    // 优先使用自定义标题，然后是原始标题
    const displayTitle = tabData.customTitle || tabData.title || 'New Tab'
    // 如果标签页在休眠，添加休眠图标
    const sleepIcon = tabData.isSleeping ? '💤 ' : ''
    title.textContent = sleepIcon + displayTitle
    
    const closeBtn = document.createElement('button')
    closeBtn.className = 'tab-close'
    closeBtn.textContent = '×'
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation()
      this.closeTab(tabId)
    })
    
    tab.appendChild(title)
    tab.appendChild(closeBtn)
    
    tab.addEventListener('click', () => {
      window.electronAPI.switchTab(tabId)
    })

    // 添加右键菜单
    tab.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      this.showTabContextMenu(e, tabId, tabData)
    })

    // 添加拖拽事件
    tab.addEventListener('dragstart', (e) => {
      e.dataTransfer.setData('text/plain', tabId)
      tab.classList.add('dragging')
    })

    tab.addEventListener('dragend', () => {
      tab.classList.remove('dragging')
    })

    tab.addEventListener('dragover', (e) => {
      e.preventDefault()
    })

    tab.addEventListener('drop', (e) => {
      e.preventDefault()
      const draggedTabId = parseInt(e.dataTransfer.getData('text/plain'))
      if (draggedTabId !== tabId) {
        this.reorderTabs(draggedTabId, tabId)
      }
    })
    
    // 插入到新建标签按钮之前
    this.tabBar.insertBefore(tab, this.newTabBtn)
    this.tabs.set(tabId, { element: tab, data: tabData })

    // 更新标签宽度
    this.updateTabWidths()
  }

  addRightTabToUI(tabId, tabData) {
    if (this.rightTabs.has(tabId)) return

    const tab = document.createElement('div')
    tab.className = 'tab'
    tab.dataset.tabId = tabId
    
    const title = document.createElement('span')
    title.className = 'tab-title'
    title.textContent = tabData.title || 'New Tab'
    
    const closeBtn = document.createElement('button')
    closeBtn.className = 'tab-close'
    closeBtn.textContent = '×'
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation()
      this.closeRightTab(tabId)
    })
    
    tab.appendChild(title)
    tab.appendChild(closeBtn)
    
    tab.addEventListener('click', () => {
      window.electronAPI.switchRightTab(tabId)
    })
    
    this.rightTabBar.appendChild(tab)
    this.rightTabs.set(tabId, { element: tab, data: tabData })
  }

  updateTab(tabId, tabData) {
    const tab = this.tabs.get(tabId)
    if (!tab) return

    tab.data = { ...tab.data, ...tabData }
    const titleElement = tab.element.querySelector('.tab-title')

    // 优先使用自定义标题，然后是原始标题
    const displayTitle = tabData.customTitle || tabData.title || 'Loading...'
    // 如果标签页在休眠，添加休眠图标
    const sleepIcon = tabData.isSleeping ? '💤 ' : ''
    titleElement.textContent = sleepIcon + displayTitle

    if (tabId === this.activeTabId) {
      this.addressBar.value = tabData.url || ''
      this.statusText.textContent = '就绪'
    }
  }

  updateRightTab(tabId, tabData) {
    const tab = this.rightTabs.get(tabId)
    if (!tab) return
    
    tab.data = { ...tab.data, ...tabData }
    const titleElement = tab.element.querySelector('.tab-title')
    titleElement.textContent = tabData.title || 'Loading...'
    
    if (tabId === this.activeRightTabId) {
      this.rightAddressBar.value = tabData.url || ''
    }
  }

  switchToTab(tabId, tabData) {
    if (this.activeTabId) {
      const prevTab = this.tabs.get(this.activeTabId)
      if (prevTab) {
        prevTab.element.classList.remove('active')
      }
    }
    
    this.activeTabId = tabId
    this.updateActiveTab()
    this.addressBar.value = tabData.url || ''
  }

  switchToRightTab(tabId, tabData) {
    if (this.activeRightTabId) {
      const prevTab = this.rightTabs.get(this.activeRightTabId)
      if (prevTab) {
        prevTab.element.classList.remove('active')
      }
    }
    
    this.activeRightTabId = tabId
    this.updateActiveRightTab()
    this.rightAddressBar.value = tabData.url || ''
  }

  updateActiveTab() {
    const tab = this.tabs.get(this.activeTabId)
    if (tab) {
      tab.element.classList.add('active')
      tab.element.scrollIntoView({ behavior: 'smooth', inline: 'nearest' })
    }
  }

  updateActiveRightTab() {
    const tab = this.rightTabs.get(this.activeRightTabId)
    if (tab) {
      tab.element.classList.add('active')
      tab.element.scrollIntoView({ behavior: 'smooth', inline: 'nearest' })
    }
  }

  removeTab(tabId) {
    const tab = this.tabs.get(tabId)
    if (tab) {
      tab.element.remove()
      this.tabs.delete(tabId)
    }
  }

  removeRightTab(tabId) {
    const tab = this.rightTabs.get(tabId)
    if (tab) {
      tab.element.remove()
      this.rightTabs.delete(tabId)
    }
  }

  closeTab(tabId) {
    window.electronAPI.closeTab(tabId)
  }

  closeRightTab(tabId) {
    window.electronAPI.closeRightTab(tabId)
  }

  updateNavigationState(data) {
    this.backBtn.disabled = !data.canGoBack
    this.forwardBtn.disabled = !data.canGoForward
    
    if (data.url) {
      this.addressBar.value = data.url
    }
  }

  updateRightNavigationState(data) {
    this.rightBackBtn.disabled = !data.canGoBack
    this.rightForwardBtn.disabled = !data.canGoForward
    
    if (data.url) {
      this.rightAddressBar.value = data.url
    }
  }

  showSettings() {
    const modal = document.createElement('div')
    modal.className = 'settings-modal'
    modal.innerHTML = `
      <div class="settings-content">
        <h3>设置</h3>
        <div class="setting-item">
          <label>主页地址:</label>
          <input type="text" id="homepage-input" value="${this.settings.homepage || 'https://www.baidu.com'}" placeholder="https://www.google.com">
        </div>
        <div class="setting-item">
          <label>搜索引擎:</label>
          <select id="search-engine-select">
            <option value="google" ${this.settings.searchEngine === 'google' ? 'selected' : ''}>Google</option>
            <option value="bing" ${this.settings.searchEngine === 'bing' ? 'selected' : ''}>Bing</option>
            <option value="baidu" ${this.settings.searchEngine === 'baidu' ? 'selected' : ''}>百度</option>
          </select>
        </div>
        <div class="setting-buttons">
          <button id="save-settings">保存</button>
          <button id="cancel-settings">取消</button>
        </div>
      </div>
    `
    
    document.body.appendChild(modal)
    
    document.getElementById('save-settings').addEventListener('click', async () => {
      const homepage = document.getElementById('homepage-input').value
      const searchEngine = document.getElementById('search-engine-select').value
      
      this.settings.homepage = homepage
      this.settings.searchEngine = searchEngine
      
      await this.saveSettings()
      modal.remove()
    })
    
    document.getElementById('cancel-settings').addEventListener('click', () => {
      modal.remove()
    })
  }

  async loadInitialTabs() {
    const tabs = await window.electronAPI.getAllTabs()
    for (const [id, tab] of Object.entries(tabs)) {
      this.addTabToUI(parseInt(id), tab)
      if (tab.active) {
        this.activeTabId = parseInt(id)
        this.updateActiveTab()
      }
    }
  }

  setupMouseGestures() {
    let isRightMouseDown = false
    let gestureStartX = 0
    let gestureStartY = 0
    let gesturePath = []
    let gestureTriggered = false

    document.addEventListener('mousedown', (e) => {
      if (e.button === 2) { // 右键
        isRightMouseDown = true
        gestureStartX = e.clientX
        gestureStartY = e.clientY
        gesturePath = []
        gestureTriggered = false
      }
    })

    document.addEventListener('mousemove', (e) => {
      if (isRightMouseDown) {
        const deltaX = e.clientX - gestureStartX
        const deltaY = e.clientY - gestureStartY

        if (Math.abs(deltaX) > 50 || Math.abs(deltaY) > 50) {
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            if (deltaX > 0) {
              gesturePath.push('right')
            } else {
              gesturePath.push('left')
            }
          } else {
            if (deltaY > 0) {
              gesturePath.push('down')
            } else {
              gesturePath.push('up')
            }
          }
          gestureStartX = e.clientX
          gestureStartY = e.clientY
          gestureTriggered = true
        }
      }
    })

    document.addEventListener('mouseup', (e) => {
      if (e.button === 2 && isRightMouseDown) {
        isRightMouseDown = false

        if (gestureTriggered) {
          this.executeGesture(gesturePath)
          e.preventDefault()
        }
      }
    })
  }

  executeGesture(path) {
    const pathString = path.join('-')
    
    switch (pathString) {
      case 'left':
        window.electronAPI.goBack()
        break
      case 'right':
        window.electronAPI.goForward()
        break
      case 'down-right':
        if (this.activeTabId) {
          window.electronAPI.closeTab(this.activeTabId)
        }
        break
    }
  }

  updateLeftPanelWidth() {
    this.leftPanel.style.width = this.leftPanelWidth + 'px'
    document.documentElement.style.setProperty('--left-panel-width', this.leftPanelWidth + 'px')
  }

  async syncLeftPanelWidth() {
    try {
      // 获取当前左侧栏的实际宽度
      const computedStyle = window.getComputedStyle(this.leftPanel)
      const actualWidth = parseInt(computedStyle.width)

      if (actualWidth && actualWidth !== this.leftPanelWidth) {
        this.leftPanelWidth = actualWidth
        // 通知主进程更新左侧栏宽度，这会触发标签页边界更新
        await window.electronAPI.setSettings({ leftPanelWidth: actualWidth })
      }
    } catch (error) {
      console.error('Failed to sync left panel width:', error)
    }
  }

  async createNewWorkspace() {
    try {
      const result = await window.electronAPI.promptWorkspaceType()
      if (result) {
        if (result.type === 'separator') {
          await window.electronAPI.createWorkspaceSeparator(result.name)
        } else {
          await window.electronAPI.createWorkspace(result.name)
        }
      }
    } catch (error) {
      console.error('Failed to create workspace:', error)
    }
  }

  addWorkspaceToUI(workspaceId, workspace) {
    const workspaceItem = document.createElement('div')
    workspaceItem.className = 'workspace-item'
    workspaceItem.dataset.workspaceId = workspaceId
    workspaceItem.textContent = workspace.name
    workspaceItem.draggable = true

    workspaceItem.addEventListener('click', () => {
      window.electronAPI.switchWorkspace(workspaceId)
    })

    // 添加右键菜单
    workspaceItem.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      this.showWorkspaceContextMenu(e, workspaceId, workspace)
    })

    // 添加拖拽事件
    workspaceItem.addEventListener('dragstart', (e) => {
      e.dataTransfer.setData('text/plain', workspaceId)
      e.dataTransfer.setData('application/x-workspace-id', workspaceId) // 添加特定的数据类型
      workspaceItem.classList.add('dragging')
    })

    workspaceItem.addEventListener('dragend', () => {
      workspaceItem.classList.remove('dragging')
    })

    workspaceItem.addEventListener('dragover', (e) => {
      e.preventDefault()
    })

    workspaceItem.addEventListener('drop', (e) => {
      e.preventDefault()
      const draggedWorkspaceId = e.dataTransfer.getData('text/plain')
      if (draggedWorkspaceId !== workspaceId) {
        this.reorderWorkspaces(draggedWorkspaceId, workspaceId)
      }
    })

    this.workspaceList.appendChild(workspaceItem)
    this.workspaces.set(workspaceId, { element: workspaceItem, data: workspace })
  }

  addSeparatorToUI(separatorId, separator) {
    const separatorItem = document.createElement('div')
    separatorItem.className = 'workspace-separator'
    separatorItem.dataset.separatorId = separatorId
    separatorItem.innerHTML = `
      <div class="separator-label">${separator.name}</div>
      <div class="separator-toggle ${separator.collapsed ? 'collapsed' : ''}" onclick="this.toggleSeparator('${separatorId}')">▼</div>
    `

    separatorItem.addEventListener('click', (e) => {
      if (!e.target.classList.contains('separator-toggle')) {
        this.toggleSeparator(separatorId)
      }
    })

    // 添加右键菜单
    separatorItem.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      e.stopPropagation()
      this.showSeparatorContextMenu(e, separatorId, separator)
    })

    // 添加拖拽事件监听器，让工作区可以拖拽到分割线上
    separatorItem.addEventListener('dragover', (e) => {
      e.preventDefault()
      // 添加视觉反馈
      separatorItem.classList.add('drag-over')
    })

    separatorItem.addEventListener('dragleave', (e) => {
      // 移除视觉反馈
      separatorItem.classList.remove('drag-over')
    })

    separatorItem.addEventListener('drop', (e) => {
      e.preventDefault()
      separatorItem.classList.remove('drag-over')

      // 优先使用特定的数据类型，回退到通用类型
      const draggedWorkspaceId = e.dataTransfer.getData('application/x-workspace-id') ||
                                 e.dataTransfer.getData('text/plain')

      if (draggedWorkspaceId && draggedWorkspaceId.startsWith('workspace_')) {
        this.moveWorkspaceToSeparator(draggedWorkspaceId, separatorId)
      }
    })

    this.workspaceList.appendChild(separatorItem)

    // 存储分割线信息
    if (!this.separators) {
      this.separators = new Map()
    }
    this.separators.set(separatorId, { element: separatorItem, data: separator })
  }

  async toggleSeparator(separatorId) {
    const separator = this.separators.get(separatorId)
    if (!separator) return

    const isCollapsed = !separator.data.collapsed
    separator.data.collapsed = isCollapsed

    const toggle = separator.element.querySelector('.separator-toggle')
    toggle.classList.toggle('collapsed', isCollapsed)
    toggle.textContent = isCollapsed ? '▶' : '▼'

    // 找到这个分割线下方的所有工作区，直到下一个分割线
    let nextElement = separator.element.nextElementSibling
    while (nextElement && !nextElement.classList.contains('workspace-separator')) {
      if (nextElement.classList.contains('workspace-item')) {
        nextElement.style.display = isCollapsed ? 'none' : 'block'
      }
      nextElement = nextElement.nextElementSibling
    }

    // 通过IPC保存分割线状态到session.json
    await window.electronAPI.updateSeparator(separatorId, { collapsed: isCollapsed })
  }

  async saveSeparatorStates() {
    // 不再使用localStorage，而是通过IPC保存到session.json
    // 分割线状态的保存已经在toggleSeparator中通过updateSeparator处理
  }

  // 分割线状态现在从session.json恢复，不再需要单独的loadSeparatorStates方法

  showSeparatorContextMenu(e, separatorId, separator) {
    const menu = document.createElement('div')
    menu.className = 'context-menu'
    menu.style.cssText = `
      position: fixed;
      left: ${e.clientX}px;
      top: ${e.clientY}px;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 10000;
      min-width: 150px;
    `

    const menuItems = [
      { text: '修改名称', action: () => this.editSeparatorName(separatorId, separator) },
      { text: '删除分割线', action: () => this.deleteSeparator(separatorId) }
    ]

    menuItems.forEach(item => {
      const menuItem = document.createElement('div')
      menuItem.className = 'context-menu-item'
      menuItem.textContent = item.text
      menuItem.style.cssText = `
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
      `

      menuItem.addEventListener('mouseenter', () => {
        menuItem.style.background = '#f0f0f0'
      })

      menuItem.addEventListener('mouseleave', () => {
        menuItem.style.background = 'white'
      })

      menuItem.addEventListener('click', () => {
        item.action()
        menu.remove()
      })

      menu.appendChild(menuItem)
    })

    // 移除最后一个分隔线
    const lastItem = menu.lastElementChild
    if (lastItem) {
      lastItem.style.borderBottom = 'none'
    }

    document.body.appendChild(menu)

    // 点击其他地方关闭菜单
    const closeMenu = (event) => {
      if (!menu.contains(event.target)) {
        menu.remove()
        document.removeEventListener('click', closeMenu)
      }
    }
    setTimeout(() => document.addEventListener('click', closeMenu), 0)
  }

  async editSeparatorName(separatorId, separator) {
    this.showSeparatorNameInputModal(separatorId, separator)
  }

  showSeparatorNameInputModal(separatorId, separator) {
    const modal = document.createElement('div')
    modal.className = 'modal'
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    `

    modal.innerHTML = `
      <div class="modal-content" style="
        background: white;
        border-radius: 12px;
        padding: 24px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      ">
        <div class="modal-header" style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        ">
          <h3 style="margin: 0; color: #333;">编辑分组名称</h3>
          <button class="btn-close" style="
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
          ">&times;</button>
        </div>

        <div class="form-group" style="margin-bottom: 16px;">
          <label style="
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #333;
          ">分组名称</label>
          <input type="text" id="separator-name-input" style="
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            outline: none;
            box-sizing: border-box;
          " value="${separator.name}" autofocus>
        </div>

        <div class="modal-actions" style="
          display: flex;
          gap: 10px;
          justify-content: flex-end;
          margin-top: 20px;
        ">
          <button type="button" class="btn-cancel" style="
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #666;
            cursor: pointer;
          ">取消</button>
          <button type="button" class="btn-save" style="
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            background: #667eea;
            color: white;
            cursor: pointer;
          ">保存</button>
        </div>
      </div>
    `

    document.body.appendChild(modal)

    const input = document.getElementById('separator-name-input')
    const closeBtn = modal.querySelector('.btn-close')
    const cancelBtn = modal.querySelector('.btn-cancel')
    const saveBtn = modal.querySelector('.btn-save')

    // 聚焦输入框并选中文本
    setTimeout(() => {
      input.focus()
      input.select()
    }, 100)

    // 关闭模态框
    const closeModal = () => {
      modal.remove()
    }

    // 保存名称
    const saveName = async () => {
      const newName = input.value.trim()
      if (newName && newName !== separator.name) {
        // 更新本地数据
        separator.name = newName

        // 更新UI
        const separatorElement = this.separators.get(separatorId)?.element
        if (separatorElement) {
          const labelElement = separatorElement.querySelector('.separator-label')
          if (labelElement) {
            labelElement.textContent = newName
          }
        }

        // 通过IPC保存到session.json
        await window.electronAPI.updateSeparator(separatorId, { name: newName })
      }
      closeModal()
    }

    // 事件监听
    closeBtn.addEventListener('click', closeModal)
    cancelBtn.addEventListener('click', closeModal)
    saveBtn.addEventListener('click', saveName)

    // 回车键保存
    input.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        saveName()
      } else if (e.key === 'Escape') {
        closeModal()
      }
    })

    // 点击模态框外部关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal()
      }
    })
  }

  async deleteSeparator(separatorId) {
    if (confirm('确定要删除这个分割线吗？')) {
      // 从UI中移除
      const separatorData = this.separators.get(separatorId)
      if (separatorData && separatorData.element) {
        separatorData.element.remove()
      }

      // 从本地Map中移除
      this.separators.delete(separatorId)

      // 通过IPC从主进程中删除
      await window.electronAPI.deleteSeparator(separatorId)
    }
  }

  switchToWorkspace(workspaceId, workspace) {
    // 更新活跃工作区
    if (this.activeWorkspaceId) {
      const prevWorkspace = this.workspaces.get(this.activeWorkspaceId)
      if (prevWorkspace) {
        prevWorkspace.element.classList.remove('active')
      }
    }

    this.activeWorkspaceId = workspaceId
    const currentWorkspace = this.workspaces.get(workspaceId)
    if (currentWorkspace) {
      currentWorkspace.element.classList.add('active')
    }

    // 清空当前标签页显示
    this.tabBar.innerHTML = ''
    this.tabBar.appendChild(this.newTabBtn)
    this.tabs.clear()
    this.activeTabId = null

    // 重新加载工作区的标签页
    this.loadWorkspaceTabs()
  }

  async loadWorkspaceTabs() {
    // 重新加载当前工作区的标签页
    const tabs = await window.electronAPI.getAllTabs()
    for (const [id, tab] of Object.entries(tabs)) {
      this.addTabToUI(parseInt(id), tab)
      if (tab.active) {
        this.activeTabId = parseInt(id)
        this.updateActiveTab()
        this.addressBar.value = tab.url || ''
      }
    }
  }

  async loadWorkspaces() {
    // 清空现有的工作区列表
    //this.workspaceList.innerHTML = ''
    //this.workspaces.clear()
    if (this.separators) {
      this.separators.clear()
    }

    // 获取按顺序排列的工作区和分割线
    const orderedItems = await window.electronAPI.getOrderedWorkspaceItems()

    for (const item of orderedItems) {
      if (item.type === 'workspace') {
        this.addWorkspaceToUI(item.id, item)
      } else if (item.type === 'separator') {
        this.addSeparatorToUI(item.id, item)
      }
    }

    // 设置默认工作区为活跃状态
    const defaultWorkspace = this.workspaces.get('default')
    if (defaultWorkspace) {
      defaultWorkspace.element.classList.add('active')
    }
  }

  showWorkspaceContextMenu(e, workspaceId, workspace) {
    const menu = document.createElement('div')
    menu.className = 'context-menu'
    menu.style.cssText = `
      position: fixed;
      left: ${e.clientX}px;
      top: ${e.clientY}px;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 999999;
      min-width: 150px;
    `

    const menuItems = []

    // 基础菜单项
    menuItems.push(
      { text: '修改名称', action: () => this.editWorkspaceName(workspaceId, workspace) },
      { text: '设置主页', action: () => this.editWorkspaceHomepage(workspaceId, workspace) }
    )

    // 如果不是默认工作区，可以删除
    if (workspaceId !== 'default') {
      menuItems.push(
        { text: '---', action: null }, // 分隔线
        { text: '删除工作区', action: () => this.deleteWorkspace(workspaceId) }
      )
    }

    menuItems.forEach(item => {
      if (item.text === '---') {
        // 分隔线
        const separator = document.createElement('div')
        separator.style.cssText = `
          height: 1px;
          background: #ddd;
          margin: 4px 0;
        `
        menu.appendChild(separator)
      } else {
        const menuItem = document.createElement('div')
        menuItem.textContent = item.text
        menuItem.style.cssText = `
          padding: 8px 12px;
          cursor: pointer;
          border-bottom: 1px solid #eee;
        `
        menuItem.addEventListener('click', () => {
          if (item.action) {
            item.action()
          }
          menu.remove()
        })
        menuItem.addEventListener('mouseenter', () => {
          menuItem.style.background = '#f0f0f0'
        })
        menuItem.addEventListener('mouseleave', () => {
          menuItem.style.background = 'white'
        })
        menu.appendChild(menuItem)
      }
    })

    document.body.appendChild(menu)

    // 点击其他地方关闭菜单
    const closeMenu = (e) => {
      if (!menu.contains(e.target)) {
        menu.remove()
        document.removeEventListener('click', closeMenu)
      }
    }
    setTimeout(() => document.addEventListener('click', closeMenu), 0)
  }

  async editWorkspaceName(workspaceId, workspace) {
    const newName = await window.electronAPI.promptInput({
      title: '修改工作区名称',
      message: '请输入新的工作区名称:',
      defaultValue: workspace.name,
      placeholder: '工作区名称'
    })
    if (newName && newName.trim() && newName.trim() !== workspace.name) {
      await window.electronAPI.updateWorkspace(workspaceId, { name: newName.trim() })
    }
  }

  async editWorkspaceHomepage(workspaceId, workspace) {
    const newHomepage = await window.electronAPI.promptInput({
      title: '设置工作区主页',
      message: '请输入工作区主页地址:',
      defaultValue: workspace.homepage || 'https://www.baidu.com',
      placeholder: 'https://www.baidu.com'
    })
    if (newHomepage && newHomepage.trim() && newHomepage.trim() !== workspace.homepage) {
      await window.electronAPI.updateWorkspace(workspaceId, { homepage: newHomepage.trim() })
    }
  }

  async deleteWorkspace(workspaceId) {
    if (confirm('确定要删除这个工作区吗？删除后无法恢复。')) {
      await window.electronAPI.deleteWorkspace(workspaceId)
    }
  }

  removeWorkspaceFromUI(workspaceId) {
    const workspace = this.workspaces.get(workspaceId)
    if (workspace) {
      workspace.element.remove()
      this.workspaces.delete(workspaceId)
    }
  }

  updateWorkspaceInUI(workspaceId, workspace) {
    const workspaceUI = this.workspaces.get(workspaceId)
    if (workspaceUI) {
      workspaceUI.element.textContent = workspace.name
      workspaceUI.data = workspace
    }
  }

  async showTabContextMenu(e, tabId, tabData) {
    const menu = document.createElement('div')
    menu.className = 'context-menu'
    menu.style.cssText = `
      display: flex;
      position: fixed;
      left: ${e.clientX}px;
      top: ${e.clientY}px;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 999999;
      min-width: 150px;
    `

    const menuItems = []

    // 基础菜单项
    menuItems.push(
      { text: '修改标签名', action: () => this.editTabTitle(tabId, tabData) },
      { text: '静音', action: () => this.toggleTabMute(tabId) }
    )

    // 获取所有工作区，添加移动选项
    const workspaces = await window.electronAPI.getAllWorkspaces()
    if (workspaces.length > 1) {
      menuItems.push({ text: '---', action: null }) // 分隔线

      for (const workspace of workspaces) {
        if (workspace.id !== this.activeWorkspaceId) {
          menuItems.push({
            text: `移动到 ${workspace.name}`,
            action: () => this.moveTabToWorkspace(tabId, workspace.id)
          })
        }
      }
    }

    menuItems.forEach(item => {
      if (item.text === '---') {
        // 分隔线
        const separator = document.createElement('div')
        separator.style.cssText = `
          height: 1px;
          background: #ddd;
          margin: 4px 0;
        `
        menu.appendChild(separator)
      } else {
        const menuItem = document.createElement('div')
        menuItem.textContent = item.text
        menuItem.style.cssText = `
          padding: 8px 12px;
          cursor: pointer;
          border-bottom: 1px solid #eee;
        `
        menuItem.addEventListener('click', () => {
          if (item.action) {
            item.action()
          }
          menu.remove()
        })
        menuItem.addEventListener('mouseenter', () => {
          menuItem.style.background = '#f0f0f0'
        })
        menuItem.addEventListener('mouseleave', () => {
          menuItem.style.background = 'white'
        })
        menu.appendChild(menuItem)
      }
    })

    document.body.appendChild(menu)

    // 点击其他地方关闭菜单
    const closeMenu = (e) => {
      if (!menu.contains(e.target)) {
        menu.remove()
        document.removeEventListener('click', closeMenu)
      }
    }
    setTimeout(() => document.addEventListener('click', closeMenu), 0)
  }

  async editTabTitle(tabId, tabData) {
    const currentTitle = tabData.customTitle || tabData.title
    const newTitle = await window.electronAPI.promptInput({
      title: '修改标签名',
      message: '请输入新的标签名称:',
      defaultValue: currentTitle,
      placeholder: '标签名称'
    })

    if (newTitle && newTitle.trim() && newTitle.trim() !== currentTitle) {
      await window.electronAPI.updateTabTitle(tabId, newTitle.trim())
    }
  }

  async moveTabToWorkspace(tabId, workspaceId) {
    await window.electronAPI.moveTabToWorkspace(tabId, workspaceId)
  }

  toggleTabMute(tabId) {
    window.electronAPI.toggleTabMute(tabId)
  }

  removeTabFromUI(tabId) {
    const tab = this.tabs.get(tabId)
    if (tab) {
      tab.element.remove()
      this.tabs.delete(tabId)
      // 更新标签宽度
      this.updateTabWidths()
    }
  }

  reorderTabs(draggedTabId, targetTabId) {
    const draggedTab = this.tabs.get(draggedTabId)
    const targetTab = this.tabs.get(targetTabId)

    if (!draggedTab || !targetTab) return

    // 获取目标标签的位置
    const targetElement = targetTab.element
    const targetRect = targetElement.getBoundingClientRect()
    const tabBarRect = this.tabBar.getBoundingClientRect()

    // 移除被拖拽的标签
    draggedTab.element.remove()

    // 在目标位置插入
    if (targetElement.nextSibling) {
      this.tabBar.insertBefore(draggedTab.element, targetElement.nextSibling)
    } else {
      this.tabBar.insertBefore(draggedTab.element, this.newTabBtn)
    }

    // 保存标签顺序到会话
    window.electronAPI.reorderTabs()
  }

  reorderWorkspaces(draggedWorkspaceId, targetWorkspaceId) {
    const draggedWorkspace = this.workspaces.get(draggedWorkspaceId)
    const targetWorkspace = this.workspaces.get(targetWorkspaceId)

    if (!draggedWorkspace || !targetWorkspace) return

    // 移除被拖拽的工作区
    draggedWorkspace.element.remove()

    // 在目标位置插入
    const targetElement = targetWorkspace.element
    if (targetElement.nextSibling) {
      this.workspaceList.insertBefore(draggedWorkspace.element, targetElement.nextSibling)
    } else {
      this.workspaceList.appendChild(draggedWorkspace.element)
    }

    // 保存工作区顺序
    window.electronAPI.reorderWorkspaces()
  }

  moveWorkspaceToSeparator(draggedWorkspaceId, targetSeparatorId) {
    const draggedWorkspace = this.workspaces.get(draggedWorkspaceId)
    const targetSeparator = this.separators.get(targetSeparatorId)

    if (!draggedWorkspace || !targetSeparator) return

    // 移除被拖拽的工作区
    draggedWorkspace.element.remove()

    // 在分割线下方插入工作区
    const targetElement = targetSeparator.element
    if (targetElement.nextSibling) {
      this.workspaceList.insertBefore(draggedWorkspace.element, targetElement.nextSibling)
    } else {
      this.workspaceList.appendChild(draggedWorkspace.element)
    }

    // 保存工作区顺序
    window.electronAPI.reorderWorkspaces()
  }

  markTabAsSleeping(tabId, isRightPanel = false) {
    const tabs = isRightPanel ? this.rightTabs : this.tabs
    const tab = tabs.get(tabId)
    if (tab) {
      tab.element.classList.add('sleeping')
      tab.element.title = '标签页已休眠 - 点击唤醒'
      // 可以添加一个休眠图标
      const sleepIcon = tab.element.querySelector('.sleep-icon')
      if (!sleepIcon) {
        const icon = document.createElement('span')
        icon.className = 'sleep-icon'
        icon.textContent = '💤'
        icon.style.cssText = 'margin-left: 4px; font-size: 12px;'
        tab.element.appendChild(icon)
      }
    }
  }

  markTabAsAwake(tabId, isRightPanel = false) {
    const tabs = isRightPanel ? this.rightTabs : this.tabs
    const tab = tabs.get(tabId)
    if (tab) {
      tab.element.classList.remove('sleeping')
      tab.element.title = ''
      // 移除休眠图标
      const sleepIcon = tab.element.querySelector('.sleep-icon')
      if (sleepIcon) {
        sleepIcon.remove()
      }
    }
  }


}

// 初始化浏览器UI
document.addEventListener('DOMContentLoaded', () => {
  new BrowserUI()
})
