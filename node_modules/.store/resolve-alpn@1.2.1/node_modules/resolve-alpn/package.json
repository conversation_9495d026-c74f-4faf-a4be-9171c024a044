{"name": "resolve-alpn", "version": "1.2.1", "description": "Detects the ALPN protocol", "main": "index.js", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "keywords": ["alpn", "tls", "socket", "http2"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "pem": "1.14.3", "xo": "^0.38.2"}, "__npminstall_done": true, "_from": "resolve-alpn@1.2.1", "_resolved": "https://registry.npmmirror.com/resolve-alpn/-/resolve-alpn-1.2.1.tgz"}