{"electron": ["34.5.8"], "extract-zip": ["2.0.1"], "get-stream": ["5.2.0"], "yauzl": ["2.10.0"], "@types/yauzl": ["2.10.3"], "debug": ["4.4.1"], "@electron/get": ["2.0.3"], "buffer-crc32": ["0.2.13"], "ms": ["2.1.3"], "fd-slicer": ["1.1.0"], "pump": ["3.0.3"], "env-paths": ["2.2.1"], "progress": ["2.0.3"], "semver": ["6.3.1", "7.7.2"], "sumchecker": ["3.0.1"], "pend": ["1.2.0"], "once": ["1.4.0"], "fs-extra": ["8.1.0"], "end-of-stream": ["1.4.5"], "got": ["11.8.6"], "global-agent": ["3.0.0"], "wrappy": ["1.0.2"], "universalify": ["0.1.2"], "jsonfile": ["4.0.0"], "graceful-fs": ["4.2.11"], "lowercase-keys": ["2.0.0"], "decompress-response": ["6.0.0"], "cacheable-lookup": ["5.0.4"], "p-cancelable": ["2.1.1"], "cacheable-request": ["7.0.4"], "@szmarczak/http-timer": ["4.0.6"], "responselike": ["2.0.1"], "@types/responselike": ["1.0.3"], "@types/cacheable-request": ["6.0.3"], "http2-wrapper": ["1.0.3"], "@sindresorhus/is": ["4.6.0"], "serialize-error": ["7.0.1"], "matcher": ["3.0.0"], "es6-error": ["4.1.1"], "boolean": ["3.2.0"], "roarr": ["2.15.4"], "mimic-response": ["3.1.0", "1.0.1"], "clone-response": ["1.0.3"], "defer-to-connect": ["2.0.1"], "normalize-url": ["6.1.0"], "http-cache-semantics": ["4.2.0"], "keyv": ["4.5.4"], "@types/keyv": ["3.1.4"], "@types/node": ["20.19.8"], "@types/http-cache-semantics": ["4.0.4"], "resolve-alpn": ["1.2.1"], "quick-lru": ["5.1.1"], "escape-string-regexp": ["4.0.0"], "detect-node": ["2.1.0"], "type-fest": ["0.13.1"], "json-stringify-safe": ["5.0.1"], "semver-compare": ["1.0.0"], "sprintf-js": ["1.1.3"], "globalthis": ["1.0.4"], "json-buffer": ["3.0.1"], "undici-types": ["6.21.0"], "gopd": ["1.2.0"], "define-properties": ["1.2.1"], "has-property-descriptors": ["1.0.2"], "object-keys": ["1.1.1"], "define-data-property": ["1.1.4"], "es-define-property": ["1.0.1"], "es-errors": ["1.3.0"]}