# 📁 本地文件加载功能说明

## 🎯 问题解决

### 修复前的问题
- ❌ 在地址栏输入本地文件路径无法正确加载
- ❌ 包含中文字符的路径处理错误
- ❌ file://协议被错误地添加https://前缀
- ❌ URL编码路径无法正确识别

### 修复后的功能
- ✅ 支持直接输入file://协议URL
- ✅ 自动识别和处理本地文件路径
- ✅ 正确处理URL编码的中文路径
- ✅ 兼容Windows、macOS、Linux路径格式
- ✅ 保持拖拽文件功能正常工作

## 🚀 使用方法

### 1. 直接输入file://协议URL
```
file:///Volumes/HIKVISION/传文件/taobao/汙/卡片/卡片排版.html
file:///Users/<USER>/Documents/test.html
file:///C:/Users/<USER>/Documents/test.html
```

### 2. 输入绝对路径（自动转换）
```
/Volumes/HIKVISION/传文件/taobao/汙/卡片/卡片排版.html
/Users/<USER>/Documents/test.html
C:\Users\<USER>\Documents\test.html
```

### 3. 输入URL编码路径
```
file:///Volumes/HIKVISION/%E4%BC%A0%E6%96%87%E4%BB%B6/taobao/%E6%B1%A1/%E5%8D%A1%E7%89%87/%E5%8D%A1%E7%89%87%E6%8E%92%E7%89%88.html
```

### 4. 拖拽文件到浏览器
- 直接将HTML文件拖拽到浏览器窗口
- 系统会自动在新标签页中打开文件

## 🔧 技术实现

### 前端处理（renderer.js）
```javascript
// 检查是否为本地文件路径
isLocalFilePath(url) {
  // 检查file://协议
  if (url.startsWith('file://')) return true
  
  // 检查绝对路径
  if (url.match(/^[A-Za-z]:\\/) || url.startsWith('/')) return true
  
  // 检查URL编码路径
  if (url.includes('%') && (url.includes('/') || url.includes('\\'))) {
    // 尝试解码并验证
  }
  
  return false
}

// 规范化本地文件URL
normalizeLocalFileUrl(url) {
  if (url.startsWith('file://')) return url
  
  // Windows路径: C:\ -> file:///C:/
  if (url.match(/^[A-Za-z]:\\/)) {
    return 'file:///' + url.replace(/\\/g, '/')
  }
  
  // Unix路径: / -> file://
  if (url.startsWith('/')) {
    return 'file://' + url
  }
  
  return url
}
```

### 后端处理（main.js）
```javascript
// 修复navigate方法，支持file://协议
navigateActiveTab(url) {
  if (!url.includes('.') && !url.startsWith('http') && !url.startsWith('file://')) {
    // 搜索处理
  } else if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('file://')) {
    url = 'https://' + url
  }
  
  tab.view.webContents.loadURL(url)
}
```

## 📋 支持的路径格式

### macOS/Linux路径
```bash
# 绝对路径
/Users/<USER>/Documents/file.html
/Volumes/ExternalDrive/folder/file.html

# file://协议
file:///Users/<USER>/Documents/file.html
file:///Volumes/ExternalDrive/folder/file.html

# URL编码路径（中文）
file:///Users/<USER>/文档/文件.html
file:///Users/<USER>/%E6%96%87%E6%A1%A3/%E6%96%87%E4%BB%B6.html
```

### Windows路径
```cmd
# 绝对路径
C:\Users\<USER>\Documents\file.html
D:\Projects\website\index.html

# file://协议
file:///C:/Users/<USER>/Documents/file.html
file:///D:/Projects/website/index.html

# URL编码路径（中文）
file:///C:/Users/<USER>/文档/文件.html
file:///C:/Users/<USER>/%E6%96%87%E6%A1%A3/%E6%96%87%E4%BB%B6.html
```

## 🧪 测试方法

### 1. 使用测试文件
项目中包含了一个测试文件：`test-local-file.html`

在地址栏输入以下路径进行测试：
```
file:///完整路径/test-local-file.html
```

### 2. 测试您的文件
将您的HTML文件路径按以下格式输入：

**原始路径：**
```
/Volumes/HIKVISION/传文件/taobao/汙/卡片/卡片排版.html
```

**输入格式：**
```
file:///Volumes/HIKVISION/传文件/taobao/汙/卡片/卡片排版.html
```

或者直接输入绝对路径，系统会自动转换：
```
/Volumes/HIKVISION/传文件/taobao/汙/卡片/卡片排版.html
```

### 3. 验证功能
- ✅ 文件能够正常加载
- ✅ 中文路径显示正确
- ✅ 页面内容完整显示
- ✅ 相对资源（CSS、JS、图片）正常加载

## ⚠️ 注意事项

### 1. 文件权限
确保Electron应用有权限访问目标文件和目录。

### 2. 相对路径资源
本地HTML文件中的相对路径资源（CSS、JS、图片）需要确保路径正确。

### 3. 安全限制
某些系统可能对本地文件访问有安全限制，请确保文件路径可访问。

### 4. 路径编码
包含特殊字符的路径建议使用URL编码格式。

## 🔍 故障排除

### 问题1：文件无法加载
**可能原因：**
- 文件路径不正确
- 文件不存在
- 权限不足

**解决方案：**
- 检查文件路径是否正确
- 确认文件存在
- 检查文件权限

### 问题2：中文路径显示乱码
**可能原因：**
- URL编码问题
- 字符集设置问题

**解决方案：**
- 使用正确的URL编码
- 确保HTML文件使用UTF-8编码

### 问题3：相对资源无法加载
**可能原因：**
- 相对路径错误
- 资源文件不存在

**解决方案：**
- 检查相对路径是否正确
- 确认资源文件存在
- 使用绝对路径

## 🎉 总结

现在您可以：
1. 直接在地址栏输入本地文件路径
2. 支持包含中文字符的路径
3. 自动处理不同操作系统的路径格式
4. 继续使用拖拽文件功能

享受更便捷的本地文件浏览体验！
