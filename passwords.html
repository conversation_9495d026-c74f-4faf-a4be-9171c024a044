<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>密码管理器</title>
  <style>
    * {
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .container {
      width: 95%;
      max-width: 1200px;
      height: 90vh;
      background: white;
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
    }
    
    .header-icon {
      font-size: 32px;
    }
    
    .header-close-btn {
      background: rgba(255,255,255,0.2);
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      padding: 10px;
      border-radius: 8px;
      transition: all 0.3s;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .header-close-btn:hover {
      background: rgba(255,255,255,0.3);
      transform: scale(1.05);
    }
    
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .toolbar {
      padding: 20px 30px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      gap: 20px;
      align-items: center;
      flex-wrap: wrap;
    }
    
    .search-container {
      flex: 1;
      min-width: 300px;
      position: relative;
    }
    
    .search-input {
      width: 100%;
      padding: 12px 16px 12px 45px;
      border: 2px solid #e9ecef;
      border-radius: 25px;
      font-size: 16px;
      outline: none;
      transition: all 0.3s;
      background: white;
    }
    
    .search-input:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
    }
    
    .search-icon {
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
      font-size: 18px;
    }
    
    .toolbar-actions {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(102,126,234,0.3);
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #5a6268;
      transform: translateY(-2px);
    }
    
    .content {
      flex: 1;
      padding: 30px;
      overflow-y: auto;
      background: #f8f9fa;
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #6c757d;
    }
    
    .empty-state-icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.5;
    }
    
    .empty-state h3 {
      margin: 0 0 10px 0;
      font-size: 24px;
      color: #495057;
    }
    
    .empty-state p {
      margin: 0;
      font-size: 16px;
    }
    
    .password-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
    }
    
    .password-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: all 0.3s;
      border: 1px solid #e9ecef;
    }
    
    .password-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .password-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;
    }
    
    .password-title {
      font-size: 18px;
      font-weight: 600;
      color: #212529;
      margin: 0;
    }
    
    .password-actions {
      display: flex;
      gap: 5px;
    }
    
    .action-btn {
      background: none;
      border: none;
      padding: 5px;
      border-radius: 4px;
      cursor: pointer;
      color: #6c757d;
      transition: all 0.3s;
      font-size: 16px;
    }
    
    .action-btn:hover {
      background: #f8f9fa;
      color: #495057;
    }
    
    .password-url {
      color: #6c757d;
      font-size: 14px;
      margin-bottom: 15px;
      word-break: break-all;
    }
    
    .password-info {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    
    .info-row {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .info-label {
      font-weight: 500;
      color: #495057;
      min-width: 60px;
      font-size: 14px;
    }
    
    .info-value {
      flex: 1;
      padding: 8px 12px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      font-family: monospace;
      font-size: 14px;
      color: #495057;
    }
    
    .copy-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 6px 10px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s;
    }
    
    .copy-btn:hover {
      background: #0056b3;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="header-left">
        <div class="header-icon">🔐</div>
        <h1>密码管理器</h1>
      </div>
      <button class="header-close-btn" onclick="closeWindow()">×</button>
    </div>
    
    <div class="main-content">
      <div class="toolbar">
        <div class="search-container">
          <div class="search-icon">🔍</div>
          <input type="text" class="search-input" id="search-input" placeholder="搜索网站、用户名或备注..." onkeyup="filterPasswords()">
        </div>
        <div class="toolbar-actions">
          <button class="btn btn-primary" onclick="showAddPasswordModal()">
            ➕ 添加密码
          </button>
          <button class="btn btn-secondary" onclick="exportPasswords()">
            📤 导出
          </button>
          <button class="btn btn-secondary" onclick="importPasswords()">
            📥 导入
          </button>
        </div>
      </div>
      
      <div class="content">
        <div id="passwords-list">
          <!-- 密码列表将在这里动态生成 -->
        </div>
        
        <div id="empty-state" class="empty-state" style="display: none;">
          <div class="empty-state-icon">🔐</div>
          <h3>还没有保存的密码</h3>
          <p>点击"添加密码"按钮开始管理您的密码</p>
        </div>
      </div>
    </div>
  </div>
  
  <input type="file" id="import-file" style="display: none;" accept=".json" onchange="handleImportFile(event)">

  <!-- 添加/编辑密码模态框 -->
  <div id="password-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: 12px; padding: 30px; width: 90%; max-width: 500px; max-height: 80vh; overflow-y: auto;">
      <h2 id="modal-title" style="margin: 0 0 20px 0; color: #212529;">添加新密码</h2>

      <form id="password-form">
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #495057;">网站名称 *</label>
          <input type="text" id="password-title" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; outline: none; transition: border-color 0.3s;">
        </div>

        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #495057;">网站URL</label>
          <input type="url" id="password-url" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; outline: none; transition: border-color 0.3s;">
        </div>

        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #495057;">用户名 *</label>
          <input type="text" id="password-username" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; outline: none; transition: border-color 0.3s;">
        </div>

        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #495057;">密码 *</label>
          <div style="position: relative;">
            <input type="password" id="password-password" required style="width: 100%; padding: 12px 50px 12px 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; outline: none; transition: border-color 0.3s;">
            <button type="button" onclick="togglePasswordVisibility()" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; font-size: 18px;">👁️</button>
          </div>
          <button type="button" onclick="generatePassword()" style="margin-top: 10px; background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">生成强密码</button>
        </div>

        <div style="margin-bottom: 30px;">
          <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #495057;">备注</label>
          <textarea id="password-notes" rows="3" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 16px; outline: none; transition: border-color 0.3s; resize: vertical;"></textarea>
        </div>

        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="button" onclick="hidePasswordModal()" style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">取消</button>
          <button type="submit" style="padding: 12px 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">保存</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    let passwords = []
    let editingPasswordId = null

    // 初始化
    document.addEventListener('DOMContentLoaded', async () => {
      await loadPasswords()
      renderPasswordsList()
    })

    // 关闭窗口
    function closeWindow() {
      window.close()
    }

    // 加载密码
    async function loadPasswords() {
      try {
        passwords = await window.electronAPI.getPasswords()
      } catch (error) {
        console.error('Failed to load passwords:', error)
        passwords = []
      }
    }

    // 保存密码
    async function savePasswords() {
      try {
        await window.electronAPI.savePasswords(passwords)
      } catch (error) {
        console.error('Failed to save passwords:', error)
      }
    }

    // 渲染密码列表
    function renderPasswordsList() {
      const container = document.getElementById('passwords-list')
      const emptyState = document.getElementById('empty-state')

      if (passwords.length === 0) {
        container.innerHTML = ''
        emptyState.style.display = 'block'
        return
      }

      emptyState.style.display = 'none'
      container.innerHTML = '<div class="password-grid"></div>'
      const grid = container.querySelector('.password-grid')

      passwords.forEach(password => {
        const card = createPasswordCard(password)
        grid.appendChild(card)
      })
    }

    // 创建密码卡片
    function createPasswordCard(password) {
      const card = document.createElement('div')
      card.className = 'password-card'
      card.innerHTML = `
        <div class="password-header">
          <h3 class="password-title">${escapeHtml(password.title)}</h3>
          <div class="password-actions">
            <button class="action-btn" onclick="editPassword('${password.id}')" title="编辑">✏️</button>
            <button class="action-btn" onclick="deletePassword('${password.id}')" title="删除">🗑️</button>
          </div>
        </div>
        ${password.url ? `<div class="password-url">${escapeHtml(password.url)}</div>` : ''}
        <div class="password-info">
          <div class="info-row">
            <span class="info-label">用户名:</span>
            <span class="info-value">${escapeHtml(password.username)}</span>
            <button class="copy-btn" onclick="copyToClipboard('${escapeHtml(password.username)}')">复制</button>
          </div>
          <div class="info-row">
            <span class="info-label">密码:</span>
            <span class="info-value">••••••••</span>
            <button class="copy-btn" onclick="copyToClipboard('${escapeHtml(password.password)}')">复制</button>
          </div>
          ${password.notes ? `<div class="info-row"><span class="info-label">备注:</span><span class="info-value">${escapeHtml(password.notes)}</span></div>` : ''}
        </div>
      `
      return card
    }

    // HTML转义
    function escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    }

    // 复制到剪贴板
    async function copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        // 可以添加一个简单的提示
        console.log('已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
      }
    }

    // 显示添加密码模态框
    function showAddPasswordModal() {
      editingPasswordId = null
      document.getElementById('modal-title').textContent = '添加新密码'
      document.getElementById('password-form').reset()
      document.getElementById('password-modal').style.display = 'flex'
    }

    // 隐藏密码模态框
    function hidePasswordModal() {
      document.getElementById('password-modal').style.display = 'none'
      editingPasswordId = null
    }

    // 编辑密码
    function editPassword(passwordId) {
      const password = passwords.find(p => p.id === passwordId)
      if (!password) return

      editingPasswordId = passwordId
      document.getElementById('modal-title').textContent = '编辑密码'
      document.getElementById('password-title').value = password.title
      document.getElementById('password-url').value = password.url || ''
      document.getElementById('password-username').value = password.username
      document.getElementById('password-password').value = password.password
      document.getElementById('password-notes').value = password.notes || ''
      document.getElementById('password-modal').style.display = 'flex'
    }

    // 删除密码
    function deletePassword(passwordId) {
      if (confirm('确定要删除这个密码吗？')) {
        passwords = passwords.filter(p => p.id !== passwordId)
        savePasswords()
        renderPasswordsList()
      }
    }

    // 切换密码可见性
    function togglePasswordVisibility() {
      const passwordInput = document.getElementById('password-password')
      const button = passwordInput.nextElementSibling

      if (passwordInput.type === 'password') {
        passwordInput.type = 'text'
        button.textContent = '🙈'
      } else {
        passwordInput.type = 'password'
        button.textContent = '👁️'
      }
    }

    // 生成强密码
    function generatePassword() {
      const length = 16
      const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
      let password = ''

      for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length))
      }

      document.getElementById('password-password').value = password
    }

    // 过滤密码
    function filterPasswords() {
      const searchTerm = document.getElementById('search-input').value.toLowerCase()
      const cards = document.querySelectorAll('.password-card')

      cards.forEach(card => {
        const title = card.querySelector('.password-title').textContent.toLowerCase()
        const url = card.querySelector('.password-url')?.textContent.toLowerCase() || ''
        const username = card.querySelector('.info-value').textContent.toLowerCase()

        if (title.includes(searchTerm) || url.includes(searchTerm) || username.includes(searchTerm)) {
          card.style.display = 'block'
        } else {
          card.style.display = 'none'
        }
      })
    }

    // 表单提交
    document.getElementById('password-form').addEventListener('submit', async (e) => {
      e.preventDefault()

      const title = document.getElementById('password-title').value.trim()
      const url = document.getElementById('password-url').value.trim()
      const username = document.getElementById('password-username').value.trim()
      const password = document.getElementById('password-password').value
      const notes = document.getElementById('password-notes').value.trim()

      if (!title || !username || !password) {
        alert('请填写必填字段')
        return
      }

      const passwordData = {
        id: editingPasswordId || Date.now().toString() + Math.random().toString(36).substr(2, 9),
        title,
        url,
        username,
        password,
        notes,
        createdAt: editingPasswordId ? passwords.find(p => p.id === editingPasswordId).createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      if (editingPasswordId) {
        const index = passwords.findIndex(p => p.id === editingPasswordId)
        passwords[index] = passwordData
      } else {
        passwords.push(passwordData)
      }

      await savePasswords()
      renderPasswordsList()
      hidePasswordModal()
    })

    // 导出密码
    function exportPasswords() {
      if (passwords.length === 0) {
        alert('没有密码可以导出')
        return
      }

      const dataStr = JSON.stringify(passwords, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)

      const link = document.createElement('a')
      link.href = url
      link.download = `passwords_${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }

    // 导入密码
    function importPasswords() {
      document.getElementById('import-file').click()
    }

    // 处理导入文件
    function handleImportFile(event) {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = async function(e) {
        try {
          const importedPasswords = JSON.parse(e.target.result)

          if (!Array.isArray(importedPasswords)) {
            alert('导入文件格式错误')
            return
          }

          const validPasswords = importedPasswords.filter(password =>
            password.id && password.title && password.username && password.password
          )

          if (validPasswords.length === 0) {
            alert('导入文件中没有有效的密码记录')
            return
          }

          const shouldReplace = confirm(`将导入 ${validPasswords.length} 个密码记录。是否覆盖现有密码？\n点击"确定"覆盖，点击"取消"追加到现有密码。`)

          if (shouldReplace) {
            passwords = validPasswords
          } else {
            validPasswords.forEach(password => {
              password.id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
              passwords.push(password)
            })
          }

          await savePasswords()
          renderPasswordsList()
          alert(`成功导入 ${validPasswords.length} 个密码记录`)
        } catch (error) {
          console.error('Import error:', error)
          alert('导入失败：文件格式错误')
        }
      }
      reader.readAsText(file)

      event.target.value = ''
    }
  </script>
</body>
</html>
