<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>密码管理器</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .container {
      max-width: 900px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    .header {
      background: #007acc;
      color: white;
      padding: 20px;
      text-align: center;
      position: relative;
    }
    
    .header h1 {
      margin: 0;
      font-size: 24px;
    }
    
    .header-close-btn {
      position: absolute;
      top: 15px;
      right: 20px;
      background: none;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;
      transition: background-color 0.3s;
    }
    
    .header-close-btn:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
    
    .content {
      padding: 20px;
    }
    
    .password-item {
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 15px;
      padding: 15px;
      background: #fafafa;
    }
    
    .password-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .password-title {
      font-weight: bold;
      font-size: 16px;
      color: #333;
    }
    
    .password-url {
      color: #666;
      font-size: 14px;
      margin-bottom: 10px;
    }
    
    .password-fields {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      margin-bottom: 10px;
    }
    
    .password-field {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .password-field label {
      font-weight: bold;
      min-width: 60px;
    }
    
    .password-field input {
      flex: 1;
      padding: 6px 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .password-actions {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .btn-primary {
      background: #007acc;
      color: white;
    }
    
    .btn-secondary {
      background: #e0e0e0;
      color: #333;
    }
    
    .btn-danger {
      background: #dc3545;
      color: white;
    }
    
    .btn-success {
      background: #28a745;
      color: white;
    }
    
    .btn:hover {
      opacity: 0.8;
    }
    
    .add-password {
      border: 2px dashed #ddd;
      border-radius: 4px;
      padding: 20px;
      text-align: center;
      margin-bottom: 20px;
      cursor: pointer;
      transition: border-color 0.3s;
    }
    
    .add-password:hover {
      border-color: #007acc;
    }

    .actions-bar {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      align-items: flex-start;
    }

    .add-password {
      flex: 1;
    }

    .import-export-buttons {
      display: flex;
      flex-direction: column;
      gap: 10px;
      min-width: 150px;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    .form-group input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 1000;
    }
    
    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 8px;
      padding: 20px;
      width: 90%;
      max-width: 500px;
      max-height: 80%;
      overflow-y: auto;
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .modal-title {
      font-size: 18px;
      font-weight: bold;
    }
    
    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }
    
    .close-btn:hover {
      color: #333;
    }
    
    .search-box {
      margin-bottom: 20px;
    }
    
    .search-box input {
      width: 100%;
      padding: 10px 15px;
      border: 1px solid #ddd;
      border-radius: 25px;
      font-size: 16px;
      box-sizing: border-box;
    }
    
    .empty-state {
      text-align: center;
      color: #666;
      padding: 40px 20px;
    }
    
    .copy-btn {
      background: #28a745;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .copy-btn:hover {
      background: #218838;
    }
    
    .password-hidden {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      padding: 6px 10px;
      border-radius: 4px;
      font-family: monospace;
      letter-spacing: 2px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>密码管理器</h1>
      <button class="header-close-btn" onclick="closeWindow()" title="关闭窗口">&times;</button>
    </div>
    
    <div class="content">
      <div class="search-box">
        <input type="text" id="search-input" placeholder="搜索网站或用户名..." onkeyup="filterPasswords()">
      </div>

      <div class="actions-bar">
        <div class="add-password" onclick="showAddPasswordModal()">
          <h3>+ 添加新密码</h3>
          <p>点击这里添加新的密码记录</p>
        </div>

        <div class="import-export-buttons">
          <button class="btn btn-secondary" onclick="exportPasswords()">导出所有密码</button>
          <button class="btn btn-secondary" onclick="importPasswords()">导入密码</button>
          <input type="file" id="import-file" style="display: none;" accept=".json" onchange="handleImportFile(event)">
        </div>
      </div>

      <div id="passwords-list">
        <!-- 密码列表将在这里动态生成 -->
      </div>
    </div>
  </div>
  
  <!-- 添加/编辑密码模态框 -->
  <div id="password-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title" id="modal-title">添加密码</div>
        <button class="close-btn" onclick="hidePasswordModal()">&times;</button>
      </div>
      
      <form id="password-form">
        <div class="form-group">
          <label for="password-title">网站名称:</label>
          <input type="text" id="password-title" required>
        </div>
        
        <div class="form-group">
          <label for="password-url">网站地址:</label>
          <input type="url" id="password-url" placeholder="https://example.com">
        </div>
        
        <div class="form-group">
          <label for="password-username">用户名/邮箱:</label>
          <input type="text" id="password-username" required>
        </div>
        
        <div class="form-group">
          <label for="password-password">密码:</label>
          <input type="password" id="password-password" required>
        </div>
        
        <div class="form-group">
          <label for="password-notes">备注:</label>
          <input type="text" id="password-notes" placeholder="可选备注信息">
        </div>
        
        <div class="password-actions">
          <button type="submit" class="btn btn-primary">保存</button>
          <button type="button" class="btn btn-secondary" onclick="hidePasswordModal()">取消</button>
        </div>
      </form>
    </div>
  </div>
  
  <script>
    let passwords = []
    let editingPasswordId = null
    
    // 初始化
    document.addEventListener('DOMContentLoaded', async () => {
      await loadPasswords()
      renderPasswordsList()
    })
    
    // 关闭窗口
    function closeWindow() {
      window.close()
    }
    
    // 加载密码
    async function loadPasswords() {
      try {
        passwords = await window.electronAPI.getPasswords()
      } catch (error) {
        console.error('Failed to load passwords:', error)
        passwords = []
      }
    }
    
    // 渲染密码列表
    function renderPasswordsList() {
      const container = document.getElementById('passwords-list')
      container.innerHTML = ''
      
      const filteredPasswords = getFilteredPasswords()
      
      if (filteredPasswords.length === 0) {
        container.innerHTML = '<div class="empty-state">暂无密码记录</div>'
        return
      }
      
      filteredPasswords.forEach(password => {
        const passwordElement = createPasswordElement(password)
        container.appendChild(passwordElement)
      })
    }
    
    // 获取过滤后的密码列表
    function getFilteredPasswords() {
      const searchTerm = document.getElementById('search-input').value.toLowerCase()
      if (!searchTerm) return passwords
      
      return passwords.filter(password => 
        password.title.toLowerCase().includes(searchTerm) ||
        password.username.toLowerCase().includes(searchTerm) ||
        (password.url && password.url.toLowerCase().includes(searchTerm))
      )
    }
    
    // 过滤密码
    function filterPasswords() {
      renderPasswordsList()
    }

    // 创建密码元素
    function createPasswordElement(password) {
      const div = document.createElement('div')
      div.className = 'password-item'
      div.innerHTML = `
        <div class="password-header">
          <div class="password-title">${password.title}</div>
        </div>
        <div class="password-url">${password.url || '无网址'}</div>
        <div class="password-fields">
          <div class="password-field">
            <label>用户名:</label>
            <input type="text" value="${password.username}" readonly>
            <button class="copy-btn" onclick="copyToClipboard('${password.username}')">复制</button>
          </div>
          <div class="password-field">
            <label>密码:</label>
            <div class="password-hidden" onclick="togglePassword(this, '${password.password}')">••••••••</div>
            <button class="copy-btn" onclick="copyToClipboard('${password.password}')">复制</button>
          </div>
        </div>
        ${password.notes ? `<div style="margin-bottom: 10px; color: #666;">备注: ${password.notes}</div>` : ''}
        <div class="password-actions">
          <button class="btn btn-primary" onclick="editPassword('${password.id}')">编辑</button>
          <button class="btn btn-danger" onclick="deletePassword('${password.id}')">删除</button>
        </div>
      `
      return div
    }

    // 显示/隐藏密码
    function togglePassword(element, password) {
      if (element.textContent === '••••••••') {
        element.textContent = password
        element.style.fontFamily = 'monospace'
      } else {
        element.textContent = '••••••••'
        element.style.fontFamily = 'inherit'
      }
    }

    // 复制到剪贴板
    async function copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        // 可以添加一个临时提示
        console.log('已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
      }
    }

    // 显示添加密码模态框
    function showAddPasswordModal() {
      editingPasswordId = null
      document.getElementById('modal-title').textContent = '添加密码'
      document.getElementById('password-form').reset()
      document.getElementById('password-modal').style.display = 'block'
    }

    // 隐藏密码模态框
    function hidePasswordModal() {
      document.getElementById('password-modal').style.display = 'none'
    }

    // 编辑密码
    function editPassword(passwordId) {
      const password = passwords.find(p => p.id === passwordId)
      if (!password) return

      editingPasswordId = passwordId
      document.getElementById('modal-title').textContent = '编辑密码'
      document.getElementById('password-title').value = password.title
      document.getElementById('password-url').value = password.url || ''
      document.getElementById('password-username').value = password.username
      document.getElementById('password-password').value = password.password
      document.getElementById('password-notes').value = password.notes || ''
      document.getElementById('password-modal').style.display = 'block'
    }

    // 删除密码
    async function deletePassword(passwordId) {
      if (!confirm('确定要删除这个密码记录吗？')) return

      passwords = passwords.filter(p => p.id !== passwordId)
      await savePasswords()
      renderPasswordsList()
    }

    // 保存密码
    async function savePasswords() {
      try {
        await window.electronAPI.savePasswords(passwords)
      } catch (error) {
        console.error('Failed to save passwords:', error)
        alert('保存失败: ' + error.message)
      }
    }

    // 表单提交处理
    document.getElementById('password-form').addEventListener('submit', async (e) => {
      e.preventDefault()

      const title = document.getElementById('password-title').value.trim()
      const url = document.getElementById('password-url').value.trim()
      const username = document.getElementById('password-username').value.trim()
      const password = document.getElementById('password-password').value.trim()
      const notes = document.getElementById('password-notes').value.trim()

      if (!title || !username || !password) {
        alert('请填写必填字段')
        return
      }

      const passwordRecord = {
        id: editingPasswordId || Date.now().toString(),
        title,
        url,
        username,
        password,
        notes,
        createdAt: editingPasswordId ? passwords.find(p => p.id === editingPasswordId).createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      if (editingPasswordId) {
        const index = passwords.findIndex(p => p.id === editingPasswordId)
        if (index !== -1) {
          passwords[index] = passwordRecord
        }
      } else {
        passwords.push(passwordRecord)
      }

      await savePasswords()
      renderPasswordsList()
      hidePasswordModal()
    })

    // 导出密码
    function exportPasswords() {
      if (passwords.length === 0) {
        alert('没有密码可以导出')
        return
      }

      const dataStr = JSON.stringify(passwords, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)

      const link = document.createElement('a')
      link.href = url
      link.download = `passwords_${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }

    // 导入密码
    function importPasswords() {
      document.getElementById('import-file').click()
    }

    // 处理导入文件
    function handleImportFile(event) {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = async function(e) {
        try {
          const importedPasswords = JSON.parse(e.target.result)

          if (!Array.isArray(importedPasswords)) {
            alert('导入文件格式错误')
            return
          }

          // 验证密码格式
          const validPasswords = importedPasswords.filter(password =>
            password.id && password.title && password.username && password.password
          )

          if (validPasswords.length === 0) {
            alert('导入文件中没有有效的密码记录')
            return
          }

          // 询问是否覆盖现有密码
          const shouldReplace = confirm(`将导入 ${validPasswords.length} 个密码记录。是否覆盖现有密码？\n点击"确定"覆盖，点击"取消"追加到现有密码。`)

          if (shouldReplace) {
            passwords = validPasswords
          } else {
            // 追加密码，避免ID冲突
            validPasswords.forEach(password => {
              password.id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
              passwords.push(password)
            })
          }

          await savePasswords()
          renderPasswordsList()
          alert(`成功导入 ${validPasswords.length} 个密码记录`)
        } catch (error) {
          console.error('Import error:', error)
          alert('导入失败：文件格式错误')
        }
      }
      reader.readAsText(file)

      // 清空文件输入
      event.target.value = ''
    }
  </script>
</body>
</html>
