#!/bin/bash
# 通用启动脚本 - 适用于Linux/Unix系统

# 设置脚本在出错时退出
set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Electron浏览器启动器 ===${NC}"
echo -e "${BLUE}当前目录: $SCRIPT_DIR${NC}"

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误: 未检测到Node.js，请先安装Node.js${NC}"
    echo -e "${YELLOW}📥 下载地址: https://nodejs.org/${NC}"
    echo -e "${YELLOW}💡 Ubuntu/Debian: sudo apt install nodejs npm${NC}"
    echo -e "${YELLOW}💡 CentOS/RHEL: sudo yum install nodejs npm${NC}"
    echo -e "${YELLOW}💡 Arch Linux: sudo pacman -S nodejs npm${NC}"
    read -p "按回车键退出..."
    exit 1
fi

# 检查npm是否可用
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ 错误: npm不可用，请检查Node.js安装${NC}"
    read -p "按回车键退出..."
    exit 1
fi

echo -e "${GREEN}✅ Node.js版本: $(node --version)${NC}"
echo -e "${GREEN}✅ npm版本: $(npm --version)${NC}"

# 检查package.json是否存在
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ 错误: 未找到package.json文件，请确保在正确的目录中运行${NC}"
    read -p "按回车键退出..."
    exit 1
fi

# 检查node_modules是否存在，如果不存在则安装依赖
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 正在安装依赖包...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 错误: 依赖包安装失败${NC}"
        read -p "按回车键退出..."
        exit 1
    fi
    echo -e "${GREEN}✅ 依赖包安装完成${NC}"
fi

# 启动应用
echo -e "${GREEN}🚀 正在启动Electron浏览器...${NC}"
npm start

# 如果应用异常退出，显示错误信息
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 应用启动失败，错误代码: $?${NC}"
    read -p "按回车键退出..."
fi
