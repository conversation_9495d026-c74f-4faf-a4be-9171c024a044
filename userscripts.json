[{"id": "1753057587337", "name": "修改背景色", "match": "*://*.so.com/*", "code": "body{background:red}", "enabled": true}, {"id": "1753064539861", "name": "手机壳", "match": "*://*.haixingdiy.cn/*", "code": "// Your code here...\nvar dd = 'tbody > tr.order-table-row.ant-table-row.ng-star-inserted > td:nth-child(5) > div > app-input-address > div.edit > span:nth-child(2)'\n        //'table > tbody > tr > td:nth-child(4) > app-input-address > span > span > img'\nvar ming = 'table > tbody > tr.order-table-tr.ant-table-row.ng-star-inserted > td:nth-child(2) > div > div > div.v-goods-item-goodsname > span:nth-child(2)'\n//'table > tbody > tr > td:nth-child(2) > div > div> div.gig-vertical.v-goods-item-goodsname > span:nth-child(2)'\nvar im = 'table > tbody > tr > td:nth-child(2) > div > div > div:nth-child(1) > div > img'\nvar v0 = 'ant-tabs-tab-active'\nvar inp = '  td  input[class*=\"checkbox\"]'\nvar kd = 'body > app-root > app-dt > app-layout > nz-layout > nz-content > nz-spin > div > app-works-manage > div > div > nz-layout > nz-content.cont.ant-layout-content > nz-affix > div > div > button:nth-child(3)'\nvar tb = 'label input[class*=\"radio\"] '\nvar 初始数量 = 0\n\nsetInterval(() => {\n    //if (document.querySelectorAll(' div.ant-tabs-bar.ant-tabs-top-bar.ant-tabs-default-bar.ng-star-inserted > div > div > div > div > div:nth-child(1) > div:nth-child(6)')[0].className.indexOf(v0) == -1) {\n        var 数量 = document.querySelectorAll(dd).length\n        初始数量 = document.getElementsByClassName('确认手机型号').length\n\n        if (数量 != 初始数量) {\n            初始数量 = 数量;\n            console.log(数量);\n            刷新()\n            //初始数量 = document.getElementsByClassName('确认手机型号').length\n        }\n    //}\n}, 1000)\nfunction 刷新() {\n    console.log('刷新');\n    for (var i = 0; i < 初始数量; i++) {\n        document.querySelectorAll(dd)[i].style.display = \"none\"\n\n        var 型号 = document.querySelectorAll(ming)[i].innerText;\n        const 确认手机型号 = document.createElement('button');\n        确认手机型号.innerText = '是【' + 型号 + '】吗？';\n        确认手机型号.className = '确认手机型号';\n        确认手机型号.dataset.id = i\n        确认手机型号.addEventListener('click', (item) => {\n            document.getElementsByClassName('不是')[item.target.dataset.id].style.display = \"none\"\n            document.getElementsByClassName('拼多多')[item.target.dataset.id].style.display = \"block\"\n            document.getElementsByClassName('淘宝')[item.target.dataset.id].style.display = \"block\"\n            document.getElementsByClassName('确认手机型号')[item.target.dataset.id].style.display = \"none\"\n        })\n\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(确认手机型号)\n        const not = document.createElement('button');\n        not.innerText = '不是';\n        not.className = '不是';\n        not.dataset.id = i\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(not)\n        not.addEventListener('click', (item) => {\n            console.log(im, item.target.dataset.id);\n            document.querySelectorAll(im)[item.target.dataset.id].click();\n        })\n        const 确认平台1 = document.createElement('button');\n        确认平台1.innerText = '拼多多';\n        确认平台1.className = '拼多多';\n        确认平台1.style.display = \"none\";\n        //确认平台1.style.display=\"none\";\n        确认平台1.dataset.id = i\n        确认平台1.addEventListener('click', (item) => {\n            document.querySelectorAll(dd)[item.target.dataset.id].style.display = \"block\";\n            document.querySelectorAll(dd)[item.target.dataset.id].click();\n            document.getElementsByClassName('淘宝')[item.target.dataset.id].style.display = \"none\"\n            document.getElementsByClassName('拼多多')[item.target.dataset.id].style.display = \"none\"\n        })\n        const 确认平台2 = document.createElement('button');\n        确认平台2.innerText = '淘宝';\n        确认平台2.className = '淘宝';\n        确认平台2.style.display = \"none\";\n        确认平台2.dataset.id = i\n        确认平台2.addEventListener('click', (item) => {\n            document.querySelectorAll(dd)[item.target.dataset.id].style.display = \"block\";\n            document.querySelectorAll(inp)[item.target.dataset.id].click();\n            setTimeout(function () {\n                document.querySelector(kd).click();\n            }\n                , 100)\n            setTimeout(function () {\n                document.querySelectorAll(tb)[3].parentNode.parentNode.style.border = \"1px solid #f00\";\n                document.querySelectorAll(tb)[3].click();\n\n            }\n                , 300)\n            document.getElementsByClassName('淘宝')[item.target.dataset.id].style.display = \"none\"\n\n        }\n        )\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(确认平台1)\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(确认平台2)\n    }\n}\nvar 新style = document.createElement('style');\n新style.innerHTML = `\n    .淘宝{background-color:#ff9000;color:#fff;border:1px solid #ff9000;border-radius:3px;padding:2px 5px;cursor:pointer;font-size:12px;}\n    .确认手机型号,.拼多多{background-color:#ff0030;color:#fff;border:1px solid #ff0030;border-radius:3px;padding:2px 5px;cursor:pointer;font-size:12px;}\n    .提醒:hover{color:white;bakckground_color:green;}\n     #floatingDiv {\n            position: absolute;\n            width: 100px;\n            height: 100px;\n            background-color: red;\n            color: white;\n            text-align: center;\n            padding: 10px;\n            font-size: 16px;\n            border-radius: 50%;\n        }\n    `\n\n/*ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(2),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(4),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(5),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(7),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(8){ top: calc(100vh - 38px);    background: #37b3b0;}\n nz-content.v-search.ant-layout-content{height:10px!important;background:#666;overflow:HIDDEN;margin-top:-30px!important;position:absolute}\n    nz-content.v-search.ant-layout-content:hover{height:unset!important;background:unset;}\n  */\ndocument.head.appendChild(新style);\n`var 提醒=document.createElement('div')\n//提醒.style.position='fixed';\n提醒.style.padding='5px';\n提醒.style.left='0px';\n提醒.style.top='70%';\n提醒.style.backgroundColor='red';\n提醒.innerHTML=\"注意是不是淘宝订单！抖音订单！\"\n提醒.style`\n\n\n  // 创建一个 div 元素\n        var floatingDiv = document.createElement('div');\n        floatingDiv.id = 'floatingDiv';\n        floatingDiv.textContent = '注意是不是淘宝订单！抖音订单！';\n\n        // 将 div 添加到文档的 body 中\n        document.body.appendChild(floatingDiv);\n\n        // 获取 div 的宽度和高度\n        var divWidth = floatingDiv.offsetWidth;\n        var divHeight = floatingDiv.offsetHeight;\n\n        // 初始化 div 的位置\n        var posX = Math.random() * (window.innerWidth - divWidth);\n        var posY = Math.random() * (window.innerHeight - divHeight);\n\n        // 初始化移动速度（随机生成）\n        var speedX = (Math.random() - 0.5) * 4; // 水平速度\n        var speedY = (Math.random() - 0.5) * 4; // 垂直速度\n\n        // 更新 div 位置的函数\n        function updatePosition() {\n            // 更新位置\n            posX += speedX;\n            posY += speedY;\n\n            // 检测是否碰到左右边界\n            if (posX <= 0 || posX >= window.innerWidth - divWidth) {\n                speedX = -speedX; // 反转水平方向\n            }\n\n            // 检测是否碰到上下边界\n            if (posY <= 0 || posY >= window.innerHeight - divHeight) {\n                speedY = -speedY; // 反转垂直方向\n            }\n\n            // 设置 div 的新位置\n            floatingDiv.style.left = posX + 'px';\n            floatingDiv.style.top = posY + 'px';\n        }\n\n        // 每 16 毫秒更新一次位置（约 60 帧/秒）\n        setInterval(updatePosition, 16);\ndocument.body.appendChild(floatingDiv);", "enabled": true}, {"id": "1753077602513", "name": "comfyui", "match": "*://192.168.1*", "code": "GM_addStyle(`\n            .prompt-manager-menu, .prompt-manager-modal, .prompt-manager-modal2 {\n                transition: background-color 0.3s ease;\n            }\n            \n        .toast-container {\n                position: fixed;\n                bottom: 40%;\n                left: 50%;\n                transform: translateX(-50%);\n                z-index: 10001;\n                pointer-events: none;\n            }\n            .toast {\n                background: rgba(0, 0, 0, 0.8);\n                color: white;\n                padding: 10px 20px;\n                border-radius: 4px;\n                margin-top: 10px;\n                animation: fadeInOut 3s ease-in-out;\n            }\n        \n        @keyframes fadeIn {\n            from { opacity: 0; transform: translateY(20px); }\n            to { opacity: 1; transform: translateY(0); }\n        }\n\n        /* 通用颜色变量 - 亮色模式 */\n        :root {\n             --pm-text-color: #ddd;\n            --pm-border-color: #555;\n            --pm-primary-color: #3a80d2;\n            --pm-primary-hover: #2a70c2;\n            --pm-danger-color: #c73c2e;\n            --pm-secondary-color: #777;\n            --pm-border-light: #444;\n        }\n        \n        /* 深色模式 */\n        .prompt-manager-dark-mode {\n            --pm-text-color: #ddd;\n            --pm-border-color: #555;\n            \n            --pm-primary-color: #3a80d2;\n            --pm-primary-hover: #2a70c2;\n            --pm-danger-color: #c73c2e;\n            --pm-secondary-color: #777;\n            --pm-border-light: #444;\n        }\n        \n        .prompt-manager-menu {\n            position: fixed;\n            background: var(--pm-bg-color);\n            border: 1px solid var(--pm-border-color);\n            border-radius: 4px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n            padding: 8px;\n            z-index: 9999;\n            min-width: 150px;\n            max-height: 400px;\n            \n            color: var(--pm-text-color);\n        }\n        \n        .prompt-manager-toolbar {\n            display: flex;\n            justify-content: space-between;\n            padding-bottom: 8px;\n            margin-bottom: 8px;\n        }\n        \n        .prompt-manager-toolbar button {\n            background: none;\n            border: none;\n             margin:0;\n            cursor: pointer;\n            font-size: 14px;\n            padding: 4px;\n            color: var(--pm-text-color);\n        }\n        \n        .prompt-manager-category {\n            padding: 0px 8px;\n            font-size: 12px;\n            cursor: pointer;\n            margin: 2px 0;\n            color: var(--pm-text-color);\n        }\n        \n        .prompt-manager-category:hover {\n            background: var(--pm-hover-color);\n            border-radius: 4px;\n        }\n        #div1{\n        position: absolute;\n        top: 0;\n        left: 0;\n        background: var(--pm-bg-color);\n        border: 1px solid #666;\n        }\n        .prompt-manager-modal {\n            padding: 10px;\n            min-height:100% ;\n            z-index: 10000;\n            min-width: 300px;\n            max-width: 520px;\n            color: var(--pm-text-color);\n            display: flex\n;\n    flex-direction: column;\n    justify-content: space-between;\n            }\n\n .prompt-manager-modal2{\n    position: fixed;\n    top: 50%;\n    left: 8px;\n    transform: translateY(-50%);\n    background: var(--pm-bg-color);\n    padding: 20px;\n    border-radius: 8px;\n    box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n    z-index: 10000;\n    min-width: 300px;\n    max-width: 520px;\n    color: var(--pm-text-color);\n}\n            \n            .settings-section {\n                padding-bottom: 5px;\n                border-bottom: 1px solid var(--pm-border-light);\n            }\n            \n            .settings-section:last-child {\n                border-bottom: none;\n                margin-bottom: 15px;\n            }\n            \n            .settings-section h4 {\n                margin: 0 0 10px 0;\n                color: var(--pm-text-color);\n            }\n            \n            \n        \n        .prompt-manager-input {\n            width: 100%;\n            padding: 8px;\n            margin: 8px 0;\n            background:#222;\n            border: 1px solid #ddd;\n            border-radius: 4px;\n            color: var(--pm-text-color);\n        }\n        \n        .prompt-manager-button {\n            padding: 8px 16px;\n            margin: 4px;\n            border: none;\n            border-radius: 4px;\n            background: var(--pm-primary-color);\n            color: white;\n            cursor: pointer;\n        }\n        \n        .prompt-manager-button:hover {\n            background: var(--pm-primary-hover);\n        }\n        \n        .prompt-manager-button.danger {\n            background: var(--pm-danger-color);\n        }\n        \n        .prompt-manager-button.secondary {\n            background: #999;\n        }\n        \n        .prompt-manager-list {\n            max-height: 300px;\n            overflow-y: auto;\n            margin: 10px 0;\n            display: flex;\n            flex-wrap: wrap;\n        }\n        \n        .prompt-manager-list-item {\n            background-color:#222;\n            margin:2px;\n            border-radius: 5px;\n            cursor: pointer;\n            color: var(--pm-text-color);\n            max-width:120px;\n            padding:1px 7px;\n        }\n        \n        .prompt-manager-list-item:hover {\n            background: var(--pm-hover-color);\n        }\n    `);\n\n    // 数据结构\n    let data = {\n        categories: [],  // [{id: string, name: string, order: number}]\n        prompts: [],    // [{id: string, text: string, alias: string, categoryId: string, favorite: boolean, useCount: number, isTemplate: boolean, lastUsed: string}]\n        lastUsedCategoryId: null,  // 记录上次使用的分组ID\n        backups: [],    // 自动备份历史 [{date: string, data: Object}]\n        settings: {\n            darkMode: false,       // 深色模式\n            modalPosition: null,   // 模态框位置\n            sortBy: 'time',       // 排序方式：time（添加时间）, name（名称）, usage（使用次数）\n            showFavoriteOnly: false, // 是否只显示收藏的提示词\n            enableContextMenu: true, // 启用右键菜单\n            autoBackup: true,      // 自动备份\n            backupInterval: 7,     // 备份间隔（天）\n            maxBackups: 5,         // 最大备份数量\n            categorySort: 'custom',  // 分类排序方式：custom（自定义）, name（名称）\n            panelOpacity: 0.9      // 面板透明度，默认90%\n        }\n    };\n\n    // 初始化数据\n    function initData() {\n        const savedData = GM_getValue('promptManagerData');\n        if (savedData) {\n            try {\n                data = JSON.parse(savedData);\n\n                // 兼容旧版本数据结构\n                if (!data.settings) {\n                    data.settings = {\n                        darkMode: false,\n                        modalPosition: null,\n                        sortBy: 'time',\n                        showFavoriteOnly: false,\n                        enableContextMenu: true,\n                        autoBackup: true,\n                        backupInterval: 7\n                    };\n                }\n\n                if (!data.backups) {\n                    data.backups = [];\n                }\n\n                // 检查是否需要创建备份\n                checkBackup();\n            } catch (e) {\n                console.error('数据解析错误', e);\n                // 尝试恢复最近的备份\n                tryRestore();\n            }\n        }\n    }\n\n    // 保存数据\n    function saveData() {\n        try {\n            GM_setValue('promptManagerData', JSON.stringify(data));\n        } catch (e) {\n            console.error('保存数据失败', e);\n            alert('保存数据失败，请检查浏览器存储空间或导出备份数据');\n        }\n    }\n\n    // 检查是否需要创建备份\n    function checkBackup() {\n        if (!data.settings.autoBackup) return;\n\n        const now = new Date();\n        const lastBackup = data.backups[0]?.date ? new Date(data.backups[0].date) : null;\n\n        // 如果没有备份或最后一次备份超过指定天数\n        if (!lastBackup || (now - lastBackup) / (1000 * 60 * 60 * 24) > data.settings.backupInterval) {\n            // 创建备份，不包含之前的备份数据\n            const backupData = JSON.parse(JSON.stringify(data));\n            delete backupData.backups;\n\n            // 添加新备份到开头\n            data.backups.unshift({\n                date: now.toISOString(),\n                data: backupData\n            });\n\n            // 保留最多5个备份\n            if (data.backups.length > 5) {\n                data.backups = data.backups.slice(0, 5);\n            }\n\n            saveData();\n        }\n    }\n\n    // 尝试从备份恢复\n    function tryRestore() {\n        const savedBackups = GM_getValue('promptManagerBackups');\n        if (savedBackups) {\n            try {\n                const backups = JSON.parse(savedBackups);\n                if (backups.length > 0) {\n                    // 使用最新的备份\n                    data = backups[0].data;\n                    alert('检测到数据损坏，已自动从最近备份恢复');\n                    return;\n                }\n            } catch (e) {\n                console.error('备份解析错误', e);\n            }\n        }\n\n        // 如果没有可用备份，重置数据\n        data = {\n            categories: [],\n            prompts: [],\n            lastUsedCategoryId: null,\n            backups: [],\n            settings: {\n                darkMode: false,\n                modalPosition: null,\n                sortBy: 'time',\n                showFavoriteOnly: false,\n                enableContextMenu: true,\n                autoBackup: true,\n                backupInterval: 7\n            }\n        };\n        alert('无法恢复数据，已重置为默认设置');\n    }\n\n    // 初始化数据\n    // = GM_getValue('promptManagerData');\n    // 显示提示信息\n    function showToast(message, duration = 3000) {\n        let container = document.querySelector('.toast-container');\n        if (!container) {\n            container = document.createElement('div');\n            container.className = 'toast-container';\n            document.body.appendChild(container);\n        }\n\n        const toast = document.createElement('div');\n        toast.className = 'toast';\n        toast.innerHTML = message;\n        container.appendChild(toast);\n\n        setTimeout(() => {\n            toast.remove();\n            if (container.children.length === 0) {\n                container.remove();\n            }\n        }, duration);\n    }\n\n    // 创建浮动菜单\n    function createFloatingMenu(x, y) {\n        GM_addStyle(`\n            \n                        .prompt-manager-menu, .prompt-manager-modal, .prompt-manager-modal2 {\n                         background-color: rgba(51, 51, 51, ${data.settings.panelOpacity});\n            \n            }\n            `)\n        const menu = document.createElement('div');\n        menu.className = 'prompt-manager-menu';\n        menu.style.left = x + 'px';\n        menu.style.top = y + 'px';\n        console.log(x);\n        const div1 = document.createElement('div');\n        div1.style.borderRadius = '8px 0 0 8px';\n        div1.style.transform = 'translateX(-100%)';\n        div1.style.borderRight = '0';\n        div1.style.minHeight = '100%';\n        menu.appendChild(div1);\n        const div2 = document.createElement('div');\n        menu.appendChild(div2);\n        div2.id = 'div2';\n        const div3 = document.createElement('div');\n        div3.style.borderRadius = '0px 8px 8px 0';\n        div3.style.borderLeft = '0';\n        menu.appendChild(div3);\n        div3.style.transform = 'translateX(148px) translateY(100px)';\n        div3.style.minHeight = 'calc(100% - 100px)';\n        if (x > 400) {\n            div1.id = 'div1';\n        } else {\n            div3.id = 'div1';\n        }\n\n        // 工具栏\n        const toolbar = document.createElement('div');\n        toolbar.className = 'prompt-manager-toolbar';\n        makeDraggable(menu);\n\n        const settingsBtn = document.createElement('button');\n        settingsBtn.innerHTML = '⚙️';\n        settingsBtn.onclick = showSettings;\n        \n\n        const 空格 = document.createElement('button');\n        空格.innerHTML = '🈳';\n        空格.className = 'prompt-space'\n        空格.onclick = 替换空格;\n        //添加鼠标浮动提示\n        空格.title = '替换空格为_';\n        空格.onmouseover = function () {\n            //提示信息\n            showToast('替换空格为_',1000);\n        }\n\n        const 浏览器 = document.createElement('button');\n        浏览器.innerHTML = '🛜';\n        浏览器.className = 'prompt-space'\n        浏览器.onclick = 显示浏览器面板;\n        //添加鼠标浮动提示\n        浏览器.title = '开启内置浏览器，需要安装hiframe插件';\n        浏览器.onmouseover = function () {\n            //提示信息\n            showToast('开启内置浏览器，需要安装hiframe插件',1000);\n        }\n        const addCategoryBtn = document.createElement('button');\n        addCategoryBtn.innerHTML = '+🗂️';\n        addCategoryBtn.onclick = () => showAddCategoryDialog();\n        addCategoryBtn.onmouseover = function () {\n            //提示信息\n            showToast('新建分组',1000);\n        }\n\n        toolbar.appendChild(settingsBtn);\n        toolbar.appendChild(空格);\n        toolbar.appendChild(浏览器);\n        toolbar.appendChild(addCategoryBtn);\n        div2.appendChild(toolbar);\n\n        // 分类列表\n        data.categories.forEach(category => {\n            const categoryDiv = document.createElement('div');\n            categoryDiv.className = 'prompt-manager-category';\n            categoryDiv.textContent = category.name;\n            categoryDiv.onclick = () => showPromptList(category.id);\n            div2.appendChild(categoryDiv);\n        });\n\n        document.body.appendChild(menu);\n        return menu;\n    }\n\n    // 显示添加分类对话框\n    function showAddCategoryDialog() {\n        const modal = createModal2()//create浮动Modal();\n        modal.innerHTML = `\n            <h3>新建分类</h3>\n            <input type=\"text\" class=\"prompt-manager-input\" placeholder=\"输入分类名称\">\n            <button class=\"prompt-manager-button\">确定</button>\n            <button class=\"prompt-manager-button secondary\">取消</button>\n        `;\n        makeDraggable(modal);\n        const input = modal.querySelector('input');\n        const [confirmBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        confirmBtn.onclick = () => {\n            if (input.value.trim()) {\n                const newCategory = {\n                    id: Date.now().toString(),\n                    name: input.value.trim()\n                };\n                data.categories.push(newCategory);\n                saveData();\n                closeModal();\n                // 刷新菜单\n                const oldMenu = document.querySelector('.prompt-manager-menu');\n                if (oldMenu) {\n                    const rect = oldMenu.getBoundingClientRect();\n                    oldMenu.remove();\n                    createFloatingMenu(rect.left, rect.top);\n                }\n            }\n        };\n\n        cancelBtn.onclick = closeModal;\n    }\n\n    // 显示提示词列表\n    function showPromptList(categoryId) {\n        try {\n            document.getElementsByClassName('prompt-manager-modal')[0].remove();\n        } catch (e) { }\n\n        console.log(document.getElementById('div1').innerHTML)\n        const prompts = data.prompts.filter(p => p.categoryId === categoryId);\n        const category = data.categories.find(c => c.id === categoryId);\n\n        const modal = createModal();\n        modal.innerHTML = `\n            <div style=\"display: flex; justify-content: space-between; align-items: center;height:30px;\">\n                <h3>${category.name}</h3>\n                <div class=\"prompt-toolbar\">\n                \n                \n                <button class=\"toolbar-btn\" id=\"share-category\" title=\"分享分类\">📤</button>\n            </div>\n            </div>\n            \n            <div class=\"batch-actions\">\n                <button class=\"prompt-manager-button\" id=\"move-selected\">移动到分类</button>\n                <button class=\"prompt-manager-button danger\" id=\"delete-selected\">删除选中</button>\n                <button class=\"prompt-manager-button secondary\" id=\"cancel-select\">取消选择</button>\n            </div>\n            <div class=\"prompt-manager-list\">\n                ${prompts.map(prompt => `\n                    <div class=\"prompt-manager-list-item\" data-id=\"${prompt.id}\" data-text=\"${prompt.text}\" data-alias=\"${prompt.alias || ''}\">\n                        \n                        \n                        <span class=\"prompt-text\" style=\"font-size:13px\">${prompt.alias || prompt.text}</span>\n                        <span class=\"prompt-actions\">\n                            \n                            <button class=\"prompt-edit\" style=\"display:none\" title=\"编辑\">✏️</button>\n                            <button class=\"prompt-delete\" style=\"font-size:13px;margin-right:-2px;display:none\" title=\"删除\">❌</button>\n                        </span>\n                    </div>\n                `).join('')}\n            </div>\n            <div class=\"prompt-manager-toolbar\" style=\" border-bottom: none; padding-bottom: 0;\">\n            <button class=\"prompt-manager-button\" style=\"margin-left: 0;\">编辑/删除分类</button>\n                <button class=\"prompt-manager-button\" style=\"margin-left: 0;background: var(--pm-primary-color);\">管理提示词</button>\n            </div>\n        `;\n\n        // 添加CSS样式\n        const style = document.createElement('style');\n        style.textContent = `\n            .prompt-manager-list-item {\n                display: flex;\n                flex-wrap: wrap;\n                flex-direction: row;\n                justify-content: space-between;\n                align-items: center;\n                position: relative;\n                padding-left: 5px;\n                \n            }\n            .prompt-text {\n                flex: 1;\n                overflow: hidden;\n                text-overflow: ellipsis;\n                white-space: nowrap;\n                user-select: none;\n            }\n            .prompt-favorite {\n                background: none;\n                border: none;\n                cursor: pointer;\n                font-size: 16px;\n                color: gold;\n            }\n            .prompt-checkbox {\n                position: absolute;\n                left: 5px;\n                width: 16px;\n                height: 16px;\n                cursor: pointer;\n                opacity: 0;\n            }\n            .prompt-checkbox-custom {\n                position: absolute;\n                left: 5px;\n                width: 16px;\n                height: 16px;\n                border: 2px solid var(--pm-text-color);\n                border-radius: 3px;\n                pointer-events: none;\n            }\n            .prompt-checkbox:checked + .prompt-checkbox-custom::after {\n                content: '✓';\n                position: absolute;\n                top: 50%;\n                left: 50%;\n                transform: translate(-50%, -50%);\n                color: var(--pm-primary-color);\n            }\n            .prompt-manager-list-item.selected {\n                background-color: rgba(74, 144, 226, 0.1);\n            }\n            .batch-actions {\n                display: none;\n                margin-bottom: 10px;\n                gap: 8px;\n                flex-wrap: wrap;\n            }\n            .batch-actions.visible {\n                display: flex;\n            }\n            /* 操作提示和反馈 */\n            \n            @keyframes fadeInOut {\n                0% { opacity: 0; transform: translateY(20px); }\n                10% { opacity: 1; transform: translateY(0); }\n                90% { opacity: 1; transform: translateY(0); }\n                100% { opacity: 0; transform: translateY(-20px); }\n            }\n            \n            .prompt-actions {\n                opacity: 0.3;\n                transition: opacity 0.2s;\n            }\n            .prompt-manager-list-item:hover .prompt-actions {\n                opacity: 1;\n            }\n            .prompt-delete, .prompt-edit {\n                background: none;\n                border: none;\n                cursor: pointer;\n                font-size: 10px;\n            }\n            .prompt-manager-list-item.active {\n                background-color: rgba(74, 144, 226, 0.1);\n            }\n            .empty-list-message {\n                padding: 20px;\n                text-align: center;\n                color: #999;\n            }\n            .prompt-toolbar {\n                display: flex;\n                gap: 8px;\n            }\n            .toolbar-btn {\n                background: none;\n                border: none;\n                cursor: pointer;\n                padding: 4px 8px;\n                font-size: 16px;\n                color: var(--pm-text-color);\n                border-radius: 4px;\n            }\n            .toolbar-btn:hover {\n                background: var(--pm-hover-color);\n            }\n            .search-bar {\n                position: relative;\n            }\n        `;\n        document.head.appendChild(style);\n\n\n        // 排序函数\n        const sortPrompts = (items) => {\n            return Array.from(items).sort((a, b) => {\n                const aPrompt = data.prompts.find(p => p.id === a.dataset.id);\n                const bPrompt = data.prompts.find(p => p.id === b.dataset.id);\n\n                if (!aPrompt || !bPrompt) return 0;\n\n                switch (data.settings.sortBy) {\n                    case 'name':\n                        return (aPrompt.alias || aPrompt.text).localeCompare(bPrompt.alias || bPrompt.text);\n                    case 'usage':\n                        return (bPrompt.useCount || 0) - (aPrompt.useCount || 0);\n                    default: // 'time'\n                        return bPrompt.id.localeCompare(aPrompt.id);\n                }\n            });\n        };\n\n\n\n\n\n\n        // 提示词点击事件\n        const items = modal.querySelectorAll('.prompt-manager-list-item');\n        items.forEach(item => {\n\n            // 提示词文本点击事件\n            const textSpan = item.querySelector('.prompt-text');\n            textSpan.onclick = (e) => {\n                e.stopPropagation(); // 阻止事件冒泡\n\n                const promptId = item.dataset.id;\n                const prompt = data.prompts.find(p => p.id === promptId);\n\n\n                if (!prompt) {\n                    alert('提示词数据已损坏，请尝试刷新页面');\n                    return;\n                }\n\n                let text = prompt.text;\n\n                // 如果是模板，处理变量替换\n                if (prompt.isTemplate) {\n                    // 提取所有变量\n                    const variables = [];\n                    const regex = /\\{\\{([^}]+)\\}\\}/g;\n                    let match;\n\n                    while ((match = regex.exec(text)) !== null) {\n                        if (!variables.includes(match[1])) {\n                            variables.push(match[1]);\n                        }\n                    }\n\n                    // 如果有变量，提示用户输入\n                    if (variables.length > 0) {\n                        // 收集变量值\n                        const values = {};\n                        for (const variable of variables) {\n                            const value = prompt(`请输入\"${variable}\"的值：`);\n                            if (value === null) {\n                                // 用户取消了输入\n                                return;\n                            }\n                            values[variable] = value;\n                        }\n\n                        // 替换所有变量\n                        for (const [variable, value] of Object.entries(values)) {\n                            const regex = new RegExp(`\\\\{\\\\{${variable}\\\\}\\\\}`, 'g');\n                            text = text.replace(regex, value);\n                        }\n                    }\n                }\n\n                // 检查文本结尾是否是逗号，如果不是则添加逗号\n                if (!text.endsWith(',')) {\n                    text += ',';\n                }\n\n                // 记录最近聚焦的输入元素\n                let targetInput = window.lastFocusedInput;\n\n                // 如果没有记录或记录的元素不再有效，则尝试查找页面上可见的输入框\n                if (!targetInput || !document.body.contains(targetInput)) {\n                    // 查找页面上所有的输入框和文本框\n                    const inputs = document.querySelectorAll('input[type=\"text\"], textarea');\n                    targetInput = Array.from(inputs).find(input => {\n                        // 检查元素是否可见且可编辑\n                        const style = window.getComputedStyle(input);\n                        const rect = input.getBoundingClientRect();\n                        return style.display !== 'none' &&\n                            !input.readOnly &&\n                            !input.disabled &&\n                            rect.width > 0 &&\n                            rect.height > 0;\n                    });\n                }\n\n                if (targetInput) {\n                    // 将文本插入到输入框\n                    targetInput.focus();\n                    const start = targetInput.selectionStart || 0;\n                    const end = targetInput.selectionEnd || 0;\n                    targetInput.value = targetInput.value.substring(0, start) + text + targetInput.value.substring(end);\n                    targetInput.setSelectionRange(start + text.length, start + text.length);\n\n                    // 触发输入事件，确保动态网页能检测到内容变化\n                    const inputEvent = new Event('input', { bubbles: true });\n                    targetInput.dispatchEvent(inputEvent);\n\n                    // 更新使用次数\n                    const promptId = item.dataset.id;\n                    const promptIndex = data.prompts.findIndex(p => p.id === promptId);\n                    if (promptIndex !== -1) {\n                        data.prompts[promptIndex].useCount = (data.prompts[promptIndex].useCount || 0) + 1;\n                        saveData();\n                    }\n                }\n                //closeModal();\n            };\n            //给prompt-edit添加动作，修改提示词\n            const editBtn = item.querySelector('.prompt-edit');\n            editBtn.onclick = (e) => { \n                    //e.stopPropagation();\n                    console.log('editBtn clicked');\n                    const promptId = item.dataset.id;\n                    const prompt = data.prompts.find(p => p.id === promptId);\n                    if (prompt) {\n                        showEditPromptDialog(promptId, prompt.text, prompt.alias, prompt.categoryId);\n                    }\n                \n            }\n\n            // 删除按钮点击事件\n            const deleteBtn = item.querySelector('.prompt-delete');\n            deleteBtn.onclick = (e) => {\n                e.stopPropagation();\n                const promptId = item.dataset.id;\n\n                if (confirm('确定要删除这个提示词吗？')) {\n                    try {\n                        data.prompts = data.prompts.filter(p => p.id !== promptId);\n                        saveData();\n                        item.remove();\n\n                        // 检查列表是否为空\n                        const listItems = modal.querySelectorAll('.prompt-manager-list-item:not([style*=\"display: none\"])');\n                        const listContainer = modal.querySelector('.prompt-manager-list');\n\n                        if (listItems.length === 0) {\n                            const message = data.settings.showFavoriteOnly ? '没有收藏的提示词' : '没有提示词';\n                            listContainer.innerHTML = `<div class=\"empty-list-message\">${message}</div>`;\n                        }\n\n                        showToast('提示词已删除');\n                    } catch (error) {\n                        console.error('删除提示词失败', error);\n                        showToast('删除失败，请重试');\n                    }\n                }\n            };\n        });\n\n        // 工具栏按钮\n        const shareCategoryBtn = modal.querySelector('#share-category');\n        const [editCategoryBtn, edititem, closeBtn] = modal.querySelectorAll('.prompt-manager-toolbar button');\n\n\n\n        // 分享分类按钮点击事件\n        shareCategoryBtn.onclick = () => {\n            const category = data.categories.find(c => c.id === categoryId);\n            if (!category) return;\n\n            const prompts = data.prompts.filter(p => p.categoryId === categoryId);\n            if (prompts.length === 0) {\n                showToast('当前分类没有提示词可分享');\n                return;\n            }\n\n            const shareModal = createModal2()//create浮动Modal();\n            shareModal.innerHTML = `\n                <h3>分享分类：${category.name}</h3>\n                <div style=\"margin-bottom: 15px;\">\n                    <label style=\"display: block; margin-bottom: 10px;\">选择分享格式：</label>\n                    <select class=\"prompt-manager-input\" id=\"share-format\">\n                        <option value=\"text\">纯文本格式</option>\n                        <option value=\"markdown\">Markdown格式</option>\n                        <option value=\"json\">JSON格式</option>\n                    </select>\n                </div>\n                <div style=\"margin-bottom: 15px;\">\n                    <label style=\"display: block; margin-bottom: 10px;\">分享内容预览：</label>\n                    <textarea class=\"prompt-manager-input\" style=\"height: 200px;\" readonly></textarea>\n                </div>\n                <div style=\"display: flex; gap: 10px;\">\n                    <button class=\"prompt-manager-button\" id=\"copy-share\">复制内容</button>\n                    <button class=\"prompt-manager-button\" id=\"download-share\">下载文件</button>\n                    \n                </div>\n            `;\n            makeDraggable(shareModal);\n            const formatSelect = shareModal.querySelector('#share-format');\n            const previewArea = shareModal.querySelector('textarea');\n            const copyBtn = shareModal.querySelector('#copy-share');\n            const downloadBtn = shareModal.querySelector('#download-share');\n            const closeShareBtn = shareModal.querySelector('.prompt-manager-button.secondary');\n\n            // 生成分享内容\n            const generateContent = (format) => {\n                switch (format) {\n                    case 'text':\n                        return prompts.map(p => {\n                            const text = p.text.replace(/\\n/g, ' ');\n                            return p.alias ? `${text}|${p.alias}` : text;\n                        }).join('\\n');\n                    case 'markdown':\n                        return `# ${category.name}\\n\\n` + prompts.map(p => {\n                            const text = p.text.replace(/\\n/g, ' ');\n                            return `- ${p.alias ? `**${p.alias}**: ` : ''}${text}`;\n                        }).join('\\n');\n                    case 'json':\n                        return JSON.stringify({\n                            category: category.name,\n                            prompts: prompts.map(p => ({\n                                text: p.text,\n                                alias: p.alias || null\n                            }))\n                        }, null, 2);\n                }\n            };\n\n            // 更新预览\n            const updatePreview = () => {\n                previewArea.value = generateContent(formatSelect.value);\n            };\n\n            formatSelect.onchange = updatePreview;\n            updatePreview();\n\n            // 复制内容\n            copyBtn.onclick = async () => {\n                try {\n                    await navigator.clipboard.writeText(previewArea.value);\n                    showToast('内容已复制到剪贴板');\n                } catch (error) {\n                    console.error('复制失败', error);\n                    // 降级方案\n                    previewArea.select();\n                    document.execCommand('copy');\n                    showToast('内容已复制到剪贴板');\n                }\n            };\n\n            // 下载文件\n            downloadBtn.onclick = () => {\n                const format = formatSelect.value;\n                const content = generateContent(format);\n                const blob = new Blob([content], {\n                    type: format === 'json' ? 'application/json' : 'text/plain'\n                });\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = `prompts-${category.name}.${format === 'markdown' ? 'md' : format}`;\n                a.click();\n                URL.revokeObjectURL(url);\n                showToast('文件已开始下载');\n            };\n\n            closeShareBtn.onclick = () => closeModal(shareModal);\n        };\n        edititem.onclick = () => {\n            showx()\n        }\n        //showx()\n        function showx() {\n\n            const categories = document.querySelectorAll('.prompt-actions .prompt-delete')\n            for (let i = 0; i < categories.length; i++) {\n                if (categories[i].style.display != \"none\") {\n\n                    categories[i].style.display = \"none\";\n                    categories[i].parentNode.style.paddingRight = \"7px\";\n                    categories[i].parentNode.style.paddingLeft = \"7px\";\n\n                } else {\n                    categories[i].style.display = \"block\"\n                    categories[i].parentNode.style.paddingRight = \"0px\";\n                    categories[i].parentNode.style.paddingLeft = \"0px\";\n                }\n\n            }\n            const categories2 = document.querySelectorAll('.prompt-actions .prompt-edit')\n            for (let i = 0; i < categories2.length; i++) {\n                if (categories2[i].style.display != \"none\") {\n\n                    categories2[i].style.display = \"none\";\n                    categories2[i].parentNode.style.paddingRight = \"7px\";\n                    categories2[i].parentNode.style.paddingLeft = \"7px\";\n\n                } else {\n                    categories2[i].style.display = \"block\"\n                    categories2[i].parentNode.style.paddingRight = \"0px\";\n                    categories2[i].parentNode.style.paddingLeft = \"0px\";\n                }\n\n            }\n        }\n        editCategoryBtn.onclick = () => {\n            showEditCategoryDialog(categoryId);\n        };\n\n        //closeBtn.onclick = closeModal;\n    }\n\n    function 替换空格() {\n\n        let targetInput = window.lastFocusedInput || document.activeElement;\n        if (targetInput && (targetInput.tagName === 'INPUT' || targetInput.tagName === 'TEXTAREA')) {\n            // 获取文本框的当前值\n            const originalText = targetInput.value;\n\n            // 替换空格为下划线，但空格前不是逗号或句号的情况\n            const replacedText = originalText.replace(/(?<![,.])\\s/g, '_');\n\n            // 如果替换后的文本与原始文本不同，则更新文本框的内容\n            if (replacedText !== originalText) {\n                targetInput.value = replacedText;\n\n                // 将光标移动到文本末尾（可选）\n                targetInput.setSelectionRange(replacedText.length, replacedText.length);\n\n                // 触发输入事件\n                const inputEvent = new Event('input', { bubbles: true });\n                targetInput.dispatchEvent(inputEvent);\n            }\n        }\n    };\n    function 显示浏览器面板() {\n        if (floatBtn.style.display === 'none') {\n            floatWindow.style.display = 'block';\n            floatBtn.style.display = 'block';\n        } else {\n            floatWindow.style.display = 'none';\n            floatBtn.style.display = 'none';\n        }\n    }\n    \n    // 显示设置界面\n    function showSettings() {\n        \n        const modal = createModal2()//create浮动Modal();\n        modal.innerHTML = `\n            <h3>设置</h3>\n            <div class=\"settings-section\">\n                <h4>导入导出</h4>\n                <div style=\"display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 15px;\">\n                    <button class=\"prompt-manager-button\" id=\"export-json\">导出数据</button>\n                    <button class=\"prompt-manager-button\">导入数据</button>\n                    <button class=\"prompt-manager-button\" >批量导入</button>\n                    <span style=\"display:none\">每行一个提示词，如果有简称以\"|\"分隔。每次请只添加一个分组的，因为添加后所有的都会被分到同一个分组</span>\n                </div>\n            </div>\n            <div class=\"settings-section\">\n                <div style=\"margin-bottom: 5px;\">\n                    <label style=\"display: block; margin-bottom: 5px;\">面板透明度: ${Math.round(data.settings.panelOpacity * 100)}%</label>\n                    <input type=\"range\" min=\"10\" max=\"100\" value=\"${data.settings.panelOpacity * 100}\" \n                           id=\"opacity-slider\" style=\"width: 100%;\">\n                </div>\n            </div>\n            <div class=\"settings-section\">\n                <h4>其他操作</h4>\n                <div style=\"display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 5px;\">\n                    <button class=\"prompt-manager-button\" style=\"display:none\">使用帮助</button>\n                    <button class=\"prompt-manager-button danger\">清空数据</button>\n                    <a class=\"prompt-manager-button danger\" target='_blank' href='https://www.bilibili.com/video/BV1N3o1YpEY3/?vd_source=09698d8717805cee2a04f9dab13e298d#reply260706414480'>反馈bug</a>\n                    <a class=\"prompt-manager-button danger\" target='_blank'  href='https://scriptcat.org/zh-CN/script-show-page/3009/version'>更新记录</a>\n                </div>\n            </div>\n            <span>\n            <h6>使用帮助</h6>\n            1、点击\"+🗂️\"可以添加分组<br>\n            2、在页面中划词可以添加提示词<br>\n            3、点击提示词可以填入到编辑框里<br>\n            4、点击🈳可以替换掉所有空格为\"_\"<br>\n            5、点击🛜开启页面内浏览器，可以临时打开翻译网站，需要安装<a style=\"color:orange\" href=\"https://chromewebstore.google.com/detail/joibipdfkleencgfgbbncoheaekffdfn\" target=\"_blank\">Hiframe插件</a>\n            </span>\n            \n<button class=\"prompt-manager-button secondary\">关闭</button>\n            <input type=\"file\" style=\"display: none\" accept=\".json\" id=\"import-file\">\n            <input type=\"file\" style=\"display: none\" accept=\".txt,.csv\" id=\"batch-import-file\">\n        `;\n        makeDraggable(modal);\n        const closeBtn1 = modal.querySelector('.prompt-manager-button.secondary');\n        closeBtn1.onclick = closeModal;\n        console.log(data.settings.panelOpacity)\n        document.getElementById('opacity-slider')?.addEventListener('input', function(e) {\n            console.log(e.target.value)\n                const opacity = e.target.value / 100;\n                data.settings.panelOpacity = opacity;\n                saveData();\n                \n                // 更新所有面板的背景色透明度\n                document.querySelectorAll('.prompt-manager-menu, .prompt-manager-modal, .prompt-manager-modal2').forEach(el => {\n                    const currentBg = window.getComputedStyle(el).backgroundColor;\n                    const newBg = currentBg.replace(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*[\\d.]+)?\\)/, \n                        `rgba($1, $2, $3, ${opacity})`);\n                    el.style.backgroundColor = newBg;\n                });\n                \n                // 更新标签显示\n                e.target.previousElementSibling.textContent = `面板透明度: ${e.target.value}%`;\n            });\n\n\n        const exportJsonBtn = modal.querySelector('#export-json');\n        const exportTxtBtn = modal.querySelector('#export-txt');\n        const [, importBtn, batchImportBtn, helpBtn, clearBtn, closeBtn] = modal.querySelectorAll('.prompt-manager-button');\n        const fileInput = modal.querySelector('#import-file');\n        const batchFileInput = modal.querySelector('#batch-import-file');\n\n\n\n        // 导出仅包含prompts数组的JSON数据\n        exportJsonBtn.onclick = () => {\n            const exportData = { categories: data.categories, prompts: data.prompts };\n            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });\n            //const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = 'prompt-manager-data.json';\n            a.click();\n            URL.revokeObjectURL(url);\n        };\n\n\n\n        importBtn.onclick = () => fileInput.click();\n\n        // 批量导入按钮\n        batchImportBtn.onclick = () => showBatchImportDialog()//batchFileInput.click();\n\n        // 批量导入文件处理\n        batchFileInput.onchange = async () => {\n            const file = batchFileInput.files[0];\n            if (file) {\n                try {\n                    const text = await file.text();\n                    showBatchImportDialog(text);\n                } catch (e) {\n                    alert('文件读取失败');\n                }\n            }\n        };\n\n        fileInput.onchange = async () => {\n            const file = fileInput.files[0];\n            if (file) {\n                try {\n                    const text = await file.text();\n                    const importedData = JSON.parse(text);\n                    if (importedData.categories && importedData.prompts) {\n                        // 保留lastUsedCategoryId或使用导入数据中的值\n                        const currentLastUsedCategory = data.lastUsedCategoryId;\n                        data = importedData;\n\n                        // 如果导入的数据没有lastUsedCategoryId，保留当前的值\n                        if (!data.lastUsedCategoryId && currentLastUsedCategory) {\n                            data.lastUsedCategoryId = currentLastUsedCategory;\n                        }\n                        saveData();\n                        alert('数据导入成功！');\n                        closeModal();\n                    }\n                } catch (e) {\n                    alert('导入失败：无效的数据格式');\n                }\n            }\n        };\n\n        clearBtn.onclick = () => {\n            if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {\n                data = {\n                    categories: [],\n                    prompts: [],\n                    lastUsedCategoryId: null\n                };\n                saveData();\n                closeModal();\n            }\n        };\n\n        //closeBtn.onclick = closeModal;\n    }\n\n    // 显示保存提示词对话框\n    function showSavePromptDialog(text) {\n        const modal = createModal2();\n        modal.innerHTML = `\n            <h3>保存提示词</h3>\n            <textarea class=\"prompt-manager-input\" style=\"height: 100px;width:250px;resize:auto;\">${text}</textarea>\n            <div style='display:flex;width:250px'><input type=\"text\" class=\"prompt-manager-input\" placeholder=\"简称（可选）\">\n            <select class=\"prompt-manager-input\">\n                <option value=\"\">选择分类...</option>\n                ${data.categories.map(c => `\n                    <option value=\"${c.id}\">${c.name}</option>\n                `).join('')}\n            </select>\n            <button class=\"prompt-manager-button\">保存</button></div>\n            \n        `;\n\n        // 防止点击事件冒泡\n        modal.addEventListener('click', (e) => {\n            e.stopPropagation();\n        });\n        // 添加拖拽功能\n        makeDraggable(modal);\n        const aliasInput = modal.querySelector('input');\n        const categorySelect = modal.querySelector('select');\n        const [saveBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        saveBtn.onclick = () => {\n            const categoryId = categorySelect.value;\n            if (!categoryId) {\n                alert('请选择分类！');\n                return;\n            }\n\n            const textArea = modal.querySelector('textarea');\n            const isTemplateCheckbox = modal.querySelector('#is-template') || { checked: false };\n            const newPrompt = {\n                id: Date.now().toString(),\n                text: textArea.value.trim(),\n                alias: aliasInput.value.trim(),\n                categoryId: categoryId,\n                favorite: false,\n                useCount: 0,\n                isTemplate: isTemplateCheckbox.checked\n            };\n\n            // 保存提示词的同时记录最后使用的分组ID\n            data.lastUsedCategoryId = categoryId;\n            data.prompts.push(newPrompt);\n            saveData();\n            closeModal();\n\n                //提示信息\n                showToast('新建成功',1000);\n            \n        };\n\n        // 如果有上次使用的分组，自动选中\n        if (data.lastUsedCategoryId) {\n            const option = categorySelect.querySelector(`option[value=\"${data.lastUsedCategoryId}\"]`);\n            if (option) {\n                categorySelect.value = data.lastUsedCategoryId;\n            } else {\n                // 如果找不到对应的分类（可能已被删除），清除记录\n                data.lastUsedCategoryId = null;\n                saveData();\n            }\n        }\n\n        cancelBtn.onclick = closeModal;\n    }\n\n    // 显示批量导入对话框\n    function showBatchImportDialog(text) {\n        const modal = createModal2()//create浮动Modal();\n        modal.innerHTML = `\n            <h3>批量导入提示词</h3>\n            <p>每行一个提示词，格式：提示词文本|别名（可选）。导入后需要刷新</p>\n            <textarea class=\"prompt-manager-input\" style=\"height: 200px;\">${text}</textarea>\n            <select class=\"prompt-manager-input\">\n                <option value=\"\">选择导入分类...</option>\n                ${data.categories.map(c => `\n                    <option value=\"${c.id}\">${c.name}</option>\n                `).join('')}\n            </select>\n            <button class=\"prompt-manager-button\">导入</button>\n            <button class=\"prompt-manager-button secondary\">取消</button>\n        `;\n        makeDraggable(modal);\n        const textArea = modal.querySelector('textarea');\n        const categorySelect = modal.querySelector('select');\n        const [importBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        // 如果有上次使用的分组，自动选中\n        if (data.lastUsedCategoryId) {\n            categorySelect.value = data.lastUsedCategoryId;\n        }\n\n        importBtn.onclick = () => {\n            const categoryId = categorySelect.value;\n            if (!categoryId) {\n                alert('请选择分类！');\n                return;\n            }\n\n            const lines = textArea.value.split('\\n').filter(line => line.trim());\n            if (lines.length === 0) {\n                alert('没有找到有效的提示词');\n                return;\n            }\n\n            let importCount = 0;\n            lines.forEach(line => {\n                const parts = line.split('|');\n                const text = parts[0].trim();\n                const alias = parts.length > 1 ? parts[1].trim() : '';\n\n                if (text) {\n                    const newPrompt = {\n                        id: Date.now().toString() + importCount,\n                        text: text,\n                        alias: alias,\n                        categoryId: categoryId,\n                        favorite: false\n                    };\n\n                    data.prompts.push(newPrompt);\n                    importCount++;\n                }\n            });\n\n            if (importCount > 0) {\n                data.lastUsedCategoryId = categoryId;\n                saveData();\n                alert(`成功导入 ${importCount} 个提示词`);\n                closeModal();\n            } else {\n                alert('没有找到有效的提示词');\n            }\n        };\n\n        cancelBtn.onclick = closeModal;\n    }\n\n\n    // 创建模态框\n    function createModal() {\n        const modal = document.createElement('div');\n        modal.className = 'prompt-manager-modal';\n\n\n        // 添加拖拽功能\n        makeDraggable(modal);\n        const menu = document.querySelector('#div1').appendChild(modal);\n        return modal;\n    }\n    function create浮动Modal() {\n        const modal = document.createElement('div');\n        modal.className = 'prompt-manager-modal2 浮动';\n\n        // 如果有保存的位置，使用保存的位置\n        // if (data.settings?.modalPosition) {\n        modal.style.left = '20%';\n        modal.style.top = '20%';\n        modal.style.transform = 'none';\n        //}*/\n\n        // 添加拖拽功能\n        makeDraggable(modal);\n        //const menu = document.querySelector('#div1').appendChild(modal);\n        document.body.appendChild(modal);\n        return modal;\n    }\n    function createModal2() {\n        const modal = document.createElement('div');\n        modal.className = 'prompt-manager-modal2 浮动modal2';\n\n        // 如果有保存的位置，使用保存的位置\n        if (data.settings?.modalPosition) {\n            modal.style.left = data.settings.modalPosition.left + 'px';\n            modal.style.top = data.settings.modalPosition.top + 'px';\n            modal.style.transform = 'none';\n        }\n\n        makeDraggable(modal);\n        //const menu = document.querySelector('#div1').appendChild(modal);\n        document.body.appendChild(modal);\n        return modal;\n    }\n\n    // 使元素可拖拽\n    function makeDraggable(element) {\n        let isDragging = false;\n        let offsetX, offsetY;\n\n        // 创建拖拽手柄\n        const dragHandle = document.createElement('div');\n        dragHandle.style.cssText = `\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            height: 10px;\n            cursor: move;\n            background-color: rgb(87 122 77 / 64%);\n        `;\n        element.appendChild(dragHandle);\n        //console.log(dragHandle);\n        // 鼠标按下事件\n        dragHandle.addEventListener('mousedown', (e) => {\n            isDragging = true;\n\n            // 获取鼠标在元素内的相对位置\n            const rect = element.getBoundingClientRect();\n            offsetX = e.clientX - rect.left;\n            offsetY = e.clientY - rect.top;\n\n            // 阻止默认行为和冒泡\n            e.preventDefault();\n            e.stopPropagation();\n        });\n\n        // 鼠标移动事件\n        document.addEventListener('mousemove', (e) => {\n            if (!isDragging) return;\n\n            // 计算新位置\n            const x = e.clientX - offsetX;\n            const y = e.clientY - offsetY;\n\n            // 应用新位置\n            element.style.left = x + 'px';\n            element.style.top = y + 'px';\n            element.style.transform = 'none';\n\n            // 保存位置到设置\n            data.settings.modalPosition = { left: x, top: y };\n        });\n\n        // 鼠标释放事件\n        document.addEventListener('mouseup', () => {\n            if (isDragging) {\n                isDragging = false;\n                saveData();\n            }\n        });\n    }\n\n    // 关闭模态框\n    function closeModal() {\n        const modal = document.querySelector('.prompt-manager-modal');\n        const modal2 = document.querySelector('.prompt-manager-modal2');\n        if (modal) modal.remove();\n        if (modal2) modal2.remove();\n    }\n    function closeModal2() {\n        const modal = document.querySelector('.prompt-manager-modal2');\n        if (modal) modal.remove();\n    }\n    // 点击页面任意位置关闭模态框（使用捕获阶段）\n    document.addEventListener('click', (e) => {\n        const modal = document.querySelector('.prompt-manager-modal2');\n        if (modal && !e.target.closest('.prompt-manager-modal2')) {\n            closeModal2();\n        }\n    }, true);\n\n    \n    // 显示编辑提示词对话框\n    function showEditPromptDialog(promptId, text, alias, categoryId) {\n        console.log(promptId, text, alias, categoryId);\n        const modal = createModal2();\n        modal.innerHTML = `\n            <h3>编辑提示词</h3>\n            <textarea class=\"prompt-manager-input\" style=\"height: 100px\">${text}</textarea>\n            <input type=\"text\" class=\"prompt-manager-input\" placeholder=\"简称（可选）\" value=\"${alias || ''}\">\n            <select class=\"prompt-manager-input\">\n                <option value=\"\">选择分类...</option>\n                ${data.categories.map(c => `\n                    <option value=\"${c.id}\" ${c.id === categoryId ? 'selected' : ''}>${c.name}</option>\n                `).join('')}\n            </select>\n            <button class=\"prompt-manager-button\">保存</button>\n            <button class=\"prompt-manager-button secondary\">取消</button>\n        `;\n        makeDraggable(modal);\n        // 防止点击事件冒泡\n        modal.addEventListener('click', (e) => {\n            e.stopPropagation();\n        });\n\n        const textArea = modal.querySelector('textarea');\n        const aliasInput = modal.querySelector('input[type=\"text\"]');\n        const isTemplateCheckbox = modal.querySelector('#is-template') || { checked: false };\n        //const templateHelp = modal.querySelector('.template-help');\n        const categorySelect = modal.querySelector('select');\n        const [saveBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        // 模板帮助提示\n        //templateHelp.onclick = () => {\n        //    alert('模板支持使用变量占位符，格式为 {{变量名}}。\\n\\n使用时会提示输入变量值，自动替换占位符。\\n\\n例如：\\n\"你好，我是{{名字}}，我来自{{地点}}。\"\\n\\n使用时会提示输入\"名字\"和\"地点\"的值。');\n        //};\n\n        saveBtn.onclick = () => {\n            const newCategoryId = categorySelect.value;\n            if (!newCategoryId) {\n                alert('请选择分类！');\n                return;\n            }\n\n            const newText = textArea.value;\n            if (!newText.trim()) {\n                alert('提示词内容不能为空！');\n                return;\n            }\n\n            // 更新提示词\n            const promptIndex = data.prompts.findIndex(p => p.id === promptId);\n            if (promptIndex !== -1) {\n                data.prompts[promptIndex] = {\n                    ...data.prompts[promptIndex],\n                    text: newText,\n                    alias: aliasInput.value.trim(),\n                    categoryId: newCategoryId\n                };\n\n                saveData();\n                closeModal();\n\n                // 如果分类改变了，需要刷新提示词列表\n                if (newCategoryId !== categoryId) {\n                    showPromptList(newCategoryId);\n                } else {\n                    showPromptList(categoryId);\n                }\n            }\n        };\n\n        cancelBtn.onclick = closeModal;\n    }\n\n    // 显示编辑分类对话框\n    function showEditCategoryDialog(categoryId) {\n        const category = data.categories.find(c => c.id === categoryId);\n        if (!category) return;\n\n        const modal = createModal2()//create浮动Modal();\n        modal.innerHTML = `\n            <h3>编辑分类</h3>\n            <input type=\"text\" class=\"prompt-manager-input\" value=\"${category.name}\" placeholder=\"分类名称\">\n            <div class=\"prompt-manager-toolbar\" style=\"border: none; justify-content: space-between;height:30px;\">\n                <button class=\"prompt-manager-button\">保存</button>\n                <button class=\"prompt-manager-button danger\">删除分类</button>\n                <button class=\"prompt-manager-button secondary\">取消</button>\n            </div>\n        `;\n        makeDraggable(modal);\n        const nameInput = modal.querySelector('input');\n        const [saveBtn, deleteBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        saveBtn.onclick = () => {\n            const newName = nameInput.value.trim();\n            if (newName) {\n                category.name = newName;\n                saveData();\n                closeModal();\n                // 刷新分类列表\n                showPromptList(categoryId);\n            } else {\n                alert('分类名称不能为空');\n            }\n        };\n\n        deleteBtn.onclick = () => {\n            if (confirm(`确定要删除分类\"${category.name}\"吗？该分类下的所有提示词也将被删除！`)) {\n                // 删除该分类下的所有提示词\n                data.prompts = data.prompts.filter(p => p.categoryId !== categoryId);\n                // 删除分类\n                data.categories = data.categories.filter(c => c.id !== categoryId);\n                // 如果删除的是上次使用的分类，清除记录\n                if (data.lastUsedCategoryId === categoryId) {\n                    data.lastUsedCategoryId = null;\n                }\n                saveData();\n                closeModal();\n\n                // 刷新菜单\n                const menu = document.querySelector('.prompt-manager-menu');\n                if (menu) {\n                    const rect = menu.getBoundingClientRect();\n                    menu.remove();\n                    createFloatingMenu(rect.left, rect.top);\n                }\n            }\n        };\n\n        cancelBtn.onclick = closeModal;\n    }\n\n\n\n    // 初始化\n    initData();\n\n    // 记录最近聚焦的输入元素\n    window.lastFocusedInput = null;\n    document.addEventListener('focusin', (e) => {\n        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {\n            window.lastFocusedInput = e.target;\n        }\n    });\n\n    // 监听文本框点击\n    document.addEventListener('click', (e) => {\n        const target = e.target;\n        if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {\n            const menu = document.querySelector('.prompt-manager-menu');\n            if (menu) menu.remove();\n\n            const rect = target.getBoundingClientRect();\n            createFloatingMenu(rect.left - 170, rect.top);\n        } else if (!e.target.closest('.prompt-manager-menu') && !e.target.closest('.prompt-manager-modal')) {\n            const menu = document.querySelector('.prompt-manager-menu');\n            if (menu) menu.remove();\n        }\n    });\n\n    // 监听选中文本\n    document.addEventListener('mouseup', (e) => {\n        // 检查是否在模态框内，如果是则不处理\n        if (e.target.closest('.prompt-manager-modal') || e.target.closest('.prompt-manager-menu') || e.target.closest('.prompt-manager-modal2')) {\n            return;\n        }\n        const selection = window.getSelection();\n        const text = selection.toString().trim();\n\n        // 如果有选中的文本，则显示保存对话框\n        if (text) {\n            // 延迟显示保存对话框，避免与其他点击事件冲突\n            setTimeout(() => {\n                const menu = document.querySelector('.prompt-manager-menu');\n                if (menu) menu.remove();\n                showSavePromptDialog(text);\n            }, 100);\n        }\n    });\n    \n    //_____________________________\n\n    // 创建主浮动按钮\n    const floatBtn = document.createElement('div');\n    floatBtn.innerHTML = '🌐';\n    floatBtn.style.display = 'none';\n    floatBtn.style.position = 'fixed';\n    floatBtn.style.left = '10px';\n    floatBtn.style.top = '0px';\n    floatBtn.style.zIndex = '9999';\n    floatBtn.style.width = '23px';\n    floatBtn.style.height = '23px';\n    floatBtn.style.borderRadius = '50%';\n    floatBtn.style.backgroundColor = '#4285f4';\n    floatBtn.style.color = 'white';\n    floatBtn.style.justifyContent = 'center';\n    floatBtn.style.alignItems = 'center';\n    floatBtn.style.cursor = 'pointer';\n    floatBtn.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';\n    document.body.appendChild(floatBtn);\n\n    // 创建浮动窗口容器\n    const floatWindow = document.createElement('div');\n    floatWindow.style.display = 'none';\n    floatWindow.style.position = 'fixed';\n    floatWindow.style.zIndex = '9998';\n    floatWindow.style.width = '300px';\n    floatWindow.style.height = '400px';\n    floatWindow.style.top = '50px';\n    floatWindow.style.left = '100px';\n    floatWindow.style.backgroundColor = 'white';\n    floatWindow.style.borderRadius = '8px';\n    floatWindow.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';\n    floatWindow.style.overflow = 'hidden';\n    document.body.appendChild(floatWindow);\n\n    // 创建标题栏\n    const titleBar = document.createElement('div');\n    titleBar.style.height = '40px';\n    titleBar.style.backgroundColor = '#4285f4';\n    titleBar.style.color = 'white';\n    titleBar.style.display = 'flex';\n    titleBar.style.alignItems = 'center';\n    titleBar.style.padding = '0 10px';\n    titleBar.style.cursor = 'move';\n    floatWindow.appendChild(titleBar);\n\n    // 标题栏文本\n    const titleText = document.createElement('div');\n    titleText.textContent = '网页控制器';\n    titleText.style.flexGrow = '1';\n    titleBar.appendChild(titleText);\n\n    // 创建控制按钮容器\n    const controls = document.createElement('div');\n    controls.style.display = 'flex';\n    controls.style.gap = '5px';\n    titleBar.appendChild(controls);\n\n    // 最大化按钮\n    const maxBtn = document.createElement('button');\n    maxBtn.textContent = '最大化';\n    maxBtn.style.padding = '2px 8px';\n    maxBtn.style.border = 'none';\n    maxBtn.style.borderRadius = '4px';\n    maxBtn.style.cursor = 'pointer';\n    controls.appendChild(maxBtn);\n\n    // 恢复按钮\n    const restoreBtn = document.createElement('button');\n    restoreBtn.textContent = '恢复';\n    restoreBtn.style.padding = '2px 8px';\n    restoreBtn.style.border = 'none';\n    restoreBtn.style.borderRadius = '4px';\n    restoreBtn.style.cursor = 'pointer';\n    restoreBtn.style.display = 'none'; // 默认隐藏\n    controls.appendChild(restoreBtn);\n\n    // 右侧布局按钮\n    const rightLayoutBtn = document.createElement('button');\n    rightLayoutBtn.textContent = '➡️';\n    rightLayoutBtn.style.padding = '2px 8px';\n    rightLayoutBtn.style.border = 'none';\n    rightLayoutBtn.style.borderRadius = '4px';\n    rightLayoutBtn.style.cursor = 'pointer';\n    controls.appendChild(rightLayoutBtn);\n\n    // 左侧布局按钮\n    const leftLayoutBtn = document.createElement('button');\n    leftLayoutBtn.textContent = '⬅️';\n    leftLayoutBtn.style.padding = '2px 8px';\n    leftLayoutBtn.style.border = 'none';\n    leftLayoutBtn.style.borderRadius = '4px';\n    leftLayoutBtn.style.cursor = 'pointer';\n    leftLayoutBtn.style.display = 'none';\n    controls.appendChild(leftLayoutBtn);\n\n    // 布局按钮点击事件\n    rightLayoutBtn.addEventListener('click', () => {\n        // 右侧布局\n        floatWindow.style.width = '40%';\n        floatWindow.style.height = '90%';\n        floatWindow.style.left = 'calc(60% - 50px)';\n        floatWindow.style.top = '5%';\n        rightLayoutBtn.style.display = 'none';\n        leftLayoutBtn.style.display = 'inline-block';\n    });\n\n    leftLayoutBtn.addEventListener('click', () => {\n        // 左侧布局\n        floatWindow.style.width = '40%';\n        floatWindow.style.height = '90%';\n        floatWindow.style.left = '50px';\n        floatWindow.style.top = '5%';\n        leftLayoutBtn.style.display = 'none';\n        rightLayoutBtn.style.display = 'inline-block';\n    });\n\n    // 关闭按钮\n    const closeBtn = document.createElement('button');\n    closeBtn.textContent = '×';\n    closeBtn.style.padding = '2px 8px';\n    closeBtn.style.border = 'none';\n    closeBtn.style.borderRadius = '4px';\n    closeBtn.style.cursor = 'pointer';\n    controls.appendChild(closeBtn);\n\n    // 创建URL输入框容器\n    const urlContainer = document.createElement('div');\n    urlContainer.style.padding = '10px';\n    urlContainer.style.display = 'flex';\n    urlContainer.style.gap = '5px';\n    floatWindow.appendChild(urlContainer);\n\n    // 添加预设翻译按钮\n    const sogouBtn = document.createElement('button');\n    sogouBtn.textContent = '狗';\n    sogouBtn.style.padding = '5px 10px';\n    sogouBtn.style.border = 'none';\n    sogouBtn.style.borderRadius = '4px';\n    sogouBtn.style.backgroundColor = '#34a853';\n    sogouBtn.style.color = 'white';\n    sogouBtn.style.cursor = 'pointer';\n    sogouBtn.title = '搜狗翻译';\n    urlContainer.appendChild(sogouBtn);\n\n    const baiduBtn = document.createElement('button');\n    baiduBtn.textContent = '百';\n    baiduBtn.style.padding = '5px 10px';\n    baiduBtn.style.border = 'none';\n    baiduBtn.style.borderRadius = '4px';\n    baiduBtn.style.backgroundColor = '#4285f4';\n    baiduBtn.style.color = 'white';\n    baiduBtn.style.cursor = 'pointer';\n    baiduBtn.title = '百度翻译';\n    urlContainer.appendChild(baiduBtn);\n\n    const youdaoBtn = document.createElement('button');\n    youdaoBtn.textContent = '有';\n    youdaoBtn.style.padding = '5px 10px';\n    youdaoBtn.style.border = 'none';\n    youdaoBtn.style.borderRadius = '4px';\n    youdaoBtn.style.backgroundColor = '#fbbc05';\n    youdaoBtn.style.color = 'white';\n    youdaoBtn.style.cursor = 'pointer';\n    youdaoBtn.title = '有道翻译';\n    urlContainer.appendChild(youdaoBtn);\n    // URL输入框\n    const urlInput = document.createElement('input');\n    urlInput.type = 'text';\n    urlInput.placeholder = '请先安装Hiframe插件，否则不能加载网页。输入网址后点击加载。';\n    urlInput.style.flexGrow = '1';\n    urlInput.style.padding = '5px';\n    urlInput.style.border = '1px solid #ddd';\n    urlInput.style.borderRadius = '4px';\n    urlContainer.appendChild(urlInput);\n\n    // 预设翻译按钮点击事件\n    sogouBtn.addEventListener('click', () => {\n        urlInput.value = 'fanyi.sogou.com';\n        loadBtn.click();\n    });\n\n    baiduBtn.addEventListener('click', () => {\n        urlInput.value = 'fanyi.baidu.com';\n        loadBtn.click();\n    });\n\n    youdaoBtn.addEventListener('click', () => {\n        urlInput.value = 'fanyi.youdao.com';\n        loadBtn.click();\n    });\n\n    // 加载按钮\n    const loadBtn = document.createElement('button');\n    loadBtn.textContent = '加载';\n    loadBtn.style.padding = '5px 10px';\n    loadBtn.style.border = 'none';\n    loadBtn.style.borderRadius = '4px';\n    loadBtn.style.backgroundColor = '#4285f4';\n    loadBtn.style.color = 'white';\n    loadBtn.style.cursor = 'pointer';\n    urlContainer.appendChild(loadBtn);\n\n    // 创建iframe\n    const iframe = document.createElement('iframe');\n    iframe.style.width = '100%';\n    iframe.style.height = 'calc(100% - 90px)';\n    iframe.style.border = 'none';\n    iframe.style.marginTop = '5px';\n    floatWindow.appendChild(iframe);\n\n    // 设置移动端视图\n    const setMobileView = () => {\n        const viewportMeta = document.createElement('meta');\n        viewportMeta.name = 'viewport';\n        viewportMeta.content = 'width=device-width, initial-scale=1.0';\n        iframe.contentDocument.head.appendChild(viewportMeta);\n    };\n\n    // 拖动功能\n    let isDragging = false;\n    let offsetX, offsetY;\n\n    titleBar.addEventListener('mousedown', (e) => {\n        isDragging = true;\n        offsetX = e.clientX - floatWindow.getBoundingClientRect().left;\n        offsetY = e.clientY - floatWindow.getBoundingClientRect().top;\n    });\n\n    document.addEventListener('mousemove', (e) => {\n        if (!isDragging) return;\n        floatWindow.style.left = (e.clientX - offsetX) + 'px';\n        floatWindow.style.top = (e.clientY - offsetY) + 'px';\n    });\n\n    document.addEventListener('mouseup', () => {\n        isDragging = false;\n    });\n\n    // 按钮点击事件\n    floatBtn.addEventListener('click', () => {\n        console.log('浮动按钮被点击');\n        floatWindow.style.display = floatWindow.style.display === 'none' ? 'block' : 'none';\n    });\n\n    // 最大化功能\n    maxBtn.addEventListener('click', () => {\n        const windowWidth = window.innerWidth * 0.9;\n        const windowHeight = window.innerHeight * 0.9;\n        floatWindow.style.width = windowWidth + 'px';\n        floatWindow.style.height = windowHeight + 'px';\n        floatWindow.style.left = (window.innerWidth - windowWidth) / 2 + 'px';\n        floatWindow.style.top = (window.innerHeight - windowHeight) / 2 + 'px';\n        maxBtn.style.display = 'none'; // 隐藏最大化按钮\n        restoreBtn.style.display = 'block'; // 显示恢复按钮\n    });\n\n    // 恢复功能\n    restoreBtn.addEventListener('click', () => {\n        floatWindow.style.width = '300px';\n        floatWindow.style.height = '400px';\n        restoreBtn.style.display = 'none'; // 隐藏恢复按钮\n        maxBtn.style.display = 'block'; // 显示最大化按钮\n    });\n\n    // 关闭功能\n    closeBtn.addEventListener('click', () => {\n        floatWindow.style.display = 'none';\n    });\n\n    // 加载网页\n    loadBtn.addEventListener('click', () => {\n        let url = urlInput.value.trim();\n        if (!url.startsWith('http://') && !url.startsWith('https://')) {\n            url = 'https://' + url;\n        }\n        iframe.src = url;\n\n        // 监听iframe加载完成事件\n        iframe.onload = function () {\n            try {\n                setMobileView();\n            } catch (e) {\n                console.log('无法设置移动端视图:', e);\n            }\n        };\n    });\n\n    // 创建缩放控制按钮\n    const resizeHandle = document.createElement('div');\n    resizeHandle.style.position = 'absolute';\n    resizeHandle.style.right = '0';\n    resizeHandle.style.bottom = '0';\n    resizeHandle.style.width = '20px';\n    resizeHandle.style.height = '20px';\n    resizeHandle.style.cursor = 'nwse-resize';\n    resizeHandle.style.background = 'linear-gradient(135deg, transparent 0%, transparent 50%, #4285f4 50%, #4285f4 100%)';\n    floatWindow.appendChild(resizeHandle);\n\n    // 缩放功能\n    let isResizing = false;\n    resizeHandle.addEventListener('mousedown', (e) => {\n        isResizing = true;\n        e.stopPropagation();\n    });\n\n    document.addEventListener('mousemove', (e) => {\n        if (!isResizing) return;\n        const newWidth = e.clientX - floatWindow.getBoundingClientRect().left;\n        const newHeight = e.clientY - floatWindow.getBoundingClientRect().top;\n        floatWindow.style.width = Math.max(200, newWidth) + 'px';\n        floatWindow.style.height = Math.max(200, newHeight) + 'px';\n\n        document.addEventListener('mouseup', () => {\n            if (isResizing) { isResizing = false;\n             // 清除页面中的文本选择\n             if (window.getSelection) {\n                 window.getSelection().removeAllRanges();\n             } else if (document.selection) {\n                 document.selection.empty();\n             }}\n         });\n    });\n\n    // 添加一些基础样式\n    GM_addStyle(`\n        button {\n            transition: all 0.2s;\n        }\n        button:hover {\n            opacity: 0.8;\n        }\n        input:focus {\n            outline: none;\n            border-color: #4285f4 !important;\n        }\n        .resize-handle {\n            position: absolute;\n            right: 0;\n            bottom: 0;\n            width: 20px;\n            height: 20px;\n            cursor: nwse-resize;\n            background: linear-gradient(135deg, transparent 0%, transparent 50%, #4285f4 50%, #4285f4 100%);\n        }\n    `);\n    //加载页面后提示更新信息\n//如果是iframe则不显示，如果标题不包含comfyui则不显示\nif (window.self !== window.top|| document.title.indexOf('comfyui')==-1) {\n    return;\n}else{\n    showToast(`25年5月更新：4.5 版本<br>1、可以编辑提示词了😄🎉🎉\n        <br>2、新增🛜按钮，点击后可以在页面内打开翻译网站，需要安装<a style=\"color:orange\" href=\"https://chromewebstore.google.com/detail/joibipdfkleencgfgbbncoheaekffdfn\" target=\"_blank\">Hiframe插件</a>。\n         <br>3、新增🈳按钮，点击后把所有空格替换成_ <br>\n        <br>如果您不想在所有网页启用此脚本，请把// @match   *://*/* 替换成您想要显示的网站`,8000);\n    }", "enabled": true}]