[{"id": "1753057587337", "name": "修改背景色", "match": "*://*.so.com/*", "code": "body{background:red}", "enabled": true}, {"id": "1753064539861", "name": "手机壳", "match": "*://*.haixingdiy.cn/*", "code": "// Your code here...\nvar dd = 'tbody > tr.order-table-row.ant-table-row.ng-star-inserted > td:nth-child(5) > div > app-input-address > div.edit > span:nth-child(2)'\n        //'table > tbody > tr > td:nth-child(4) > app-input-address > span > span > img'\nvar ming = 'table > tbody > tr.order-table-tr.ant-table-row.ng-star-inserted > td:nth-child(2) > div > div > div.v-goods-item-goodsname > span:nth-child(2)'\n//'table > tbody > tr > td:nth-child(2) > div > div> div.gig-vertical.v-goods-item-goodsname > span:nth-child(2)'\nvar im = 'table > tbody > tr > td:nth-child(2) > div > div > div:nth-child(1) > div > img'\nvar v0 = 'ant-tabs-tab-active'\nvar inp = '  td  input[class*=\"checkbox\"]'\nvar kd = 'body > app-root > app-dt > app-layout > nz-layout > nz-content > nz-spin > div > app-works-manage > div > div > nz-layout > nz-content.cont.ant-layout-content > nz-affix > div > div > button:nth-child(3)'\nvar tb = 'label input[class*=\"radio\"] '\nvar 初始数量 = 0\n\nsetInterval(() => {\n    //if (document.querySelectorAll(' div.ant-tabs-bar.ant-tabs-top-bar.ant-tabs-default-bar.ng-star-inserted > div > div > div > div > div:nth-child(1) > div:nth-child(6)')[0].className.indexOf(v0) == -1) {\n        var 数量 = document.querySelectorAll(dd).length\n        初始数量 = document.getElementsByClassName('确认手机型号').length\n\n        if (数量 != 初始数量) {\n            初始数量 = 数量;\n            console.log(数量);\n            刷新()\n            //初始数量 = document.getElementsByClassName('确认手机型号').length\n        }\n    //}\n}, 1000)\nfunction 刷新() {\n    console.log('刷新');\n    for (var i = 0; i < 初始数量; i++) {\n        document.querySelectorAll(dd)[i].style.display = \"none\"\n\n        var 型号 = document.querySelectorAll(ming)[i].innerText;\n        const 确认手机型号 = document.createElement('button');\n        确认手机型号.innerText = '是【' + 型号 + '】吗？';\n        确认手机型号.className = '确认手机型号';\n        确认手机型号.dataset.id = i\n        确认手机型号.addEventListener('click', (item) => {\n            document.getElementsByClassName('不是')[item.target.dataset.id].style.display = \"none\"\n            document.getElementsByClassName('拼多多')[item.target.dataset.id].style.display = \"block\"\n            document.getElementsByClassName('淘宝')[item.target.dataset.id].style.display = \"block\"\n            document.getElementsByClassName('确认手机型号')[item.target.dataset.id].style.display = \"none\"\n        })\n\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(确认手机型号)\n        const not = document.createElement('button');\n        not.innerText = '不是';\n        not.className = '不是';\n        not.dataset.id = i\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(not)\n        not.addEventListener('click', (item) => {\n            console.log(im, item.target.dataset.id);\n            document.querySelectorAll(im)[item.target.dataset.id].click();\n        })\n        const 确认平台1 = document.createElement('button');\n        确认平台1.innerText = '拼多多';\n        确认平台1.className = '拼多多';\n        确认平台1.style.display = \"none\";\n        //确认平台1.style.display=\"none\";\n        确认平台1.dataset.id = i\n        确认平台1.addEventListener('click', (item) => {\n            document.querySelectorAll(dd)[item.target.dataset.id].style.display = \"block\";\n            document.querySelectorAll(dd)[item.target.dataset.id].click();\n            document.getElementsByClassName('淘宝')[item.target.dataset.id].style.display = \"none\"\n            document.getElementsByClassName('拼多多')[item.target.dataset.id].style.display = \"none\"\n        })\n        const 确认平台2 = document.createElement('button');\n        确认平台2.innerText = '淘宝';\n        确认平台2.className = '淘宝';\n        确认平台2.style.display = \"none\";\n        确认平台2.dataset.id = i\n        确认平台2.addEventListener('click', (item) => {\n            document.querySelectorAll(dd)[item.target.dataset.id].style.display = \"block\";\n            document.querySelectorAll(inp)[item.target.dataset.id].click();\n            setTimeout(function () {\n                document.querySelector(kd).click();\n            }\n                , 100)\n            setTimeout(function () {\n                document.querySelectorAll(tb)[3].parentNode.parentNode.style.border = \"1px solid #f00\";\n                document.querySelectorAll(tb)[3].click();\n\n            }\n                , 300)\n            document.getElementsByClassName('淘宝')[item.target.dataset.id].style.display = \"none\"\n\n        }\n        )\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(确认平台1)\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(确认平台2)\n    }\n}\nvar 新style = document.createElement('style');\n新style.innerHTML = `\n    .淘宝{background-color:#ff9000;color:#fff;border:1px solid #ff9000;border-radius:3px;padding:2px 5px;cursor:pointer;font-size:12px;}\n    .确认手机型号,.拼多多{background-color:#ff0030;color:#fff;border:1px solid #ff0030;border-radius:3px;padding:2px 5px;cursor:pointer;font-size:12px;}\n    .提醒:hover{color:white;bakckground_color:green;}\n     #floatingDiv {\n            position: absolute;\n            width: 100px;\n            height: 100px;\n            background-color: red;\n            color: white;\n            text-align: center;\n            padding: 10px;\n            font-size: 16px;\n            border-radius: 50%;\n        }\n    `\n\n/*ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(2),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(4),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(5),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(7),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(8){ top: calc(100vh - 38px);    background: #37b3b0;}\n nz-content.v-search.ant-layout-content{height:10px!important;background:#666;overflow:HIDDEN;margin-top:-30px!important;position:absolute}\n    nz-content.v-search.ant-layout-content:hover{height:unset!important;background:unset;}\n  */\ndocument.head.appendChild(新style);\n`var 提醒=document.createElement('div')\n//提醒.style.position='fixed';\n提醒.style.padding='5px';\n提醒.style.left='0px';\n提醒.style.top='70%';\n提醒.style.backgroundColor='red';\n提醒.innerHTML=\"注意是不是淘宝订单！抖音订单！\"\n提醒.style`\n\n\n  // 创建一个 div 元素\n        var floatingDiv = document.createElement('div');\n        floatingDiv.id = 'floatingDiv';\n        floatingDiv.textContent = '注意是不是淘宝订单！抖音订单！';\n\n        // 将 div 添加到文档的 body 中\n        document.body.appendChild(floatingDiv);\n\n        // 获取 div 的宽度和高度\n        var divWidth = floatingDiv.offsetWidth;\n        var divHeight = floatingDiv.offsetHeight;\n\n        // 初始化 div 的位置\n        var posX = Math.random() * (window.innerWidth - divWidth);\n        var posY = Math.random() * (window.innerHeight - divHeight);\n\n        // 初始化移动速度（随机生成）\n        var speedX = (Math.random() - 0.5) * 4; // 水平速度\n        var speedY = (Math.random() - 0.5) * 4; // 垂直速度\n\n        // 更新 div 位置的函数\n        function updatePosition() {\n            // 更新位置\n            posX += speedX;\n            posY += speedY;\n\n            // 检测是否碰到左右边界\n            if (posX <= 0 || posX >= window.innerWidth - divWidth) {\n                speedX = -speedX; // 反转水平方向\n            }\n\n            // 检测是否碰到上下边界\n            if (posY <= 0 || posY >= window.innerHeight - divHeight) {\n                speedY = -speedY; // 反转垂直方向\n            }\n\n            // 设置 div 的新位置\n            floatingDiv.style.left = posX + 'px';\n            floatingDiv.style.top = posY + 'px';\n        }\n\n        // 每 16 毫秒更新一次位置（约 60 帧/秒）\n        setInterval(updatePosition, 16);\ndocument.body.appendChild(floatingDiv);", "enabled": true}, {"id": "1753077602513", "name": "comfyui", "match": "http://192.168.101.11:8168/", "code": "console.log('comfyui加载');\nGM_addStyle(`\n            .prompt-manager-menu, .prompt-manager-modal, .prompt-manager-modal2 {\n                transition: background-color 0.3s ease;\n            }\n            \n        .toast-container {\n                position: fixed;\n                bottom: 40%;\n                left: 50%;\n                transform: translateX(-50%);\n                z-index: 10001;\n                pointer-events: none;\n            }\n            .toast {\n                background: rgba(0, 0, 0, 0.8);\n                color: white;\n                padding: 10px 20px;\n                border-radius: 4px;\n                margin-top: 10px;\n                animation: fadeInOut 3s ease-in-out;\n            }\n        \n        @keyframes fadeIn {\n            from { opacity: 0; transform: translateY(20px); }\n            to { opacity: 1; transform: translateY(0); }\n        }\n\n        /* 通用颜色变量 - 亮色模式 */\n        :root {\n             --pm-text-color: #ddd;\n            --pm-border-color: #555;\n            --pm-primary-color: #3a80d2;\n            --pm-primary-hover: #2a70c2;\n            --pm-danger-color: #c73c2e;\n            --pm-secondary-color: #777;\n            --pm-border-light: #444;\n        }\n        \n        /* 深色模式 */\n        .prompt-manager-dark-mode {\n            --pm-text-color: #ddd;\n            --pm-border-color: #555;\n            \n            --pm-primary-color: #3a80d2;\n            --pm-primary-hover: #2a70c2;\n            --pm-danger-color: #c73c2e;\n            --pm-secondary-color: #777;\n            --pm-border-light: #444;\n        }\n        \n        .prompt-manager-menu {\n            position: fixed;\n            background: var(--pm-bg-color);\n            border: 1px solid var(--pm-border-color);\n            border-radius: 4px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n            padding: 8px;\n            z-index: 9999;\n            min-width: 150px;\n            max-height: 400px;\n            \n            color: var(--pm-text-color);\n        }\n        \n        .prompt-manager-toolbar {\n            display: flex;\n            justify-content: space-between;\n            padding-bottom: 8px;\n            margin-bottom: 8px;\n        }\n        \n        .prompt-manager-toolbar button {\n            background: none;\n            border: none;\n             margin:0;\n            cursor: pointer;\n            font-size: 14px;\n            padding: 4px;\n            color: var(--pm-text-color);\n        }\n        \n        .prompt-manager-category {\n            padding: 0px 8px;\n            font-size: 12px;\n            cursor: pointer;\n            margin: 2px 0;\n            color: var(--pm-text-color);\n        }\n        \n        .prompt-manager-category:hover {\n            background: var(--pm-hover-color);\n            border-radius: 4px;\n        }\n        #div1{\n        position: absolute;\n        top: 0;\n        left: 0;\n        background: var(--pm-bg-color);\n        border: 1px solid #666;\n        }\n        .prompt-manager-modal {\n            padding: 10px;\n            min-height:100% ;\n            z-index: 10000;\n            min-width: 300px;\n            max-width: 520px;\n            color: var(--pm-text-color);\n            display: flex\n;\n    flex-direction: column;\n    justify-content: space-between;\n            }\n\n .prompt-manager-modal2{\n    position: fixed;\n    top: 50%;\n    left: 8px;\n    transform: translateY(-50%);\n    background: var(--pm-bg-color);\n    padding: 20px;\n    border-radius: 8px;\n    box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n    z-index: 10000;\n    min-width: 300px;\n    max-width: 520px;\n    color: var(--pm-text-color);\n}\n            \n            .settings-section {\n                padding-bottom: 5px;\n                border-bottom: 1px solid var(--pm-border-light);\n            }\n            \n            .settings-section:last-child {\n                border-bottom: none;\n                margin-bottom: 15px;\n            }\n            \n            .settings-section h4 {\n                margin: 0 0 10px 0;\n                color: var(--pm-text-color);\n            }\n            \n            \n        \n        .prompt-manager-input {\n            width: 100%;\n            padding: 8px;\n            margin: 8px 0;\n            background:#222;\n            border: 1px solid #ddd;\n            border-radius: 4px;\n            color: var(--pm-text-color);\n        }\n        \n        .prompt-manager-button {\n            padding: 8px 16px;\n            margin: 4px;\n            border: none;\n            border-radius: 4px;\n            background: var(--pm-primary-color);\n            color: white;\n            cursor: pointer;\n        }\n        \n        .prompt-manager-button:hover {\n            background: var(--pm-primary-hover);\n        }\n        \n        .prompt-manager-button.danger {\n            background: var(--pm-danger-color);\n        }\n        \n        .prompt-manager-button.secondary {\n            background: #999;\n        }\n        \n        .prompt-manager-list {\n            max-height: 300px;\n            overflow-y: auto;\n            margin: 10px 0;\n            display: flex;\n            flex-wrap: wrap;\n        }\n        \n        .prompt-manager-list-item {\n            background-color:#222;\n            margin:2px;\n            border-radius: 5px;\n            cursor: pointer;\n            color: var(--pm-text-color);\n            max-width:120px;\n            padding:1px 7px;\n        }\n        \n        .prompt-manager-list-item:hover {\n            background: var(--pm-hover-color);\n        }\n    `);\n\n    // 数据结构\n    let data = {\n        categories: [],  // [{id: string, name: string, order: number}]\n        prompts: [],    // [{id: string, text: string, alias: string, categoryId: string, favorite: boolean, useCount: number, isTemplate: boolean, lastUsed: string}]\n        lastUsedCategoryId: null,  // 记录上次使用的分组ID\n        backups: [],    // 自动备份历史 [{date: string, data: Object}]\n        settings: {\n            darkMode: false,       // 深色模式\n            modalPosition: null,   // 模态框位置\n            sortBy: 'time',       // 排序方式：time（添加时间）, name（名称）, usage（使用次数）\n            showFavoriteOnly: false, // 是否只显示收藏的提示词\n            enableContextMenu: true, // 启用右键菜单\n            autoBackup: true,      // 自动备份\n            backupInterval: 7,     // 备份间隔（天）\n            maxBackups: 5,         // 最大备份数量\n            categorySort: 'custom',  // 分类排序方式：custom（自定义）, name（名称）\n            panelOpacity: 0.9      // 面板透明度，默认90%\n        }\n    };\n\n    // 初始化数据\n    function initData() {\n        const savedData = GM_getValue('promptManagerData');\n        if (savedData) {\n            try {\n                data = JSON.parse(savedData);\n\n                // 兼容旧版本数据结构\n                if (!data.settings) {\n                    data.settings = {\n                        darkMode: false,\n                        modalPosition: null,\n                        sortBy: 'time',\n                        showFavoriteOnly: false,\n                        enableContextMenu: true,\n                        autoBackup: true,\n                        backupInterval: 7\n                    };\n                }\n\n                if (!data.backups) {\n                    data.backups = [];\n                }\n\n                // 检查是否需要创建备份\n                checkBackup();\n            } catch (e) {\n                console.error('数据解析错误', e);\n                // 尝试恢复最近的备份\n                tryRestore();\n            }\n        }\n    }\n\n    // 保存数据\n    function saveData() {\n        try {\n            GM_setValue('promptManagerData', JSON.stringify(data));\n        } catch (e) {\n            console.error('保存数据失败', e);\n            alert('保存数据失败，请检查浏览器存储空间或导出备份数据');\n        }\n    }\n\n    // 检查是否需要创建备份\n    function checkBackup() {\n        if (!data.settings.autoBackup) return;\n\n        const now = new Date();\n        const lastBackup = data.backups[0]?.date ? new Date(data.backups[0].date) : null;\n\n        // 如果没有备份或最后一次备份超过指定天数\n        if (!lastBackup || (now - lastBackup) / (1000 * 60 * 60 * 24) > data.settings.backupInterval) {\n            // 创建备份，不包含之前的备份数据\n            const backupData = JSON.parse(JSON.stringify(data));\n            delete backupData.backups;\n\n            // 添加新备份到开头\n            data.backups.unshift({\n                date: now.toISOString(),\n                data: backupData\n            });\n\n            // 保留最多5个备份\n            if (data.backups.length > 5) {\n                data.backups = data.backups.slice(0, 5);\n            }\n\n            saveData();\n        }\n    }\n\n    // 尝试从备份恢复\n    function tryRestore() {\n        const savedBackups = GM_getValue('promptManagerBackups');\n        if (savedBackups) {\n            try {\n                const backups = JSON.parse(savedBackups);\n                if (backups.length > 0) {\n                    // 使用最新的备份\n                    data = backups[0].data;\n                    alert('检测到数据损坏，已自动从最近备份恢复');\n                    return;\n                }\n            } catch (e) {\n                console.error('备份解析错误', e);\n            }\n        }\n\n        // 如果没有可用备份，重置数据\n        data = {\n            categories: [],\n            prompts: [],\n            lastUsedCategoryId: null,\n            backups: [],\n            settings: {\n                darkMode: false,\n                modalPosition: null,\n                sortBy: 'time',\n                showFavoriteOnly: false,\n                enableContextMenu: true,\n                autoBackup: true,\n                backupInterval: 7\n            }\n        };\n        alert('无法恢复数据，已重置为默认设置');\n    }\n\n    // 初始化数据\n    // = GM_getValue('promptManagerData');\n    // 显示提示信息\n    function showToast(message, duration = 3000) {\n        let container = document.querySelector('.toast-container');\n        if (!container) {\n            container = document.createElement('div');\n            container.className = 'toast-container';\n            document.body.appendChild(container);\n        }\n\n        const toast = document.createElement('div');\n        toast.className = 'toast';\n        toast.innerHTML = message;\n        container.appendChild(toast);\n\n        setTimeout(() => {\n            toast.remove();\n            if (container.children.length === 0) {\n                container.remove();\n            }\n        }, duration);\n    }\n\n    // 创建浮动菜单\n    function createFloatingMenu(x, y) {\n        GM_addStyle(`\n            \n                        .prompt-manager-menu, .prompt-manager-modal, .prompt-manager-modal2 {\n                         background-color: rgba(51, 51, 51, ${data.settings.panelOpacity});\n            \n            }\n            `)\n        const menu = document.createElement('div');\n        menu.className = 'prompt-manager-menu';\n        menu.style.left = x + 'px';\n        menu.style.top = y + 'px';\n        console.log(x);\n        const div1 = document.createElement('div');\n        div1.style.borderRadius = '8px 0 0 8px';\n        div1.style.transform = 'translateX(-100%)';\n        div1.style.borderRight = '0';\n        div1.style.minHeight = '100%';\n        menu.appendChild(div1);\n        const div2 = document.createElement('div');\n        menu.appendChild(div2);\n        div2.id = 'div2';\n        const div3 = document.createElement('div');\n        div3.style.borderRadius = '0px 8px 8px 0';\n        div3.style.borderLeft = '0';\n        menu.appendChild(div3);\n        div3.style.transform = 'translateX(148px) translateY(100px)';\n        div3.style.minHeight = 'calc(100% - 100px)';\n        if (x > 400) {\n            div1.id = 'div1';\n        } else {\n            div3.id = 'div1';\n        }\n\n        // 工具栏\n        const toolbar = document.createElement('div');\n        toolbar.className = 'prompt-manager-toolbar';\n        makeDraggable(menu);\n\n        const settingsBtn = document.createElement('button');\n        settingsBtn.innerHTML = '⚙️';\n        settingsBtn.onclick = showSettings;\n        \n\n        const 空格 = document.createElement('button');\n        空格.innerHTML = '🈳';\n        空格.className = 'prompt-space'\n        空格.onclick = 替换空格;\n        //添加鼠标浮动提示\n        空格.title = '替换空格为_';\n        空格.onmouseover = function () {\n            //提示信息\n            showToast('替换空格为_',1000);\n        }\n\n        const 浏览器 = document.createElement('button');\n        浏览器.innerHTML = '🛜';\n        浏览器.className = 'prompt-space'\n        浏览器.onclick = 显示浏览器面板;\n        //添加鼠标浮动提示\n        浏览器.title = '开启内置浏览器，需要安装hiframe插件';\n        浏览器.onmouseover = function () {\n            //提示信息\n            showToast('开启内置浏览器，需要安装hiframe插件',1000);\n        }\n        const addCategoryBtn = document.createElement('button');\n        addCategoryBtn.innerHTML = '+🗂️';\n        addCategoryBtn.onclick = () => showAddCategoryDialog();\n        addCategoryBtn.onmouseover = function () {\n            //提示信息\n            showToast('新建分组',1000);\n        }\n\n        toolbar.appendChild(settingsBtn);\n        toolbar.appendChild(空格);\n        toolbar.appendChild(浏览器);\n        toolbar.appendChild(addCategoryBtn);\n        div2.appendChild(toolbar);\n\n        // 分类列表\n        data.categories.forEach(category => {\n            const categoryDiv = document.createElement('div');\n            categoryDiv.className = 'prompt-manager-category';\n            categoryDiv.textContent = category.name;\n            categoryDiv.onclick = () => showPromptList(category.id);\n            div2.appendChild(categoryDiv);\n        });\n\n        document.body.appendChild(menu);\n        return menu;\n    }\n\n    // 显示添加分类对话框\n    function showAddCategoryDialog() {\n        const modal = createModal2()//create浮动Modal();\n        modal.innerHTML = `\n            <h3>新建分类</h3>\n            <input type=\"text\" class=\"prompt-manager-input\" placeholder=\"输入分类名称\">\n            <button class=\"prompt-manager-button\">确定</button>\n            <button class=\"prompt-manager-button secondary\">取消</button>\n        `;\n        makeDraggable(modal);\n        const input = modal.querySelector('input');\n        const [confirmBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        confirmBtn.onclick = () => {\n            if (input.value.trim()) {\n                const newCategory = {\n                    id: Date.now().toString(),\n                    name: input.value.trim()\n                };\n                data.categories.push(newCategory);\n                saveData();\n                closeModal();\n                // 刷新菜单\n                const oldMenu = document.querySelector('.prompt-manager-menu');\n                if (oldMenu) {\n                    const rect = oldMenu.getBoundingClientRect();\n                    oldMenu.remove();\n                    createFloatingMenu(rect.left, rect.top);\n                }\n            }\n        };\n\n        cancelBtn.onclick = closeModal;\n    }\n\n    // 显示提示词列表\n    function showPromptList(categoryId) {\n        try {\n            document.getElementsByClassName('prompt-manager-modal')[0].remove();\n        } catch (e) { }\n\n        console.log(document.getElementById('div1').innerHTML)\n        const prompts = data.prompts.filter(p => p.categoryId === categoryId);\n        const category = data.categories.find(c => c.id === categoryId);\n\n        const modal = createModal();\n        modal.innerHTML = `\n            <div style=\"display: flex; justify-content: space-between; align-items: center;height:30px;\">\n                <h3>${category.name}</h3>\n                <div class=\"prompt-toolbar\">\n                \n                \n                <button class=\"toolbar-btn\" id=\"share-category\" title=\"分享分类\">📤</button>\n            </div>\n            </div>\n            \n            <div class=\"batch-actions\">\n                <button class=\"prompt-manager-button\" id=\"move-selected\">移动到分类</button>\n                <button class=\"prompt-manager-button danger\" id=\"delete-selected\">删除选中</button>\n                <button class=\"prompt-manager-button secondary\" id=\"cancel-select\">取消选择</button>\n            </div>\n            <div class=\"prompt-manager-list\">\n                ${prompts.map(prompt => `\n                    <div class=\"prompt-manager-list-item\" data-id=\"${prompt.id}\" data-text=\"${prompt.text}\" data-alias=\"${prompt.alias || ''}\">\n                        \n                        \n                        <span class=\"prompt-text\" style=\"font-size:13px\">${prompt.alias || prompt.text}</span>\n                        <span class=\"prompt-actions\">\n                            \n                            <button class=\"prompt-edit\" style=\"display:none\" title=\"编辑\">✏️</button>\n                            <button class=\"prompt-delete\" style=\"font-size:13px;margin-right:-2px;display:none\" title=\"删除\">❌</button>\n                        </span>\n                    </div>\n                `).join('')}\n            </div>\n            <div class=\"prompt-manager-toolbar\" style=\" border-bottom: none; padding-bottom: 0;\">\n            <button class=\"prompt-manager-button\" style=\"margin-left: 0;\">编辑/删除分类</button>\n                <button class=\"prompt-manager-button\" style=\"margin-left: 0;background: var(--pm-primary-color);\">管理提示词</button>\n            </div>\n        `;\n\n        // 添加CSS样式\n        const style = document.createElement('style');\n        style.textContent = `\n            .prompt-manager-list-item {\n                display: flex;\n                flex-wrap: wrap;\n                flex-direction: row;\n                justify-content: space-between;\n                align-items: center;\n                position: relative;\n                padding-left: 5px;\n                \n            }\n            .prompt-text {\n                flex: 1;\n                overflow: hidden;\n                text-overflow: ellipsis;\n                white-space: nowrap;\n                user-select: none;\n            }\n            .prompt-favorite {\n                background: none;\n                border: none;\n                cursor: pointer;\n                font-size: 16px;\n                color: gold;\n            }\n            .prompt-checkbox {\n                position: absolute;\n                left: 5px;\n                width: 16px;\n                height: 16px;\n                cursor: pointer;\n                opacity: 0;\n            }\n            .prompt-checkbox-custom {\n                position: absolute;\n                left: 5px;\n                width: 16px;\n                height: 16px;\n                border: 2px solid var(--pm-text-color);\n                border-radius: 3px;\n                pointer-events: none;\n            }\n            .prompt-checkbox:checked + .prompt-checkbox-custom::after {\n                content: '✓';\n                position: absolute;\n                top: 50%;\n                left: 50%;\n                transform: translate(-50%, -50%);\n                color: var(--pm-primary-color);\n            }\n            .prompt-manager-list-item.selected {\n                background-color: rgba(74, 144, 226, 0.1);\n            }\n            .batch-actions {\n                display: none;\n                margin-bottom: 10px;\n                gap: 8px;\n                flex-wrap: wrap;\n            }\n            .batch-actions.visible {\n                display: flex;\n            }\n            /* 操作提示和反馈 */\n            \n            @keyframes fadeInOut {\n                0% { opacity: 0; transform: translateY(20px); }\n                10% { opacity: 1; transform: translateY(0); }\n                90% { opacity: 1; transform: translateY(0); }\n                100% { opacity: 0; transform: translateY(-20px); }\n            }\n            \n            .prompt-actions {\n                opacity: 0.3;\n                transition: opacity 0.2s;\n            }\n            .prompt-manager-list-item:hover .prompt-actions {\n                opacity: 1;\n            }\n            .prompt-delete, .prompt-edit {\n                background: none;\n                border: none;\n                cursor: pointer;\n                font-size: 10px;\n            }\n            .prompt-manager-list-item.active {\n                background-color: rgba(74, 144, 226, 0.1);\n            }\n            .empty-list-message {\n                padding: 20px;\n                text-align: center;\n                color: #999;\n            }\n            .prompt-toolbar {\n                display: flex;\n                gap: 8px;\n            }\n            .toolbar-btn {\n                background: none;\n                border: none;\n                cursor: pointer;\n                padding: 4px 8px;\n                font-size: 16px;\n                color: var(--pm-text-color);\n                border-radius: 4px;\n            }\n            .toolbar-btn:hover {\n                background: var(--pm-hover-color);\n            }\n            .search-bar {\n                position: relative;\n            }\n        `;\n        document.head.appendChild(style);\n\n\n        // 排序函数\n        const sortPrompts = (items) => {\n            return Array.from(items).sort((a, b) => {\n                const aPrompt = data.prompts.find(p => p.id === a.dataset.id);\n                const bPrompt = data.prompts.find(p => p.id === b.dataset.id);\n\n                if (!aPrompt || !bPrompt) return 0;\n\n                switch (data.settings.sortBy) {\n                    case 'name':\n                        return (aPrompt.alias || aPrompt.text).localeCompare(bPrompt.alias || bPrompt.text);\n                    case 'usage':\n                        return (bPrompt.useCount || 0) - (aPrompt.useCount || 0);\n                    default: // 'time'\n                        return bPrompt.id.localeCompare(aPrompt.id);\n                }\n            });\n        };\n\n\n\n\n\n\n        // 提示词点击事件\n        const items = modal.querySelectorAll('.prompt-manager-list-item');\n        items.forEach(item => {\n\n            // 提示词文本点击事件\n            const textSpan = item.querySelector('.prompt-text');\n            textSpan.onclick = (e) => {\n                e.stopPropagation(); // 阻止事件冒泡\n\n                const promptId = item.dataset.id;\n                const prompt = data.prompts.find(p => p.id === promptId);\n\n\n                if (!prompt) {\n                    alert('提示词数据已损坏，请尝试刷新页面');\n                    return;\n                }\n\n                let text = prompt.text;\n\n                // 如果是模板，处理变量替换\n                if (prompt.isTemplate) {\n                    // 提取所有变量\n                    const variables = [];\n                    const regex = /\\{\\{([^}]+)\\}\\}/g;\n                    let match;\n\n                    while ((match = regex.exec(text)) !== null) {\n                        if (!variables.includes(match[1])) {\n                            variables.push(match[1]);\n                        }\n                    }\n\n                    // 如果有变量，提示用户输入\n                    if (variables.length > 0) {\n                        // 收集变量值\n                        const values = {};\n                        for (const variable of variables) {\n                            const value = prompt(`请输入\"${variable}\"的值：`);\n                            if (value === null) {\n                                // 用户取消了输入\n                                return;\n                            }\n                            values[variable] = value;\n                        }\n\n                        // 替换所有变量\n                        for (const [variable, value] of Object.entries(values)) {\n                            const regex = new RegExp(`\\\\{\\\\{${variable}\\\\}\\\\}`, 'g');\n                            text = text.replace(regex, value);\n                        }\n                    }\n                }\n\n                // 检查文本结尾是否是逗号，如果不是则添加逗号\n                if (!text.endsWith(',')) {\n                    text += ',';\n                }\n\n                // 记录最近聚焦的输入元素\n                let targetInput = window.lastFocusedInput;\n\n                // 如果没有记录或记录的元素不再有效，则尝试查找页面上可见的输入框\n                if (!targetInput || !document.body.contains(targetInput)) {\n                    // 查找页面上所有的输入框和文本框\n                    const inputs = document.querySelectorAll('input[type=\"text\"], textarea');\n                    targetInput = Array.from(inputs).find(input => {\n                        // 检查元素是否可见且可编辑\n                        const style = window.getComputedStyle(input);\n                        const rect = input.getBoundingClientRect();\n                        return style.display !== 'none' &&\n                            !input.readOnly &&\n                            !input.disabled &&\n                            rect.width > 0 &&\n                            rect.height > 0;\n                    });\n                }\n\n                if (targetInput) {\n                    // 将文本插入到输入框\n                    targetInput.focus();\n                    const start = targetInput.selectionStart || 0;\n                    const end = targetInput.selectionEnd || 0;\n                    targetInput.value = targetInput.value.substring(0, start) + text + targetInput.value.substring(end);\n                    targetInput.setSelectionRange(start + text.length, start + text.length);\n\n                    // 触发输入事件，确保动态网页能检测到内容变化\n                    const inputEvent = new Event('input', { bubbles: true });\n                    targetInput.dispatchEvent(inputEvent);\n\n                    // 更新使用次数\n                    const promptId = item.dataset.id;\n                    const promptIndex = data.prompts.findIndex(p => p.id === promptId);\n                    if (promptIndex !== -1) {\n                        data.prompts[promptIndex].useCount = (data.prompts[promptIndex].useCount || 0) + 1;\n                        saveData();\n                    }\n                }\n                //closeModal();\n            };\n            //给prompt-edit添加动作，修改提示词\n            const editBtn = item.querySelector('.prompt-edit');\n            editBtn.onclick = (e) => { \n                    //e.stopPropagation();\n                    console.log('editBtn clicked');\n                    const promptId = item.dataset.id;\n                    const prompt = data.prompts.find(p => p.id === promptId);\n                    if (prompt) {\n                        showEditPromptDialog(promptId, prompt.text, prompt.alias, prompt.categoryId);\n                    }\n                \n            }\n\n            // 删除按钮点击事件\n            const deleteBtn = item.querySelector('.prompt-delete');\n            deleteBtn.onclick = (e) => {\n                e.stopPropagation();\n                const promptId = item.dataset.id;\n\n                if (confirm('确定要删除这个提示词吗？')) {\n                    try {\n                        data.prompts = data.prompts.filter(p => p.id !== promptId);\n                        saveData();\n                        item.remove();\n\n                        // 检查列表是否为空\n                        const listItems = modal.querySelectorAll('.prompt-manager-list-item:not([style*=\"display: none\"])');\n                        const listContainer = modal.querySelector('.prompt-manager-list');\n\n                        if (listItems.length === 0) {\n                            const message = data.settings.showFavoriteOnly ? '没有收藏的提示词' : '没有提示词';\n                            listContainer.innerHTML = `<div class=\"empty-list-message\">${message}</div>`;\n                        }\n\n                        showToast('提示词已删除');\n                    } catch (error) {\n                        console.error('删除提示词失败', error);\n                        showToast('删除失败，请重试');\n                    }\n                }\n            };\n        });\n\n        // 工具栏按钮\n        const shareCategoryBtn = modal.querySelector('#share-category');\n        const [editCategoryBtn, edititem, closeBtn] = modal.querySelectorAll('.prompt-manager-toolbar button');\n\n\n\n        // 分享分类按钮点击事件\n        shareCategoryBtn.onclick = () => {\n            const category = data.categories.find(c => c.id === categoryId);\n            if (!category) return;\n\n            const prompts = data.prompts.filter(p => p.categoryId === categoryId);\n            if (prompts.length === 0) {\n                showToast('当前分类没有提示词可分享');\n                return;\n            }\n\n            const shareModal = createModal2()//create浮动Modal();\n            shareModal.innerHTML = `\n                <h3>分享分类：${category.name}</h3>\n                <div style=\"margin-bottom: 15px;\">\n                    <label style=\"display: block; margin-bottom: 10px;\">选择分享格式：</label>\n                    <select class=\"prompt-manager-input\" id=\"share-format\">\n                        <option value=\"text\">纯文本格式</option>\n                        <option value=\"markdown\">Markdown格式</option>\n                        <option value=\"json\">JSON格式</option>\n                    </select>\n                </div>\n                <div style=\"margin-bottom: 15px;\">\n                    <label style=\"display: block; margin-bottom: 10px;\">分享内容预览：</label>\n                    <textarea class=\"prompt-manager-input\" style=\"height: 200px;\" readonly></textarea>\n                </div>\n                <div style=\"display: flex; gap: 10px;\">\n                    <button class=\"prompt-manager-button\" id=\"copy-share\">复制内容</button>\n                    <button class=\"prompt-manager-button\" id=\"download-share\">下载文件</button>\n                    \n                </div>\n            `;\n            makeDraggable(shareModal);\n            const formatSelect = shareModal.querySelector('#share-format');\n            const previewArea = shareModal.querySelector('textarea');\n            const copyBtn = shareModal.querySelector('#copy-share');\n            const downloadBtn = shareModal.querySelector('#download-share');\n            const closeShareBtn = shareModal.querySelector('.prompt-manager-button.secondary');\n\n            // 生成分享内容\n            const generateContent = (format) => {\n                switch (format) {\n                    case 'text':\n                        return prompts.map(p => {\n                            const text = p.text.replace(/\\n/g, ' ');\n                            return p.alias ? `${text}|${p.alias}` : text;\n                        }).join('\\n');\n                    case 'markdown':\n                        return `# ${category.name}\\n\\n` + prompts.map(p => {\n                            const text = p.text.replace(/\\n/g, ' ');\n                            return `- ${p.alias ? `**${p.alias}**: ` : ''}${text}`;\n                        }).join('\\n');\n                    case 'json':\n                        return JSON.stringify({\n                            category: category.name,\n                            prompts: prompts.map(p => ({\n                                text: p.text,\n                                alias: p.alias || null\n                            }))\n                        }, null, 2);\n                }\n            };\n\n            // 更新预览\n            const updatePreview = () => {\n                previewArea.value = generateContent(formatSelect.value);\n            };\n\n            formatSelect.onchange = updatePreview;\n            updatePreview();\n\n            // 复制内容\n            copyBtn.onclick = async () => {\n                try {\n                    await navigator.clipboard.writeText(previewArea.value);\n                    showToast('内容已复制到剪贴板');\n                } catch (error) {\n                    console.error('复制失败', error);\n                    // 降级方案\n                    previewArea.select();\n                    document.execCommand('copy');\n                    showToast('内容已复制到剪贴板');\n                }\n            };\n\n            // 下载文件\n            downloadBtn.onclick = () => {\n                const format = formatSelect.value;\n                const content = generateContent(format);\n                const blob = new Blob([content], {\n                    type: format === 'json' ? 'application/json' : 'text/plain'\n                });\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = `prompts-${category.name}.${format === 'markdown' ? 'md' : format}`;\n                a.click();\n                URL.revokeObjectURL(url);\n                showToast('文件已开始下载');\n            };\n\n            closeShareBtn.onclick = () => closeModal(shareModal);\n        };\n        edititem.onclick = () => {\n            showx()\n        }\n        //showx()\n        function showx() {\n\n            const categories = document.querySelectorAll('.prompt-actions .prompt-delete')\n            for (let i = 0; i < categories.length; i++) {\n                if (categories[i].style.display != \"none\") {\n\n                    categories[i].style.display = \"none\";\n                    categories[i].parentNode.style.paddingRight = \"7px\";\n                    categories[i].parentNode.style.paddingLeft = \"7px\";\n\n                } else {\n                    categories[i].style.display = \"block\"\n                    categories[i].parentNode.style.paddingRight = \"0px\";\n                    categories[i].parentNode.style.paddingLeft = \"0px\";\n                }\n\n            }\n            const categories2 = document.querySelectorAll('.prompt-actions .prompt-edit')\n            for (let i = 0; i < categories2.length; i++) {\n                if (categories2[i].style.display != \"none\") {\n\n                    categories2[i].style.display = \"none\";\n                    categories2[i].parentNode.style.paddingRight = \"7px\";\n                    categories2[i].parentNode.style.paddingLeft = \"7px\";\n\n                } else {\n                    categories2[i].style.display = \"block\"\n                    categories2[i].parentNode.style.paddingRight = \"0px\";\n                    categories2[i].parentNode.style.paddingLeft = \"0px\";\n                }\n\n            }\n        }\n        editCategoryBtn.onclick = () => {\n            showEditCategoryDialog(categoryId);\n        };\n\n        //closeBtn.onclick = closeModal;\n    }\n\n    function 替换空格() {\n\n        let targetInput = window.lastFocusedInput || document.activeElement;\n        if (targetInput && (targetInput.tagName === 'INPUT' || targetInput.tagName === 'TEXTAREA')) {\n            // 获取文本框的当前值\n            const originalText = targetInput.value;\n\n            // 替换空格为下划线，但空格前不是逗号或句号的情况\n            const replacedText = originalText.replace(/(?<![,.])\\s/g, '_');\n\n            // 如果替换后的文本与原始文本不同，则更新文本框的内容\n            if (replacedText !== originalText) {\n                targetInput.value = replacedText;\n\n                // 将光标移动到文本末尾（可选）\n                targetInput.setSelectionRange(replacedText.length, replacedText.length);\n\n                // 触发输入事件\n                const inputEvent = new Event('input', { bubbles: true });\n                targetInput.dispatchEvent(inputEvent);\n            }\n        }\n    };\n    function 显示浏览器面板() {\n        if (floatBtn.style.display === 'none') {\n            floatWindow.style.display = 'block';\n            floatBtn.style.display = 'block';\n        } else {\n            floatWindow.style.display = 'none';\n            floatBtn.style.display = 'none';\n        }\n    }\n    \n    // 显示设置界面\n    function showSettings() {\n        \n        const modal = createModal2()//create浮动Modal();\n        modal.innerHTML = `\n            <h3>设置</h3>\n            <div class=\"settings-section\">\n                <h4>导入导出</h4>\n                <div style=\"display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 15px;\">\n                    <button class=\"prompt-manager-button\" id=\"export-json\">导出数据</button>\n                    <button class=\"prompt-manager-button\">导入数据</button>\n                    <button class=\"prompt-manager-button\" >批量导入</button>\n                    <span style=\"display:none\">每行一个提示词，如果有简称以\"|\"分隔。每次请只添加一个分组的，因为添加后所有的都会被分到同一个分组</span>\n                </div>\n            </div>\n            <div class=\"settings-section\">\n                <div style=\"margin-bottom: 5px;\">\n                    <label style=\"display: block; margin-bottom: 5px;\">面板透明度: ${Math.round(data.settings.panelOpacity * 100)}%</label>\n                    <input type=\"range\" min=\"10\" max=\"100\" value=\"${data.settings.panelOpacity * 100}\" \n                           id=\"opacity-slider\" style=\"width: 100%;\">\n                </div>\n            </div>\n            <div class=\"settings-section\">\n                <h4>其他操作</h4>\n                <div style=\"display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 5px;\">\n                    <button class=\"prompt-manager-button\" style=\"display:none\">使用帮助</button>\n                    <button class=\"prompt-manager-button danger\">清空数据</button>\n                    <a class=\"prompt-manager-button danger\" target='_blank' href='https://www.bilibili.com/video/BV1N3o1YpEY3/?vd_source=09698d8717805cee2a04f9dab13e298d#reply260706414480'>反馈bug</a>\n                    <a class=\"prompt-manager-button danger\" target='_blank'  href='https://scriptcat.org/zh-CN/script-show-page/3009/version'>更新记录</a>\n                </div>\n            </div>\n            <span>\n            <h6>使用帮助</h6>\n            1、点击\"+🗂️\"可以添加分组<br>\n            2、在页面中划词可以添加提示词<br>\n            3、点击提示词可以填入到编辑框里<br>\n            4、点击🈳可以替换掉所有空格为\"_\"<br>\n            5、点击🛜开启页面内浏览器，可以临时打开翻译网站，需要安装<a style=\"color:orange\" href=\"https://chromewebstore.google.com/detail/joibipdfkleencgfgbbncoheaekffdfn\" target=\"_blank\">Hiframe插件</a>\n            </span>\n            \n<button class=\"prompt-manager-button secondary\">关闭</button>\n            <input type=\"file\" style=\"display: none\" accept=\".json\" id=\"import-file\">\n            <input type=\"file\" style=\"display: none\" accept=\".txt,.csv\" id=\"batch-import-file\">\n        `;\n        makeDraggable(modal);\n        const closeBtn1 = modal.querySelector('.prompt-manager-button.secondary');\n        closeBtn1.onclick = closeModal;\n        console.log(data.settings.panelOpacity)\n        document.getElementById('opacity-slider')?.addEventListener('input', function(e) {\n            console.log(e.target.value)\n                const opacity = e.target.value / 100;\n                data.settings.panelOpacity = opacity;\n                saveData();\n                \n                // 更新所有面板的背景色透明度\n                document.querySelectorAll('.prompt-manager-menu, .prompt-manager-modal, .prompt-manager-modal2').forEach(el => {\n                    const currentBg = window.getComputedStyle(el).backgroundColor;\n                    const newBg = currentBg.replace(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*[\\d.]+)?\\)/, \n                        `rgba($1, $2, $3, ${opacity})`);\n                    el.style.backgroundColor = newBg;\n                });\n                \n                // 更新标签显示\n                e.target.previousElementSibling.textContent = `面板透明度: ${e.target.value}%`;\n            });\n\n\n        const exportJsonBtn = modal.querySelector('#export-json');\n        const exportTxtBtn = modal.querySelector('#export-txt');\n        const [, importBtn, batchImportBtn, helpBtn, clearBtn, closeBtn] = modal.querySelectorAll('.prompt-manager-button');\n        const fileInput = modal.querySelector('#import-file');\n        const batchFileInput = modal.querySelector('#batch-import-file');\n\n\n\n        // 导出仅包含prompts数组的JSON数据\n        exportJsonBtn.onclick = () => {\n            const exportData = { categories: data.categories, prompts: data.prompts };\n            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });\n            //const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = 'prompt-manager-data.json';\n            a.click();\n            URL.revokeObjectURL(url);\n        };\n\n\n\n        importBtn.onclick = () => fileInput.click();\n\n        // 批量导入按钮\n        batchImportBtn.onclick = () => showBatchImportDialog()//batchFileInput.click();\n\n        // 批量导入文件处理\n        batchFileInput.onchange = async () => {\n            const file = batchFileInput.files[0];\n            if (file) {\n                try {\n                    const text = await file.text();\n                    showBatchImportDialog(text);\n                } catch (e) {\n                    alert('文件读取失败');\n                }\n            }\n        };\n\n        fileInput.onchange = async () => {\n            const file = fileInput.files[0];\n            if (file) {\n                try {\n                    const text = await file.text();\n                    const importedData = JSON.parse(text);\n                    if (importedData.categories && importedData.prompts) {\n                        // 保留lastUsedCategoryId或使用导入数据中的值\n                        const currentLastUsedCategory = data.lastUsedCategoryId;\n                        data = importedData;\n\n                        // 如果导入的数据没有lastUsedCategoryId，保留当前的值\n                        if (!data.lastUsedCategoryId && currentLastUsedCategory) {\n                            data.lastUsedCategoryId = currentLastUsedCategory;\n                        }\n                        saveData();\n                        alert('数据导入成功！');\n                        closeModal();\n                    }\n                } catch (e) {\n                    alert('导入失败：无效的数据格式');\n                }\n            }\n        };\n\n        clearBtn.onclick = () => {\n            if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {\n                data = {\n                    categories: [],\n                    prompts: [],\n                    lastUsedCategoryId: null\n                };\n                saveData();\n                closeModal();\n            }\n        };\n\n        //closeBtn.onclick = closeModal;\n    }\n\n    // 显示保存提示词对话框\n    function showSavePromptDialog(text) {\n        const modal = createModal2();\n        modal.innerHTML = `\n            <h3>保存提示词</h3>\n            <textarea class=\"prompt-manager-input\" style=\"height: 100px;width:250px;resize:auto;\">${text}</textarea>\n            <div style='display:flex;width:250px'><input type=\"text\" class=\"prompt-manager-input\" placeholder=\"简称（可选）\">\n            <select class=\"prompt-manager-input\">\n                <option value=\"\">选择分类...</option>\n                ${data.categories.map(c => `\n                    <option value=\"${c.id}\">${c.name}</option>\n                `).join('')}\n            </select>\n            <button class=\"prompt-manager-button\">保存</button></div>\n            \n        `;\n\n        // 防止点击事件冒泡\n        modal.addEventListener('click', (e) => {\n            e.stopPropagation();\n        });\n        // 添加拖拽功能\n        makeDraggable(modal);\n        const aliasInput = modal.querySelector('input');\n        const categorySelect = modal.querySelector('select');\n        const [saveBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        saveBtn.onclick = () => {\n            const categoryId = categorySelect.value;\n            if (!categoryId) {\n                alert('请选择分类！');\n                return;\n            }\n\n            const textArea = modal.querySelector('textarea');\n            const isTemplateCheckbox = modal.querySelector('#is-template') || { checked: false };\n            const newPrompt = {\n                id: Date.now().toString(),\n                text: textArea.value.trim(),\n                alias: aliasInput.value.trim(),\n                categoryId: categoryId,\n                favorite: false,\n                useCount: 0,\n                isTemplate: isTemplateCheckbox.checked\n            };\n\n            // 保存提示词的同时记录最后使用的分组ID\n            data.lastUsedCategoryId = categoryId;\n            data.prompts.push(newPrompt);\n            saveData();\n            closeModal();\n\n                //提示信息\n                showToast('新建成功',1000);\n            \n        };\n\n        // 如果有上次使用的分组，自动选中\n        if (data.lastUsedCategoryId) {\n            const option = categorySelect.querySelector(`option[value=\"${data.lastUsedCategoryId}\"]`);\n            if (option) {\n                categorySelect.value = data.lastUsedCategoryId;\n            } else {\n                // 如果找不到对应的分类（可能已被删除），清除记录\n                data.lastUsedCategoryId = null;\n                saveData();\n            }\n        }\n\n        cancelBtn.onclick = closeModal;\n    }\n\n    // 显示批量导入对话框\n    function showBatchImportDialog(text) {\n        const modal = createModal2()//create浮动Modal();\n        modal.innerHTML = `\n            <h3>批量导入提示词</h3>\n            <p>每行一个提示词，格式：提示词文本|别名（可选）。导入后需要刷新</p>\n            <textarea class=\"prompt-manager-input\" style=\"height: 200px;\">${text}</textarea>\n            <select class=\"prompt-manager-input\">\n                <option value=\"\">选择导入分类...</option>\n                ${data.categories.map(c => `\n                    <option value=\"${c.id}\">${c.name}</option>\n                `).join('')}\n            </select>\n            <button class=\"prompt-manager-button\">导入</button>\n            <button class=\"prompt-manager-button secondary\">取消</button>\n        `;\n        makeDraggable(modal);\n        const textArea = modal.querySelector('textarea');\n        const categorySelect = modal.querySelector('select');\n        const [importBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        // 如果有上次使用的分组，自动选中\n        if (data.lastUsedCategoryId) {\n            categorySelect.value = data.lastUsedCategoryId;\n        }\n\n        importBtn.onclick = () => {\n            const categoryId = categorySelect.value;\n            if (!categoryId) {\n                alert('请选择分类！');\n                return;\n            }\n\n            const lines = textArea.value.split('\\n').filter(line => line.trim());\n            if (lines.length === 0) {\n                alert('没有找到有效的提示词');\n                return;\n            }\n\n            let importCount = 0;\n            lines.forEach(line => {\n                const parts = line.split('|');\n                const text = parts[0].trim();\n                const alias = parts.length > 1 ? parts[1].trim() : '';\n\n                if (text) {\n                    const newPrompt = {\n                        id: Date.now().toString() + importCount,\n                        text: text,\n                        alias: alias,\n                        categoryId: categoryId,\n                        favorite: false\n                    };\n\n                    data.prompts.push(newPrompt);\n                    importCount++;\n                }\n            });\n\n            if (importCount > 0) {\n                data.lastUsedCategoryId = categoryId;\n                saveData();\n                alert(`成功导入 ${importCount} 个提示词`);\n                closeModal();\n            } else {\n                alert('没有找到有效的提示词');\n            }\n        };\n\n        cancelBtn.onclick = closeModal;\n    }\n\n\n    // 创建模态框\n    function createModal() {\n        const modal = document.createElement('div');\n        modal.className = 'prompt-manager-modal';\n\n\n        // 添加拖拽功能\n        makeDraggable(modal);\n        const menu = document.querySelector('#div1').appendChild(modal);\n        return modal;\n    }\n    function create浮动Modal() {\n        const modal = document.createElement('div');\n        modal.className = 'prompt-manager-modal2 浮动';\n\n        // 如果有保存的位置，使用保存的位置\n        // if (data.settings?.modalPosition) {\n        modal.style.left = '20%';\n        modal.style.top = '20%';\n        modal.style.transform = 'none';\n        //}*/\n\n        // 添加拖拽功能\n        makeDraggable(modal);\n        //const menu = document.querySelector('#div1').appendChild(modal);\n        document.body.appendChild(modal);\n        return modal;\n    }\n    function createModal2() {\n        const modal = document.createElement('div');\n        modal.className = 'prompt-manager-modal2 浮动modal2';\n\n        // 如果有保存的位置，使用保存的位置\n        if (data.settings?.modalPosition) {\n            modal.style.left = data.settings.modalPosition.left + 'px';\n            modal.style.top = data.settings.modalPosition.top + 'px';\n            modal.style.transform = 'none';\n        }\n\n        makeDraggable(modal);\n        //const menu = document.querySelector('#div1').appendChild(modal);\n        document.body.appendChild(modal);\n        return modal;\n    }\n\n    // 使元素可拖拽\n    function makeDraggable(element) {\n        let isDragging = false;\n        let offsetX, offsetY;\n\n        // 创建拖拽手柄\n        const dragHandle = document.createElement('div');\n        dragHandle.style.cssText = `\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            height: 10px;\n            cursor: move;\n            background-color: rgb(87 122 77 / 64%);\n        `;\n        element.appendChild(dragHandle);\n        //console.log(dragHandle);\n        // 鼠标按下事件\n        dragHandle.addEventListener('mousedown', (e) => {\n            isDragging = true;\n\n            // 获取鼠标在元素内的相对位置\n            const rect = element.getBoundingClientRect();\n            offsetX = e.clientX - rect.left;\n            offsetY = e.clientY - rect.top;\n\n            // 阻止默认行为和冒泡\n            e.preventDefault();\n            e.stopPropagation();\n        });\n\n        // 鼠标移动事件\n        document.addEventListener('mousemove', (e) => {\n            if (!isDragging) return;\n\n            // 计算新位置\n            const x = e.clientX - offsetX;\n            const y = e.clientY - offsetY;\n\n            // 应用新位置\n            element.style.left = x + 'px';\n            element.style.top = y + 'px';\n            element.style.transform = 'none';\n\n            // 保存位置到设置\n            data.settings.modalPosition = { left: x, top: y };\n        });\n\n        // 鼠标释放事件\n        document.addEventListener('mouseup', () => {\n            if (isDragging) {\n                isDragging = false;\n                saveData();\n            }\n        });\n    }\n\n    // 关闭模态框\n    function closeModal() {\n        const modal = document.querySelector('.prompt-manager-modal');\n        const modal2 = document.querySelector('.prompt-manager-modal2');\n        if (modal) modal.remove();\n        if (modal2) modal2.remove();\n    }\n    function closeModal2() {\n        const modal = document.querySelector('.prompt-manager-modal2');\n        if (modal) modal.remove();\n    }\n    // 点击页面任意位置关闭模态框（使用捕获阶段）\n    document.addEventListener('click', (e) => {\n        const modal = document.querySelector('.prompt-manager-modal2');\n        if (modal && !e.target.closest('.prompt-manager-modal2')) {\n            closeModal2();\n        }\n    }, true);\n\n    \n    // 显示编辑提示词对话框\n    function showEditPromptDialog(promptId, text, alias, categoryId) {\n        console.log(promptId, text, alias, categoryId);\n        const modal = createModal2();\n        modal.innerHTML = `\n            <h3>编辑提示词</h3>\n            <textarea class=\"prompt-manager-input\" style=\"height: 100px\">${text}</textarea>\n            <input type=\"text\" class=\"prompt-manager-input\" placeholder=\"简称（可选）\" value=\"${alias || ''}\">\n            <select class=\"prompt-manager-input\">\n                <option value=\"\">选择分类...</option>\n                ${data.categories.map(c => `\n                    <option value=\"${c.id}\" ${c.id === categoryId ? 'selected' : ''}>${c.name}</option>\n                `).join('')}\n            </select>\n            <button class=\"prompt-manager-button\">保存</button>\n            <button class=\"prompt-manager-button secondary\">取消</button>\n        `;\n        makeDraggable(modal);\n        // 防止点击事件冒泡\n        modal.addEventListener('click', (e) => {\n            e.stopPropagation();\n        });\n\n        const textArea = modal.querySelector('textarea');\n        const aliasInput = modal.querySelector('input[type=\"text\"]');\n        const isTemplateCheckbox = modal.querySelector('#is-template') || { checked: false };\n        //const templateHelp = modal.querySelector('.template-help');\n        const categorySelect = modal.querySelector('select');\n        const [saveBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        // 模板帮助提示\n        //templateHelp.onclick = () => {\n        //    alert('模板支持使用变量占位符，格式为 {{变量名}}。\\n\\n使用时会提示输入变量值，自动替换占位符。\\n\\n例如：\\n\"你好，我是{{名字}}，我来自{{地点}}。\"\\n\\n使用时会提示输入\"名字\"和\"地点\"的值。');\n        //};\n\n        saveBtn.onclick = () => {\n            const newCategoryId = categorySelect.value;\n            if (!newCategoryId) {\n                alert('请选择分类！');\n                return;\n            }\n\n            const newText = textArea.value;\n            if (!newText.trim()) {\n                alert('提示词内容不能为空！');\n                return;\n            }\n\n            // 更新提示词\n            const promptIndex = data.prompts.findIndex(p => p.id === promptId);\n            if (promptIndex !== -1) {\n                data.prompts[promptIndex] = {\n                    ...data.prompts[promptIndex],\n                    text: newText,\n                    alias: aliasInput.value.trim(),\n                    categoryId: newCategoryId\n                };\n\n                saveData();\n                closeModal();\n\n                // 如果分类改变了，需要刷新提示词列表\n                if (newCategoryId !== categoryId) {\n                    showPromptList(newCategoryId);\n                } else {\n                    showPromptList(categoryId);\n                }\n            }\n        };\n\n        cancelBtn.onclick = closeModal;\n    }\n\n    // 显示编辑分类对话框\n    function showEditCategoryDialog(categoryId) {\n        const category = data.categories.find(c => c.id === categoryId);\n        if (!category) return;\n\n        const modal = createModal2()//create浮动Modal();\n        modal.innerHTML = `\n            <h3>编辑分类</h3>\n            <input type=\"text\" class=\"prompt-manager-input\" value=\"${category.name}\" placeholder=\"分类名称\">\n            <div class=\"prompt-manager-toolbar\" style=\"border: none; justify-content: space-between;height:30px;\">\n                <button class=\"prompt-manager-button\">保存</button>\n                <button class=\"prompt-manager-button danger\">删除分类</button>\n                <button class=\"prompt-manager-button secondary\">取消</button>\n            </div>\n        `;\n        makeDraggable(modal);\n        const nameInput = modal.querySelector('input');\n        const [saveBtn, deleteBtn, cancelBtn] = modal.querySelectorAll('button');\n\n        saveBtn.onclick = () => {\n            const newName = nameInput.value.trim();\n            if (newName) {\n                category.name = newName;\n                saveData();\n                closeModal();\n                // 刷新分类列表\n                showPromptList(categoryId);\n            } else {\n                alert('分类名称不能为空');\n            }\n        };\n\n        deleteBtn.onclick = () => {\n            if (confirm(`确定要删除分类\"${category.name}\"吗？该分类下的所有提示词也将被删除！`)) {\n                // 删除该分类下的所有提示词\n                data.prompts = data.prompts.filter(p => p.categoryId !== categoryId);\n                // 删除分类\n                data.categories = data.categories.filter(c => c.id !== categoryId);\n                // 如果删除的是上次使用的分类，清除记录\n                if (data.lastUsedCategoryId === categoryId) {\n                    data.lastUsedCategoryId = null;\n                }\n                saveData();\n                closeModal();\n\n                // 刷新菜单\n                const menu = document.querySelector('.prompt-manager-menu');\n                if (menu) {\n                    const rect = menu.getBoundingClientRect();\n                    menu.remove();\n                    createFloatingMenu(rect.left, rect.top);\n                }\n            }\n        };\n\n        cancelBtn.onclick = closeModal;\n    }\n\n\n\n    // 初始化\n    initData();\n\n    // 记录最近聚焦的输入元素\n    window.lastFocusedInput = null;\n    document.addEventListener('focusin', (e) => {\n        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {\n            window.lastFocusedInput = e.target;\n        }\n    });\n\n    // 监听文本框点击\n    document.addEventListener('click', (e) => {\n        const target = e.target;\n        if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {\n            const menu = document.querySelector('.prompt-manager-menu');\n            if (menu) menu.remove();\n\n            const rect = target.getBoundingClientRect();\n            createFloatingMenu(rect.left - 170, rect.top);\n        } else if (!e.target.closest('.prompt-manager-menu') && !e.target.closest('.prompt-manager-modal')) {\n            const menu = document.querySelector('.prompt-manager-menu');\n            if (menu) menu.remove();\n        }\n    });\n\n    // 监听选中文本\n    document.addEventListener('mouseup', (e) => {\n        // 检查是否在模态框内，如果是则不处理\n        if (e.target.closest('.prompt-manager-modal') || e.target.closest('.prompt-manager-menu') || e.target.closest('.prompt-manager-modal2')) {\n            return;\n        }\n        const selection = window.getSelection();\n        const text = selection.toString().trim();\n\n        // 如果有选中的文本，则显示保存对话框\n        if (text) {\n            // 延迟显示保存对话框，避免与其他点击事件冲突\n            setTimeout(() => {\n                const menu = document.querySelector('.prompt-manager-menu');\n                if (menu) menu.remove();\n                showSavePromptDialog(text);\n            }, 100);\n        }\n    });\n    \n    //_____________________________\n\n    // 创建主浮动按钮\n    const floatBtn = document.createElement('div');\n    floatBtn.innerHTML = '🌐';\n    floatBtn.style.display = 'none';\n    floatBtn.style.position = 'fixed';\n    floatBtn.style.left = '10px';\n    floatBtn.style.top = '0px';\n    floatBtn.style.zIndex = '9999';\n    floatBtn.style.width = '23px';\n    floatBtn.style.height = '23px';\n    floatBtn.style.borderRadius = '50%';\n    floatBtn.style.backgroundColor = '#4285f4';\n    floatBtn.style.color = 'white';\n    floatBtn.style.justifyContent = 'center';\n    floatBtn.style.alignItems = 'center';\n    floatBtn.style.cursor = 'pointer';\n    floatBtn.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';\n    document.body.appendChild(floatBtn);\n\n    // 创建浮动窗口容器\n    const floatWindow = document.createElement('div');\n    floatWindow.style.display = 'none';\n    floatWindow.style.position = 'fixed';\n    floatWindow.style.zIndex = '9998';\n    floatWindow.style.width = '300px';\n    floatWindow.style.height = '400px';\n    floatWindow.style.top = '50px';\n    floatWindow.style.left = '100px';\n    floatWindow.style.backgroundColor = 'white';\n    floatWindow.style.borderRadius = '8px';\n    floatWindow.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';\n    floatWindow.style.overflow = 'hidden';\n    document.body.appendChild(floatWindow);\n\n    // 创建标题栏\n    const titleBar = document.createElement('div');\n    titleBar.style.height = '40px';\n    titleBar.style.backgroundColor = '#4285f4';\n    titleBar.style.color = 'white';\n    titleBar.style.display = 'flex';\n    titleBar.style.alignItems = 'center';\n    titleBar.style.padding = '0 10px';\n    titleBar.style.cursor = 'move';\n    floatWindow.appendChild(titleBar);\n\n    // 标题栏文本\n    const titleText = document.createElement('div');\n    titleText.textContent = '网页控制器';\n    titleText.style.flexGrow = '1';\n    titleBar.appendChild(titleText);\n\n    // 创建控制按钮容器\n    const controls = document.createElement('div');\n    controls.style.display = 'flex';\n    controls.style.gap = '5px';\n    titleBar.appendChild(controls);\n\n    // 最大化按钮\n    const maxBtn = document.createElement('button');\n    maxBtn.textContent = '最大化';\n    maxBtn.style.padding = '2px 8px';\n    maxBtn.style.border = 'none';\n    maxBtn.style.borderRadius = '4px';\n    maxBtn.style.cursor = 'pointer';\n    controls.appendChild(maxBtn);\n\n    // 恢复按钮\n    const restoreBtn = document.createElement('button');\n    restoreBtn.textContent = '恢复';\n    restoreBtn.style.padding = '2px 8px';\n    restoreBtn.style.border = 'none';\n    restoreBtn.style.borderRadius = '4px';\n    restoreBtn.style.cursor = 'pointer';\n    restoreBtn.style.display = 'none'; // 默认隐藏\n    controls.appendChild(restoreBtn);\n\n    // 右侧布局按钮\n    const rightLayoutBtn = document.createElement('button');\n    rightLayoutBtn.textContent = '➡️';\n    rightLayoutBtn.style.padding = '2px 8px';\n    rightLayoutBtn.style.border = 'none';\n    rightLayoutBtn.style.borderRadius = '4px';\n    rightLayoutBtn.style.cursor = 'pointer';\n    controls.appendChild(rightLayoutBtn);\n\n    // 左侧布局按钮\n    const leftLayoutBtn = document.createElement('button');\n    leftLayoutBtn.textContent = '⬅️';\n    leftLayoutBtn.style.padding = '2px 8px';\n    leftLayoutBtn.style.border = 'none';\n    leftLayoutBtn.style.borderRadius = '4px';\n    leftLayoutBtn.style.cursor = 'pointer';\n    leftLayoutBtn.style.display = 'none';\n    controls.appendChild(leftLayoutBtn);\n\n    // 布局按钮点击事件\n    rightLayoutBtn.addEventListener('click', () => {\n        // 右侧布局\n        floatWindow.style.width = '40%';\n        floatWindow.style.height = '90%';\n        floatWindow.style.left = 'calc(60% - 50px)';\n        floatWindow.style.top = '5%';\n        rightLayoutBtn.style.display = 'none';\n        leftLayoutBtn.style.display = 'inline-block';\n    });\n\n    leftLayoutBtn.addEventListener('click', () => {\n        // 左侧布局\n        floatWindow.style.width = '40%';\n        floatWindow.style.height = '90%';\n        floatWindow.style.left = '50px';\n        floatWindow.style.top = '5%';\n        leftLayoutBtn.style.display = 'none';\n        rightLayoutBtn.style.display = 'inline-block';\n    });\n\n    // 关闭按钮\n    const closeBtn = document.createElement('button');\n    closeBtn.textContent = '×';\n    closeBtn.style.padding = '2px 8px';\n    closeBtn.style.border = 'none';\n    closeBtn.style.borderRadius = '4px';\n    closeBtn.style.cursor = 'pointer';\n    controls.appendChild(closeBtn);\n\n    // 创建URL输入框容器\n    const urlContainer = document.createElement('div');\n    urlContainer.style.padding = '10px';\n    urlContainer.style.display = 'flex';\n    urlContainer.style.gap = '5px';\n    floatWindow.appendChild(urlContainer);\n\n    // 添加预设翻译按钮\n    const sogouBtn = document.createElement('button');\n    sogouBtn.textContent = '狗';\n    sogouBtn.style.padding = '5px 10px';\n    sogouBtn.style.border = 'none';\n    sogouBtn.style.borderRadius = '4px';\n    sogouBtn.style.backgroundColor = '#34a853';\n    sogouBtn.style.color = 'white';\n    sogouBtn.style.cursor = 'pointer';\n    sogouBtn.title = '搜狗翻译';\n    urlContainer.appendChild(sogouBtn);\n\n    const baiduBtn = document.createElement('button');\n    baiduBtn.textContent = '百';\n    baiduBtn.style.padding = '5px 10px';\n    baiduBtn.style.border = 'none';\n    baiduBtn.style.borderRadius = '4px';\n    baiduBtn.style.backgroundColor = '#4285f4';\n    baiduBtn.style.color = 'white';\n    baiduBtn.style.cursor = 'pointer';\n    baiduBtn.title = '百度翻译';\n    urlContainer.appendChild(baiduBtn);\n\n    const youdaoBtn = document.createElement('button');\n    youdaoBtn.textContent = '有';\n    youdaoBtn.style.padding = '5px 10px';\n    youdaoBtn.style.border = 'none';\n    youdaoBtn.style.borderRadius = '4px';\n    youdaoBtn.style.backgroundColor = '#fbbc05';\n    youdaoBtn.style.color = 'white';\n    youdaoBtn.style.cursor = 'pointer';\n    youdaoBtn.title = '有道翻译';\n    urlContainer.appendChild(youdaoBtn);\n    // URL输入框\n    const urlInput = document.createElement('input');\n    urlInput.type = 'text';\n    urlInput.placeholder = '请先安装Hiframe插件，否则不能加载网页。输入网址后点击加载。';\n    urlInput.style.flexGrow = '1';\n    urlInput.style.padding = '5px';\n    urlInput.style.border = '1px solid #ddd';\n    urlInput.style.borderRadius = '4px';\n    urlContainer.appendChild(urlInput);\n\n    // 预设翻译按钮点击事件\n    sogouBtn.addEventListener('click', () => {\n        urlInput.value = 'fanyi.sogou.com';\n        loadBtn.click();\n    });\n\n    baiduBtn.addEventListener('click', () => {\n        urlInput.value = 'fanyi.baidu.com';\n        loadBtn.click();\n    });\n\n    youdaoBtn.addEventListener('click', () => {\n        urlInput.value = 'fanyi.youdao.com';\n        loadBtn.click();\n    });\n\n    // 加载按钮\n    const loadBtn = document.createElement('button');\n    loadBtn.textContent = '加载';\n    loadBtn.style.padding = '5px 10px';\n    loadBtn.style.border = 'none';\n    loadBtn.style.borderRadius = '4px';\n    loadBtn.style.backgroundColor = '#4285f4';\n    loadBtn.style.color = 'white';\n    loadBtn.style.cursor = 'pointer';\n    urlContainer.appendChild(loadBtn);\n\n    // 创建iframe\n    const iframe = document.createElement('iframe');\n    iframe.style.width = '100%';\n    iframe.style.height = 'calc(100% - 90px)';\n    iframe.style.border = 'none';\n    iframe.style.marginTop = '5px';\n    floatWindow.appendChild(iframe);\n\n    // 设置移动端视图\n    const setMobileView = () => {\n        const viewportMeta = document.createElement('meta');\n        viewportMeta.name = 'viewport';\n        viewportMeta.content = 'width=device-width, initial-scale=1.0';\n        iframe.contentDocument.head.appendChild(viewportMeta);\n    };\n\n    // 拖动功能\n    let isDragging = false;\n    let offsetX, offsetY;\n\n    titleBar.addEventListener('mousedown', (e) => {\n        isDragging = true;\n        offsetX = e.clientX - floatWindow.getBoundingClientRect().left;\n        offsetY = e.clientY - floatWindow.getBoundingClientRect().top;\n    });\n\n    document.addEventListener('mousemove', (e) => {\n        if (!isDragging) return;\n        floatWindow.style.left = (e.clientX - offsetX) + 'px';\n        floatWindow.style.top = (e.clientY - offsetY) + 'px';\n    });\n\n    document.addEventListener('mouseup', () => {\n        isDragging = false;\n    });\n\n    // 按钮点击事件\n    floatBtn.addEventListener('click', () => {\n        console.log('浮动按钮被点击');\n        floatWindow.style.display = floatWindow.style.display === 'none' ? 'block' : 'none';\n    });\n\n    // 最大化功能\n    maxBtn.addEventListener('click', () => {\n        const windowWidth = window.innerWidth * 0.9;\n        const windowHeight = window.innerHeight * 0.9;\n        floatWindow.style.width = windowWidth + 'px';\n        floatWindow.style.height = windowHeight + 'px';\n        floatWindow.style.left = (window.innerWidth - windowWidth) / 2 + 'px';\n        floatWindow.style.top = (window.innerHeight - windowHeight) / 2 + 'px';\n        maxBtn.style.display = 'none'; // 隐藏最大化按钮\n        restoreBtn.style.display = 'block'; // 显示恢复按钮\n    });\n\n    // 恢复功能\n    restoreBtn.addEventListener('click', () => {\n        floatWindow.style.width = '300px';\n        floatWindow.style.height = '400px';\n        restoreBtn.style.display = 'none'; // 隐藏恢复按钮\n        maxBtn.style.display = 'block'; // 显示最大化按钮\n    });\n\n    // 关闭功能\n    closeBtn.addEventListener('click', () => {\n        floatWindow.style.display = 'none';\n    });\n\n    // 加载网页\n    loadBtn.addEventListener('click', () => {\n        let url = urlInput.value.trim();\n        if (!url.startsWith('http://') && !url.startsWith('https://')) {\n            url = 'https://' + url;\n        }\n        iframe.src = url;\n\n        // 监听iframe加载完成事件\n        iframe.onload = function () {\n            try {\n                setMobileView();\n            } catch (e) {\n                console.log('无法设置移动端视图:', e);\n            }\n        };\n    });\n\n    // 创建缩放控制按钮\n    const resizeHandle = document.createElement('div');\n    resizeHandle.style.position = 'absolute';\n    resizeHandle.style.right = '0';\n    resizeHandle.style.bottom = '0';\n    resizeHandle.style.width = '20px';\n    resizeHandle.style.height = '20px';\n    resizeHandle.style.cursor = 'nwse-resize';\n    resizeHandle.style.background = 'linear-gradient(135deg, transparent 0%, transparent 50%, #4285f4 50%, #4285f4 100%)';\n    floatWindow.appendChild(resizeHandle);\n\n    // 缩放功能\n    let isResizing = false;\n    resizeHandle.addEventListener('mousedown', (e) => {\n        isResizing = true;\n        e.stopPropagation();\n    });\n\n    document.addEventListener('mousemove', (e) => {\n        if (!isResizing) return;\n        const newWidth = e.clientX - floatWindow.getBoundingClientRect().left;\n        const newHeight = e.clientY - floatWindow.getBoundingClientRect().top;\n        floatWindow.style.width = Math.max(200, newWidth) + 'px';\n        floatWindow.style.height = Math.max(200, newHeight) + 'px';\n\n        document.addEventListener('mouseup', () => {\n            if (isResizing) { isResizing = false;\n             // 清除页面中的文本选择\n             if (window.getSelection) {\n                 window.getSelection().removeAllRanges();\n             } else if (document.selection) {\n                 document.selection.empty();\n             }}\n         });\n    });\n\n    // 添加一些基础样式\n    GM_addStyle(`\n        button {\n            transition: all 0.2s;\n        }\n        button:hover {\n            opacity: 0.8;\n        }\n        input:focus {\n            outline: none;\n            border-color: #4285f4 !important;\n        }\n        .resize-handle {\n            position: absolute;\n            right: 0;\n            bottom: 0;\n            width: 20px;\n            height: 20px;\n            cursor: nwse-resize;\n            background: linear-gradient(135deg, transparent 0%, transparent 50%, #4285f4 50%, #4285f4 100%);\n        }\n    `);\n    //加载页面后提示更新信息\n//如果是iframe则不显示，如果标题不包含comfyui则不显示\nif (window.self !== window.top|| document.title.indexOf('comfyui')==-1) {\n    return;\n}else{\n    showToast(`25年5月更新：4.5 版本<br>1、可以编辑提示词了😄🎉🎉\n        <br>2、新增🛜按钮，点击后可以在页面内打开翻译网站，需要安装<a style=\"color:orange\" href=\"https://chromewebstore.google.com/detail/joibipdfkleencgfgbbncoheaekffdfn\" target=\"_blank\">Hiframe插件</a>。\n         <br>3、新增🈳按钮，点击后把所有空格替换成_ <br>\n        <br>如果您不想在所有网页启用此脚本，请把// @match   *://*/* 替换成您想要显示的网站`,8000);\n    }", "enabled": true}, {"id": "1753092024681", "name": "iframe", "match": "*://*so.com*", "code": "'use strict';\nconsole.log('一家在');\n    // 创建主浮动按钮\n    const floatBtn = document.createElement('div');\n    floatBtn.innerHTML = '🌐';\n    floatBtn.style.position = 'fixed';\n    floatBtn.style.left = '10px';\n    floatBtn.style.top = '10px';\n    floatBtn.style.zIndex = '9999';\n    floatBtn.style.width = '40px';\n    floatBtn.style.height = '40px';\n    floatBtn.style.borderRadius = '50%';\n    floatBtn.style.backgroundColor = '#4285f4';\n    floatBtn.style.color = 'white';\n    floatBtn.style.display = 'flex';\n    floatBtn.style.justifyContent = 'center';\n    floatBtn.style.alignItems = 'center';\n    floatBtn.style.cursor = 'pointer';\n    floatBtn.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';\n    document.body.appendChild(floatBtn);\n\n    // 创建浮动窗口容器\n    const floatWindow = document.createElement('div');\n    floatWindow.style.display = 'none';\n    floatWindow.style.position = 'fixed';\n    floatWindow.style.zIndex = '9998';\n    floatWindow.style.width = '300px';\n    floatWindow.style.height = '400px';\n    floatWindow.style.top = '50px';\n    floatWindow.style.left = '100px';\n    floatWindow.style.backgroundColor = 'white';\n    floatWindow.style.borderRadius = '8px';\n    floatWindow.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';\n    floatWindow.style.overflow = 'hidden';\n    document.body.appendChild(floatWindow);\n\n    // 创建标题栏\n    const titleBar = document.createElement('div');\n    titleBar.style.height = '40px';\n    titleBar.style.backgroundColor = '#4285f4';\n    titleBar.style.color = 'white';\n    titleBar.style.display = 'flex';\n    titleBar.style.alignItems = 'center';\n    titleBar.style.padding = '0 10px';\n    titleBar.style.cursor = 'move';\n    floatWindow.appendChild(titleBar);\n\n    // 标题栏文本\n    const titleText = document.createElement('div');\n    titleText.textContent = '网页控制器';\n    titleText.style.flexGrow = '1';\n    titleBar.appendChild(titleText);\n\n    // 创建控制按钮容器\n    const controls = document.createElement('div');\n    controls.style.display = 'flex';\n    controls.style.gap = '5px';\n    titleBar.appendChild(controls);\n\n    // 最大化按钮\n    const maxBtn = document.createElement('button');\n    maxBtn.textContent = '最大化';\n    maxBtn.style.padding = '2px 8px';\n    maxBtn.style.border = 'none';\n    maxBtn.style.borderRadius = '4px';\n    maxBtn.style.cursor = 'pointer';\n    controls.appendChild(maxBtn);\n\n    // 恢复按钮\n    const restoreBtn = document.createElement('button');\n    restoreBtn.textContent = '恢复';\n    restoreBtn.style.padding = '2px 8px';\n    restoreBtn.style.border = 'none';\n    restoreBtn.style.borderRadius = '4px';\n    restoreBtn.style.cursor = 'pointer';\n    restoreBtn.style.display = 'none'; // 默认隐藏\n    controls.appendChild(restoreBtn);\n\n    // 右侧布局按钮\n    const rightLayoutBtn = document.createElement('button');\n    rightLayoutBtn.textContent = '➡️';\n    rightLayoutBtn.style.padding = '2px 8px';\n    rightLayoutBtn.style.border = 'none';\n    rightLayoutBtn.style.borderRadius = '4px';\n    rightLayoutBtn.style.cursor = 'pointer';\n    controls.appendChild(rightLayoutBtn);\n\n    // 左侧布局按钮\n    const leftLayoutBtn = document.createElement('button');\n    leftLayoutBtn.textContent = '⬅️';\n    leftLayoutBtn.style.padding = '2px 8px';\n    leftLayoutBtn.style.border = 'none';\n    leftLayoutBtn.style.borderRadius = '4px';\n    leftLayoutBtn.style.cursor = 'pointer';\n    leftLayoutBtn.style.display = 'none';\n    controls.appendChild(leftLayoutBtn);\n\n    // 布局按钮点击事件\n    rightLayoutBtn.addEventListener('click', () => {\n        // 右侧布局\n        floatWindow.style.width = '40%';\n        floatWindow.style.height = '90%';\n        //floatWindow.style.position = 'absolute';\n        floatWindow.style.left = 'calc(60% - 50px)';\n        floatWindow.style.top = '5%';\n        //floatWindow.style.transform = 'translateY(-50%)';\n        rightLayoutBtn.style.display = 'none';\n        leftLayoutBtn.style.display = 'inline-block';\n    });\n\n    leftLayoutBtn.addEventListener('click', () => {\n        // 左侧布局\n        floatWindow.style.width = '40%';\n        floatWindow.style.height = '90%';\n        //floatWindow.style.position = 'absolute';\n        floatWindow.style.left = '50px';\n        floatWindow.style.top = '5%';\n        //floatWindow.style.transform = 'translateY(-50%)';\n        leftLayoutBtn.style.display = 'none';\n        rightLayoutBtn.style.display = 'inline-block';\n    });\n\n    // 关闭按钮\n    const closeBtn = document.createElement('button');\n    closeBtn.textContent = '×';\n    closeBtn.style.padding = '2px 8px';\n    closeBtn.style.border = 'none';\n    closeBtn.style.borderRadius = '4px';\n    closeBtn.style.cursor = 'pointer';\n    controls.appendChild(closeBtn);\n\n    // 创建URL输入框容器\n    const urlContainer = document.createElement('div');\n    urlContainer.style.padding = '10px';\n    urlContainer.style.display = 'flex';\n    urlContainer.style.gap = '5px';\n    floatWindow.appendChild(urlContainer);\n\n    // 添加预设翻译按钮\n    const sogouBtn = document.createElement('button');\n    sogouBtn.textContent = '狗';\n    sogouBtn.style.padding = '5px 10px';\n    sogouBtn.style.border = 'none';\n    sogouBtn.style.borderRadius = '4px';\n    sogouBtn.style.backgroundColor = '#34a853';\n    sogouBtn.style.color = 'white';\n    sogouBtn.style.cursor = 'pointer';\n    sogouBtn.title = '搜狗翻译';\n    urlContainer.appendChild(sogouBtn);\n\n    const baiduBtn = document.createElement('button');\n    baiduBtn.textContent = '百';\n    baiduBtn.style.padding = '5px 10px';\n    baiduBtn.style.border = 'none';\n    baiduBtn.style.borderRadius = '4px';\n    baiduBtn.style.backgroundColor = '#4285f4';\n    baiduBtn.style.color = 'white';\n    baiduBtn.style.cursor = 'pointer';\n    baiduBtn.title = '百度翻译';\n    urlContainer.appendChild(baiduBtn);\n\n    const youdaoBtn = document.createElement('button');\n    youdaoBtn.textContent = '有';\n    youdaoBtn.style.padding = '5px 10px';\n    youdaoBtn.style.border = 'none';\n    youdaoBtn.style.borderRadius = '4px';\n    youdaoBtn.style.backgroundColor = '#fbbc05';\n    youdaoBtn.style.color = 'white';\n    youdaoBtn.style.cursor = 'pointer';\n    youdaoBtn.title = '有道翻译';\n    urlContainer.appendChild(youdaoBtn);\n    // URL输入框\n    const urlInput = document.createElement('input');\n    urlInput.type = 'text';\n    urlInput.placeholder = '输入网址 (例如: https://example.com)';\n    urlInput.style.flexGrow = '1';\n    urlInput.style.padding = '5px';\n    urlInput.style.border = '1px solid #ddd';\n    urlInput.style.borderRadius = '4px';\n    urlContainer.appendChild(urlInput);\n\n    // 预设翻译按钮点击事件\n    sogouBtn.addEventListener('click', () => {\n        urlInput.value = 'fanyi.sogou.com';\n        loadBtn.click();\n    });\n\n    baiduBtn.addEventListener('click', () => {\n        urlInput.value = 'fanyi.baidu.com';\n        loadBtn.click();\n    });\n    \n    youdaoBtn.addEventListener('click', () => {\n        urlInput.value = 'fanyi.youdao.com';\n        loadBtn.click();\n    });\n\n    // 加载按钮\n    const loadBtn = document.createElement('button');\n    loadBtn.textContent = '加载';\n    loadBtn.style.padding = '5px 10px';\n    loadBtn.style.border = 'none';\n    loadBtn.style.borderRadius = '4px';\n    loadBtn.style.backgroundColor = '#4285f4';\n    loadBtn.style.color = 'white';\n    loadBtn.style.cursor = 'pointer';\n    urlContainer.appendChild(loadBtn);\n\n    // 创建iframe\n    const iframe = document.createElement('iframe');\n    iframe.style.width = '100%';\n    iframe.style.height = 'calc(100% - 90px)';\n    iframe.style.border = 'none';\n    iframe.style.marginTop = '5px';\n    floatWindow.appendChild(iframe);\n\n    // 设置移动端视图\n    const setMobileView = () => {\n        const viewportMeta = document.createElement('meta');\n        viewportMeta.name = 'viewport';\n        viewportMeta.content = 'width=device-width, initial-scale=1.0';\n        iframe.contentDocument.head.appendChild(viewportMeta);\n    };\n\n    // 拖动功能\n    let isDragging = false;\n    let offsetX, offsetY;\n\n    titleBar.addEventListener('mousedown', (e) => {\n        isDragging = true;\n        offsetX = e.clientX - floatWindow.getBoundingClientRect().left;\n        offsetY = e.clientY - floatWindow.getBoundingClientRect().top;\n    });\n\n    document.addEventListener('mousemove', (e) => {\n        if (!isDragging) return;\n        floatWindow.style.left = (e.clientX - offsetX) + 'px';\n        floatWindow.style.top = (e.clientY - offsetY) + 'px';\n    });\n\n    document.addEventListener('mouseup', () => {\n        isDragging = false;\n    });\n\n    // 按钮点击事件\n    floatBtn.addEventListener('click', () => {\n        console.log('浮动按钮被点击');\n        floatWindow.style.display = floatWindow.style.display === 'none' ? 'block' : 'none';\n    });\n\n    // 最大化功能\n    maxBtn.addEventListener('click', () => {\n        const windowWidth = window.innerWidth * 0.9;\n        const windowHeight = window.innerHeight * 0.9;\n        floatWindow.style.width = windowWidth + 'px';\n        floatWindow.style.height = windowHeight + 'px';\n        floatWindow.style.left = (window.innerWidth - windowWidth) / 2 + 'px';\n        floatWindow.style.top = (window.innerHeight - windowHeight) / 2 + 'px';\n        maxBtn.style.display = 'none'; // 隐藏最大化按钮\n        restoreBtn.style.display = 'block'; // 显示恢复按钮\n    });\n\n    // 恢复功能\n    restoreBtn.addEventListener('click', () => {\n        floatWindow.style.width = '300px';\n        floatWindow.style.height = '400px';\n        restoreBtn.style.display = 'none'; // 隐藏恢复按钮\n        maxBtn.style.display = 'block'; // 显示最大化按钮\n    });\n\n    // 关闭功能\n    closeBtn.addEventListener('click', () => {\n        floatWindow.style.display = 'none';\n    });\n\n    // 加载网页\n    loadBtn.addEventListener('click', () => {\n        let url = urlInput.value.trim();\n        if (!url.startsWith('http://') && !url.startsWith('https://')) {\n            url = 'https://' + url;\n        }\n        iframe.src = url;\n        \n        // 监听iframe加载完成事件\n        iframe.onload = function() {\n            try {\n                setMobileView();\n            } catch (e) {\n                console.log('无法设置移动端视图:', e);\n            }\n        };\n    });\n\n    // 创建缩放控制按钮\n    const resizeHandle = document.createElement('div');\n    resizeHandle.style.position = 'absolute';\n    resizeHandle.style.right = '0';\n    resizeHandle.style.bottom = '0';\n    resizeHandle.style.width = '20px';\n    resizeHandle.style.height = '20px';\n    resizeHandle.style.cursor = 'nwse-resize';\n    resizeHandle.style.background = 'linear-gradient(135deg, transparent 0%, transparent 50%, #4285f4 50%, #4285f4 100%)';\n    floatWindow.appendChild(resizeHandle);\n\n    // 缩放功能\n    let isResizing = false;\n    resizeHandle.addEventListener('mousedown', (e) => {\n        isResizing = true;\n        e.stopPropagation();\n    });\n\n    document.addEventListener('mousemove', (e) => {\n        if (!isResizing) return;\n        const newWidth = e.clientX - floatWindow.getBoundingClientRect().left;\n        const newHeight = e.clientY - floatWindow.getBoundingClientRect().top;\n        floatWindow.style.width = Math.max(200, newWidth) + 'px';\n        floatWindow.style.height = Math.max(200, newHeight) + 'px';\n    \n//只执行一次，防止重复执行\n        \n        \n\n    document.addEventListener('mouseup', () => {\n       if (isResizing) { isResizing = false;\n        // 清除页面中的文本选择\n        if (window.getSelection) {\n            window.getSelection().removeAllRanges();\n        } else if (document.selection) {\n            document.selection.empty();\n        }}\n    });\n\n});\n    // 添加一些基础样式\n    GM_addStyle(`\n        button {\n            transition: all 0.2s;\n        }\n        button:hover {\n            opacity: 0.8;\n        }\n        input:focus {\n            outline: none;\n            border-color: #4285f4 !important;\n        }\n        .resize-handle {\n            position: absolute;\n            right: 0;\n            bottom: 0;\n            width: 20px;\n            height: 20px;\n            cursor: nwse-resize;\n            background: linear-gradient(135deg, transparent 0%, transparent 50%, #4285f4 50%, #4285f4 100%);\n        }\n    `);", "enabled": false}, {"id": "1753093549715", "name": "密码", "match": "*://so.com", "code": "// 创建样式\nconst style = document.createElement('style');\nstyle.textContent = `\n  .text-saver-container {\n    position: absolute;\n    z-index: 99999;\n    display: flex;\n    gap: 5px;\n    margin-left: 5px;\n  }\n  \n  .text-saver-btn {\n    width: 30px;\n    height: 30px;\n    border: none;\n    border-radius: 50%;\n    cursor: pointer;\n    font-size: 16px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: 0 2px 5px rgba(0,0,0,0.2);\n    background: white;\n    transition: all 0.2s;\n  }\n  \n  .text-saver-btn:hover {\n    transform: scale(1.1);\n  }\n  \n  .text-saver-list {\n    position: absolute;\n    z-index: 99999;\n    background: white;\n    border: 1px solid #ccc;\n    border-radius: 5px;\n    padding: 10px;\n    max-height: 200px;\n    overflow-y: auto;\n    min-width: 200px;\n    box-shadow: 0 3px 10px rgba(0,0,0,0.2);\n  }\n  \n  .text-saver-item {\n    padding: 8px;\n    margin: 5px 0;\n    border-radius: 3px;\n    cursor: pointer;\n    transition: background 0.2s;\n  }\n  \n  .text-saver-item:hover {\n    background: #f0f0f0;\n  }\n`;\ndocument.head.appendChild(style);\n\n// 全局变量\nlet currentContainer = null;\nlet currentInput = null;\nlet listPopup = null;\n\n// 获取当前域名\nconst currentDomain = window.location.hostname;\n\n// 创建按钮容器\nfunction createContainer() {\n  const container = document.createElement('div');\n  container.className = 'text-saver-container';\n  \n  // 创建保存按钮\n  const saveBtn = document.createElement('button');\n  saveBtn.className = 'text-saver-btn';\n  saveBtn.innerHTML = '📝';\n  saveBtn.title = '保存当前文本';\n  saveBtn.addEventListener('click', () => {\n    if (currentInput) {\n      // 调用预加载脚本提供的API保存文本\n      window.textStorage.saveText(currentDomain, currentInput.value);\n      // 简单提示\n      saveBtn.innerHTML = '✅';\n      setTimeout(() => {\n        saveBtn.innerHTML = '📝';\n      }, 1000);\n    }\n  });\n  \n  container.appendChild(saveBtn);\n  \n  // 检查是否有保存的文本，如果有则创建加载按钮\n  const savedTexts = window.textStorage.getSavedTexts(currentDomain);\n  if (savedTexts.length > 0) {\n    const loadBtn = document.createElement('button');\n    loadBtn.className = 'text-saver-btn';\n    loadBtn.innerHTML = '📭';\n    loadBtn.title = '加载保存的文本';\n    loadBtn.addEventListener('click', (e) => {\n      e.stopPropagation();\n      toggleSavedList(container);\n    });\n    \n    container.appendChild(loadBtn);\n  }\n  \n  return container;\n}\n\n// 显示/隐藏保存的文本列表\nfunction toggleSavedList(container) {\n  if (listPopup) {\n    listPopup.remove();\n    listPopup = null;\n    return;\n  }\n  \n  const savedTexts = window.textStorage.getSavedTexts(currentDomain);\n  \n  listPopup = document.createElement('div');\n  listPopup.className = 'text-saver-list';\n  \n  // 定位到按钮下方\n  const rect = container.getBoundingClientRect();\n  listPopup.style.top = `${rect.bottom + window.scrollY + 5}px`;\n  listPopup.style.left = `${rect.left + window.scrollX}px`;\n  \n  // 添加标题\n  const title = document.createElement('div');\n  title.style.fontWeight = 'bold';\n  title.style.marginBottom = '10px';\n  title.textContent = `已保存的文本 (${savedTexts.length})`;\n  listPopup.appendChild(title);\n  \n  // 添加分隔线\n  const divider = document.createElement('hr');\n  divider.style.margin = '5px 0';\n  listPopup.appendChild(divider);\n  \n  // 添加文本项\n  savedTexts.forEach(item => {\n    const itemEl = document.createElement('div');\n    itemEl.className = 'text-saver-item';\n    \n    // 显示文本预览\n    const preview = document.createElement('div');\n    preview.textContent = item.text.length > 30 \n      ? item.text.substring(0, 30) + '...' \n      : item.text;\n    itemEl.appendChild(preview);\n    \n    // 显示时间\n    const time = document.createElement('div');\n    time.style.fontSize = '10px';\n    time.style.color = '#666';\n    time.textContent = new Date(item.timestamp).toLocaleString();\n    itemEl.appendChild(time);\n    \n    // 点击事件：将文本填入输入框\n    itemEl.addEventListener('click', () => {\n      if (currentInput) {\n        currentInput.value = item.text;\n        // 触发输入事件，确保表单能检测到变化\n        const event = new Event('input', { bubbles: true });\n        currentInput.dispatchEvent(event);\n      }\n      listPopup.remove();\n      listPopup = null;\n    });\n    \n    listPopup.appendChild(itemEl);\n  });\n  \n  document.body.appendChild(listPopup);\n  \n  // 点击页面其他地方关闭列表\n  document.addEventListener('click', closeListOnOutsideClick, { once: true });\n}\n\n// 点击外部关闭列表\nfunction closeListOnOutsideClick(e) {\n  if (listPopup && !listPopup.contains(e.target) && \n      !e.target.classList.contains('text-saver-btn')) {\n    listPopup.remove();\n    listPopup = null;\n  }\n}\n\n// 处理输入框获得焦点事件\nfunction handleFocus(e) {\n  // 只处理文本输入框\n  const tagName = e.target.tagName.toLowerCase();\n  const type = e.target.type ? e.target.type.toLowerCase() : '';\n  \n  if ((tagName === 'input' && (type === 'text' || type === 'search' || \n      type === 'textarea' || !type)) || tagName === 'textarea') {\n    \n    // 移除之前的容器\n    if (currentContainer) {\n      currentContainer.remove();\n    }\n    \n    currentInput = e.target;\n    \n    // 创建新容器\n    currentContainer = createContainer();\n    \n    // 设置容器位置\n    const rect = e.target.getBoundingClientRect();\n    currentContainer.style.top = `${rect.top + window.scrollY}px`;\n    currentContainer.style.left = `${rect.right + window.scrollX}px`;\n    \n    document.body.appendChild(currentContainer);\n  }\n}\n\n// 处理输入框失去焦点事件\nfunction handleBlur() {\n  // 延迟移除，以便点击按钮能生效\n  setTimeout(() => {\n    if (currentContainer && (!document.activeElement || \n        !currentContainer.contains(document.activeElement))) {\n      currentContainer.remove();\n      currentContainer = null;\n      currentInput = null;\n      \n      // 关闭列表\n      if (listPopup) {\n        listPopup.remove();\n        listPopup = null;\n      }\n    }\n  }, 200);\n}\n\n// 监听所有输入框的焦点事件\ndocument.addEventListener('focusin', handleFocus);\ndocument.addEventListener('focusout', handleBlur);\n\n// 页面滚动时调整按钮位置\nwindow.addEventListener('scroll', () => {\n  if (currentContainer && currentInput) {\n    const rect = currentInput.getBoundingClientRect();\n    currentContainer.style.top = `${rect.top + window.scrollY}px`;\n    currentContainer.style.left = `${rect.right + window.scrollX}px`;\n  }\n  \n  if (listPopup && currentContainer) {\n    const rect = currentContainer.getBoundingClientRect();\n    listPopup.style.top = `${rect.bottom + window.scrollY + 5}px`;\n    listPopup.style.left = `${rect.left + window.scrollX}px`;\n  }\n});", "enabled": false}, {"id": "1753246829614", "name": "库存助手", "match": "https://qn.taobao.com/*,https://mms.pinduoduo.com/print/*", "code": "// 最大存储订单数\n    const MAX_ORDERS = 1000;\n    // 数据保留天数\n    const DATA_EXPIRE_DAYS = 30;\n\n    // 添加样式\n    const style = document.createElement('style');\n    style.textContent = `\n        .no-memo-row {\n            background-color: #ffeeee !important;\n        }\n        .inventory-match {\n            display: inline-flex;\n            align-items: center;\n            margin-left: 8px;\n            padding: 2px 8px;\n            background: #f0f7ff;\n            border-radius: 4px;\n            border: 1px solid #d0e3ff;\n            position: relative;\n        }\n        .inventory-match img {\n            width: 16px;\n            height: 16px;\n            margin-right: 4px;\n            border-radius: 2px;\n        }\n        .inventory-match .short-name {\n            font-size: 12px;\n            margin-right: 4px;\n            color: #333;\n        }\n        .inventory-match .stock-info {\n            font-size: 12px;\n            color: #666;\n        }\n        .inventory-match .confirm-btn {\n            margin-left: 4px;\n            padding: 1px 6px;\n            font-size: 12px;\n            background: #1890ff;\n            color: white;\n            border: none;\n            border-radius: 2px;\n            cursor: pointer;\n        }\n        .inventory-match .confirm-btn:hover {\n            background: #40a9ff;\n        }\n        .inventory-match .confirmed {\n            color: #52c41a;\n        }\n        .inventory-match .dropdown-btn {\n            margin-left: 4px;\n            padding: 1px 6px;\n            font-size: 12px;\n            background: #f0f0f0;\n            color: #333;\n            border: none;\n            border-radius: 2px;\n            cursor: pointer;\n        }\n        .inventory-match .dropdown-btn:hover {\n            background: #e0e0e0;\n        }\n        .inventory-match .options-dropdown {\n            position: absolute;\n            top: 100%;\n            left: 0;\n            background: white;\n            border: 1px solid #ddd;\n            border-radius: 4px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            z-index: 100;\n            display: none;\n        }\n        .inventory-match .options-dropdown.show {\n            display: block;\n        }\n        .inventory-match .option-item {\n            padding: 6px 12px;\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n        }\n        .inventory-match .option-item:hover {\n            background: #f5f5f5;\n        }\n        .inventory-match .option-item img {\n            width: 20px;\n            height: 20px;\n            margin-right: 8px;\n        }\n    `;\ndocument.body.appendChild(style);\n    // 清理过期数据\n    function cleanExpiredData() {\n        const now = Date.now();\n        // 将GM_listValues替换为localStorage键获取\n        const allKeys = Object.keys(localStorage);\n        const orderKeys = allKeys.filter(key => key.startsWith('order_'));\n\n        if (orderKeys.length > MAX_ORDERS) {\n            // 按时间排序，删除最早的\n            orderKeys.sort((a, b) => {\n                return JSON.parse(localStorage.getItem(a)).timestamp - JSON.parse(localStorage.getItem(b)).timestamp;\n            });\n\n            const toDelete = orderKeys.slice(0, orderKeys.length - MAX_ORDERS);\n            toDelete.forEach(key => localStorage.removeItem(key));\n        }\n\n        // 清理30天前的数据\n        orderKeys.forEach(key => {\n            const data = JSON.parse(localStorage.getItem(key));\n            if (now - data.timestamp > DATA_EXPIRE_DAYS * 24 * 60 * 60 * 1000) {\n                localStorage.removeItem(key);\n            }\n        });\n    }\n\n    // 初始化清理\n    cleanExpiredData();\n\n    // 主处理函数\n    function processOrders() {\n        console.groupCollapsed('[Inventory Helper] 开始处理订单...');\n        // 判断当前是拼多多还是淘宝页面\n        const isPinduoduo = window.location.href.includes('pinduoduo');\n        const isTaobao = window.location.href.includes('taobao');\n        console.log(`检测到页面: ${isPinduoduo ? '拼多多' : isTaobao ? '淘宝' : '未知'}`);\n\n        let totalRows = 0;\n        let processedRows = 0;\n        let skippedRows = 0;\n        let totalProducts = 0;\n        let processedProducts = 0;\n        let skippedProducts = 0;\n\n        let orderRows, orderSnSelector, productItemsSelector, productNameSelector;\n\n        if (isPinduoduo) {\n            orderRows = document.querySelectorAll('tr[data-testid=\"beast-core-table-body-tr\"]');\n            orderSnSelector = '.order-sn';\n            productItemsSelector = '.content-wrapper';\n            productNameSelector = 'span:first-child';\n            console.log('[Inventory Helper] 使用拼多多选择器');\n        } else if (isTaobao) {\n            orderRows = document.querySelectorAll('tr.art-table-row');\n            orderSnSelector = '.ORDER_ID_mergeOrderItemList__i4jwQ, .order-sn'; // 更宽松的选择器\n            productItemsSelector = '.ITEM_INFO_gridBoxShowAll__hEEYY > div, .ITEM_INFO_gridBox__4og3b > div:not(.ITEM_INFO_ellipsisbtn__NMAaI):has(.ITEM_INFO_titleBox__5yPen)'; // 两种可能的商品容器结构\n            productNameSelector = '.ITEM_INFO_gridBox__4og3b .ITEM_INFO_itemLayout2__6o20D span:first-child'; // 精确匹配商品简称\n            console.log('[Inventory Helper] 使用淘宝选择器 - 将匹配商品简称，使用选择器:', productItemsSelector);\n        }\n\n        if (!orderRows) return;\n\n        orderRows.forEach(row => {\n            totalRows++;\n            const orderSn = row.querySelector(orderSnSelector)?.textContent?.trim();\n            if (!orderSn) {\n                console.log(`[跳过] 订单行 ${totalRows}: 未找到订单号`);\n                skippedRows++;\n                return;\n            }\n\n            // 检查备注\n            let hasMemo = false;\n            if (isPinduoduo) {\n                const memoCell = row.querySelector('[class=\"sc-kvZOFW jadrmy\"]');\n                if (memoCell) {\n                    // 有备注时会显示\"条备注\"文本\n                    hasMemo = memoCell.textContent.includes('条备注');\n                }\n            } else if (isTaobao) {\n                hasMemo = row.querySelector('.MEMO_OR_MESSAGE_box__m4EEl div')?.textContent?.trim();\n            }\n\n            if (!hasMemo) {\n                row.classList.add('no-memo-row');\n                console.log(`订单 ${orderSn}: 无备注`);\n            } else {\n                row.classList.remove('no-memo-row');\n            }\n\n            // 检查是否已处理过\n            const orderKey = `order_${orderSn}`;\n            // 将GM_getValue替换为localStorage获取\n            const processedData = JSON.parse(localStorage.getItem(orderKey));\n\n            const productItems = row.querySelectorAll(productItemsSelector);\n            totalProducts += productItems.length;\n            console.groupCollapsed(`订单 ${orderSn}: 找到 ${productItems.length} 个商品项`);\n\n                productItems.forEach((item, index) => {\n                    // 排除脚本自己添加的元素\n                    if (item.classList.contains('inventory-match') || item.dataset.inventoryProcessed) {\n                        console.log(`[跳过] 商品 ${index + 1}: 已处理过`);\n                        skippedProducts++;\n                        return;\n                    }\n\n                // 新的商品简称匹配逻辑\n                let productName = null;\n\n                // 平台特定处理\n                if (isPinduoduo) {\n                    // 拼多多商品简称提取\n                    productName = item.querySelector('span:first-child')?.textContent?.trim();\n                } else {\n                    // 淘宝商品简称提取\n                    // 方法1：查找包含文本的最后一个.ITEM_INFO_itemLayout2__6o20D\n                    const shortNameElements = item.querySelectorAll('.ITEM_INFO_itemLayout2__6o20D');\n                    for (let i = shortNameElements.length - 1; i >= 0; i--) {\n                        const text = shortNameElements[i].textContent.trim();\n                        if (text) {\n                            productName = text;\n                            break;\n                        }\n                    }\n\n                    // 方法2：如果没找到，尝试从商品标题中提取可能的简称\n                    if (!productName) {\n                        const title = item.querySelector('.ITEM_INFO_titleBox__5yPen span span')?.textContent?.trim();\n                        if (title) {\n                            // 尝试从标题中提取类似\"abc123\"的格式\n                            const match = title.match(/\\b[a-z]{3,}\\d{2,}\\b/i);\n                            if (match) {\n                                productName = match[0];\n                            }\n                        }\n                    }\n                }\n\n                console.log('[Inventory Helper] 商品元素结构:', item);\n                console.log(`商品 ${index + 1} 简称:`, productName || '未获取到商品名');\n                if (!productName) {\n                    console.log(`[跳过] 商品 ${index + 1}: 无法获取商品简称`);\n                    skippedProducts++;\n                    return;\n                }\n                processedProducts++;\n\n                // 如果已处理过，显示已确认状态\n                if (processedData?.products?.includes(productName)) {\n                    if (!item.querySelector('.inventory-match.confirmed')) {\n                        const confirmedTag = document.createElement('span');\n                        confirmedTag.className = 'inventory-match confirmed';\n                        confirmedTag.innerHTML = '✓ 已关联';\n                        confirmedTag.style.color = 'green';\n                        const productContainer = item.querySelector(':scope > div:nth-child(2) > div') || item;\n                        productContainer.appendChild(confirmedTag);\n                    }\n                    return;\n                }\n\n                // 搜索匹配商品\n                fetch(`http://127.0.0.1:7215/search?keyword=${encodeURIComponent(productName)}`)\n                    .then(response => {\n                        if (!response.ok) throw new Error('Network response error');\n                        return response.json();\n                    })\n                    .then(products => {\n                        if (products.length > 0) {\n                            console.log('[Inventory Helper] 匹配到商品:', products);\n                            addInventoryInfo(item, products, orderSn, productName);\n                        }\n                    })\n                    .catch(error => {\n                        console.error('Failed to fetch products:', error);\n                    });\n                console.groupEnd();\n            });\n\n            console.groupEnd();\n        });\n\n        console.log(`处理统计:\n总订单行数: ${totalRows}\n已处理订单: ${processedRows}\n跳过订单: ${skippedRows}\n总商品数: ${totalProducts}\n已处理商品: ${processedProducts}\n跳过商品: ${skippedProducts}`);\n        console.groupEnd();\n    }\n\n    // 添加库存信息\n    function addInventoryInfo(item, products, orderSn, productName) {\n        // 检查是否已添加过\n        if (item.querySelector('.inventory-match') || item.dataset.inventoryProcessed) {\n            return;\n        }\n\n        const inventoryTag = document.createElement('div');\n        inventoryTag.className = 'inventory-match';\n\n        // 创建下拉菜单\n        const dropdown = document.createElement('div');\n        dropdown.className = 'options-dropdown';\n\n        // 添加选项\n        products.forEach(product => {\n            const option = document.createElement('div');\n            option.className = 'option-item';\n            option.innerHTML = `\n                <img src=\"${product.image}\" onerror=\"this.style.display='none'\">\n                <span>${product.shortName || product.name} (库存: ${product.stock})</span>\n            `;\n            option.addEventListener('click', () => {\n                selectProduct(product);\n                dropdown.classList.remove('show');\n            });\n            dropdown.appendChild(option);\n        });\n\n        // 主显示区域\n        const mainProduct = products[0];\n        inventoryTag.innerHTML = `\n            <img src=\"${mainProduct.image}\" onerror=\"this.style.display='none'\">\n            <span class=\"short-name\">${mainProduct.shortName || mainProduct.name}</span>\n            <span class=\"stock-info\">库存: ${mainProduct.stock}</span>\n            <button class=\"confirm-btn\">确认</button>\n            ${products.length > 1 ? '<button class=\"dropdown-btn\">▼</button>' : ''}\n        `;\n        inventoryTag.appendChild(dropdown);\n\n        // 淘宝页面插入到商品容器内部\n        if (window.location.href.includes('taobao')) {\n            const productContainer = item.querySelector(':scope > div:nth-child(2) > div') || item;\n            productContainer.appendChild(inventoryTag);\n        } else {\n            item.appendChild(inventoryTag);\n        }\n\n        item.dataset.inventoryProcessed = 'true';\n\n        // 下拉按钮点击事件\n        if (products.length > 1) {\n            const dropdownBtn = inventoryTag.querySelector('.dropdown-btn');\n            dropdownBtn.addEventListener('click', (e) => {\n                e.stopPropagation();\n                dropdown.classList.toggle('show');\n            });\n        }\n\n        // 点击外部关闭下拉\n        document.addEventListener('click', () => {\n            dropdown.classList.remove('show');\n        });\n\n        // 选择商品函数\n        function selectProduct(product) {\n            // 更新库存\n            fetch(`http://127.0.0.1:7215/sell_product/${product.id}`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            })\n            .then(response => {\n                if (!response.ok) throw new Error('Network response error');\n                return response.json();\n            })\n            .then(result => {\n                if (result.success) {\n                    // 更新UI\n                    inventoryTag.innerHTML = '<span class=\"confirmed\">✓ 已关联</span>';\n\n                    // 保存订单处理记录\n                    const orderKey = `order_${orderSn}`;\n                    const existingData = JSON.parse(localStorage.getItem(orderKey)) || {products: [], timestamp: Date.now()};\n                    existingData.products.push(productName);\n                    console.log('[Inventory Helper] 保存订单数据:', orderKey, existingData);\n                    localStorage.setItem(orderKey, JSON.stringify(existingData));\n                    console.log('[Inventory Helper] 当前存储的订单:', Object.keys(localStorage).filter(k => k.startsWith('order_')));\n                } else {\n                    alert(result.message || '库存更新失败');\n                }\n            })\n            .catch(error => {\n                console.error('Failed to process sale:', error);\n                alert('库存更新失败');\n            });\n        }\n\n        // 确认按钮点击事件\n        inventoryTag.querySelector('.confirm-btn').addEventListener('click', function() {\n            selectProduct(mainProduct);\n        });\n    }\n\n    // 监听DOM变化\n    const observer = new MutationObserver(function(mutations) {\n        let shouldProcess = false;\n        mutations.forEach(function(mutation) {\n            if (mutation.addedNodes.length) {\n                shouldProcess = true;\n            }\n        });\n        if (shouldProcess) {\n            // 使用防抖避免频繁处理\n            clearTimeout(observer.debounceTimer);\n            observer.debounceTimer = setTimeout(processOrders, 300);\n        }\n    });\n\n    // 开始观察\n    observer.observe(document.body, {\n        childList: true,\n        subtree: true\n    });\n\n    // 初始处理\n    processOrders();", "enabled": true}, {"id": "1753254903091", "name": "密码2", "match": "*://*", "code": "const STORAGE_KEY = `textHistory_${location.hostname}`;\n            \n            // 为所有输入框和文本域添加处理\n            setupTextHistory();\n            \n            // 初始化演示页面的功能\n            initDemoPage();\n            \n            function setupTextHistory() {\n                // 事件委托处理焦点事件\n                document.addEventListener('focus', function(e) {\n                    const target = e.target;\n                    if (isTextInput(target)) {\n                        addHistoryButtons(target);\n                    }\n                }, true);\n                \n                // 事件委托处理失去焦点事件\n                document.addEventListener('blur', function(e) {\n                    const target = e.target;\n                    if (isTextInput(target)) {\n                        const container = target.closest('.history-container');\n                        if (container) {\n                            // 延迟隐藏按钮，以便点击按钮\n                            setTimeout(() => {\n                                if (document.activeElement !== target && \n                                    !container.contains(document.activeElement)) {\n                                    container.classList.remove('focused');\n                                }\n                            }, 200);\n                        }\n                    }\n                }, true);\n                \n                // 点击页面其他地方时关闭所有下拉框\n                document.addEventListener('click', function(e) {\n                    document.querySelectorAll('.history-dropdown').forEach(dropdown => {\n                        if (!dropdown.contains(e.target)) {\n                            dropdown.classList.remove('show');\n                        }\n                    });\n                });\n            }\n            \n            function isTextInput(element) {\n                return (element.tagName === 'INPUT' && element.type === 'text') || \n                       element.tagName === 'TEXTAREA';\n            }\n            \n            function addHistoryButtons(inputElement) {\n                // 查找或创建容器\n                let container = inputElement.closest('.history-container');\n                if (!container) {\n                    container = createHistoryContainer(inputElement);\n                }\n                \n                // 添加focused类以显示按钮\n                container.classList.add('focused');\n                \n                // 获取相关元素\n                const saveBtn = container.querySelector('.save-btn');\n                const historyBtn = container.querySelector('.history-toggle');\n                const dropdown = container.querySelector('.history-dropdown');\n                const historyList = container.querySelector('.history-list');\n                const clearBtn = container.querySelector('.clear-history');\n                \n                // 更新按钮可见性\n                updateButtonsVisibility(container);\n                \n                // 添加事件监听器\n                saveBtn.onclick = () => saveText(inputElement, container);\n                historyBtn.onclick = (e) => {\n                    e.stopPropagation();\n                    dropdown.classList.toggle('show');\n                    if (dropdown.classList.contains('show')) {\n                        populateDropdown(historyList, inputElement);\n                    }\n                };\n                \n                clearBtn.onclick = (e) => {\n                    e.stopPropagation();\n                    clearHistory(container);\n                };\n            }\n            \n            function createHistoryContainer(inputElement) {\n                // 创建容器\n                const container = document.createElement('div');\n                container.className = 'history-container';\n                \n                // 包装原始输入框\n                const wrapper = inputElement.parentNode;\n                wrapper.insertBefore(container, inputElement);\n                container.appendChild(inputElement);\n                \n                // 创建按钮组\n                const buttonGroup = document.createElement('div');\n                buttonGroup.className = 'history-buttons';\n                container.appendChild(buttonGroup);\n                \n                // 创建保存按钮\n                const saveBtn = document.createElement('button');\n                saveBtn.innerHTML = '📝';\n                saveBtn.className = 'history-btn save-btn';\n                saveBtn.title = '保存当前文本';\n                \n                // 创建历史按钮\n                const historyBtn = document.createElement('button');\n                historyBtn.innerHTML = '📭';\n                historyBtn.className = 'history-btn history-toggle';\n                historyBtn.title = '查看历史记录';\n                historyBtn.style.display = 'none';\n                \n                buttonGroup.appendChild(saveBtn);\n                buttonGroup.appendChild(historyBtn);\n                \n                // 创建下拉菜单\n                const dropdown = document.createElement('div');\n                dropdown.className = 'history-dropdown';\n                container.appendChild(dropdown);\n                \n                // 创建下拉菜单头部\n                const header = document.createElement('div');\n                header.className = 'history-header';\n                header.innerHTML = '<span>历史记录</span><button class=\"clear-history\">清除</button>';\n                \n                // 创建历史列表\n                const historyList = document.createElement('ul');\n                historyList.className = 'history-list';\n                \n                dropdown.appendChild(header);\n                dropdown.appendChild(historyList);\n                \n                return container;\n            }\n            \n            function updateButtonsVisibility(container) {\n                const historyBtn = container.querySelector('.history-toggle');\n                const history = getHistory();\n                \n                if (history.length > 0) {\n                    historyBtn.style.display = 'flex';\n                } else {\n                    historyBtn.style.display = 'none';\n                    container.querySelector('.history-dropdown').classList.remove('show');\n                }\n            }\n            \n            function saveText(inputElement, container) {\n                const text = inputElement.value.trim();\n                if (!text) return;\n                \n                const history = getHistory();\n                \n                // 防止重复保存相同的文本\n                if (!history.includes(text)) {\n                    history.unshift(text); // 添加到开头\n                    \n                    // 限制历史记录数量（最多20条）\n                    if (history.length > 20) {\n                        history.pop();\n                    }\n                    \n                    localStorage.setItem(STORAGE_KEY, JSON.stringify(history));\n                    updateButtonsVisibility(container);\n                    \n                    // 显示保存成功反馈\n                    const saveBtn = container.querySelector('.save-btn');\n                    const originalHTML = saveBtn.innerHTML;\n                    saveBtn.innerHTML = '✓';\n                    saveBtn.style.background = '#40c057';\n                    \n                    setTimeout(() => {\n                        saveBtn.innerHTML = originalHTML;\n                        saveBtn.style.background = '';\n                    }, 1000);\n                }\n            }\n            \n            function getHistory() {\n                const historyJSON = localStorage.getItem(STORAGE_KEY);\n                return historyJSON ? JSON.parse(historyJSON) : [];\n            }\n            \n            function populateDropdown(historyList, inputElement) {\n                historyList.innerHTML = '';\n                const history = getHistory();\n                \n                if (history.length === 0) {\n                    const emptyItem = document.createElement('div');\n                    emptyItem.className = 'history-empty';\n                    emptyItem.textContent = '无历史记录';\n                    historyList.appendChild(emptyItem);\n                    return;\n                }\n                \n                history.forEach(text => {\n                    const item = document.createElement('li');\n                    item.className = 'history-item';\n                    item.textContent = text;\n                    item.onclick = () => {\n                        inputElement.value = text;\n                        inputElement.focus();\n                        inputElement.closest('.history-container')\n                            .querySelector('.history-dropdown')\n                            .classList.remove('show');\n                    };\n                    historyList.appendChild(item);\n                });\n            }\n            \n            function clearHistory(container) {\n                localStorage.removeItem(STORAGE_KEY);\n                updateButtonsVisibility(container);\n                container.querySelector('.history-dropdown').classList.remove('show');\n            }\n            \n            // 演示页面初始化\n            function initDemoPage() {\n                // 为演示页面的输入框初始化容器\n                document.querySelectorAll('input[type=\"text\"], textarea').forEach(input => {\n                    if (!input.closest('.history-container')) {\n                        addHistoryButtons(input);\n                    }\n                });\n                \n                // 添加一些示例历史记录\n                if (!localStorage.getItem(STORAGE_KEY)) {\n                    const demoHistory = [\n                        \"这是一个示例历史记录\",\n                        \"欢迎使用文本框历史功能\",\n                        \"点击📝保存当前文本\",\n                        \"点击📭查看历史记录\"\n                    ];\n                    localStorage.setItem(STORAGE_KEY, JSON.stringify(demoHistory));\n                }\n            }", "enabled": false}, {"id": "1753276376911", "name": "密码3", "match": "*://*,https://loginmyseller.taobao.com/*", "code": "// ==UserScript==\n// @name         文本框内容记录工具\n// @namespace    http://tampermonkey.net/\n// @version      0.1\n// @description  在文本框焦点时显示记录按钮，保存内容到localStorage并提供恢复功能\n// <AUTHOR>\n// @match        *://*/*\n// @grant        none\n// ==/UserScript==\n\n(function() {\n    'use strict';\n\n    // 存储当前活动的文本框元素\n    let activeInput = null;\n    // 存储按钮元素\n    let saveButton = null;\n    let loadButton = null;\n    let historyPanel = null;\n\n    // 初始化函数\n    function init() {\n        // 创建样式\n        createStyles();\n        console.log('已监听focus');\n        \n        // 方案1：使用MutationObserver监听动态生成的输入框\n        const observer = new MutationObserver((mutations) => {\n            mutations.forEach(mutation => {\n                if (mutation.addedNodes.length) {\n                    const loginForm = document.getElementById('login-form');\n                    if (loginForm) {\n                        // 绑定具体输入框事件\n                        const usernameInput = document.getElementById('fm-login-id');\n                        const passwordInput = document.getElementById('fm-login-password');\n                        if (usernameInput) usernameInput.addEventListener('focus', handleInputFocus);\n                        if (passwordInput) passwordInput.addEventListener('focus', handleInputFocus);\n                        observer.disconnect(); // 绑定后停止观察\n                    }\n                }\n            });\n        });\n\n        // 观察body下的DOM变化\n        observer.observe(document.body, {\n            childList: true,\n            subtree: true\n        });\n\n        // 方案2：直接查询并绑定（备用）\n        setTimeout(() => {\n            const usernameInput = document.getElementById('fm-login-id');\n            const passwordInput = document.getElementById('fm-login-password');\n            if (usernameInput) usernameInput.addEventListener('focus', handleInputFocus);\n            if (passwordInput) passwordInput.addEventListener('focus', handleInputFocus);\n        }, 1000);\n    }\n\n    // 创建必要的CSS样式\n    function createStyles() {\n        console.log('创建样式');\n        const style = document.createElement('style');\n        style.textContent = `\n            .text-saver-btn {\n                position: absolute;\n                padding: 5px 10px;\n                margin-left: 5px;\n                cursor: pointer;\n                border: none;\n                border-radius: 4px;\n                font-size: 16px;\n                z-index: 9999;\n            }\n            .save-btn {\n                background-color: #4CAF50;\n                color: white;\n            }\n            .load-btn {\n                background-color: #2196F3;\n                color: white;\n                margin-left: 5px;\n            }\n            .history-panel {\n                position: absolute;\n                background: white;\n                border: 1px solid #ccc;\n                border-radius: 4px;\n                box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n                padding: 10px;\n                max-height: 200px;\n                overflow-y: auto;\n                z-index: 10000;\n            }\n            .history-item {\n                padding: 5px;\n                margin: 3px 0;\n                border-radius: 3px;\n                cursor: pointer;\n            }\n            .history-item:hover {\n                background-color: #f0f0f0;\n            }\n        `;\n        document.head.appendChild(style);\n    }\n\n    // 处理输入框获得焦点事件\n    function handleInputFocus(e) {\n        const target = e.target;\n        console.log('检测到焦点元素:', target.tagName, target.type); // 添加调试日志\n        // 扩大选择器范围，包含更多可能的输入类型\n        if ((target.tagName === 'INPUT' && ['text', 'password', 'email', 'tel', 'url', 'number'].includes(target.type)) ||\n            target.tagName === 'TEXTAREA' || target.isContentEditable) {\n            console.log('输入框获得焦点');\n            activeInput = target;\n            createButtons();\n            checkForHistory();\n        }\n    }\n\n    // 处理输入框失去焦点事件\n    function handleInputBlur(e) {\n        console.log('输入框失去焦点');\n        // 延迟移除按钮，以便点击按钮能触发事件\n        setTimeout(() => {\n            if (activeInput && document.activeElement !== saveButton && document.activeElement !== loadButton &&\n                !historyPanel?.contains(document.activeElement)) {\n                removeButtons();\n                activeInput = null;\n            }\n        }, 200);\n    }\n\n    // 创建保存和加载按钮\n    function createButtons() {\n        // 先移除已存在的按钮\n        removeButtons();\n\n        // 创建保存按钮\n        saveButton = document.createElement('button');\n        saveButton.className = 'text-saver-btn save-btn';\n        saveButton.textContent = '📝';\n        saveButton.title = '保存当前内容';\n        saveButton.addEventListener('click', saveCurrentContent);\n\n        // 获取输入框位置并定位按钮\n        positionButton(saveButton);\n        console.log('保存按钮位置:', saveButton.style.left, saveButton.style.top);\n        // 添加到文档\n        document.body.appendChild(saveButton);\n    }\n\n    // 检查是否有历史记录并创建加载按钮\n    function checkForHistory() {\n        const history = getSavedHistory();\n        if (history && history.length > 0) {\n            // 创建加载按钮\n            loadButton = document.createElement('button');\n            loadButton.className = 'text-saver-btn load-btn';\n            loadButton.textContent = '📭';\n            loadButton.title = '查看保存的内容';\n            loadButton.addEventListener('click', toggleHistoryPanel);\n\n            // 定位在保存按钮旁边\n            positionButton(loadButton, true);\n\n            document.body.appendChild(loadButton);\n        }\n    }\n\n    // 定位按钮\n    function positionButton(button, isLoadButton = false) {\n        if (!activeInput) return;\n\n        const rect = activeInput.getBoundingClientRect();\n        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;\n\n        button.style.top = `${rect.top + scrollTop}px`;\n        if (isLoadButton && saveButton) {\n            button.style.left = `${rect.right + scrollLeft + saveButton.offsetWidth + 5}px`;\n        } else {\n            button.style.left = `${rect.right + scrollLeft + 5}px`;\n        }\n    }\n\n    // 移除所有按钮和面板\n    function removeButtons() {\n        if (saveButton) {\n            saveButton.remove();\n            saveButton = null;\n        }\n        if (loadButton) {\n            loadButton.remove();\n            loadButton = null;\n        }\n        if (historyPanel) {\n            historyPanel.remove();\n            historyPanel = null;\n        }\n    }\n    // 保存当前内容到localStorage\n    function saveCurrentContent() {\n        if (!activeInput) return;\n\n        const content = activeInput.value;\n        if (!content.trim()) return;\n\n        // 获取当前域名\n        const domain = window.location.hostname;\n        // 获取现有历史记录\n        let history = getSavedHistory();\n        if (!history) history = [];\n\n        // 添加新记录\n        history.unshift({\n            content: content,\n            timestamp: new Date().toISOString(),\n            page: document.title,\n            url: window.location.href\n        });\n\n        // 限制历史记录数量（最多50条）\n        if (history.length > 50) {\n            history = history.slice(0, 50);\n        }\n\n        // 保存到localStorage\n        localStorage.setItem(`textSaver_${domain}`, JSON.stringify(history));\n\n        // 显示加载按钮\n        checkForHistory();\n\n        // 简单的反馈\n        alert('内容已保存！');\n    }\n    // 从localStorage获取保存的历史记录\n    function getSavedHistory() {\n        const domain = window.location.hostname;\n        const historyStr = localStorage.getItem(`textSaver_${domain}`);\n        return historyStr ? JSON.parse(historyStr) : null;\n    }\n\n    // 切换历史记录面板\n    function toggleHistoryPanel() {\n        if (historyPanel) {\n            historyPanel.remove();\n            historyPanel = null;\n            return;\n        }\n\n        const history = getSavedHistory();\n        if (!history || history.length === 0) return;\n\n        // 创建面板\n        historyPanel = document.createElement('div');\n        historyPanel.className = 'history-panel';\n\n        // 定位面板\n        positionButton(historyPanel, true);\n        historyPanel.style.top = `${parseInt(loadButton.style.top) + loadButton.offsetHeight + 5}px`;\n        historyPanel.style.width = '300px';\n\n        // 添加标题\n        const title = document.createElement('div');\n        title.textContent = '保存的内容:';\n        title.style.fontWeight = 'bold';\n        title.style.marginBottom = '10px';\n        historyPanel.appendChild(title);\n\n        // 添加历史记录项\n        history.forEach((item, index) => {\n            const itemEl = document.createElement('div');\n            itemEl.className = 'history-item';\n            itemEl.title = `保存于: ${new Date(item.timestamp).toLocaleString()}\\n页面: ${item.page}`;\n\n            // 显示预览内容（最多50个字符）\n            const preview = item.content.length > 50 ? item.content.substring(0, 50) + '...' : item.content;\n            itemEl.textContent = preview;\n\n            // 点击填充到输入框\n            itemEl.addEventListener('click', () => {\n                if (activeInput) {\n                    activeInput.value = item.content;\n                    // 触发输入事件，确保表单能检测到变化\n                    const event = new Event('input', { bubbles: true });\n                    activeInput.dispatchEvent(event);\n                    historyPanel.remove();\n                    historyPanel = null;\n                }\n            });\n\n            historyPanel.appendChild(itemEl);\n        });\n\n        document.body.appendChild(historyPanel);\n    }\nconsole.log('初始化完成');\n    // 页面加载完成后初始化\n    if (document.readyState === 'loading') {\n        document.addEventListener('DOMContentLoaded', init);\n        console.log('等待DOM加载完成');\n    } else {\n        init();\n    }\n})();", "enabled": true}, {"id": "1753277450956", "name": "c测试", "match": "https://*taobao.com/*", "code": "// ==UserScript==\n// @name         文本框内容记录工具\n// @namespace    http://tampermonkey.net/\n// @version      0.1\n// @description  在文本框焦点时显示记录按钮，保存内容到localStorage并提供恢复功能\n// <AUTHOR>\n// @match        *://*/*\n// @grant        none\n// ==/UserScript==\n\n(function() {\n    'use strict';\n\n    // 存储当前活动的文本框元素\n    let activeInput = null;\n    // 存储按钮元素\n    let saveButton = null;\n    let loadButton = null;\n    let historyPanel = null;\n\n    // 初始化函数\n    function init() {\n        // 创建样式\n        createStyles();\n        console.log('已监听focus');\n        \n        // 添加文本选择监听\n        document.addEventListener('mouseup', handleTextSelection);\n        \n        // 创建右上角可拖动历史记录按钮\n        createDraggableHistoryButton();\n        \n        // 方案1：使用MutationObserver监听动态生成的输入框\n        const observer = new MutationObserver((mutations) => {\n            mutations.forEach(mutation => {\n                if (mutation.addedNodes.length) {\n                    const loginForm = document.getElementById('login-form');\n                    if (loginForm) {\n                        // 绑定具体输入框事件\n                        const usernameInput = document.getElementById('fm-login-id');\n                        const passwordInput = document.getElementById('fm-login-password');\n                        if (usernameInput) usernameInput.addEventListener('focus', handleInputFocus);\n                        if (passwordInput) passwordInput.addEventListener('focus', handleInputFocus);\n                        observer.disconnect(); // 绑定后停止观察\n                    }\n                }\n            });\n        });\n\n        // 观察body下的DOM变化\n        observer.observe(document.body, {\n            childList: true,\n            subtree: true\n        });\n\n        // 方案2：直接查询并绑定（备用）\n        setTimeout(() => {\n            const usernameInput = document.getElementById('fm-login-id');\n            const passwordInput = document.getElementById('fm-login-password');\n            if (usernameInput) usernameInput.addEventListener('focus', handleInputFocus);\n            if (passwordInput) passwordInput.addEventListener('focus', handleInputFocus);\n        }, 1000);\n    }\n\n    // 创建必要的CSS样式\n    function createStyles() {\n        console.log('创建样式');\n        const style = document.createElement('style');\n        style.textContent = `\n            .text-saver-btn {\n                position: absolute;\n                padding: 5px 10px;\n                margin-left: 5px;\n                cursor: pointer;\n                border: none;\n                border-radius: 4px;\n                font-size: 16px;\n                z-index: 9999;\n            }\n            .save-btn {\n                background-color: #4CAF50;\n                color: white;\n            }\n            .load-btn {\n                background-color: #2196F3;\n                color: white;\n                margin-left: 5px;\n            }\n            .history-panel {\n                position: absolute;\n                background: white;\n                border: 1px solid #ccc;\n                border-radius: 4px;\n                box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n                padding: 10px;\n                max-height: 200px;\n                overflow-y: auto;\n                z-index: 10000;\n            }\n            .history-item {\n                padding: 5px;\n                margin: 3px 0;\n                border-radius: 3px;\n                cursor: pointer;\n            }\n            .history-item:hover {\n                background-color: #f0f0f0;\n            }\n            /* 页面内提示框样式 */\n            .notification {\n                position: fixed;\n                top: 20px;\n                left: 50%;\n                transform: translateX(-50%);\n                padding: 12px 20px;\n                background-color: #4CAF50;\n                color: white;\n                border-radius: 4px;\n                box-shadow: 0 3px 10px rgba(0,0,0,0.2);\n                z-index: 99999;\n                opacity: 0;\n                transition: opacity 0.3s, transform 0.3s;\n                transform: translate(-50%, -20px);\n            }\n            .notification.show {\n                opacity: 1;\n                transform: translate(-50%, 0);\n            }\n        `;\n        document.head.appendChild(style);\n    }\n\n    // 处理输入框获得焦点事件\n    function handleInputFocus(e) {\n        const target = e.target;\n        console.log('检测到焦点元素:', target.tagName, target.type); // 添加调试日志\n        // 扩大选择器范围，包含更多可能的输入类型\n        if ((target.tagName === 'INPUT' && ['text', 'password', 'email', 'tel', 'url', 'number'].includes(target.type)) ||\n            target.tagName === 'TEXTAREA' || target.isContentEditable) {\n            console.log('输入框获得焦点');\n            activeInput = target;\n            createButtons();\n            checkForHistory();\n        }\n    }\n\n    // 处理输入框失去焦点事件\n    function handleInputBlur(e) {\n        console.log('输入框失去焦点');\n        // 延迟移除按钮，以便点击按钮能触发事件\n        setTimeout(() => {\n            if (activeInput && document.activeElement !== saveButton && document.activeElement !== loadButton &&\n                !historyPanel?.contains(document.activeElement)) {\n                removeButtons();\n                activeInput = null;\n            }\n        }, 200);\n    }\n\n    // 创建保存和加载按钮\n    function createButtons() {\n        // 先移除已存在的按钮\n        removeButtons();\n\n        // 创建保存按钮\n        saveButton = document.createElement('button');\n        saveButton.className = 'text-saver-btn save-btn';\n        saveButton.textContent = '📝';\n        saveButton.title = '保存当前内容';\n        saveButton.addEventListener('click', saveCurrentContent);\n\n        // 获取输入框位置并定位按钮\n        positionButton(saveButton);\n        console.log('保存按钮位置:', saveButton.style.left, saveButton.style.top);\n        // 添加到文档\n        document.body.appendChild(saveButton);\n    }\n\n    // 检查是否有历史记录并创建加载按钮\n    function checkForHistory() {\n        const history = getSavedHistory();\n        if (history && history.length > 0) {\n            // 创建加载按钮\n            loadButton = document.createElement('button');\n            loadButton.className = 'text-saver-btn load-btn';\n            loadButton.textContent = '📭';\n            loadButton.title = '查看保存的内容';\n            loadButton.addEventListener('click', toggleHistoryPanel);\n\n            // 定位在保存按钮旁边\n            positionButton(loadButton, true);\n\n            document.body.appendChild(loadButton);\n        }\n    }\n\n    // 定位按钮\n    function positionButton(button, isLoadButton = false) {\n        if (!activeInput) return;\n\n        const rect = activeInput.getBoundingClientRect();\n        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;\n\n        button.style.top = `${rect.top + scrollTop}px`;\n        if (isLoadButton && saveButton) {\n            button.style.left = `${rect.right + scrollLeft + saveButton.offsetWidth + 5}px`;\n        } else {\n            button.style.left = `${rect.right + scrollLeft + 5}px`;\n        }\n    }\n\n    // 移除所有按钮和面板\n    function removeButtons() {\n        if (saveButton) {\n            saveButton.remove();\n            saveButton = null;\n        }\n        if (loadButton) {\n            loadButton.remove();\n            loadButton = null;\n        }\n        if (historyPanel) {\n            historyPanel.remove();\n            historyPanel = null;\n        }\n    }\n    // 保存选择的文本到localStorage\n    function saveSelectedText(text) {\n        if (!text.trim()) return;\n\n        // 获取当前域名\n        const domain = window.location.hostname;\n        // 获取现有历史记录\n        let history = getSavedHistory();\n        if (!history) history = [];\n\n        // 添加新记录\n        history.unshift({\n            content: text,\n            timestamp: new Date().toISOString(),\n            page: document.title,\n            url: window.location.href,\n            type: 'selection'\n        });\n\n        // 限制历史记录数量（最多50条）\n        if (history.length > 50) {\n            history = history.slice(0, 50);\n        }\n\n        // 保存到localStorage\n        localStorage.setItem(`textSaver_${domain}`, JSON.stringify(history));\n\n        // 显示页面内提示\n        showNotification('文本已保存到历史记录');\n    }\n\n    // 保存当前内容到localStorage\n    function saveCurrentContent() {\n        if (!activeInput) return;\n\n        const content = activeInput.value;\n        if (!content.trim()) return;\n\n        // 获取当前域名\n        const domain = window.location.hostname;\n        // 获取现有历史记录\n        let history = getSavedHistory();\n        if (!history) history = [];\n\n        // 添加新记录\n        history.unshift({\n            content: content,\n            timestamp: new Date().toISOString(),\n            page: document.title,\n            url: window.location.href,\n            type: 'input'\n        });\n\n        // 限制历史记录数量（最多50条）\n        if (history.length > 50) {\n            history = history.slice(0, 50);\n        }\n\n        // 保存到localStorage\n        localStorage.setItem(`textSaver_${domain}`, JSON.stringify(history));\n\n        // 显示加载按钮\n        checkForHistory();\n\n        // 显示页面内提示\n        showNotification('内容已保存');\n    }\n    // 从localStorage获取保存的历史记录\n    function getSavedHistory() {\n        const domain = window.location.hostname;\n        const historyStr = localStorage.getItem(`textSaver_${domain}`);\n        return historyStr ? JSON.parse(historyStr) : null;\n    }\n\n    // 切换历史记录面板\n    function toggleHistoryPanel() {\n        if (historyPanel) {\n            historyPanel.remove();\n            historyPanel = null;\n            return;\n        }\n\n        const history = getSavedHistory();\n        if (!history || history.length === 0) return;\n\n        // 创建面板\n        historyPanel = document.createElement('div');\n        historyPanel.className = 'history-panel';\n\n        // 定位面板\n        positionButton(historyPanel, true);\n        historyPanel.style.top = `${parseInt(loadButton.style.top) + loadButton.offsetHeight + 5}px`;\n        historyPanel.style.width = '300px';\n\n        // 添加标题\n        const title = document.createElement('div');\n        title.textContent = '保存的内容:';\n        title.style.fontWeight = 'bold';\n        title.style.marginBottom = '10px';\n        historyPanel.appendChild(title);\n\n        // 添加历史记录项\n        history.forEach((item, index) => {\n            const itemEl = document.createElement('div');\n            itemEl.className = 'history-item';\n            itemEl.title = `保存于: ${new Date(item.timestamp).toLocaleString()}\\n页面: ${item.page}`;\n\n            // 显示预览内容（最多50个字符）\n            const preview = item.content.length > 50 ? item.content.substring(0, 50) + '...' : item.content;\n            itemEl.textContent = preview;\n\n            // 点击填充到输入框\n            itemEl.addEventListener('click', () => {\n                if (activeInput) {\n                    activeInput.value = item.content;\n                    // 触发输入事件，确保表单能检测到变化\n                    const event = new Event('input', { bubbles: true });\n                    activeInput.dispatchEvent(event);\n                    showNotification('已加载保存的内容');\n                    historyPanel.remove();\n                    historyPanel = null;\n                } else {\n                    showNotification('未找到活动输入框', 'warning');\n                }\n            });\n\n            historyPanel.appendChild(itemEl);\n        });\n\n        document.body.appendChild(historyPanel);\n    }\nconsole.log('初始化完成');\n    // 页面加载完成后初始化\n    if (document.readyState === 'loading') {\n        document.addEventListener('DOMContentLoaded', init);\n        console.log('等待DOM加载完成');\n    } else {\n        init();\n    }\n\n\n    \n\n    // 手动检测输入框并绑定事件\n    function manualDetectInputs() {\n        console.log('手动检测输入框...');\n        const inputs = document.querySelectorAll('input[type=\"text\"], input[type=\"password\"], textarea');\n        \n        if (inputs.length > 0) {\n            // 检测到输入框时的处理逻辑\n            inputs.forEach(input => {\n                input.removeEventListener('focus', handleInputFocus);\n                input.addEventListener('focus', handleInputFocus);\n                console.log('已绑定输入框:', input.id || input.name);\n            });\n            \n            showNotification(`已检测到 ${inputs.length} 个输入框，点击输入框即可使用功能`);\n            if (inputs[0]) inputs[0].focus();\n        } else {\n            // 未检测到输入框，进入手动选择模式\n            startManualSelectionMode();\n        }\n    }\n\n    // 进入手动选择模式\n    function startManualSelectionMode() {\n        console.log('进入手动选择模式，请点击目标输入框');\n        \n        // 创建半透明遮罩\n        const overlay = document.createElement('div');\n        overlay.id = 'manual-selection-overlay';\n        overlay.style.cssText = `\n            position: fixed;\n            top: 0;\n            left: 0;\n            width: 100vw;\n            height: 100vh;\n            background: rgba(0,0,0,0.5);\n            z-index: 99998;\n            cursor: crosshair;\n        `;\n        document.body.appendChild(overlay);\n        \n        // 创建提示文字\n        const tip = document.createElement('div');\n        tip.style.cssText = `\n            position: fixed;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            color: white;\n            font-size: 18px;\n            z-index: 99999;\n        `;\n        tip.textContent = '请点击页面上的输入框区域';\n        document.body.appendChild(tip);\n        \n        // 一次性点击事件监听\n        const handleClick = (e) => {\n            e.stopPropagation();\n            e.preventDefault();\n\n            // 移除遮罩和提示\n            overlay.remove();\n            tip.remove();\n\n            // 获取点击坐标\n            const x = e.clientX;\n            const y = e.clientY;\n            \n            // 根据坐标获取实际元素（穿透遮罩和iframe）\n            let target = document.elementFromPoint(x, y);\n            console.log('初始点击元素:', target.tagName);\n\n            // 如果是iframe，尝试获取内部文档元素\n            if (target.tagName === 'IFRAME') {\n                try {\n                    const iframeDoc = target.contentDocument || target.contentWindow.document;\n                    // 转换坐标到iframe内部\n                    const rect = target.getBoundingClientRect();\n                    const iframeX = x - rect.left;\n                    const iframeY = y - rect.top;\n                    // 在iframe内部查找元素\n                    target = iframeDoc.elementFromPoint(iframeX, iframeY);\n                    console.log('穿透iframe后找到元素:', target.tagName, target.type);\n                } catch (e) {\n                    console.log('无法访问iframe内容（跨域安全限制）:', e.message);\n                }\n            }\n\n            console.log('用户手动选择了元素:', target.tagName, target.type);\n\n            // 验证是否为输入框类型\n            if ((target.tagName === 'INPUT' && ['text', 'password', 'email', 'tel', 'url', 'number'].includes(target.type)) ||\n                target.tagName === 'TEXTAREA' || target.isContentEditable) {\n                // 绑定事件\n                target.removeEventListener('focus', handleInputFocus);\n                target.addEventListener('focus', handleInputFocus);\n                target.focus(); // 直接激活输入框\n                console.log('已手动绑定输入框:', target.id || target.name);\n            } else {\n                console.log('选择的不是输入框元素');\n            }\n        };\n        \n        // 添加点击事件监听\n        overlay.addEventListener('click', handleClick, { once: true });\n    }\n\n    // 处理文本选择事件\n    function handleTextSelection() {\n        const selection = window.getSelection();\n        const selectedText = selection.toString().trim();\n        \n        // 只有选择文本长度大于0时才显示按钮\n        if (selectedText.length > 0) {\n            // 创建划选专用的保存按钮\n            const selectionButton = document.createElement('button');\n            selectionButton.className = 'text-saver-btn save-btn';\n            selectionButton.textContent = '📝';\n            selectionButton.title = '保存所选文本';\n            selectionButton.style.zIndex = '99999';\n            \n            // 获取选择区域的位置\n            const range = selection.getRangeAt(0);\n            const rect = range.getBoundingClientRect();\n            \n            // 定位按钮在选择区域附近\n            selectionButton.style.position = 'absolute';\n            selectionButton.style.top = `${rect.bottom + window.scrollY + 5}px`;\n            selectionButton.style.left = `${rect.left + window.scrollX}px`;\n            \n            // 点击事件 - 保存到localStorage\n            selectionButton.addEventListener('click', () => {\n                saveSelectedText(selectedText);\n                selectionButton.remove();\n                // 清除选择\n                selection.removeAllRanges();\n            });\n            \n            document.body.appendChild(selectionButton);\n            \n            // 点击页面其他地方移除按钮\n            const removeOnClickOutside = (e) => {\n                if (e.target !== selectionButton) {\n                    selectionButton.remove();\n                    document.removeEventListener('click', removeOnClickOutside);\n                }\n            };\n            \n            setTimeout(() => {\n                document.addEventListener('click', removeOnClickOutside);\n            }, 0);\n        }\n    }\n\n    // 复制文本到剪贴板\n    function copyToClipboard(text) {\n        if (!navigator.clipboard) {\n            // 备用方法\n            const textarea = document.createElement('textarea');\n            textarea.value = text;\n            document.body.appendChild(textarea);\n            textarea.select();\n            document.execCommand('copy');\n            document.body.removeChild(textarea);\n            showNotification('所选文本已复制到剪贴板！');\n            return;\n        }\n        \n        navigator.clipboard.writeText(text).then(() => {\n            showNotification('所选文本已复制到剪贴板！');\n        }, (err) => {\n            console.error('无法复制文本: ', err);\n            showNotification('复制失败，请手动复制。');\n        });\n    }\n\n    // 创建右上角可拖动历史记录按钮\n    function createDraggableHistoryButton() {\n        // 从localStorage获取保存的位置\n        const savedPos = JSON.parse(localStorage.getItem('historyButtonPosition')) || {\n            top: '20px',\n            right: '20px'\n        };\n        \n        const button = document.createElement('button');\n        button.id = 'draggable-history-btn';\n        button.textContent = '📭';\n        button.title = '查看保存的内容';\n        button.style.cssText = `\n            position: fixed;\n            top: ${savedPos.top};\n            right: ${savedPos.right};\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            background-color: #2196F3;\n            color: white;\n            border: none;\n            font-size: 18px;\n            cursor: move;\n            z-index: 99999;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.2);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        `;\n        \n        // 拖动功能\n        let isDragging = false;\n        let offsetX, offsetY;\n        \n        button.addEventListener('mousedown', (e) => {\n            if (e.target.id === 'drag-handle') return;\n            isDragging = true;\n            const rect = button.getBoundingClientRect();\n            offsetX = e.clientX - rect.left;\n            offsetY = e.clientY - rect.top;\n            button.style.cursor = 'grabbing';\n        });\n        \n        document.addEventListener('mousemove', (e) => {\n            if (!isDragging) return;\n            e.preventDefault();\n            const x = e.clientX - offsetX;\n            const y = e.clientY - offsetY;\n            \n            // 限制在可视区域内\n            const maxX = window.innerWidth - button.offsetWidth;\n            const maxY = window.innerHeight - button.offsetHeight;\n            const boundedX = Math.max(0, Math.min(x, maxX));\n            const boundedY = Math.max(0, Math.min(y, maxY));\n            \n            button.style.left = `${boundedX}px`;\n            button.style.top = `${boundedY}px`;\n            button.style.right = 'auto';\n            button.style.bottom = 'auto';\n        });\n        \n        document.addEventListener('mouseup', () => {\n            if (isDragging) {\n                isDragging = false;\n                button.style.cursor = 'move';\n                // 保存位置到localStorage\n                localStorage.setItem('historyButtonPosition', JSON.stringify({\n                    top: button.style.top,\n                    left: button.style.left\n                }));\n            }\n        });\n        \n        // 添加点击事件\n        button.addEventListener('click', (e) => {\n            if (e.target.id === 'drag-handle') return;\n            // 显示历史记录面板\n            showHistoryPanelAtButton();\n        });\n        \n        // 添加拖动手柄\n        const handle = document.createElement('div');\n        handle.id = 'drag-handle';\n        handle.style.cssText = `\n            position: absolute;\n            bottom: 2px;\n            right: 2px;\n            width: 10px;\n            height: 10px;\n            background-color: rgba(255,255,255,0.5);\n            border-radius: 50%;\n            cursor: ns-resize;\n        `;\n        button.appendChild(handle);\n        \n        document.body.appendChild(button);\n    }\n\n    // 在按钮位置显示历史记录面板\n    function showHistoryPanelAtButton() {\n        const history = getSavedHistory();\n        if (!history || history.length === 0) {\n            showNotification('没有保存的内容');\n            return;\n        }\n        \n        // 移除现有面板\n        if (historyPanel) historyPanel.remove();\n        \n        // 创建面板\n        historyPanel = document.createElement('div');\n        historyPanel.className = 'history-panel';\n        \n        // 获取按钮位置\n        const historyButton = document.getElementById('draggable-history-btn');\n        const rect = historyButton.getBoundingClientRect();\n        \n        // 定位面板\n        historyPanel.style.position = 'fixed';\n        historyPanel.style.top = `${rect.bottom + 5}px`;\n        historyPanel.style.right = `${window.innerWidth - rect.right}px`;\n        historyPanel.style.width = '300px';\n        \n        // 添加标题\n        const title = document.createElement('div');\n        title.textContent = '保存的内容:';\n        title.style.fontWeight = 'bold';\n        title.style.marginBottom = '10px';\n        historyPanel.appendChild(title);\n        \n        // 添加历史记录项\n        history.forEach((item, index) => {\n            const itemEl = document.createElement('div');\n            itemEl.className = 'history-item';\n            itemEl.title = `保存于: ${new Date(item.timestamp).toLocaleString()}\n页面: ${item.page}`;\n            \n            // 显示预览内容\n            const preview = item.content.length > 50 ? item.content.substring(0, 50) + '...' : item.content;\n            itemEl.textContent = preview;\n            \n            // 点击复制到剪贴板\n            itemEl.addEventListener('click', () => {\n                copyToClipboard(item.content);\n                historyPanel.remove();\n                historyPanel = null;\n            });\n            \n            historyPanel.appendChild(itemEl);\n        });\n        \n        // 点击空白处关闭\n        const closeOnClickOutside = (e) => {\n            if (!historyPanel.contains(e.target) && e.target.id !== 'draggable-history-btn') {\n                historyPanel.remove();\n                historyPanel = null;\n                document.removeEventListener('click', closeOnClickOutside);\n            }\n        };\n        \n        setTimeout(() => {\n            document.addEventListener('click', closeOnClickOutside);\n        }, 0);\n        \n        document.body.appendChild(historyPanel);\n    }\n    // 显示页面内提示框\n    function showNotification(message, type = 'success') {\n        // 创建提示框元素\n        const notification = document.createElement('div');\n        notification.className = 'notification';\n        notification.textContent = message;\n        \n        // 设置不同类型的提示框样式\n        if (type === 'error') {\n            notification.style.backgroundColor = '#f44336';\n        } else if (type === 'warning') {\n            notification.style.backgroundColor = '#ff9800';\n        }\n        \n        // 添加到文档\n        document.body.appendChild(notification);\n        \n        // 显示提示框\n        setTimeout(() => notification.classList.add('show'), 10);\n        \n        // 3秒后自动隐藏\n        setTimeout(() => {\n            notification.classList.remove('show');\n            setTimeout(() => notification.remove(), 300);\n        }, 3000);\n    }\n})();", "enabled": true}, {"id": "1753315640827", "name": "图片管理器插件", "match": "*://*,https://*huaban.com*", "code": "// 内容脚本 - 处理页面交互和采集界面\n\nconst { ipc<PERSON>enderer, remote } = require('electron');\nconst { Menu, dialog, shell } = remote;\nconst fs = require('fs');\nconst path = require('path');\n\n// API配置\nconst API_PORTS = [3456, 3457, 3458, 3459]; // 尝试多个端口\nlet API_BASE_URL = 'http://localhost:3456/api';\nlet isApiConnected = false;\n\n// 创建右键菜单\nfunction createContextMenu() {\n  const contextMenu = Menu.buildFromTemplate([{\n    label: \"保存图片到文件夹\",\n    click: (item, focusedWindow) => {\n      // 获取选中的图片信息\n      const imageUrl = focusedWindow.getWebContents().getSelectedText();\n      ipcRenderer.send('openCollection', {\n        imageUrl: imageUrl,\n        pageUrl: focusedWindow.getURL()\n      });\n    }\n  }]);\n\n  window.addEventListener('contextmenu', (e) => {\n    e.preventDefault();\n    contextMenu.popup(remote.getCurrentWindow());\n  }, false);\n}\n\n// 在初始化时调用\ncreateContextMenu();\n\n// 监听来自background script的消息\nipcRenderer.on('message', (event, request) => {\n  if (request.action === \"openCollection\") {\n    openCollectionInterface(request.imageUrl, request.pageUrl);\n  }\n});\n\n// 检查API连接状态\nasync function checkApiConnection() {\n  // 尝试多个端口\n  for (const port of API_PORTS) {\n    try {\n      const testUrl = `http://localhost:${port}/api/health`;\n      const response = await fetch(testUrl);\n      if (response.ok) {\n        API_BASE_URL = `http://localhost:${port}/api`;\n        isApiConnected = true;\n        console.log(`图片管理软件API连接成功，端口: ${port}`);\n        return true;\n      }\n    } catch (error) {\n      // 继续尝试下一个端口\n      console.log(`端口 ${port} 连接失败:`, error.message);\n    }\n  }\n\n  isApiConnected = false;\n  console.log('所有端口都连接失败，软件可能未运行');\n  return false;\n}\n\n// 获取软件中的文件夹结构\nasync function getFoldersFromSoftware() {\n  try {\n    const response = await fetch(`${API_BASE_URL}/folders`);\n    if (response.ok) {\n      const data = await response.json();\n      return data;\n    } else if (response.status === 400) {\n      // 400错误通常表示未设置根文件夹\n      const errorData = await response.json();\n      console.log('软件提示:', errorData.error);\n      return { success: false, error: errorData.error, needSetup: true };\n    } else {\n      throw new Error(`API请求失败: ${response.status}`);\n    }\n  } catch (error) {\n    console.error('获取文件夹结构失败:', error);\n    return null;\n  }\n}\n\n// 保存图片到软件\nasync function saveImageToSoftware(imageUrl, folderPath, fileName, pageUrl) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/save-image`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        imageUrl,\n        folderPath,\n        fileName,\n        pageUrl\n      })\n    });\n\n    const result = await response.json();\n    return result;\n  } catch (error) {\n    console.error('保存图片失败:', error);\n    return { success: false, error: error.message };\n  }\n}\n\n// 替换 chrome.downloads.download\nasync function downloadImage(imageUrl, folderPath, fileName) {\n  try {\n    const response = await fetch(imageUrl);\n    const buffer = await response.buffer();\n    const filePath = path.join(folderPath, fileName);\n    fs.writeFileSync(filePath, buffer);\n    dialog.showMessageBox({ message: '图片保存成功' });\n  } catch (error) {\n    dialog.showErrorBox('下载失败', error.message);\n  }\n}\n\n// 打开采集界面\nfunction openCollectionInterface(imageUrl, pageUrl) {\n  // 检查是否已经存在采集界面\n  const existingInterface = document.getElementById('image-collection-interface');\n  if (existingInterface) {\n    existingInterface.remove();\n  }\n\n  // 创建采集界面容器\n  const interfaceContainer = document.createElement('div');\n  interfaceContainer.id = 'image-collection-interface';\n  interfaceContainer.innerHTML = `\n    <div class=\"collection-overlay\">\n      <div class=\"collection-modal\">\n        <div class=\"collection-header\">\n          <h3>图片采集</h3>\n          <div class=\"header-buttons\">\n            <button class=\"settings-btn\" id=\"settings-btn\" title=\"打开设置\">⚙️</button>\n            <button class=\"close-btn\">&times;</button>\n          </div>\n        </div>\n        <div class=\"collection-content\">\n          <div class=\"image-preview\">\n            <img src=\"${imageUrl}\" alt=\"预览图片\" />\n            <div class=\"image-info\">\n              <p>来源: ${pageUrl}</p>\n              <p>图片URL: ${imageUrl}</p>\n            </div>\n          </div>\n          <div class=\"folder-selection\">\n            <div class=\"folder-history\" style=\"display:none\">\n              <h4>历史文件夹</h4>\n              <div class=\"history-list\" id=\"history-list\">\n                <!-- 历史文件夹列表将在这里动态加载 -->\n              </div>\n            </div>\n            <div class=\"folder-tree\">\n              <h4 style=\"display:none\">文件夹结构</h4>\n              <div class=\"tree-container\" id=\"tree-container\">\n                <input type=\"text\" id=\"folder-input\" placeholder=\"输入文件夹路径或选择历史文件夹\" />\n                <button id=\"create-folder-btn\" style=\"display:none\">创建新文件夹</button>\n                <div class=\"folder-tree-view\" id=\"folder-tree-view\" style=\"color:#5e5c5c;\">\n                  <!-- 文件夹树结构将在这里显示 -->\n                </div>\n              </div>\n            </div>\n            <div class=\"file-naming\">\n              <h4>文件命名</h4>\n              <input type=\"text\" id=\"filename-input\" placeholder=\"文件名（可选，默认使用原文件名）\" />\n            </div>\n          </div>\n        </div>\n        <div class=\"collection-footer\">\n          <button id=\"save-btn\" class=\"save-btn\">保存图片</button>\n          <button id=\"cancel-btn\" class=\"cancel-btn\">取消</button>\n        </div>\n      </div>\n    </div>\n  `;\n\n  // 添加样式\n  const style = document.createElement('style');\n  style.textContent = `\n    #image-collection-interface {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 10000;\n      font-family: Arial, sans-serif;\n    }\n    \n    .collection-overlay {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background: rgba(0, 0, 0, 0.7);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n    \n    .collection-modal {\n      background: white;\n      border-radius: 8px;\n      width: 80%;\n      max-width: 800px;\n      max-height: 90%;\n      overflow-y: auto;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n    }\n    \n    .collection-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 20px;\n      border-bottom: 1px solid #eee;\n    }\n    \n    .collection-header h3 {\n      margin: 0;\n      color: #333;\n    }\n\n    .header-buttons {\n      display: flex;\n      gap: 10px;\n      align-items: center;\n    }\n\n    .settings-btn {\n      background: none;\n      border: none;\n      font-size: 18px;\n      cursor: pointer;\n      color: #666;\n      padding: 5px;\n      border-radius: 4px;\n      transition: background-color 0.2s;\n    }\n\n    .settings-btn:hover {\n      background: #f0f0f0;\n      color: #333;\n    }\n\n    .close-btn {\n      background: none;\n      border: none;\n      font-size: 24px;\n      cursor: pointer;\n      color: #666;\n    }\n    \n    .collection-content {\n      padding: 20px;\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 20px;\n    }\n    \n    .image-preview img {\n      max-width: 100%;\n      max-height: 200px;\n      object-fit: contain;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n    }\n    \n    .image-info {\n      margin-top: 10px;\n      font-size: 12px;\n      color: #666;\n    }\n    \n    .image-info p {\n      margin: 5px 0;\n      word-break: break-all;\n    }\n    \n    .folder-selection h4 {\n      margin: 0 0 10px 0;\n      color: #333;\n    }\n    \n    .history-list {\n      max-height: 150px;\n      overflow-y: auto;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      margin-bottom: 15px;\n    }\n    \n    .history-item {\n      padding: 8px 12px;\n      cursor: pointer;\n      border-bottom: 1px solid #eee;\n    }\n    \n    .history-item:hover {\n      background: #f5f5f5;\n    }\n    \n    .history-item:last-child {\n      border-bottom: none;\n    }\n    \n    .tree-container input {\n      width: 100%;\n      padding: 8px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      margin-bottom: 10px;\n    }\n\n    .tree-container button {\n      padding: 8px 16px;\n      background: #007cba;\n      color: white;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n      margin-bottom: 10px;\n    }\n\n    .tree-container button:hover {\n      background: #005a87;\n    }\n\n    .folder-tree-view {\n      max-height: 650px;\n      overflow-y: auto;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      background: #f9f9f9;\n    }\n\n    .tree-node {\n      padding: 4px 8px;\n      cursor: pointer;\n      border-bottom: 1px solid #eee;\n      display: flex;\n      align-items: center;\n    }\n\n    .tree-node:hover {\n      background: #e9ecef;\n    }\n\n    .tree-node.selected {\n      background: #007cba;\n      color: white;\n    }\n\n    .tree-node-icon {\n      margin-right: 6px;\n      font-size: 12px;\n      width: 12px;\n      text-align: center;\n    }\n\n    .tree-node-text {\n      flex: 1;\n      font-size: 13px;\n    }\n\n    .tree-node.level-0 { padding-left: 8px; }\n    .tree-node.level-1 { padding-left: 24px; }\n    .tree-node.level-2 { padding-left: 40px; }\n    .tree-node.level-3 { padding-left: 56px; }\n\n    .tree-node.back-button {\n      background: #e3f2fd;\n      border-bottom: 2px solid #2196f3;\n      font-weight: bold;\n    }\n\n    .tree-node.back-button:hover {\n      background: #bbdefb;\n    }\n    \n    .file-naming input {\n      width: 100%;\n      padding: 8px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n    }\n    \n    .collection-footer {\n      padding: 20px;\n      border-top: 1px solid #eee;\n      display: flex;\n      justify-content: flex-end;\n      gap: 10px;\n    }\n    \n    .save-btn {\n      padding: 10px 20px;\n      background: #28a745;\n      color: white;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n    }\n    \n    .save-btn:hover {\n      background: #218838;\n    }\n    \n    .cancel-btn {\n      padding: 10px 20px;\n      background: #6c757d;\n      color: white;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n    }\n    \n    .cancel-btn:hover {\n      background: #5a6268;\n    }\n  `;\n\n  document.head.appendChild(style);\n  document.body.appendChild(interfaceContainer);\n\n  // 初始化界面\n  initializeInterface(imageUrl);\n}\n\n// 初始化采集界面\nasync function initializeInterface(imageUrl) {\n  // 检查API连接\n  const connected = await checkApiConnection();\n\n  if (connected) {\n    // 使用软件API加载文件夹结构\n    await loadFolderTreeFromSoftware();\n\n    // 显示连接状态\n    showConnectionStatus(true);\n  } else {\n    // 回退到历史记录模式\n    loadFolderHistory();\n    loadFolderTree();\n\n    // 显示连接状态\n    showConnectionStatus(false);\n  }\n\n  // 从localStorage恢复上次使用的文件夹路径\n  const lastFolderPath = localStorage.getItem('lastFolderPath');\n  if (lastFolderPath) {\n    document.getElementById('folder-input').value = lastFolderPath;\n  }\n\n  // 设置默认文件名\n  const filename = getFilenameFromUrl(imageUrl);\n  document.getElementById('filename-input').value = filename;\n\n  // 绑定事件\n  bindEvents(imageUrl);\n}\n\n// 显示连接状态\nfunction showConnectionStatus(connected) {\n  const header = document.querySelector('.collection-header h3');\n  if (connected) {\n    header.innerHTML = '图片采集 <span style=\"color: #28a745; font-size: 12px;\">● 已连接到软件</span>';\n  } else {\n    header.innerHTML = '图片采集 <span style=\"color: #dc3545; font-size: 12px;\">● 软件未运行</span>';\n  }\n}\n\n// 从软件API加载文件夹结构\nasync function loadFolderTreeFromSoftware() {\n  const foldersData = await getFoldersFromSoftware();\n\n  const treeView = document.getElementById('folder-tree-view');\n  const historyList = document.getElementById('history-list');\n  const folderInput = document.getElementById('folder-input');\n\n  if (!foldersData) {\n    // 网络连接失败\n    treeView.innerHTML = '<div class=\"tree-node\" style=\"color: #dc3545; padding: 15px; text-align: center;\">无法连接到软件API</div>';\n    historyList.innerHTML = '<div class=\"history-item\" style=\"color: #dc3545;\">API连接失败</div>';\n    folderInput.placeholder = '请手动输入文件夹路径';\n    return;\n  }\n\n  if (foldersData.needSetup) {\n    // 软件未设置根文件夹\n    treeView.innerHTML = `\n      <div class=\"tree-node\" style=\"color: #ffc107; padding: 15px; text-align: center;\">\n        <div style=\"margin-bottom: 10px;\">⚠️ 软件提示</div>\n        <div style=\"font-size: 12px;\">${foldersData.error}</div>\n        <div style=\"font-size: 12px; margin-top: 8px;\">请在软件中先选择根文件夹</div>\n      </div>\n    `;\n    historyList.innerHTML = '<div class=\"history-item\" style=\"color: #ffc107;\">请在软件中设置根文件夹</div>';\n    folderInput.placeholder = '请先在软件中选择根文件夹，或手动输入路径';\n    return;\n  }\n\n  if (!foldersData.success) {\n    // 其他错误\n    treeView.innerHTML = `<div class=\"tree-node\" style=\"color: #dc3545; padding: 15px; text-align: center;\">获取文件夹失败: ${foldersData.error || '未知错误'}</div>`;\n    historyList.innerHTML = '<div class=\"history-item\" style=\"color: #dc3545;\">获取文件夹失败</div>';\n    folderInput.placeholder = '请手动输入文件夹路径';\n    return;\n  }\n\n  // 成功获取文件夹结构\n  folderInput.placeholder = `软件根目录: ${foldersData.rootPath}`;\n\n  // 渲染文件夹树\n  if (foldersData.folders && foldersData.folders.length > 0) {\n    treeView.innerHTML = renderSoftwareFolderTree(foldersData.folders, foldersData.rootPath);\n\n    // 绑定点击事件\n    treeView.addEventListener('click', (e) => {\n      const treeNode = e.target.closest('.tree-node');\n      if (treeNode && treeNode.dataset.path) {\n        const folderPath = treeNode.dataset.path;\n        folderInput.value = folderPath;\n\n        // 更新选中状态\n        treeView.querySelectorAll('.tree-node').forEach(node => {\n          node.classList.remove('selected');\n        });\n        treeNode.classList.add('selected');\n      }\n    });\n\n    // 更新历史记录显示\n    historyList.innerHTML = '<div class=\"history-item\" style=\"color: #28a745;\">使用软件文件夹结构</div>';\n  } else {\n    treeView.innerHTML = '<div class=\"tree-node\" style=\"color: #666; padding: 15px; text-align: center;\">软件中暂无文件夹</div>';\n    historyList.innerHTML = '<div class=\"history-item\" style=\"color: #dc3545;\">软件中暂无文件夹</div>';\n  }\n}\n\n// 加载文件夹历史记录\nfunction loadFolderHistory() {\n  ipcRenderer.send('getFolderHistory', (response) => {\n    const historyList = document.getElementById('history-list');\n    const history = response.history || [];\n\n    if (history.length === 0) {\n      historyList.innerHTML = '<div class=\"history-item\">暂无历史记录</div>';\n      return;\n    }\n\n    historyList.innerHTML = history.map(folder =>\n      `<div class=\"history-item\" data-folder=\"${folder}\">${folder}</div>`\n    ).join('');\n\n    // 绑定历史文件夹点击事件\n    historyList.addEventListener('click', (e) => {\n      if (e.target.classList.contains('history-item')) {\n        const folderPath = e.target.dataset.folder;\n        document.getElementById('folder-input').value = folderPath;\n        updateFolderTreeSelection(folderPath);\n        // 加载该文件夹下的子文件夹\n        loadFolderTree(folderPath);\n      }\n    });\n  });\n}\n\n// 加载文件夹树结构\nfunction loadFolderTree(currentPath = '') {\n  chrome.runtime.sendMessage({ action: \"getFolderHistory\" }, (response) => {\n    const history = response.history || [];\n    const treeView = document.getElementById('folder-tree-view');\n\n    // 获取当前路径下的子文件夹\n    const subfolders = getSubfolders(history, currentPath);\n\n    if (subfolders.length === 0) {\n      treeView.innerHTML = `\n        <div class=\"tree-node\" style=\"color: #666; cursor: default; padding: 15px; text-align: center;\">\n          ${currentPath ? `\"${currentPath}\" 下暂无子文件夹` : '暂无文件夹结构'}\n        </div>\n      `;\n      return;\n    }\n\n    treeView.innerHTML = renderSubfolders(subfolders, currentPath);\n\n    // 移除之前的事件监听器，避免重复绑定\n    const newTreeView = treeView.cloneNode(true);\n    treeView.parentNode.replaceChild(newTreeView, treeView);\n\n    // 绑定文件夹树点击事件\n    newTreeView.addEventListener('click', (e) => {\n      const treeNode = e.target.closest('.tree-node');\n      if (treeNode && treeNode.dataset.path) {\n        const folderPath = treeNode.dataset.path;\n        document.getElementById('folder-input').value = folderPath;\n\n        // 更新选中状态\n        newTreeView.querySelectorAll('.tree-node').forEach(node => {\n          node.classList.remove('selected');\n        });\n        treeNode.classList.add('selected');\n\n        // 重新加载该文件夹下的子文件夹\n        loadFolderTree(folderPath);\n      }\n    });\n  });\n}\n\n// 绑定事件\nfunction bindEvents(imageUrl) {\n  // 关闭按钮\n  document.querySelector('.close-btn').addEventListener('click', closeInterface);\n  document.getElementById('cancel-btn').addEventListener('click', closeInterface);\n\n  // 设置按钮\n  document.getElementById('settings-btn').addEventListener('click', openSettings);\n\n  // 点击遮罩层关闭\n  document.querySelector('.collection-overlay').addEventListener('click', (e) => {\n    if (e.target === e.currentTarget) {\n      closeInterface();\n    }\n  });\n\n  // 保存按钮\n  document.getElementById('save-btn').addEventListener('click', () => {\n    saveImage(imageUrl);\n  });\n\n  // 创建文件夹按钮\n  document.getElementById('create-folder-btn').addEventListener('click', createNewFolder);\n\n  // 文件夹输入框变化时更新树选中状态和加载子文件夹\n  document.getElementById('folder-input').addEventListener('input', (e) => {\n    const currentPath = e.target.value.trim();\n    updateFolderTreeSelection(currentPath);\n    // 延迟加载子文件夹，避免频繁更新\n    clearTimeout(window.folderTreeTimeout);\n    window.folderTreeTimeout = setTimeout(() => {\n      loadFolderTree(currentPath);\n    }, 500);\n  });\n}\n\n// 关闭界面\nfunction closeInterface() {\n  const interfaceElement = document.getElementById('image-collection-interface');\n  if (interfaceElement) {\n    interfaceElement.remove();\n  }\n}\n\n// 打开设置页面\nfunction openSettings() {\n  chrome.runtime.sendMessage({ action: \"openSettings\" });\n}\n\n// 保存图片\nasync function saveImage(imageUrl) {\n  const folderPath = document.getElementById('folder-input').value.trim();\n  const customFilename = document.getElementById('filename-input').value.trim();\n  const filename = customFilename || getFilenameFromUrl(imageUrl);\n  const pageUrl = window.location.href;\n\n  if (!folderPath) {\n    提示('请选择保存文件夹');\n    return;\n  }\n\n  // 显示保存状态\n  const saveBtn = document.getElementById('save-btn');\n  const originalText = saveBtn.textContent;\n  saveBtn.textContent = '保存中...';\n  saveBtn.disabled = true;\n\n  try {\n    // 保存文件夹路径到localStorage\n    if (folderPath) {\n      localStorage.setItem('lastFolderPath', folderPath);\n    }\n\n    // 尝试解析大图\n    let finalImageUrl = imageUrl;\n    const largeImageUrl = await tryParseLargeImage(imageUrl);\n    if (largeImageUrl && largeImageUrl !== imageUrl) {\n      finalImageUrl = largeImageUrl;\n      console.log('解析到大图URL:', finalImageUrl);\n    }\n\n    if (isApiConnected) {\n      // 使用软件API保存\n      const result = await saveImageToSoftware(finalImageUrl, folderPath, filename, pageUrl);\n\n      if (result.success) {\n        提示(`图片保存成功！\\n文件路径: ${result.filePath}`);\n      } else {\n        提示(`保存失败: ${result.error}`);\n        return;\n      }\n    } else {\n      // 回退到浏览器下载\n      await downloadImage(finalImageUrl, folderPath, filename);\n\n    // 保存到历史记录\n    if (folderPath) {\n      saveFolderToHistory(folderPath);\n    }\n    }\n\n    // 关闭界面\n    closeInterface();\n\n  } catch (error) {\n    console.error('保存图片时出错:', error);\n    提示('保存图片时出错: ' + error.message);\n  } finally {\n    // 恢复按钮状态\n    saveBtn.textContent = originalText;\n    saveBtn.disabled = false;\n  }\n}\n\n// 创建新文件夹\nfunction createNewFolder() {\n  const folderName = prompt('请输入新文件夹名称:');\n  if (folderName) {\n    const currentPath = document.getElementById('folder-input').value.trim();\n    const newPath = currentPath ? `${currentPath}/${folderName}` : folderName;\n    document.getElementById('folder-input').value = newPath;\n  }\n}\n\n// 保存文件夹到历史记录\nfunction saveFolderToHistory(folderPath) {\n  chrome.runtime.sendMessage({ action: \"getFolderHistory\" }, (response) => {\n    let history = response.history || [];\n    \n    // 移除重复项\n    history = history.filter(item => item !== folderPath);\n    \n    // 添加到开头\n    history.unshift(folderPath);\n    \n    // 限制历史记录数量\n    if (history.length > 10) {\n      history = history.slice(0, 10);\n    }\n    \n    // 保存更新后的历史记录\n    chrome.runtime.sendMessage({\n      action: \"saveFolderHistory\",\n      history: history\n    });\n  });\n}\n\n// 获取指定路径下的子文件夹\nfunction getSubfolders(folderPaths, currentPath) {\n  const subfolders = new Set();\n\n  folderPaths.forEach(path => {\n    if (!path) return;\n\n    if (currentPath === '') {\n      // 如果当前路径为空，显示所有根级文件夹\n      const firstPart = path.split('/')[0];\n      if (firstPart) {\n        subfolders.add(firstPart);\n      }\n    } else {\n      // 如果当前路径不为空，查找子文件夹\n      if (path.startsWith(currentPath + '/')) {\n        const remainingPath = path.substring(currentPath.length + 1);\n        const nextPart = remainingPath.split('/')[0];\n        if (nextPart) {\n          subfolders.add(currentPath + '/' + nextPart);\n        }\n      }\n    }\n  });\n\n  return Array.from(subfolders).sort();\n}\n\n// 渲染子文件夹列表\nfunction renderSubfolders(subfolders, currentPath) {\n  let html = '';\n\n  // 如果不是根目录，添加返回上级按钮\n  if (currentPath) {\n    const parentPath = currentPath.split('/').slice(0, -1).join('/');\n    html += `\n      <div class=\"tree-node back-button\" data-path=\"${parentPath}\">\n        <span class=\"tree-node-icon\">⬆️</span>\n        <span class=\"tree-node-text\">返回上级 (${parentPath || '根目录'})</span>\n      </div>\n    `;\n  }\n\n  // 添加子文件夹\n  subfolders.forEach(folderPath => {\n    const folderName = folderPath.split('/').pop();\n    html += `\n      <div class=\"tree-node\" data-path=\"${folderPath}\">\n        <span class=\"tree-node-icon\">📁</span>\n        <span class=\"tree-node-text\">${folderName}</span>\n      </div>\n    `;\n  });\n\n  return html;\n}\n\n// 构建文件夹树结构（保留原函数以备后用）\nfunction buildFolderTree(folderPaths) {\n  const tree = {};\n\n  folderPaths.forEach(path => {\n    if (!path) return;\n\n    const parts = path.split('/').filter(part => part.trim());\n    let current = tree;\n\n    parts.forEach((part, index) => {\n      if (!current[part]) {\n        current[part] = {\n          name: part,\n          path: parts.slice(0, index + 1).join('/'),\n          children: {},\n          level: index\n        };\n      }\n      current = current[part].children;\n    });\n  });\n\n  return flattenTree(tree, 0);\n}\n\n// 将树结构扁平化为数组\nfunction flattenTree(tree, level) {\n  const result = [];\n\n  Object.keys(tree).sort().forEach(key => {\n    const node = tree[key];\n    result.push({\n      name: node.name,\n      path: node.path,\n      level: level,\n      hasChildren: Object.keys(node.children).length > 0\n    });\n\n    // 递归添加子节点\n    result.push(...flattenTree(node.children, level + 1));\n  });\n\n  return result;\n}\n\n// 渲染文件夹树\nfunction renderFolderTree(treeNodes) {\n  return treeNodes.map(node => {\n    const icon = node.hasChildren ? '📁' : '📂';\n    return `\n      <div class=\"tree-node level-${node.level}\" data-path=\"${node.path}\">\n        <span class=\"tree-node-icon\">${icon}</span>\n        <span class=\"tree-node-text\">${node.name}</span>\n      </div>\n    `;\n  }).join('');\n}\n\n// 渲染软件文件夹树\nfunction renderSoftwareFolderTree(folders, rootPath, level = 0) {\n  let html = '';\n\n  folders.forEach(folder => {\n    const hasChildren = folder.children && folder.children.length > 0;\n    const icon = hasChildren ? '📁' : '📂';\n\n    html += `\n      <div class=\"tree-node level-${level}\" data-path=\"${folder.path}\">\n        <span class=\"tree-node-icon\">${icon}</span>\n        <span class=\"tree-node-text\">${folder.name}</span>\n      </div>\n    `;\n\n    // 递归渲染子文件夹\n    if (hasChildren && level < 2) { // 限制显示层级\n      html += renderSoftwareFolderTree(folder.children, rootPath, level + 1);\n    }\n  });\n\n  return html;\n}\n\n// 更新文件夹树选中状态\nfunction updateFolderTreeSelection(folderPath) {\n  const treeView = document.getElementById('folder-tree-view');\n  if (!treeView) return;\n\n  treeView.querySelectorAll('.tree-node').forEach(node => {\n    node.classList.remove('selected');\n    if (node.dataset.path === folderPath) {\n      node.classList.add('selected');\n    }\n  });\n}\n\n// 从URL获取文件名\nfunction getFilenameFromUrl(url) {\n  try {\n    const urlObj = new URL(url);\n    const pathname = urlObj.pathname;\n    const filename = pathname.split('/').pop();//.pop是取出最后一个元素\n\n    // 如果没有扩展名，添加.jpg\n     if (filename.includes('.png')) {\n      return filename + '.png';\n    }else if (filename.includes('.jpg')) {\n      return filename + '.jpg';\n    }else if (filename.includes('.jpeg')) {\n      return filename + '.jpeg';\n    }else if (filename.includes('.gif')) {\n      return filename + '.gif';\n    }else if (filename.includes('webp')) {\n      return filename + '.webp';\n    }else if (filename.includes('.svg')) {\n      return filename + '.svg';\n    }else if (!filename.includes('.')) {\n      return filename + '.png';\n    }\n\n    return filename || 'image.png';\n  } catch (error) {\n    return 'image.png';\n  }\n}\nfunction 提示(message) {\n  var 提示信息 = document.createElement('div')\n  提示信息.id = '提示信息';\n    document.body.appendChild(提示信息);\n    提示信息.style.position = 'fixed';\n    提示信息.style.zIndex = '9999';\n    提示信息.style.top = '50%';\n    提示信息.style.left = '50%';\n    提示信息.style.transform = 'translate(-50%, -50%)';\n    提示信息.style.padding = '20px';\n    提示信息.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    提示信息.style.color = 'white';\n    提示信息.style.borderRadius = '10px';\n    提示信息.style.fontSize = '18px';\n    提示信息.innerHTML = message\n    提示信息.style.display = 'block  ';\n    setTimeout(function() {\n        提示信息.style.display = 'none';\n    }, 2000);\n}\n\nfunction getSettings() {\n  return new Promise((resolve) => {\n    const settings = JSON.parse(localStorage.getItem('settings') || '{}');\n    resolve(settings);\n  });\n}\n\n// 尝试解析大图\nasync function tryParseLargeImage(originalUrl) {\n  try {\n    // 从存储中获取设置\n    const settings = await getSettings();\n\n    // 检查是否启用大图解析\n    if (!settings.enableLargeImage || !settings.largeImageRules) {\n      return originalUrl;\n    }\n\n    return parseLargeImageUrl(originalUrl, settings.largeImageRules);\n  } catch (error) {\n    console.error('大图解析失败:', error);\n    return originalUrl;\n  }\n}\n\n// 解析大图URL\nfunction parseLargeImageUrl(originalUrl, rules) {\n  const ruleLines = rules.split('\\n').filter(line => line.trim() && !line.trim().startsWith('#'));\n\n  for (const rule of ruleLines) {\n    const parts = rule.split('->').map(part => part.trim());\n    if (parts.length !== 2) continue;\n\n    const [pattern, replacement] = parts;\n\n    try {\n      // 将模式转换为正则表达式\n      const regexPattern = pattern\n        .replace(/\\{id\\}/g, '([^/]+)')\n        .replace(/\\{filename\\}/g, '([^/]+)')\n        .replace(/\\{ext\\}/g, '([^.]+)')\n        .replace(/\\{domain\\}/g, '([^/]+)')\n        .replace(/\\{path\\}/g, '(.+)')\n        .replace(/\\./g, '\\\\.')\n        .replace(/\\//g, '\\\\/');\n\n      const regex = new RegExp('^' + regexPattern + '$');\n      const match = originalUrl.match(regex);\n\n      if (match) {\n        // 替换占位符\n        let result = replacement;\n        const placeholders = pattern.match(/\\{[^}]+\\}/g) || [];\n\n        placeholders.forEach((placeholder, index) => {\n          const value = match[index + 1];\n          result = result.replace(new RegExp('\\\\' + placeholder, 'g'), value);\n        });\n\n        return result;\n      }\n    } catch (error) {\n      console.error('规则解析错误:', rule, error);\n    }\n  }\n\n  return originalUrl; // 如果没有匹配的规则，返回原URL\n}", "enabled": true}]