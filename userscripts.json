[{"id": "1753057587337", "name": "修改背景色", "match": "*://*.so.com/*", "code": "body{background:red}", "enabled": true}, {"id": "1753064539861", "name": "手机壳", "match": "*://*.haixingdiy.cn/*", "code": "// Your code here...\nvar dd = 'tbody > tr.order-table-row.ant-table-row.ng-star-inserted > td:nth-child(5) > div > app-input-address > div.edit > span:nth-child(2)'\n        //'table > tbody > tr > td:nth-child(4) > app-input-address > span > span > img'\nvar ming = 'table > tbody > tr.order-table-tr.ant-table-row.ng-star-inserted > td:nth-child(2) > div > div > div.v-goods-item-goodsname > span:nth-child(2)'\n//'table > tbody > tr > td:nth-child(2) > div > div> div.gig-vertical.v-goods-item-goodsname > span:nth-child(2)'\nvar im = 'table > tbody > tr > td:nth-child(2) > div > div > div:nth-child(1) > div > img'\nvar v0 = 'ant-tabs-tab-active'\nvar inp = '  td  input[class*=\"checkbox\"]'\nvar kd = 'body > app-root > app-dt > app-layout > nz-layout > nz-content > nz-spin > div > app-works-manage > div > div > nz-layout > nz-content.cont.ant-layout-content > nz-affix > div > div > button:nth-child(3)'\nvar tb = 'label input[class*=\"radio\"] '\nvar 初始数量 = 0\n\nsetInterval(() => {\n    //if (document.querySelectorAll(' div.ant-tabs-bar.ant-tabs-top-bar.ant-tabs-default-bar.ng-star-inserted > div > div > div > div > div:nth-child(1) > div:nth-child(6)')[0].className.indexOf(v0) == -1) {\n        var 数量 = document.querySelectorAll(dd).length\n        初始数量 = document.getElementsByClassName('确认手机型号').length\n\n        if (数量 != 初始数量) {\n            初始数量 = 数量;\n            console.log(数量);\n            刷新()\n            //初始数量 = document.getElementsByClassName('确认手机型号').length\n        }\n    //}\n}, 1000)\nfunction 刷新() {\n    console.log('刷新');\n    for (var i = 0; i < 初始数量; i++) {\n        document.querySelectorAll(dd)[i].style.display = \"none\"\n\n        var 型号 = document.querySelectorAll(ming)[i].innerText;\n        const 确认手机型号 = document.createElement('button');\n        确认手机型号.innerText = '是【' + 型号 + '】吗？';\n        确认手机型号.className = '确认手机型号';\n        确认手机型号.dataset.id = i\n        确认手机型号.addEventListener('click', (item) => {\n            document.getElementsByClassName('不是')[item.target.dataset.id].style.display = \"none\"\n            document.getElementsByClassName('拼多多')[item.target.dataset.id].style.display = \"block\"\n            document.getElementsByClassName('淘宝')[item.target.dataset.id].style.display = \"block\"\n            document.getElementsByClassName('确认手机型号')[item.target.dataset.id].style.display = \"none\"\n        })\n\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(确认手机型号)\n        const not = document.createElement('button');\n        not.innerText = '不是';\n        not.className = '不是';\n        not.dataset.id = i\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(not)\n        not.addEventListener('click', (item) => {\n            console.log(im, item.target.dataset.id);\n            document.querySelectorAll(im)[item.target.dataset.id].click();\n        })\n        const 确认平台1 = document.createElement('button');\n        确认平台1.innerText = '拼多多';\n        确认平台1.className = '拼多多';\n        确认平台1.style.display = \"none\";\n        //确认平台1.style.display=\"none\";\n        确认平台1.dataset.id = i\n        确认平台1.addEventListener('click', (item) => {\n            document.querySelectorAll(dd)[item.target.dataset.id].style.display = \"block\";\n            document.querySelectorAll(dd)[item.target.dataset.id].click();\n            document.getElementsByClassName('淘宝')[item.target.dataset.id].style.display = \"none\"\n            document.getElementsByClassName('拼多多')[item.target.dataset.id].style.display = \"none\"\n        })\n        const 确认平台2 = document.createElement('button');\n        确认平台2.innerText = '淘宝';\n        确认平台2.className = '淘宝';\n        确认平台2.style.display = \"none\";\n        确认平台2.dataset.id = i\n        确认平台2.addEventListener('click', (item) => {\n            document.querySelectorAll(dd)[item.target.dataset.id].style.display = \"block\";\n            document.querySelectorAll(inp)[item.target.dataset.id].click();\n            setTimeout(function () {\n                document.querySelector(kd).click();\n            }\n                , 100)\n            setTimeout(function () {\n                document.querySelectorAll(tb)[3].parentNode.parentNode.style.border = \"1px solid #f00\";\n                document.querySelectorAll(tb)[3].click();\n\n            }\n                , 300)\n            document.getElementsByClassName('淘宝')[item.target.dataset.id].style.display = \"none\"\n\n        }\n        )\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(确认平台1)\n        document.querySelectorAll(dd)[i].parentNode.parentNode.parentNode.parentNode.appendChild(确认平台2)\n    }\n}\nvar 新style = document.createElement('style');\n新style.innerHTML = `\n    .淘宝{background-color:#ff9000;color:#fff;border:1px solid #ff9000;border-radius:3px;padding:2px 5px;cursor:pointer;font-size:12px;}\n    .确认手机型号,.拼多多{background-color:#ff0030;color:#fff;border:1px solid #ff0030;border-radius:3px;padding:2px 5px;cursor:pointer;font-size:12px;}\n    .提醒:hover{color:white;bakckground_color:green;}\n     #floatingDiv {\n            position: absolute;\n            width: 100px;\n            height: 100px;\n            background-color: red;\n            color: white;\n            text-align: center;\n            padding: 10px;\n            font-size: 16px;\n            border-radius: 50%;\n        }\n    `\n\n/*ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(2),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(4),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(5),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(7),ul.default-menu.ant-menu.ant-menu-root.ant-menu-dark.ant-menu-horizontal > li:nth-child(8){ top: calc(100vh - 38px);    background: #37b3b0;}\n nz-content.v-search.ant-layout-content{height:10px!important;background:#666;overflow:HIDDEN;margin-top:-30px!important;position:absolute}\n    nz-content.v-search.ant-layout-content:hover{height:unset!important;background:unset;}\n  */\ndocument.head.appendChild(新style);\n`var 提醒=document.createElement('div')\n//提醒.style.position='fixed';\n提醒.style.padding='5px';\n提醒.style.left='0px';\n提醒.style.top='70%';\n提醒.style.backgroundColor='red';\n提醒.innerHTML=\"注意是不是淘宝订单！抖音订单！\"\n提醒.style`\n\n\n  // 创建一个 div 元素\n        var floatingDiv = document.createElement('div');\n        floatingDiv.id = 'floatingDiv';\n        floatingDiv.textContent = '注意是不是淘宝订单！抖音订单！';\n\n        // 将 div 添加到文档的 body 中\n        document.body.appendChild(floatingDiv);\n\n        // 获取 div 的宽度和高度\n        var divWidth = floatingDiv.offsetWidth;\n        var divHeight = floatingDiv.offsetHeight;\n\n        // 初始化 div 的位置\n        var posX = Math.random() * (window.innerWidth - divWidth);\n        var posY = Math.random() * (window.innerHeight - divHeight);\n\n        // 初始化移动速度（随机生成）\n        var speedX = (Math.random() - 0.5) * 4; // 水平速度\n        var speedY = (Math.random() - 0.5) * 4; // 垂直速度\n\n        // 更新 div 位置的函数\n        function updatePosition() {\n            // 更新位置\n            posX += speedX;\n            posY += speedY;\n\n            // 检测是否碰到左右边界\n            if (posX <= 0 || posX >= window.innerWidth - divWidth) {\n                speedX = -speedX; // 反转水平方向\n            }\n\n            // 检测是否碰到上下边界\n            if (posY <= 0 || posY >= window.innerHeight - divHeight) {\n                speedY = -speedY; // 反转垂直方向\n            }\n\n            // 设置 div 的新位置\n            floatingDiv.style.left = posX + 'px';\n            floatingDiv.style.top = posY + 'px';\n        }\n\n        // 每 16 毫秒更新一次位置（约 60 帧/秒）\n        setInterval(updatePosition, 16);\ndocument.body.appendChild(floatingDiv);", "enabled": true}]