<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>密码管理器</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header h1 {
      font-size: 20px;
      font-weight: 600;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s;
    }
    
    .btn-primary {
      background: rgba(255,255,255,0.2);
      color: white;
    }
    
    .btn-primary:hover {
      background: rgba(255,255,255,0.3);
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #5a6268;
    }
    
    .main-content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }
    
    .search-section {
      margin-bottom: 20px;
    }
    
    .search-box {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      outline: none;
    }
    
    .search-box:focus {
      border-color: #ff6b6b;
      box-shadow: 0 0 0 2px rgba(255,107,107,0.2);
    }
    
    .passwords-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
    }
    
    .password-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      transition: all 0.3s;
    }
    
    .password-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .site-info {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .site-favicon {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      background: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }
    
    .site-name {
      font-weight: 600;
      font-size: 16px;
      color: #333;
    }
    
    .card-actions {
      display: flex;
      gap: 5px;
    }
    
    .btn-small {
      padding: 4px 8px;
      font-size: 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .btn-edit {
      background: #ffc107;
      color: #333;
    }
    
    .btn-edit:hover {
      background: #e0a800;
    }
    
    .btn-delete {
      background: #dc3545;
      color: white;
    }
    
    .btn-delete:hover {
      background: #c82333;
    }
    
    .credential-field {
      margin-bottom: 12px;
    }
    
    .field-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
      display: block;
    }
    
    .field-value {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .field-input {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      outline: none;
    }
    
    .field-input:focus {
      border-color: #ff6b6b;
      box-shadow: 0 0 0 2px rgba(255,107,107,0.2);
    }
    
    .field-text {
      flex: 1;
      padding: 8px 12px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      font-size: 14px;
      font-family: monospace;
    }
    
    .password-field {
      filter: blur(4px);
      transition: filter 0.3s;
    }
    
    .password-field.revealed {
      filter: none;
    }
    
    .btn-icon {
      padding: 6px;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s;
    }
    
    .btn-icon:hover {
      background: #e9ecef;
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }
    
    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
    
    .empty-state h3 {
      font-size: 18px;
      margin-bottom: 8px;
      color: #333;
    }
    
    .empty-state p {
      font-size: 14px;
      color: #666;
    }
    
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    
    .modal-content {
      background: white;
      border-radius: 12px;
      padding: 24px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
    
    .btn-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }
    
    .form-group {
      margin-bottom: 16px;
    }
    
    .form-label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #333;
    }
    
    .form-input {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      outline: none;
    }
    
    .form-input:focus {
      border-color: #ff6b6b;
      box-shadow: 0 0 0 2px rgba(255,107,107,0.2);
    }
    
    .modal-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🔐 密码管理器</h1>
    <div class="header-actions">
      <button class="btn btn-primary" onclick="showAddModal()">添加密码</button>
      <button class="btn btn-secondary" onclick="closeWindow()">关闭</button>
    </div>
  </div>
  
  <div class="main-content">
    <div class="search-section">
      <input type="text" class="search-box" placeholder="搜索网站或用户名..." onkeyup="searchPasswords(this.value)">
    </div>
    
    <div class="passwords-grid" id="passwords-grid">
      <!-- 密码卡片将在这里动态生成 -->
    </div>
    
    <div class="empty-state" id="empty-state" style="display: none;">
      <div class="empty-state-icon">🔐</div>
      <h3>暂无保存的密码</h3>
      <p>点击"添加密码"按钮开始管理您的密码</p>
    </div>
  </div>

  <!-- 添加/编辑密码模态框 -->
  <div class="modal" id="password-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="modal-title">添加密码</h3>
        <button class="btn-close" onclick="closeModal()">&times;</button>
      </div>
      
      <form id="password-form">
        <div class="form-group">
          <label class="form-label">网站名称</label>
          <input type="text" class="form-input" id="site-name" required>
        </div>
        
        <div class="form-group">
          <label class="form-label">网站URL</label>
          <input type="url" class="form-input" id="site-url" placeholder="https://example.com">
        </div>
        
        <div class="form-group">
          <label class="form-label">用户名/邮箱</label>
          <input type="text" class="form-input" id="username" required>
        </div>
        
        <div class="form-group">
          <label class="form-label">密码</label>
          <input type="password" class="form-input" id="password" required>
        </div>
        
        <div class="form-group">
          <label class="form-label">备注</label>
          <textarea class="form-input" id="notes" rows="3" placeholder="可选的备注信息"></textarea>
        </div>
        
        <div class="modal-actions">
          <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
          <button type="submit" class="btn btn-primary" style="background:#c82333">保存</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    let passwords = []
    let editingId = null
    
    // 初始化
    document.addEventListener('DOMContentLoaded', async () => {
      await loadPasswords()
      renderPasswords()
    })
    
    // 关闭窗口
    function closeWindow() {
      window.close()
    }
    
    // 加载密码
    async function loadPasswords() {
      try {
        passwords = await window.electronAPI.getPasswords()
      } catch (error) {
        console.error('Failed to load passwords:', error)
        passwords = []
      }
    }
    
    // 保存密码
    async function savePasswords() {
      try {
        await window.electronAPI.savePasswords(passwords)
      } catch (error) {
        console.error('Failed to save passwords:', error)
      }
    }

    // 渲染密码列表
    function renderPasswords() {
      const grid = document.getElementById('passwords-grid')
      const emptyState = document.getElementById('empty-state')

      if (passwords.length === 0) {
        grid.style.display = 'none'
        emptyState.style.display = 'block'
        return
      }

      grid.style.display = 'grid'
      emptyState.style.display = 'none'
      grid.innerHTML = ''

      passwords.forEach(password => {
        const card = createPasswordCard(password)
        grid.appendChild(card)
      })
    }

    // 创建密码卡片
    function createPasswordCard(password) {
      const card = document.createElement('div')
      card.className = 'password-card'

      const domain = extractDomain(password.url)
      const favicon = password.url ? `https://www.google.com/s2/favicons?domain=${domain}&sz=24` : '🌐'

      card.innerHTML = `
        <div class="card-header">
          <div class="site-info">
            <div class="site-favicon">${typeof favicon === 'string' && favicon.startsWith('http') ? `<img src="${favicon}" width="24" height="24" onerror="this.parentElement.innerHTML='🌐'">` : favicon}</div>
            <div class="site-name">${escapeHtml(password.siteName)}</div>
          </div>
          <div class="card-actions">
            <button class="btn-small btn-edit" onclick="editPassword('${password.id}')">编辑</button>
            <button class="btn-small btn-delete" onclick="deletePassword('${password.id}')">删除</button>
          </div>
        </div>

        <div class="credential-field">
          <label class="field-label">用户名</label>
          <div class="field-value">
            <div class="field-text">${escapeHtml(password.username)}</div>
            <button class="btn-icon" onclick="copyToClipboard('${escapeHtml(password.username)}')" title="复制用户名">📋</button>
          </div>
        </div>

        <div class="credential-field">
          <label class="field-label">密码</label>
          <div class="field-value">
            <div class="field-text password-field" id="password-${password.id}">${'•'.repeat(password.password.length)}</div>
            <button class="btn-icon" onclick="togglePasswordVisibility('${password.id}')" title="显示/隐藏密码">👁</button>
            <button class="btn-icon" onclick="copyToClipboard('${escapeHtml(password.password)}')" title="复制密码">📋</button>
          </div>
        </div>

        ${password.url ? `
        <div class="credential-field">
          <label class="field-label">网站</label>
          <div class="field-value">
            <div class="field-text">${escapeHtml(password.url)}</div>
            <button class="btn-icon" onclick="openUrl('${escapeHtml(password.url)}')" title="打开网站">🔗</button>
          </div>
        </div>
        ` : ''}

        ${password.notes ? `
        <div class="credential-field">
          <label class="field-label">备注</label>
          <div class="field-value">
            <div class="field-text">${escapeHtml(password.notes)}</div>
          </div>
        </div>
        ` : ''}
      `

      return card
    }

    // 显示添加模态框
    function showAddModal() {
      editingId = null
      document.getElementById('modal-title').textContent = '添加密码'
      document.getElementById('password-form').reset()
      document.getElementById('password-modal').style.display = 'flex'
    }

    // 编辑密码
    function editPassword(id) {
      const password = passwords.find(p => p.id === id)
      if (!password) return

      editingId = id
      document.getElementById('modal-title').textContent = '编辑密码'
      document.getElementById('site-name').value = password.siteName
      document.getElementById('site-url').value = password.url || ''
      document.getElementById('username').value = password.username
      document.getElementById('password').value = password.password
      document.getElementById('notes').value = password.notes || ''
      document.getElementById('password-modal').style.display = 'flex'
    }

    // 删除密码
    function deletePassword(id) {
      const password = passwords.find(p => p.id === id)
      if (!password) return

      if (confirm(`确定要删除 ${password.siteName} 的密码吗？`)) {
        passwords = passwords.filter(p => p.id !== id)
        savePasswords()
        renderPasswords()
      }
    }

    // 关闭模态框
    function closeModal() {
      document.getElementById('password-modal').style.display = 'none'
      editingId = null
    }

    // 表单提交
    document.getElementById('password-form').addEventListener('submit', async (e) => {
      e.preventDefault()

      const formData = {
        siteName: document.getElementById('site-name').value.trim(),
        url: document.getElementById('site-url').value.trim(),
        username: document.getElementById('username').value.trim(),
        password: document.getElementById('password').value,
        notes: document.getElementById('notes').value.trim()
      }

      if (editingId) {
        // 编辑现有密码
        const index = passwords.findIndex(p => p.id === editingId)
        if (index !== -1) {
          passwords[index] = {
            ...passwords[index],
            ...formData,
            updatedAt: new Date().toISOString()
          }
        }
      } else {
        // 添加新密码
        const newPassword = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          ...formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        passwords.push(newPassword)
      }

      await savePasswords()
      renderPasswords()
      closeModal()
    })

    // 搜索密码
    function searchPasswords(query) {
      const cards = document.querySelectorAll('.password-card')
      const searchQuery = query.toLowerCase()

      cards.forEach(card => {
        const siteName = card.querySelector('.site-name').textContent.toLowerCase()
        const username = card.querySelector('.credential-field .field-text').textContent.toLowerCase()

        if (siteName.includes(searchQuery) || username.includes(searchQuery)) {
          card.style.display = 'block'
        } else {
          card.style.display = 'none'
        }
      })
    }

    // 切换密码可见性
    function togglePasswordVisibility(id) {
      const passwordField = document.getElementById(`password-${id}`)
      const password = passwords.find(p => p.id === id)

      if (passwordField.classList.contains('revealed')) {
        passwordField.classList.remove('revealed')
        passwordField.textContent = '•'.repeat(password.password.length)
      } else {
        passwordField.classList.add('revealed')
        passwordField.textContent = password.password
      }
    }

    // 复制到剪贴板
    async function copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        // 简单的提示
        const originalTitle = document.title
        document.title = '已复制到剪贴板'
        setTimeout(() => {
          document.title = originalTitle
        }, 1000)
      } catch (error) {
        console.error('Failed to copy to clipboard:', error)
      }
    }

    // 打开URL
    async function openUrl(url) {
      if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
        try {
          // 在主窗口的当前工作区新建标签打开
          await window.electronAPI.createTab(url)
        } catch (error) {
          console.error('Failed to open URL in main window:', error)
          // 如果失败，回退到新窗口打开
          window.open(url, '_blank')
        }
      }
    }

    // 提取域名
    function extractDomain(url) {
      if (!url) return ''
      try {
        return new URL(url).hostname
      } catch {
        return url
      }
    }

    // HTML转义
    function escapeHtml(text) {
      if (!text) return ''
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    }

    // 点击模态框外部关闭
    document.getElementById('password-modal').addEventListener('click', (e) => {
      if (e.target.classList.contains('modal')) {
        closeModal()
      }
    })
  </script>
</body>
</html>
