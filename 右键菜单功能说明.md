# 🖱️ 右键菜单功能说明

## ✅ 新增功能

### 1. 复制链接地址
**功能描述：** 右键点击网页中的链接时，可以复制链接地址到剪贴板

**使用方法：**
1. 在网页中右键点击任意链接
2. 选择"复制链接地址"
3. 链接地址将自动复制到系统剪贴板

**技术实现：**
```javascript
// 检测链接右键菜单
if (params.linkURL) {
  menu.append(new MenuItem({
    label: '复制链接地址',
    click: () => {
      require('electron').clipboard.writeText(params.linkURL)
    }
  }))
}
```

### 2. 复制图片地址
**功能描述：** 右键点击网页中的图片时，可以复制图片地址到剪贴板

**使用方法：**
1. 在网页中右键点击任意图片
2. 选择"复制图片地址"
3. 图片地址将自动复制到系统剪贴板

**技术实现：**
```javascript
// 检测图片右键菜单
if (params.srcURL && params.mediaType === 'image') {
  menu.append(new MenuItem({
    label: '复制图片地址',
    click: () => {
      require('electron').clipboard.writeText(params.srcURL)
    }
  }))
}
```

### 3. 发送到下载管理器（链接）
**功能描述：** 右键点击链接时，可以直接发送到下载管理器进行下载

**使用方法：**
1. 在网页中右键点击任意链接
2. 选择"发送到下载管理器"
3. 文件将自动开始下载到设置的下载目录

**特色功能：**
- ✅ 自动重命名：如果文件名重复，自动添加时间戳重命名
- ✅ 智能下载：自动获取文件名和大小
- ✅ 实时进度：下载进度实时显示
- ✅ 完成通知：下载完成后显示系统通知

### 4. 发送到下载管理器（图片）
**功能描述：** 右键点击图片时，可以直接发送到下载管理器进行下载

**使用方法：**
1. 在网页中右键点击任意图片
2. 选择"发送到下载管理器"
3. 图片将自动开始下载到设置的下载目录

**特色功能：**
- ✅ 格式保持：保持原始图片格式（JPG、PNG、GIF等）
- ✅ 自动重命名：避免文件名冲突
- ✅ 批量下载：可以连续下载多张图片

## 🔧 技术实现详情

### 自动重命名机制
```javascript
// 检查文件是否已存在，如果存在则重命名
while (fs.existsSync(fullPath)) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
  filename = `${nameWithoutExt}_${timestamp}${ext}`
  fullPath = path.join(downloadPath, filename)
  counter++
  if (counter > 1000) break // 防止无限循环
}
```

### 下载进度监控
```javascript
request.on('response', (response) => {
  download.totalBytes = parseInt(response.headers['content-length'] || '0')
  
  response.on('data', (chunk) => {
    download.receivedBytes += chunk.length
    writeStream.write(chunk)
    
    // 通知下载管理器窗口更新
    if (this.downloadManagerWindow) {
      this.downloadManagerWindow.webContents.send('download:updated', download)
    }
  })
})
```

### 完成通知
```javascript
response.on('end', () => {
  writeStream.end()
  download.state = 'completed'
  download.endTime = new Date().toISOString()
  
  // 显示系统通知
  const { Notification } = require('electron')
  new Notification({
    title: '下载完成',
    body: `${filename} 已下载完成`
  }).show()
})
```

## 📋 完整的右键菜单功能

### 基础功能
- ✅ 返回
- ✅ 前进  
- ✅ 刷新
- ✅ 复制页面地址

### 文本选择功能
- ✅ 搜索选中文字
- ✅ 保存为笔记

### 密码管理功能
- ✅ 保存当前账号密码
- ✅ 填入已保存密码

### 链接功能（新增）
- ✅ **复制链接地址**
- ✅ **发送到下载管理器**

### 图片功能（增强）
- ✅ 复制图片
- ✅ **复制图片地址**（新增）
- ✅ 保存图片
- ✅ **发送到下载管理器**（新增）

## 🎯 使用场景

### 1. 批量下载图片
1. 浏览图片网站
2. 右键图片选择"发送到下载管理器"
3. 连续下载多张图片
4. 在下载管理器中查看进度

### 2. 下载文件链接
1. 在网页中找到下载链接
2. 右键选择"发送到下载管理器"
3. 自动开始下载，无需手动保存

### 3. 复制资源地址
1. 右键链接或图片
2. 选择"复制链接地址"或"复制图片地址"
3. 在其他地方粘贴使用

## 🔍 文件重命名规则

### 重命名格式
```
原文件名_YYYY-MM-DDTHH-mm-ss.扩展名
```

### 示例
```
原文件：image.jpg
重名后：image_2025-07-23T10-02-25.jpg

原文件：document.pdf  
重名后：document_2025-07-23T10-02-25.pdf
```

## 📥 下载管理器集成

### 自动功能
- ✅ 自动创建下载记录
- ✅ 自动设置保存路径
- ✅ 自动处理文件重名
- ✅ 自动显示下载进度
- ✅ 自动发送完成通知

### 手动操作
- 📥 打开下载管理器查看列表
- 📂 打开下载文件
- 📍 在文件夹中显示文件
- 🗑️ 清空下载列表

## 🎉 总结

现在您可以：

1. **右键链接** → 复制地址或发送到下载管理器
2. **右键图片** → 复制地址、保存图片或发送到下载管理器  
3. **自动重命名** → 避免文件名冲突
4. **实时进度** → 在下载管理器中查看下载状态
5. **系统通知** → 下载完成后自动提醒

所有功能都已集成到现有的右键菜单中，使用简单直观！🚀
