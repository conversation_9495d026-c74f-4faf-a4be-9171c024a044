<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>下载管理器</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header h1 {
      font-size: 20px;
      font-weight: 600;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s;
    }
    
    .btn-primary {
      background: rgba(255,255,255,0.2);
      color: white;
    }
    
    .btn-primary:hover {
      background: rgba(255,255,255,0.3);
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #5a6268;
    }
    
    .main-content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }
    
    .settings-section {
      background: white;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .settings-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 15px;
      color: #333;
    }
    
    .setting-item {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 15px;
    }
    
    .setting-label {
      font-weight: 500;
      color: #333;
      min-width: 120px;
    }
    
    .setting-input {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      outline: none;
    }
    
    .setting-input:focus {
      border-color: #4CAF50;
      box-shadow: 0 0 0 2px rgba(76,175,80,0.2);
    }
    
    .downloads-section {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .downloads-list {
      max-height: 400px;
      overflow-y: auto;
    }
    
    .download-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      margin-bottom: 10px;
      background: #f8f9fa;
      transition: all 0.3s;
    }
    
    .download-item:hover {
      background: #e9ecef;
      border-color: #4CAF50;
    }
    
    .download-icon {
      width: 40px;
      height: 40px;
      background: #4CAF50;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 15px;
    }
    
    .download-info {
      flex: 1;
    }
    
    .download-name {
      font-weight: 600;
      font-size: 14px;
      color: #333;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .download-details {
      font-size: 12px;
      color: #666;
      display: flex;
      gap: 15px;
    }
    
    .download-actions {
      display: flex;
      gap: 8px;
    }
    
    .btn-small {
      padding: 4px 8px;
      font-size: 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .btn-open {
      background: #2196F3;
      color: white;
    }
    
    .btn-open:hover {
      background: #1976D2;
    }
    
    .btn-show {
      background: #FF9800;
      color: white;
    }
    
    .btn-show:hover {
      background: #F57C00;
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }
    
    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
    
    .empty-state h3 {
      font-size: 18px;
      margin-bottom: 8px;
      color: #333;
    }
    
    .empty-state p {
      font-size: 14px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>📥 下载管理器</h1>
    <div class="header-actions">
      <button class="btn btn-primary" onclick="clearDownloads()">清空列表</button>
      <button class="btn btn-secondary" onclick="closeWindow()">关闭</button>
    </div>
  </div>
  
  <div class="main-content">
    <div class="settings-section">
      <div class="settings-title">下载设置</div>
      <div class="setting-item">
        <label class="setting-label">下载路径</label>
        <input type="text" class="setting-input" id="download-path" placeholder="选择下载保存路径..." readonly>
        <button class="btn btn-primary" onclick="selectDownloadPath()">选择路径</button>
      </div>
    </div>
    
    <div class="downloads-section">
      <div class="settings-title">下载列表</div>
      <div class="downloads-list" id="downloads-list">
        <div class="empty-state" id="empty-state">
          <div class="empty-state-icon">📥</div>
          <h3>暂无下载任务</h3>
          <p>开始下载文件后，下载记录将显示在这里</p>
        </div>
      </div>
    </div>
  </div>

  <script>
    let downloads = []
    let downloadPath = ''
    
    // 初始化
    document.addEventListener('DOMContentLoaded', async () => {
      await loadSettings()
      await loadDownloads()
      renderDownloads()
    })
    
    // 关闭窗口
    function closeWindow() {
      window.close()
    }
    
    // 加载设置
    async function loadSettings() {
      try {
        const settings = await window.electronAPI.getDownloadSettings()
        downloadPath = settings.downloadPath || ''
        document.getElementById('download-path').value = downloadPath
      } catch (error) {
        console.error('Failed to load settings:', error)
      }
    }
    
    // 保存设置
    async function saveSettings() {
      try {
        await window.electronAPI.saveDownloadSettings({ downloadPath })
      } catch (error) {
        console.error('Failed to save settings:', error)
      }
    }
    
    // 选择下载路径
    async function selectDownloadPath() {
      try {
        const path = await window.electronAPI.selectDownloadPath()
        if (path) {
          downloadPath = path
          document.getElementById('download-path').value = path
          await saveSettings()
        }
      } catch (error) {
        console.error('Failed to select download path:', error)
      }
    }
    
    // 加载下载列表
    async function loadDownloads() {
      try {
        downloads = await window.electronAPI.getDownloads()
      } catch (error) {
        console.error('Failed to load downloads:', error)
        downloads = []
      }
    }
    
    // 渲染下载列表
    function renderDownloads() {
      const downloadsList = document.getElementById('downloads-list')
      const emptyState = document.getElementById('empty-state')

      if (downloads.length === 0) {
        emptyState.style.display = 'block'
        downloadsList.innerHTML = ''
        downloadsList.appendChild(emptyState)
        return
      }

      emptyState.style.display = 'none'
      downloadsList.innerHTML = ''

      downloads.forEach(download => {
        const item = createDownloadItem(download)
        downloadsList.appendChild(item)
      })
    }

    // 创建下载项目
    function createDownloadItem(download) {
      const item = document.createElement('div')
      item.className = 'download-item'

      const fileExtension = getFileExtension(download.filename)
      const icon = getFileIcon(fileExtension)
      const fileSize = formatFileSize(download.totalBytes || 0)
      const downloadTime = new Date(download.startTime).toLocaleString()

      item.innerHTML = `
        <div class="download-icon">${icon}</div>
        <div class="download-info">
          <div class="download-name" title="${escapeHtml(download.filename)}">${escapeHtml(download.filename)}</div>
          <div class="download-details">
            <span>大小: ${fileSize}</span>
            <span>时间: ${downloadTime}</span>
            <span>状态: ${getStatusText(download.state)}</span>
          </div>
        </div>
        <div class="download-actions">
          ${download.state === 'completed' ? `
            <button class="btn-small btn-open" onclick="openFile('${escapeHtml(download.savePath)}')" title="打开文件">打开</button>
            <button class="btn-small btn-show" onclick="showInFolder('${escapeHtml(download.savePath)}')" title="在文件夹中显示">显示</button>
          ` : ''}
        </div>
      `

      return item
    }

    // 获取文件扩展名
    function getFileExtension(filename) {
      return filename.split('.').pop().toLowerCase()
    }

    // 获取文件图标
    function getFileIcon(extension) {
      const icons = {
        'pdf': '📄',
        'doc': '📝', 'docx': '📝',
        'xls': '📊', 'xlsx': '📊',
        'ppt': '📊', 'pptx': '📊',
        'txt': '📄',
        'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'bmp': '🖼️',
        'mp4': '🎬', 'avi': '🎬', 'mov': '🎬', 'wmv': '🎬',
        'mp3': '🎵', 'wav': '🎵', 'flac': '🎵',
        'zip': '📦', 'rar': '📦', '7z': '📦',
        'exe': '⚙️', 'msi': '⚙️',
        'html': '🌐', 'htm': '🌐',
        'css': '🎨',
        'js': '⚡',
        'json': '📋'
      }
      return icons[extension] || '📄'
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 获取状态文本
    function getStatusText(state) {
      const states = {
        'progressing': '下载中',
        'completed': '已完成',
        'cancelled': '已取消',
        'interrupted': '已中断'
      }
      return states[state] || '未知'
    }

    // HTML转义
    function escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    }

    // 打开文件
    async function openFile(filePath) {
      try {
        await window.electronAPI.openFile(filePath)
      } catch (error) {
        console.error('Failed to open file:', error)
        alert('无法打开文件')
      }
    }

    // 在文件夹中显示
    async function showInFolder(filePath) {
      try {
        await window.electronAPI.showInFolder(filePath)
      } catch (error) {
        console.error('Failed to show in folder:', error)
        alert('无法在文件夹中显示')
      }
    }

    // 清空下载列表
    function clearDownloads() {
      if (confirm('确定要清空下载列表吗？')) {
        downloads = []
        renderDownloads()
      }
    }

    // 监听下载更新
    if (window.electronAPI && window.electronAPI.onDownloadUpdated) {
      window.electronAPI.onDownloadUpdated((event, download) => {
        const existingIndex = downloads.findIndex(d => d.id === download.id)
        if (existingIndex >= 0) {
          downloads[existingIndex] = download
        } else {
          downloads.unshift(download)
        }
        renderDownloads()
      })
    }
  </script>
</body>
</html>
